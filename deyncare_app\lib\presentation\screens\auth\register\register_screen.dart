import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/routes/app_router.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'package:deyncare_app/core/utils/toast_util.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_event.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_state.dart';
import 'package:deyncare_app/presentation/screens/auth/register/widgets/registration_progress_view.dart';
import 'package:deyncare_app/presentation/screens/auth/verification/widgets/email_verification_view.dart';
import 'package:deyncare_app/presentation/screens/auth/payment/widgets/payment_processing_view.dart';
import 'package:deyncare_app/presentation/screens/auth/register/widgets/registration_form.dart';

/// Registration screen that manages the 3-step registration process
class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  @override
  void dispose() {
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Register'),
        backgroundColor: AppThemes.primaryColor,
        foregroundColor: ThemeUtils.getTextColor(context, type: TextColorType.primary),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: BlocConsumer<AuthBloc, AuthState>(
            listener: (context, state) {
              if (state is RegistrationComplete) {
                ToastUtil.showSuccess('Registration completed successfully! Please login to continue.');
                AppRouter.navigateToLogin(context);
              } else if (state is AuthEmailVerificationPending) {
                // Navigate to verification screen with required data
                AppRouter.navigateToVerification(
                  context,
                  user: state.user,
                  selectedPlanId: state.selectedPlanId,
                  expiresAt: state.expiresAt,
                  replace: true,
                );
              } else if (state is AuthPaymentRequired) {
                // Ensure we have all required data before navigating
                if (state.selectedPlanId.isEmpty) {
                  ToastUtil.showError('Error: Missing plan selection data. Please try again.');
                  return;
                }
                
                AppRouter.navigateToPayment(
                  context,
                  user: state.user,
                  selectedPlanId: state.selectedPlanId,
                  replace: true,
                );
              } else if (state is AuthFailure) {
                ToastUtil.showError(state.message);
              }
            },
            builder: (context, state) {
              if (state is RegistrationInProgress) {
                return RegistrationProgressView(
                  registrationProgress: state.registrationProgress,
                );
              } else if (state is AuthEmailVerificationPending) {
                return EmailVerificationView(
                  email: state.user.email,
                  userId: state.user.userId,
                  expiresAt: state.expiresAt,
                  isLoading: state is AuthLoading,
                  onVerifyPressed: (verificationCode) {
                    context.read<AuthBloc>().add(
                          VerifyEmailRequested(
                            email: state.user.email,
                            verificationCode: verificationCode,
                          ),
                        );
                  },
                  onResendCodeRequested: () {
                    context.read<AuthBloc>().add(
                          ResendVerificationCodeRequested(
                            email: state.user.email,
                          ),
                        );
                  },
                );
              } else if (state is PaymentProcessing) {
                return PaymentProcessingView(
                  paymentMethod: state.paymentMethod,
                  planName: state.planId, // Convert plan ID to name if needed
                  amount: 50.0, // Get amount from state or plan
                  onCancel: () {
                    context.read<AuthBloc>().add(const CancelPaymentRequested());
                  },
                );
              } else if (state is AuthAuthenticated) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  AppRouter.navigateToDashboard(context, replace: true);
                });
                return const Center(child: CircularProgressIndicator());
              }
              
              // For AuthInitial, AuthUnauthenticated, AuthLoading, and AuthFailure,
              // we show the registration form. The form itself will handle the loading
              return const RegistrationForm();
            },
          ),
        ),
      ),
    );
  }
}
