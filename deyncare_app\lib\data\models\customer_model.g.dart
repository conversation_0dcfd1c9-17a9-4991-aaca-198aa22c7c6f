// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerModel _$CustomerModelFromJson(Map<String, dynamic> json) =>
    CustomerModel(
      customerId: json['customerId'] as String,
      shopId: json['shopId'] as String,
      customerName: json['CustomerName'] as String,
      customerType: json['CustomerType'] as String,
      phone: json['phone'] as String,
      email: json['email'] as String?,
      address: json['address'] as String?,
      outstandingBalance: (json['outstandingBalance'] as num).toDouble(),
      creditLimit: (json['creditLimit'] as num).toDouble(),
      totalPurchaseAmount: (json['totalPurchaseAmount'] as num).toDouble(),
      lastPurchaseDate: json['lastPurchaseDate'] == null
          ? null
          : DateTime.parse(json['lastPurchaseDate'] as String),
      category: json['category'] as String,
      notes: json['notes'] as String?,
      riskProfile:
          RiskProfile.fromJson(json['riskProfile'] as Map<String, dynamic>),
      isDeleted: json['isDeleted'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$CustomerModelToJson(CustomerModel instance) =>
    <String, dynamic>{
      'customerId': instance.customerId,
      'shopId': instance.shopId,
      'CustomerName': instance.customerName,
      'CustomerType': instance.customerType,
      'phone': instance.phone,
      'email': instance.email,
      'address': instance.address,
      'outstandingBalance': instance.outstandingBalance,
      'creditLimit': instance.creditLimit,
      'totalPurchaseAmount': instance.totalPurchaseAmount,
      'lastPurchaseDate': instance.lastPurchaseDate?.toIso8601String(),
      'category': instance.category,
      'notes': instance.notes,
      'riskProfile': instance.riskProfile,
      'isDeleted': instance.isDeleted,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

RiskProfile _$RiskProfileFromJson(Map<String, dynamic> json) => RiskProfile(
      currentRiskLevel: json['currentRiskLevel'] as String,
      riskScore: (json['riskScore'] as num).toDouble(),
      lastAssessment: json['lastAssessment'] == null
          ? null
          : DateTime.parse(json['lastAssessment'] as String),
      assessmentCount: (json['assessmentCount'] as num).toInt(),
      mlSource: json['mlSource'] as String? ?? 'system',
    );

Map<String, dynamic> _$RiskProfileToJson(RiskProfile instance) =>
    <String, dynamic>{
      'currentRiskLevel': instance.currentRiskLevel,
      'riskScore': instance.riskScore,
      'lastAssessment': instance.lastAssessment?.toIso8601String(),
      'assessmentCount': instance.assessmentCount,
      'mlSource': instance.mlSource,
    };

CreateCustomerRequest _$CreateCustomerRequestFromJson(
        Map<String, dynamic> json) =>
    CreateCustomerRequest(
      customerName: json['CustomerName'] as String,
      customerType: json['CustomerType'] as String,
      phone: json['phone'] as String,
      email: json['email'] as String?,
      address: json['address'] as String?,
      creditLimit: (json['creditLimit'] as num?)?.toDouble() ?? 0,
      category: json['category'] as String? ?? 'regular',
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$CreateCustomerRequestToJson(
        CreateCustomerRequest instance) =>
    <String, dynamic>{
      'CustomerName': instance.customerName,
      'CustomerType': instance.customerType,
      'phone': instance.phone,
      if (instance.email case final value?) 'email': value,
      if (instance.address case final value?) 'address': value,
      if (instance.creditLimit case final value?) 'creditLimit': value,
      if (instance.category case final value?) 'category': value,
      if (instance.notes case final value?) 'notes': value,
    };

UpdateCustomerRequest _$UpdateCustomerRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateCustomerRequest(
      customerName: json['CustomerName'] as String?,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      address: json['address'] as String?,
      creditLimit: (json['creditLimit'] as num?)?.toDouble(),
      category: json['category'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$UpdateCustomerRequestToJson(
        UpdateCustomerRequest instance) =>
    <String, dynamic>{
      'CustomerName': instance.customerName,
      if (instance.phone case final value?) 'phone': value,
      if (instance.email case final value?) 'email': value,
      if (instance.address case final value?) 'address': value,
      if (instance.creditLimit case final value?) 'creditLimit': value,
      if (instance.category case final value?) 'category': value,
      if (instance.notes case final value?) 'notes': value,
    };

CustomerListResponse _$CustomerListResponseFromJson(
        Map<String, dynamic> json) =>
    CustomerListResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: CustomerListData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CustomerListResponseToJson(
        CustomerListResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };

CustomerListData _$CustomerListDataFromJson(Map<String, dynamic> json) =>
    CustomerListData(
      customers: (json['customers'] as List<dynamic>)
          .map((e) => CustomerSummary.fromJson(e as Map<String, dynamic>))
          .toList(),
      pagination:
          PaginationInfo.fromJson(json['pagination'] as Map<String, dynamic>),
      summary: CustomerSummaryStats.fromJson(
          json['summary'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CustomerListDataToJson(CustomerListData instance) =>
    <String, dynamic>{
      'customers': instance.customers,
      'pagination': instance.pagination,
      'summary': instance.summary,
    };

CustomerSummary _$CustomerSummaryFromJson(Map<String, dynamic> json) =>
    CustomerSummary(
      customerId: json['customerId'] as String,
      customerName: json['customerName'] as String,
      phone: json['phone'] as String,
      customerType: json['customerType'] as String,
      riskProfile:
          RiskProfile.fromJson(json['riskProfile'] as Map<String, dynamic>),
      financialSummary: FinancialSummary.fromJson(
          json['financialSummary'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$CustomerSummaryToJson(CustomerSummary instance) =>
    <String, dynamic>{
      'customerId': instance.customerId,
      'customerName': instance.customerName,
      'phone': instance.phone,
      'customerType': instance.customerType,
      'riskProfile': instance.riskProfile,
      'financialSummary': instance.financialSummary,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

FinancialSummary _$FinancialSummaryFromJson(Map<String, dynamic> json) =>
    FinancialSummary(
      totalDebts: (json['totalDebts'] as num).toInt(),
      activeDebts: (json['activeDebts'] as num).toInt(),
      totalDebtAmount: (json['totalDebtAmount'] as num).toDouble(),
      totalOutstanding: (json['totalOutstanding'] as num).toDouble(),
      totalPaid: (json['totalPaid'] as num).toDouble(),
      paymentRatio: (json['paymentRatio'] as num).toInt(),
      hasOverdueDebts: json['hasOverdueDebts'] as bool,
    );

Map<String, dynamic> _$FinancialSummaryToJson(FinancialSummary instance) =>
    <String, dynamic>{
      'totalDebts': instance.totalDebts,
      'activeDebts': instance.activeDebts,
      'totalDebtAmount': instance.totalDebtAmount,
      'totalOutstanding': instance.totalOutstanding,
      'totalPaid': instance.totalPaid,
      'paymentRatio': instance.paymentRatio,
      'hasOverdueDebts': instance.hasOverdueDebts,
    };

PaginationInfo _$PaginationInfoFromJson(Map<String, dynamic> json) =>
    PaginationInfo(
      currentPage: (json['currentPage'] as num).toInt(),
      totalPages: (json['totalPages'] as num).toInt(),
      totalCustomers: (json['totalCustomers'] as num).toInt(),
      hasNextPage: json['hasNextPage'] as bool,
      hasPrevPage: json['hasPrevPage'] as bool,
    );

Map<String, dynamic> _$PaginationInfoToJson(PaginationInfo instance) =>
    <String, dynamic>{
      'currentPage': instance.currentPage,
      'totalPages': instance.totalPages,
      'totalCustomers': instance.totalCustomers,
      'hasNextPage': instance.hasNextPage,
      'hasPrevPage': instance.hasPrevPage,
    };

CustomerSummaryStats _$CustomerSummaryStatsFromJson(
        Map<String, dynamic> json) =>
    CustomerSummaryStats(
      totalCustomers: (json['totalCustomers'] as num).toInt(),
      riskDistribution: Map<String, int>.from(json['riskDistribution'] as Map),
      searchCriteria: json['searchCriteria'] == null
          ? null
          : SearchCriteria.fromJson(
              json['searchCriteria'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CustomerSummaryStatsToJson(
        CustomerSummaryStats instance) =>
    <String, dynamic>{
      'totalCustomers': instance.totalCustomers,
      'riskDistribution': instance.riskDistribution,
      'searchCriteria': instance.searchCriteria,
    };

SearchCriteria _$SearchCriteriaFromJson(Map<String, dynamic> json) =>
    SearchCriteria(
      search: json['search'] as String?,
      riskLevel: json['riskLevel'] as String?,
      customerType: json['customerType'] as String?,
    );

Map<String, dynamic> _$SearchCriteriaToJson(SearchCriteria instance) =>
    <String, dynamic>{
      'search': instance.search,
      'riskLevel': instance.riskLevel,
      'customerType': instance.customerType,
    };

CustomerDetailResponse _$CustomerDetailResponseFromJson(
        Map<String, dynamic> json) =>
    CustomerDetailResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: json['data'] == null
          ? null
          : CustomerDetailData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CustomerDetailResponseToJson(
        CustomerDetailResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };

CustomerDetailData _$CustomerDetailDataFromJson(Map<String, dynamic> json) =>
    CustomerDetailData(
      customer:
          CustomerBasicInfo.fromJson(json['customer'] as Map<String, dynamic>),
      statistics: json['statistics'] == null
          ? null
          : CustomerStatistics.fromJson(
              json['statistics'] as Map<String, dynamic>),
      debtHistory: json['debtHistory'] == null
          ? null
          : DebtHistory.fromJson(json['debtHistory'] as Map<String, dynamic>),
      paymentHistory: json['paymentHistory'] == null
          ? null
          : PaymentHistory.fromJson(
              json['paymentHistory'] as Map<String, dynamic>),
      recentActivity: json['recentActivity'] == null
          ? null
          : RecentActivity.fromJson(
              json['recentActivity'] as Map<String, dynamic>),
      riskProfile: json['riskProfile'] == null
          ? null
          : RiskProfile.fromJson(json['riskProfile'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CustomerDetailDataToJson(CustomerDetailData instance) =>
    <String, dynamic>{
      'customer': instance.customer,
      'statistics': instance.statistics,
      'debtHistory': instance.debtHistory,
      'paymentHistory': instance.paymentHistory,
      'recentActivity': instance.recentActivity,
      'riskProfile': instance.riskProfile,
    };

CustomerBasicInfo _$CustomerBasicInfoFromJson(Map<String, dynamic> json) =>
    CustomerBasicInfo(
      customerId: json['customerId'] as String?,
      customerName: json['customerName'] as String?,
      phone: json['phone'] as String?,
      customerType: json['customerType'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$CustomerBasicInfoToJson(CustomerBasicInfo instance) =>
    <String, dynamic>{
      'customerId': instance.customerId,
      'customerName': instance.customerName,
      'phone': instance.phone,
      'customerType': instance.customerType,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

CustomerStatistics _$CustomerStatisticsFromJson(Map<String, dynamic> json) =>
    CustomerStatistics(
      totalDebts: (json['totalDebts'] as num?)?.toInt(),
      activeDebts: (json['activeDebts'] as num?)?.toInt(),
      paidDebts: (json['paidDebts'] as num?)?.toInt(),
      financials: json['financials'] == null
          ? null
          : CustomerFinancials.fromJson(
              json['financials'] as Map<String, dynamic>),
      paymentBehavior: json['paymentBehavior'] == null
          ? null
          : PaymentBehavior.fromJson(
              json['paymentBehavior'] as Map<String, dynamic>),
      riskAnalysis: json['riskAnalysis'] == null
          ? null
          : RiskAnalysis.fromJson(json['riskAnalysis'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CustomerStatisticsToJson(CustomerStatistics instance) =>
    <String, dynamic>{
      'totalDebts': instance.totalDebts,
      'activeDebts': instance.activeDebts,
      'paidDebts': instance.paidDebts,
      'financials': instance.financials,
      'paymentBehavior': instance.paymentBehavior,
      'riskAnalysis': instance.riskAnalysis,
    };

CustomerFinancials _$CustomerFinancialsFromJson(Map<String, dynamic> json) =>
    CustomerFinancials(
      totalBorrowed: (json['totalBorrowed'] as num?)?.toDouble(),
      totalOutstanding: (json['totalOutstanding'] as num?)?.toDouble(),
      totalPaid: (json['totalPaid'] as num?)?.toDouble(),
      averageDebtAmount: (json['averageDebtAmount'] as num?)?.toDouble(),
      collectionRate: (json['collectionRate'] as num?)?.toInt(),
    );

Map<String, dynamic> _$CustomerFinancialsToJson(CustomerFinancials instance) =>
    <String, dynamic>{
      'totalBorrowed': instance.totalBorrowed,
      'totalOutstanding': instance.totalOutstanding,
      'totalPaid': instance.totalPaid,
      'averageDebtAmount': instance.averageDebtAmount,
      'collectionRate': instance.collectionRate,
    };

PaymentBehavior _$PaymentBehaviorFromJson(Map<String, dynamic> json) =>
    PaymentBehavior(
      totalPayments: (json['totalPayments'] as num?)?.toInt(),
      averagePaymentAmount: (json['averagePaymentAmount'] as num?)?.toDouble(),
      onTimePayments: (json['onTimePayments'] as num?)?.toInt(),
      latePayments: (json['latePayments'] as num?)?.toInt(),
      averagePaymentDelay: (json['averagePaymentDelay'] as num?)?.toInt(),
      maxPaymentDelay: (json['maxPaymentDelay'] as num?)?.toInt(),
    );

Map<String, dynamic> _$PaymentBehaviorToJson(PaymentBehavior instance) =>
    <String, dynamic>{
      'totalPayments': instance.totalPayments,
      'averagePaymentAmount': instance.averagePaymentAmount,
      'onTimePayments': instance.onTimePayments,
      'latePayments': instance.latePayments,
      'averagePaymentDelay': instance.averagePaymentDelay,
      'maxPaymentDelay': instance.maxPaymentDelay,
    };

RiskAnalysis _$RiskAnalysisFromJson(Map<String, dynamic> json) => RiskAnalysis(
      currentRiskLevel: json['currentRiskLevel'] as String?,
      riskScore: (json['riskScore'] as num?)?.toDouble(),
      lastAssessment: json['lastAssessment'] == null
          ? null
          : DateTime.parse(json['lastAssessment'] as String),
      assessmentHistory: (json['assessmentHistory'] as num?)?.toInt(),
      riskFactors: (json['riskFactors'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$RiskAnalysisToJson(RiskAnalysis instance) =>
    <String, dynamic>{
      'currentRiskLevel': instance.currentRiskLevel,
      'riskScore': instance.riskScore,
      'lastAssessment': instance.lastAssessment?.toIso8601String(),
      'assessmentHistory': instance.assessmentHistory,
      'riskFactors': instance.riskFactors,
    };

DebtHistory _$DebtHistoryFromJson(Map<String, dynamic> json) => DebtHistory(
      total: (json['total'] as num?)?.toInt(),
      debts: (json['debts'] as List<dynamic>?)
          ?.map((e) => DebtSummary.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$DebtHistoryToJson(DebtHistory instance) =>
    <String, dynamic>{
      'total': instance.total,
      'debts': instance.debts,
    };

DebtSummary _$DebtSummaryFromJson(Map<String, dynamic> json) => DebtSummary(
      debtId: json['debtId'] as String?,
      amount: (json['amount'] as num?)?.toDouble(),
      outstandingAmount: (json['outstandingAmount'] as num?)?.toDouble(),
      paidAmount: (json['paidAmount'] as num?)?.toDouble(),
      paymentRatio: (json['paymentRatio'] as num?)?.toInt(),
      dueDate: json['dueDate'] == null
          ? null
          : DateTime.parse(json['dueDate'] as String),
      riskLevel: json['riskLevel'] as String?,
      paymentDelay: (json['paymentDelay'] as num?)?.toInt(),
      isOnTime: json['isOnTime'] as bool?,
      status: json['status'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$DebtSummaryToJson(DebtSummary instance) =>
    <String, dynamic>{
      'debtId': instance.debtId,
      'amount': instance.amount,
      'outstandingAmount': instance.outstandingAmount,
      'paidAmount': instance.paidAmount,
      'paymentRatio': instance.paymentRatio,
      'dueDate': instance.dueDate?.toIso8601String(),
      'riskLevel': instance.riskLevel,
      'paymentDelay': instance.paymentDelay,
      'isOnTime': instance.isOnTime,
      'status': instance.status,
      'createdAt': instance.createdAt?.toIso8601String(),
    };

PaymentHistory _$PaymentHistoryFromJson(Map<String, dynamic> json) =>
    PaymentHistory(
      total: (json['total'] as num?)?.toInt(),
      recent: (json['recent'] as List<dynamic>?)
          ?.map((e) => PaymentSummary.fromJson(e as Map<String, dynamic>))
          .toList(),
      note: json['note'] as String?,
    );

Map<String, dynamic> _$PaymentHistoryToJson(PaymentHistory instance) =>
    <String, dynamic>{
      'total': instance.total,
      'recent': instance.recent,
      'note': instance.note,
    };

PaymentSummary _$PaymentSummaryFromJson(Map<String, dynamic> json) =>
    PaymentSummary(
      paymentId: json['paymentId'] as String,
      debtId: json['debtId'] as String?,
      amount: (json['amount'] as num).toDouble(),
      paymentDate: DateTime.parse(json['paymentDate'] as String),
      paymentMethod: json['paymentMethod'] as String,
      isOnTime: json['isOnTime'] as bool?,
      paymentDelay: (json['paymentDelay'] as num?)?.toInt(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$PaymentSummaryToJson(PaymentSummary instance) =>
    <String, dynamic>{
      'paymentId': instance.paymentId,
      'debtId': instance.debtId,
      'amount': instance.amount,
      'paymentDate': instance.paymentDate.toIso8601String(),
      'paymentMethod': instance.paymentMethod,
      'isOnTime': instance.isOnTime,
      'paymentDelay': instance.paymentDelay,
      'notes': instance.notes,
    };

RecentActivity _$RecentActivityFromJson(Map<String, dynamic> json) =>
    RecentActivity(
      newDebtsLast30Days: (json['newDebtsLast30Days'] as num?)?.toInt(),
      paymentsLast30Days: (json['paymentsLast30Days'] as num?)?.toInt(),
      amountPaidLast30Days: (json['amountPaidLast30Days'] as num?)?.toDouble(),
      lastPaymentDate: json['lastPaymentDate'] == null
          ? null
          : DateTime.parse(json['lastPaymentDate'] as String),
      lastDebtDate: json['lastDebtDate'] == null
          ? null
          : DateTime.parse(json['lastDebtDate'] as String),
    );

Map<String, dynamic> _$RecentActivityToJson(RecentActivity instance) =>
    <String, dynamic>{
      'newDebtsLast30Days': instance.newDebtsLast30Days,
      'paymentsLast30Days': instance.paymentsLast30Days,
      'amountPaidLast30Days': instance.amountPaidLast30Days,
      'lastPaymentDate': instance.lastPaymentDate?.toIso8601String(),
      'lastDebtDate': instance.lastDebtDate?.toIso8601String(),
    };

CustomerStatsResponse _$CustomerStatsResponseFromJson(
        Map<String, dynamic> json) =>
    CustomerStatsResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: CustomerStats.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CustomerStatsResponseToJson(
        CustomerStatsResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };

CustomerStats _$CustomerStatsFromJson(Map<String, dynamic> json) =>
    CustomerStats(
      totalCustomers: (json['totalCustomers'] as num).toInt(),
      activeCustomers: (json['activeCustomers'] as num).toInt(),
      newCustomers: (json['newCustomers'] as num).toInt(),
      returningCustomers: (json['returningCustomers'] as num).toInt(),
      riskDistribution: Map<String, int>.from(json['riskDistribution'] as Map),
      categoryDistribution:
          Map<String, int>.from(json['categoryDistribution'] as Map),
      totalOutstandingBalance:
          (json['totalOutstandingBalance'] as num).toDouble(),
      totalCreditLimit: (json['totalCreditLimit'] as num).toDouble(),
      avgRiskScore: (json['avgRiskScore'] as num).toDouble(),
    );

Map<String, dynamic> _$CustomerStatsToJson(CustomerStats instance) =>
    <String, dynamic>{
      'totalCustomers': instance.totalCustomers,
      'activeCustomers': instance.activeCustomers,
      'newCustomers': instance.newCustomers,
      'returningCustomers': instance.returningCustomers,
      'riskDistribution': instance.riskDistribution,
      'categoryDistribution': instance.categoryDistribution,
      'totalOutstandingBalance': instance.totalOutstandingBalance,
      'totalCreditLimit': instance.totalCreditLimit,
      'avgRiskScore': instance.avgRiskScore,
    };

CustomerDebtsResponse _$CustomerDebtsResponseFromJson(
        Map<String, dynamic> json) =>
    CustomerDebtsResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: json['data'] as List<dynamic>,
    );

Map<String, dynamic> _$CustomerDebtsResponseToJson(
        CustomerDebtsResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };
