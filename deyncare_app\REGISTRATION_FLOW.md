# DeynCare App: Registration Flow Explained

This document outlines the step-by-step user registration process in the DeynCare mobile application, from initial sign-up to account activation.

---

### Step 1: User Submits Registration Form

The flow begins when a new user fills out the registration form in the app. This form collects essential information:

-   **User Details**: Full Name, Email, Password, Phone Number
-   **Shop Details**: Shop Name, Shop Address
-   **Subscription Plan**: The user selects a subscription plan (e.g., Trial, Basic, Pro).

Once the form is complete, the user submits it.

---

### Step 2: API Call to Backend

The app gathers the form data and sends it to the backend via a `POST` request to the `/api/register/init` endpoint.

-   The user and shop details are sent in the request body.
-   The request is handled by the `initRegistrationController` on the backend.

---

### Step 3: Backend Creates User & Shop

The backend performs the following actions:

1.  **Input Validation**: It validates the incoming data. A critical check is to ensure the **email is not already registered**. If it is, the backend returns a `409 Conflict` error.
2.  **Create Records**: If the data is valid, it creates new `User` and `Shop` documents in the database.
3.  **Set Initial Status**: The new user's status is set to `pending_email_verification`.
4.  **Send Verification Email**: The system generates a unique verification code and sends it to the user's email address.
5.  **API Response**: The backend sends a `201 Created` response back to the app. This response includes the newly created `user` object and a `registrationProgress` object, which tells the app that the `nextStep` is `verify_email_required`.

---

### Step 4: Email Verification

The app receives the successful response and, based on the `nextStep` value, navigates the user to the **Email Verification screen**.

1.  The user enters the verification code they received in their email.
2.  The app sends this code to the backend for validation.

---

### Step 5: Account Activation (Plan-Dependent)

After the email is successfully verified, the flow depends on the subscription plan the user selected.

#### Case A: Trial Plan ($0)

If the user selected the **free trial plan**:

1.  **No Payment Needed**: Since the plan is free, the payment step is skipped entirely.
2.  **Activate Account**: The backend immediately activates the user's account and their shop. The user's status is updated to `active`.
3.  **API Response**: The backend responds with `registration_complete`.
4.  **Login & Redirect**: The Flutter app receives this response, securely stores the user's session token, and automatically redirects them to the main **Dashboard screen**. The registration is complete.

#### Case B: Paid Plan

If the user selected a **paid subscription plan**:

1.  **Payment Required**: After email verification, the backend responds with `nextStep: 'payment_required'`.
2.  **Navigate to Payment**: The app navigates the user to the **Payment screen**, where they can complete the transaction (e.g., via EVC Plus).
3.  **Process Payment**: Once payment is confirmed, the backend activates the user's account and shop.
4.  **Login & Redirect**: The backend responds with `registration_complete`, and the app logs the user in and redirects them to the Dashboard.

---

### Summary of Fixes

The original bug that caused multiple API calls was due to the app **crashing** after Step 3. The app failed to handle `null` values in the API response, which triggered a retry mechanism. The fixes I implemented ensure that the JSON parsing is **null-safe**, preventing the crash and allowing the registration flow to proceed smoothly as described above.
