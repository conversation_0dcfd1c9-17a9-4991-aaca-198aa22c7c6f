import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'package:deyncare_app/core/utils/network_error_handler.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';

// Create a test class that uses the NetworkErrorHandler mixin
class TestNetworkHandler with NetworkErrorHandler {}

void main() {
  late TestNetworkHandler networkHandler;

  setUp(() {
    networkHandler = TestNetworkHandler();
  });

  group('NetworkErrorHandler Tests', () {
    test('should return success result when network call succeeds', () async {
      // Arrange
      final expectedData = {'success': true, 'data': 'test'};
      
      // Act
      final result = await networkHandler.executeNetworkCall<Map<String, dynamic>>(
        networkCall: () async => expectedData,
        checkConnectivity: false, // Skip connectivity check for this test
      );
      
      // Assert
      expect(result.isSuccess, isTrue);
      expect(result.data, equals(expectedData));
      expect(result.error, isNull);
    });

    test('should return error result when network call fails', () async {
      // Arrange
      final exception = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.connectionTimeout,
        message: 'Connection timeout',
      );
      
      // Act
      final result = await networkHandler.executeNetworkCall<Map<String, dynamic>>(
        networkCall: () async => throw exception,
        checkConnectivity: false, // Skip connectivity check for this test
      );
      
      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.data, isNull);
      expect(result.error, isNotNull);
      expect(result.error!.type, equals(NetworkErrorType.timeout));
      expect(result.error!.message, contains('timed out'));
    });

    test('should retry network call on retryable errors', () async {
      // Arrange
      int callCount = 0;
      final exception = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.connectionTimeout,
        message: 'Connection timeout',
      );
      
      // Act
      final result = await networkHandler.executeNetworkCall<Map<String, dynamic>>(
        networkCall: () async {
          callCount++;
          if (callCount == 1) {
            throw exception;
          }
          return {'success': true, 'data': 'retry success'};
        },
        checkConnectivity: false,
        maxRetries: 1,
        retryDelay: Duration.zero,
      );
      
      // Assert
      expect(callCount, equals(2)); // Should have retried once
      expect(result.isSuccess, isTrue);
      expect(result.data!['data'], equals('retry success'));
    });

    test('should map API exceptions correctly', () async {
      // Arrange
      final apiException = ApiException(
        message: 'Test API error',
        code: 'test_error',
        statusCode: 400,
      );
      
      // Act
      final result = await networkHandler.executeNetworkCall<Map<String, dynamic>>(
        networkCall: () async => throw apiException,
        checkConnectivity: false,
      );
      
      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.error!.type, equals(NetworkErrorType.api));
      expect(result.error!.message, equals('Test API error'));
      expect(result.error!.code, equals('test_error'));
    });

    test('should map different error types correctly', () async {
      // Test for server error
      final serverException = DioException(
        requestOptions: RequestOptions(path: '/test'),
        response: Response(
          requestOptions: RequestOptions(path: '/test'),
          statusCode: 500,
        ),
        type: DioExceptionType.badResponse,
      );
      
      final serverResult = await networkHandler.executeNetworkCall<Map<String, dynamic>>(
        networkCall: () async => throw serverException,
        checkConnectivity: false,
      );
      
      expect(serverResult.error!.type, equals(NetworkErrorType.server));
      
      // Test for unauthorized error
      final authException = DioException(
        requestOptions: RequestOptions(path: '/test'),
        response: Response(
          requestOptions: RequestOptions(path: '/test'),
          statusCode: 401,
        ),
        type: DioExceptionType.badResponse,
      );
      
      final authResult = await networkHandler.executeNetworkCall<Map<String, dynamic>>(
        networkCall: () async => throw authException,
        checkConnectivity: false,
      );
      
      expect(authResult.error!.type, equals(NetworkErrorType.unauthorized));
    });
  });

  group('NetworkResult Tests', () {
    test('success constructor should set properties correctly', () {
      // Arrange & Act
      final data = {'test': 'data'};
      final result = NetworkResult<Map<String, dynamic>>.success(data);
      
      // Assert
      expect(result.isSuccess, isTrue);
      expect(result.data, equals(data));
      expect(result.error, isNull);
    });
    
    test('error constructor should set properties correctly', () {
      // Arrange & Act
      final error = NetworkError(
        type: NetworkErrorType.timeout,
        message: 'Test error',
      );
      final result = NetworkResult<Map<String, dynamic>>.error(error);
      
      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.data, isNull);
      expect(result.error, equals(error));
    });
    
    test('hasErrorOfType should return correct boolean', () {
      // Arrange
      final error = NetworkError(
        type: NetworkErrorType.timeout,
        message: 'Test error',
      );
      final result = NetworkResult<Map<String, dynamic>>.error(error);
      
      // Assert
      expect(result.hasErrorOfType(NetworkErrorType.timeout), isTrue);
      expect(result.hasErrorOfType(NetworkErrorType.offline), isFalse);
    });
  });
}
