# 📱 DeynCare Mobile App Integration Guide

## Quick Setup

### 1. Firebase Configuration
- **Project ID**: `deyncare-47d99`
- **Project Number**: `1089951025738`
- **Web API Key**: `AIzaSyCYP-Wb642o1uCI3BmjWU97jE_VWHope5k`

Download config files from Firebase Console:
- Android: `google-services.json` → `android/app/`
- iOS: `GoogleService-Info.plist` → `ios/Runner/`

### 2. Flutter Dependencies

```yaml
dependencies:
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10
  flutter_local_notifications: ^16.3.2
  dio: ^5.4.0
  provider: ^6.1.1
  flutter_secure_storage: ^9.0.0
  device_info_plus: ^9.1.1
```

### 3. Core Implementation

#### Firebase Service
```dart
// lib/services/firebase_service.dart
class FirebaseService {
  static String? _fcmToken;
  
  static Future<void> initialize() async {
    await Firebase.initializeApp();
    await _requestPermissions();
    await _getFCMToken();
    _setupHandlers();
  }
  
  static Future<String?> _getFCMToken() async {
    _fcmToken = await FirebaseMessaging.instance.getToken();
    return _fcmToken;
  }
  
  static String? get fcmToken => _fcmToken;
}
```

#### API Service
```dart
// lib/services/api_service.dart
class ApiService {
  static const String baseUrl = 'YOUR_BACKEND_URL/api';
  
  static Future<void> registerFCMToken(String token) async {
    await _dio.post('/fcm/register', data: {
      'token': token,
      'deviceInfo': await _getDeviceInfo(),
    });
  }
  
  static Future<void> sendTestNotification() async {
    await _dio.post('/fcm/test');
  }
}
```

## 🔗 API Endpoints

### Register FCM Token
```http
POST /api/fcm/register
Authorization: Bearer <token>
{
  "token": "fcm_token_here",
  "deviceInfo": {
    "platform": "android",
    "deviceId": "device_id",
    "appVersion": "1.0.0"
  }
}
```

### Test Notification
```http
POST /api/fcm/test
Authorization: Bearer <token>
```

## 📊 Notification Types

### 1. Debt Created
```json
{
  "type": "debt_created",
  "title": "💰 New Debt Created",
  "body": "Ahmed Hassan - $150.00 due Dec 28, 2024"
}
```

### 2. Payment Recorded
```json
{
  "type": "payment_recorded", 
  "title": "💳 Payment Recorded",
  "body": "Ahmed Hassan paid $75.00 - Risk: Low ✅"
}
```

### 3. Debt Reminder
```json
{
  "type": "debt_reminder",
  "title": "⏰ Debt Reminder", 
  "body": "3 debts due in 3 days - Total: $450.00"
}
```

## 🧪 Testing

### Test Screen
```dart
class TestScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Text('FCM Token: ${FirebaseService.fcmToken}'),
          ElevatedButton(
            onPressed: () => ApiService.registerFCMToken(FirebaseService.fcmToken!),
            child: Text('Register Token'),
          ),
          ElevatedButton(
            onPressed: () => ApiService.sendTestNotification(),
            child: Text('Test Notification'),
          ),
        ],
      ),
    );
  }
}
```

## 🚀 Deployment Checklist

### Android
- [ ] Add `google-services.json`
- [ ] Update `build.gradle` files
- [ ] Test on physical device

### iOS  
- [ ] Add `GoogleService-Info.plist`
- [ ] Enable push capabilities in Xcode
- [ ] Test on physical device

### Backend
- [ ] Firebase Admin SDK configured
- [ ] Environment variables set
- [ ] APIs deployed and tested

## 🔍 Troubleshooting

### Common Issues
1. **No FCM Token**: Check Firebase config files and permissions
2. **No Notifications**: Test with Firebase Console first
3. **Auth Errors**: Verify API endpoints and CORS
4. **Build Errors**: Run `flutter clean && flutter pub get`

### Debug Commands
```bash
flutter doctor
adb logcat | grep -i firebase  # Android
# Use Xcode console for iOS
```

## 📞 Support
- Backend Health: `/api/health`
- Firebase Console: [console.firebase.google.com/project/deyncare-47d99](https://console.firebase.google.com/project/deyncare-47d99)

---

**Next**: Web dashboard for SuperAdmin notification management 🚀 