import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/blocs/dashboard/dashboard_state.dart';

/// Chart data model for Syncfusion charts
class SyncfusionChartData {
  final String label;
  final double value;
  final Color? color;

  const SyncfusionChartData({
    required this.label,
    required this.value,
    this.color,
  });
}

/// A reusable chart widget supporting multiple chart types
/// Uses Syncfusion Flutter Charts with app theme colors
class DashboardChartWidget extends StatefulWidget {
  final String title;
  final ChartType chartType;
  final List<SyncfusionChartData> data;
  final bool isLoading;
  final VoidCallback? onRefresh;
  final String? subtitle;
  final bool showLegend;
  final bool enableInteraction;

  const DashboardChartWidget({
    super.key,
    required this.title,
    required this.chartType,
    required this.data,
    this.isLoading = false,
    this.onRefresh,
    this.subtitle,
    this.showLegend = true,
    this.enableInteraction = true,
  });

  @override
  State<DashboardChartWidget> createState() => _DashboardChartWidgetState();
}

class _DashboardChartWidgetState extends State<DashboardChartWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  TooltipBehavior? _tooltipBehavior;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _tooltipBehavior = TooltipBehavior(
      enable: widget.enableInteraction,
      color: AppThemes.primaryColor,
      textStyle: const TextStyle(color: Colors.white),
    );

    if (!widget.isLoading) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(DashboardChartWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!widget.isLoading && oldWidget.isLoading) {
      _animationController.forward();
    } else if (widget.isLoading && !oldWidget.isLoading) {
      _animationController.reset();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(context),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: widget.isLoading
                  ? _buildLoadingState()
                  : widget.data.isEmpty
                      ? _buildEmptyState(context)
                      : _buildChart(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                if (widget.subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    widget.subtitle!,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ],
            ),
          ),
          if (widget.onRefresh != null)
            GestureDetector(
              onTap: widget.onRefresh,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppThemes.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.refresh,
                  color: AppThemes.primaryColor,
                  size: 20,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppThemes.primaryColor,
          ),
          const SizedBox(height: 16),
          Text(
            'Loading chart data...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppThemes.textSecondaryColor,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bar_chart,
            size: 48,
            color: AppThemes.textLightColor,
          ),
          const SizedBox(height: 16),
          Text(
            'No data available',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppThemes.textSecondaryColor,
                  fontWeight: FontWeight.w500,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Pull to refresh or check back later',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppThemes.textLightColor,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildChart() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: switch (widget.chartType) {
        ChartType.pie => _buildPieChart(),
        ChartType.line => _buildLineChart(),
        ChartType.bar => _buildBarChart(),
      },
    );
  }

  Widget _buildPieChart() {
    return SfCircularChart(
      tooltipBehavior: _tooltipBehavior,
      legend: widget.showLegend
          ? const Legend(
              isVisible: true,
              position: LegendPosition.bottom,
            )
          : const Legend(isVisible: false),
      series: <PieSeries<SyncfusionChartData, String>>[
        PieSeries<SyncfusionChartData, String>(
          dataSource: widget.data,
          xValueMapper: (SyncfusionChartData data, _) => data.label,
          yValueMapper: (SyncfusionChartData data, _) => data.value,
          pointColorMapper: (SyncfusionChartData data, int index) =>
              data.color ?? _getChartColor(index),
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            textStyle: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
          enableTooltip: widget.enableInteraction,
          animationDuration: 1000,
        ),
      ],
    );
  }

  Widget _buildLineChart() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return SfCartesianChart(
      tooltipBehavior: _tooltipBehavior,
      legend: widget.showLegend
          ? const Legend(
              isVisible: true,
              position: LegendPosition.bottom,
            )
          : const Legend(isVisible: false),
      primaryXAxis: CategoryAxis(
        labelStyle: TextStyle(
          color: isDark ? Colors.grey[300] : AppThemes.textSecondaryColor,
          fontSize: 11,
        ),
        axisLine: AxisLine(
          color: isDark ? Colors.grey[600] : AppThemes.dividerColor,
        ),
        majorGridLines: const MajorGridLines(width: 0),
      ),
      primaryYAxis: NumericAxis(
        labelStyle: TextStyle(
          color: isDark ? Colors.grey[300] : AppThemes.textSecondaryColor,
          fontSize: 11,
        ),
        axisLine: AxisLine(
          color: isDark ? Colors.grey[600] : AppThemes.dividerColor,
        ),
        majorGridLines: MajorGridLines(
          color: (isDark ? Colors.grey[600] : AppThemes.dividerColor)
                  ?.withValues(alpha: 0.5) ??
              Colors.grey,
        ),
      ),
      series: <LineSeries<SyncfusionChartData, String>>[
        LineSeries<SyncfusionChartData, String>(
          dataSource: widget.data,
          xValueMapper: (SyncfusionChartData data, _) => data.label,
          yValueMapper: (SyncfusionChartData data, _) => data.value,
          color: AppThemes.primaryColor,
          width: 3,
          markerSettings: MarkerSettings(
            isVisible: true,
            color: AppThemes.primaryColor,
            borderColor: Colors.white,
            borderWidth: 2,
          ),
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            textStyle: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
          enableTooltip: widget.enableInteraction,
          animationDuration: 1000,
        ),
      ],
    );
  }

  Widget _buildBarChart() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return SfCartesianChart(
      tooltipBehavior: _tooltipBehavior,
      legend: widget.showLegend
          ? const Legend(
              isVisible: true,
              position: LegendPosition.bottom,
            )
          : const Legend(isVisible: false),
      primaryXAxis: CategoryAxis(
        labelStyle: TextStyle(
          color: isDark ? Colors.grey[300] : AppThemes.textSecondaryColor,
          fontSize: 11,
        ),
        axisLine: AxisLine(
          color: isDark ? Colors.grey[600] : AppThemes.dividerColor,
        ),
        majorGridLines: const MajorGridLines(width: 0),
      ),
      primaryYAxis: NumericAxis(
        labelStyle: TextStyle(
          color: isDark ? Colors.grey[300] : AppThemes.textSecondaryColor,
          fontSize: 11,
        ),
        axisLine: AxisLine(
          color: isDark ? Colors.grey[600] : AppThemes.dividerColor,
        ),
        majorGridLines: MajorGridLines(
          color: (isDark ? Colors.grey[600] : AppThemes.dividerColor)
                  ?.withValues(alpha: 0.5) ??
              Colors.grey,
        ),
      ),
      series: <ColumnSeries<SyncfusionChartData, String>>[
        ColumnSeries<SyncfusionChartData, String>(
          dataSource: widget.data,
          xValueMapper: (SyncfusionChartData data, _) => data.label,
          yValueMapper: (SyncfusionChartData data, _) => data.value,
          pointColorMapper: (SyncfusionChartData data, int index) =>
              data.color ?? _getChartColor(index),
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            textStyle: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
          enableTooltip: widget.enableInteraction,
          animationDuration: 1000,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(4),
            topRight: Radius.circular(4),
          ),
        ),
      ],
    );
  }

  Color _getChartColor(int index) {
    final colors = [
      AppThemes.primaryColor,
      AppThemes.secondaryColor,
      AppThemes.accentColor,
      AppThemes.warningColor,
      AppThemes.infoColor,
      AppThemes.successColor,
    ];
    return colors[index % colors.length];
  }
}

/// Chart type enumeration
enum ChartType {
  pie,
  line,
  bar,
}

/// A widget that displays multiple charts in a tabbed interface
class MultiChartWidget extends StatefulWidget {
  final String title;
  final Map<String, List<SyncfusionChartData>> chartData;
  final bool isLoading;
  final VoidCallback? onRefresh;

  const MultiChartWidget({
    super.key,
    required this.title,
    required this.chartData,
    this.isLoading = false,
    this.onRefresh,
  });

  @override
  State<MultiChartWidget> createState() => _MultiChartWidgetState();
}

class _MultiChartWidgetState extends State<MultiChartWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 3,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: 400,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(context),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                DashboardChartWidget(
                  title: '',
                  chartType: ChartType.bar,
                  data: widget.chartData['payments'] ?? [],
                  isLoading: widget.isLoading,
                  showLegend: false,
                ),
                DashboardChartWidget(
                  title: '',
                  chartType: ChartType.line,
                  data: widget.chartData['trends'] ?? [],
                  isLoading: widget.isLoading,
                  showLegend: false,
                ),
                DashboardChartWidget(
                  title: '',
                  chartType: ChartType.pie,
                  data: widget.chartData['distribution'] ?? [],
                  isLoading: widget.isLoading,
                  showLegend: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            widget.title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppThemes.textPrimaryColor,
                ),
          ),
          if (widget.onRefresh != null)
            GestureDetector(
              onTap: widget.onRefresh,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppThemes.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.refresh,
                  color: AppThemes.primaryColor,
                  size: 20,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppThemes.backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppThemes.primaryColor,
          borderRadius: BorderRadius.circular(12),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: AppThemes.textSecondaryColor,
        labelStyle: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 12,
        ),
        tabs: const [
          Tab(text: 'Payments'),
          Tab(text: 'Trends'),
          Tab(text: 'Distribution'),
        ],
      ),
    );
  }
}

/// Helper function to convert ChartData to SyncfusionChartData
List<SyncfusionChartData> convertToSyncfusionChartData(
    List<ChartData> chartDataList) {
  final List<SyncfusionChartData> result = [];

  for (final chartData in chartDataList) {
    // Convert the data list to SyncfusionChartData
    for (final dataPoint in chartData.data) {
      if (dataPoint.containsKey('label') && dataPoint.containsKey('value')) {
        result.add(SyncfusionChartData(
          label: dataPoint['label'].toString(),
          value: (dataPoint['value'] as num).toDouble(),
          color: dataPoint['color'] as Color?,
        ));
      }
    }
  }

  return result;
}
