import 'package:deyncare_app/domain/models/shop.dart';

/// Data model for shop with JSON serialization/deserialization
class ShopModel extends Shop {
  const ShopModel({
    required super.shopId,
    required super.shopName,
    required super.ownerName,
    required super.email,
    required super.phone,
    required super.address,
    super.location,
    super.businessDetails,
    required super.logoUrl,
    required super.bannerUrl,
    super.socialMedia,
    required super.status,
    required super.access,
    required super.verified,
    super.verificationDetails,
    required super.registeredBy,
    required super.paymentHistory,
    super.currentSubscriptionId,
    required super.statistics,
    super.contactPerson,
    required super.isDeleted,
    super.deletedAt,
    required super.notifications,
    required super.createdAt,
    required super.updatedAt,
  });

  factory ShopModel.fromJson(Map<String, dynamic> json) {
    return ShopModel(
      shopId: json['shopId'] ?? '',
      shopName: json['shopName'] ?? '',
      ownerName: json['ownerName'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      address: json['address'] ?? '',
      location: json['location'] != null 
          ? ShopLocationModel.fromJson(json['location'])
          : null,
      businessDetails: json['businessDetails'] != null
          ? BusinessDetailsModel.fromJson(json['businessDetails'])
          : null,
      logoUrl: json['logoUrl'] ?? '',
      bannerUrl: json['bannerUrl'] ?? '',
      socialMedia: json['socialMedia'] != null
          ? SocialMediaModel.fromJson(json['socialMedia'])
          : null,
      status: json['status'] ?? 'pending',
      access: ShopAccessModel.fromJson(json['access'] ?? {}),
      verified: json['verified'] ?? false,
      verificationDetails: json['verificationDetails'] != null
          ? VerificationDetailsModel.fromJson(json['verificationDetails'])
          : null,
      registeredBy: json['registeredBy'] ?? 'self',
      paymentHistory: (json['paymentHistory'] as List?)
          ?.map((item) => PaymentHistoryModel.fromJson(item))
          .toList() ?? [],
      currentSubscriptionId: json['currentSubscriptionId'],
      statistics: ShopStatisticsModel.fromJson(json['statistics'] ?? {}),
      contactPerson: json['contactPerson'] != null
          ? ContactPersonModel.fromJson(json['contactPerson'])
          : null,
      isDeleted: json['isDeleted'] ?? false,
      deletedAt: json['deletedAt'] != null
          ? DateTime.parse(json['deletedAt'])
          : null,
      notifications: NotificationPreferencesModel.fromJson(json['notifications'] ?? {}),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'shopId': shopId,
      'shopName': shopName,
      'ownerName': ownerName,
      'email': email,
      'phone': phone,
      'address': address,
      'location': location != null ? (location as ShopLocationModel).toJson() : null,
      'businessDetails': businessDetails != null 
          ? (businessDetails as BusinessDetailsModel).toJson()
          : null,
      'logoUrl': logoUrl,
      'bannerUrl': bannerUrl,
      'socialMedia': socialMedia != null 
          ? (socialMedia as SocialMediaModel).toJson()
          : null,
      'status': status,
      'access': (access as ShopAccessModel).toJson(),
      'verified': verified,
      'verificationDetails': verificationDetails != null
          ? (verificationDetails as VerificationDetailsModel).toJson()
          : null,
      'registeredBy': registeredBy,
      'paymentHistory': paymentHistory
          .map((item) => (item as PaymentHistoryModel).toJson())
          .toList(),
      'currentSubscriptionId': currentSubscriptionId,
      'statistics': (statistics as ShopStatisticsModel).toJson(),
      'contactPerson': contactPerson != null
          ? (contactPerson as ContactPersonModel).toJson()
          : null,
      'isDeleted': isDeleted,
      'deletedAt': deletedAt?.toIso8601String(),
      'notifications': (notifications as NotificationPreferencesModel).toJson(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Convert to domain model
  Shop toDomain() {
    return Shop(
      shopId: shopId,
      shopName: shopName,
      ownerName: ownerName,
      email: email,
      phone: phone,
      address: address,
      location: location,
      businessDetails: businessDetails,
      logoUrl: logoUrl,
      bannerUrl: bannerUrl,
      socialMedia: socialMedia,
      status: status,
      access: access,
      verified: verified,
      verificationDetails: verificationDetails,
      registeredBy: registeredBy,
      paymentHistory: paymentHistory,
      currentSubscriptionId: currentSubscriptionId,
      statistics: statistics,
      contactPerson: contactPerson,
      isDeleted: isDeleted,
      deletedAt: deletedAt,
      notifications: notifications,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}

class ShopLocationModel extends ShopLocation {
  const ShopLocationModel({
    super.street,
    super.city,
    super.district,
    super.state,
    super.postalCode,
    super.country,
    super.coordinates,
    super.placeId,
    super.formattedAddress,
  });

  factory ShopLocationModel.fromJson(Map<String, dynamic> json) {
    return ShopLocationModel(
      street: json['street'],
      city: json['city'],
      district: json['district'],
      state: json['state'],
      postalCode: json['postalCode'],
      country: json['country'],
      coordinates: json['coordinates'] != null
          ? CoordinatesModel.fromJson(json['coordinates'])
          : null,
      placeId: json['placeId'],
      formattedAddress: json['formattedAddress'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'street': street,
      'city': city,
      'district': district,
      'state': state,
      'postalCode': postalCode,
      'country': country,
      'coordinates': coordinates != null 
          ? (coordinates as CoordinatesModel).toJson()
          : null,
      'placeId': placeId,
      'formattedAddress': formattedAddress,
    };
  }
}

class CoordinatesModel extends Coordinates {
  const CoordinatesModel({
    super.latitude,
    super.longitude,
  });

  factory CoordinatesModel.fromJson(Map<String, dynamic> json) {
    return CoordinatesModel(
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}

class BusinessDetailsModel extends BusinessDetails {
  const BusinessDetailsModel({
    required super.type,
    super.category,
    super.foundedDate,
    super.registrationNumber,
    super.taxId,
    required super.employeeCount,
    super.operatingHours,
  });

  factory BusinessDetailsModel.fromJson(Map<String, dynamic> json) {
    return BusinessDetailsModel(
      type: json['type'] ?? 'retail',
      category: json['category'],
      foundedDate: json['foundedDate'] != null 
          ? DateTime.parse(json['foundedDate'])
          : null,
      registrationNumber: json['registrationNumber'],
      taxId: json['taxId'],
      employeeCount: json['employeeCount'] ?? 1,
      operatingHours: json['operatingHours'] != null
          ? OperatingHoursModel.fromJson(json['operatingHours'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'category': category,
      'foundedDate': foundedDate?.toIso8601String(),
      'registrationNumber': registrationNumber,
      'taxId': taxId,
      'employeeCount': employeeCount,
      'operatingHours': operatingHours != null
          ? (operatingHours as OperatingHoursModel).toJson()
          : null,
    };
  }
}

class OperatingHoursModel extends OperatingHours {
  const OperatingHoursModel({
    super.monday,
    super.tuesday,
    super.wednesday,
    super.thursday,
    super.friday,
    super.saturday,
    super.sunday,
  });

  factory OperatingHoursModel.fromJson(Map<String, dynamic> json) {
    return OperatingHoursModel(
      monday: json['monday'] != null 
          ? DayHoursModel.fromJson(json['monday'])
          : null,
      tuesday: json['tuesday'] != null 
          ? DayHoursModel.fromJson(json['tuesday'])
          : null,
      wednesday: json['wednesday'] != null 
          ? DayHoursModel.fromJson(json['wednesday'])
          : null,
      thursday: json['thursday'] != null 
          ? DayHoursModel.fromJson(json['thursday'])
          : null,
      friday: json['friday'] != null 
          ? DayHoursModel.fromJson(json['friday'])
          : null,
      saturday: json['saturday'] != null 
          ? DayHoursModel.fromJson(json['saturday'])
          : null,
      sunday: json['sunday'] != null 
          ? DayHoursModel.fromJson(json['sunday'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'monday': monday != null ? (monday as DayHoursModel).toJson() : null,
      'tuesday': tuesday != null ? (tuesday as DayHoursModel).toJson() : null,
      'wednesday': wednesday != null ? (wednesday as DayHoursModel).toJson() : null,
      'thursday': thursday != null ? (thursday as DayHoursModel).toJson() : null,
      'friday': friday != null ? (friday as DayHoursModel).toJson() : null,
      'saturday': saturday != null ? (saturday as DayHoursModel).toJson() : null,
      'sunday': sunday != null ? (sunday as DayHoursModel).toJson() : null,
    };
  }
}

class DayHoursModel extends DayHours {
  const DayHoursModel({
    required super.open,
    required super.close,
  });

  factory DayHoursModel.fromJson(Map<String, dynamic> json) {
    return DayHoursModel(
      open: json['open'] ?? '',
      close: json['close'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'open': open,
      'close': close,
    };
  }
}

class SocialMediaModel extends SocialMedia {
  const SocialMediaModel({
    super.facebook,
    super.instagram,
    super.twitter,
    super.website,
  });

  factory SocialMediaModel.fromJson(Map<String, dynamic> json) {
    return SocialMediaModel(
      facebook: json['facebook'],
      instagram: json['instagram'],
      twitter: json['twitter'],
      website: json['website'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'facebook': facebook,
      'instagram': instagram,
      'twitter': twitter,
      'website': website,
    };
  }
}

class ShopAccessModel extends ShopAccess {
  const ShopAccessModel({
    required super.isPaid,
    required super.isActivated,
  });

  factory ShopAccessModel.fromJson(Map<String, dynamic> json) {
    return ShopAccessModel(
      isPaid: json['isPaid'] ?? false,
      isActivated: json['isActivated'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isPaid': isPaid,
      'isActivated': isActivated,
    };
  }
}

class VerificationDetailsModel extends VerificationDetails {
  const VerificationDetailsModel({
    super.verifiedAt,
    super.verifiedBy,
    required super.documents,
  });

  factory VerificationDetailsModel.fromJson(Map<String, dynamic> json) {
    return VerificationDetailsModel(
      verifiedAt: json['verifiedAt'] != null 
          ? DateTime.parse(json['verifiedAt'])
          : null,
      verifiedBy: json['verifiedBy'],
      documents: (json['documents'] as List?)
          ?.map((item) => VerificationDocumentModel.fromJson(item))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'verifiedAt': verifiedAt?.toIso8601String(),
      'verifiedBy': verifiedBy,
      'documents': documents
          .map((item) => (item as VerificationDocumentModel).toJson())
          .toList(),
    };
  }
}

class VerificationDocumentModel extends VerificationDocument {
  const VerificationDocumentModel({
    required super.type,
    super.fileId,
    required super.verified,
    super.notes,
  });

  factory VerificationDocumentModel.fromJson(Map<String, dynamic> json) {
    return VerificationDocumentModel(
      type: json['type'] ?? '',
      fileId: json['fileId'],
      verified: json['verified'] ?? false,
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'fileId': fileId,
      'verified': verified,
      'notes': notes,
    };
  }
}

class PaymentHistoryModel extends PaymentHistory {
  const PaymentHistoryModel({
    required super.status,
    super.reason,
    required super.date,
    super.amount,
    super.currency,
    super.transactionId,
  });

  factory PaymentHistoryModel.fromJson(Map<String, dynamic> json) {
    return PaymentHistoryModel(
      status: json['status'] ?? '',
      reason: json['reason'],
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
      amount: (json['amount'] as num?)?.toDouble(),
      currency: json['currency'],
      transactionId: json['transactionId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'reason': reason,
      'date': date.toIso8601String(),
      'amount': amount,
      'currency': currency,
      'transactionId': transactionId,
    };
  }
}

class ShopStatisticsModel extends ShopStatistics {
  const ShopStatisticsModel({
    required super.totalCustomers,
    required super.totalRevenue,
    required super.totalDebts,
    required super.totalDebtAmount,
    required super.lastUpdated,
  });

  factory ShopStatisticsModel.fromJson(Map<String, dynamic> json) {
    return ShopStatisticsModel(
      totalCustomers: json['totalCustomers'] ?? 0,
      totalRevenue: (json['totalRevenue'] as num?)?.toDouble() ?? 0.0,
      totalDebts: json['totalDebts'] ?? 0,
      totalDebtAmount: (json['totalDebtAmount'] as num?)?.toDouble() ?? 0.0,
      lastUpdated: DateTime.parse(json['lastUpdated'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalCustomers': totalCustomers,
      'totalRevenue': totalRevenue,
      'totalDebts': totalDebts,
      'totalDebtAmount': totalDebtAmount,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}

class ContactPersonModel extends ContactPerson {
  const ContactPersonModel({
    super.name,
    super.phone,
    super.email,
    super.position,
  });

  factory ContactPersonModel.fromJson(Map<String, dynamic> json) {
    return ContactPersonModel(
      name: json['name'],
      phone: json['phone'],
      email: json['email'],
      position: json['position'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phone': phone,
      'email': email,
      'position': position,
    };
  }
}

class NotificationPreferencesModel extends NotificationPreferences {
  const NotificationPreferencesModel({
    required super.smsEnabled,
    required super.emailEnabled,
    required super.dailySummary,
    required super.dueReminders,
    required super.highRiskAlerts,
    required super.newCustomerNotifications,
    required super.paymentConfirmations,
  });

  factory NotificationPreferencesModel.fromJson(Map<String, dynamic> json) {
    return NotificationPreferencesModel(
      smsEnabled: json['smsEnabled'] ?? true,
      emailEnabled: json['emailEnabled'] ?? false,
      dailySummary: json['dailySummary'] ?? true,
      dueReminders: json['dueReminders'] ?? true,
      highRiskAlerts: json['highRiskAlerts'] ?? true,
      newCustomerNotifications: json['newCustomerNotifications'] ?? true,
      paymentConfirmations: json['paymentConfirmations'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'smsEnabled': smsEnabled,
      'emailEnabled': emailEnabled,
      'dailySummary': dailySummary,
      'dueReminders': dueReminders,
      'highRiskAlerts': highRiskAlerts,
      'newCustomerNotifications': newCustomerNotifications,
      'paymentConfirmations': paymentConfirmations,
    };
  }
} 