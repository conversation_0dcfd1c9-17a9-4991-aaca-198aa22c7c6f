// Main Reports Screen - Entry point from menu

import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'package:deyncare_app/core/routes/app_router.dart';
import 'package:deyncare_app/presentation/widgets/common_app_bar.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/common_list_item.dart';

class ReportsMainScreen extends StatelessWidget {
  const ReportsMainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: const CommonAppBar(
        title: 'Reports',
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          const SizedBox(height: 8),

          // Header Card
          _buildHeaderCard(context),

          const SizedBox(height: 24),

          // Available Reports Section
          _buildSectionTitle(context, 'Available Reports'),
          const SizedBox(height: 12),
          _buildAvailableReports(context),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildHeaderCard(BuildContext context) {
    return CommonCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppThemes.primaryColor.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.analytics_rounded,
                  color: AppThemes.primaryColor,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Business Reports',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: ThemeUtils.getTextColor(context,
                                type: TextColorType.primary),
                          ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Generate detailed reports for your business insights',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: ThemeUtils.getTextColor(context,
                                type: TextColorType.secondary),
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color:
                  ThemeUtils.getTextColor(context, type: TextColorType.primary),
            ),
      ),
    );
  }

  Widget _buildAvailableReports(BuildContext context) {
    return Column(
      children: [
        // Customer Report - Available
        CommonListItem(
          title: 'Customer Report',
          subtitle: 'View customer data, registrations, and risk analysis',
          leadingIcon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppThemes.successColor.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.people_outline,
              color: AppThemes.successColor,
              size: 20,
            ),
          ),
          trailingWidget: Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color:
                ThemeUtils.getIconColor(context, type: IconColorType.secondary),
          ),
          onTap: () => _navigateToCustomerReport(context),
        ),
        const SizedBox(height: 4),

        // Debt Report - Available
        CommonListItem(
          title: 'Debt Report',
          subtitle:
              'Analyze debt patterns, collections, and outstanding amounts',
          leadingIcon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppThemes.warningColor.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.account_balance_wallet_outlined,
              color: AppThemes.warningColor,
              size: 20,
            ),
          ),
          trailingWidget: Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color:
                ThemeUtils.getIconColor(context, type: IconColorType.secondary),
          ),
          onTap: () => _navigateToDebtReport(context),
        ),
        const SizedBox(height: 4),

        // Risk Report - Available
        CommonListItem(
          title: 'Repayment Risk Report',
          subtitle: 'Risk assessment, payment behavior, and credit analysis',
          leadingIcon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppThemes.errorColor.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.security_outlined,
              color: AppThemes.errorColor,
              size: 20,
            ),
          ),
          trailingWidget: Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color:
                ThemeUtils.getIconColor(context, type: IconColorType.secondary),
          ),
          onTap: () => _navigateToRiskReport(context),
        ),
      ],
    );
  }

  void _navigateToCustomerReport(BuildContext context) {
    AppRouter.navigateToCustomerReport(context);
  }

  void _navigateToDebtReport(BuildContext context) {
    AppRouter.navigateToDebtReport(context);
  }

  void _navigateToRiskReport(BuildContext context) {
    AppRouter.navigateToRiskReport(context);
  }
}
