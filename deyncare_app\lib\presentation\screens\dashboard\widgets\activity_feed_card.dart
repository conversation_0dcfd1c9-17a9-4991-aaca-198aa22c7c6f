import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/blocs/dashboard/dashboard_state.dart';

/// A reusable activity feed card widget for displaying recent activities
/// Follows the AuthButton pattern with consistent styling and animations
class ActivityFeedCard extends StatefulWidget {
  final ActivityItem activity;
  final VoidCallback? onTap;
  final bool showDivider;

  const ActivityFeedCard({
    super.key,
    required this.activity,
    this.onTap,
    this.showDivider = true,
  });

  @override
  State<ActivityFeedCard> createState() => _ActivityFeedCardState();
}

class _ActivityFeedCardState extends State<ActivityFeedCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.onTap != null) {
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (widget.onTap != null) {
      _animationController.reverse();
    }
  }

  void _handleTapCancel() {
    if (widget.onTap != null) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Opacity(
              opacity: _fadeAnimation.value,
              child: Column(
                children: [
                  _buildContent(context),
                  if (widget.showDivider) _buildDivider(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          _buildIcon(context),
          const SizedBox(width: 12),
          Expanded(
            child: _buildDetails(context),
          ),
          _buildTrailing(context),
        ],
      ),
    );
  }

  Widget _buildIcon(BuildContext context) {
    final iconData = _getIconForActivityType(widget.activity.type);
    final iconColor = _getColorForActivityType(widget.activity.type);
    
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: iconColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 20,
      ),
    );
  }

  Widget _buildDetails(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.activity.title,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        if (widget.activity.subtitle.isNotEmpty) ...[
          const SizedBox(height: 2),
          Text(
            widget.activity.subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey.withOpacity(0.7),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
        const SizedBox(height: 4),
        Text(
          _formatTimestamp(widget.activity.timestamp),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey.withOpacity(0.6),
            fontSize: 11,
          ),
        ),
      ],
    );
  }

  Widget _buildTrailing(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        if (widget.activity.amount.isNotEmpty)
          Text(
            widget.activity.amount,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: _getAmountColor(widget.activity.amount),
            ),
          ),
        if (widget.onTap != null) ...[
          const SizedBox(height: 4),
          Icon(
            Icons.arrow_forward_ios,
            color: Colors.grey.withOpacity(0.4),
            size: 12,
          ),
        ],
      ],
    );
  }

  Widget _buildDivider() {
    return Padding(
      padding: const EdgeInsets.only(left: 60),
      child: Divider(
        height: 1,
        color: Colors.grey.withOpacity(0.2),
      ),
    );
  }

  IconData _getIconForActivityType(String type) {
    switch (type.toLowerCase()) {
      case 'payment':
        return Icons.payment;
      case 'debt':
        return Icons.warning;
      case 'customer':
        return Icons.person_add;
      case 'inventory':
        return Icons.inventory;
      case 'report':
        return Icons.analytics;
      default:
        return Icons.circle;
    }
  }

  Color _getColorForActivityType(String type) {
    switch (type.toLowerCase()) {
      case 'payment':
        return Colors.blue;
      case 'debt':
        return Colors.orange;
      case 'customer':
        return AppThemes.primaryColor;
      case 'inventory':
        return Colors.purple;
      case 'report':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  Color _getAmountColor(String amount) {
    if (amount.startsWith('+') || amount.startsWith('\$') && !amount.contains('-')) {
      return Colors.green;
    } else if (amount.startsWith('-')) {
      return Colors.red;
    }
    return Colors.black87;
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}

/// A container widget for the activity feed with header and list
class ActivityFeedWidget extends StatelessWidget {
  final List<ActivityItem> activities;
  final String title;
  final VoidCallback? onViewAllTap;
  final Function(ActivityItem)? onActivityTap;
  final bool isLoading;
  final int maxItems;

  const ActivityFeedWidget({
    super.key,
    required this.activities,
    this.title = 'Recent Activity',
    this.onViewAllTap,
    this.onActivityTap,
    this.isLoading = false,
    this.maxItems = 5,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(context),
          if (isLoading)
            _buildLoadingState()
          else if (activities.isEmpty)
            _buildEmptyState(context)
          else
            _buildActivityList(),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          if (onViewAllTap != null)
            GestureDetector(
              onTap: onViewAllTap,
              child: Text(
                'View All',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppThemes.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Column(
      children: List.generate(3, (index) => 
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: double.infinity,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.grey.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      width: 100,
                      height: 12,
                      decoration: BoxDecoration(
                        color: Colors.grey.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.inbox,
            size: 48,
            color: Colors.grey.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No recent activity',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey.withOpacity(0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your recent activities will appear here',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey.withOpacity(0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActivityList() {
    final displayActivities = activities.take(maxItems).toList();
    
    return Column(
      children: displayActivities.asMap().entries.map((entry) {
        final index = entry.key;
        final activity = entry.value;
        final isLast = index == displayActivities.length - 1;
        
        return ActivityFeedCard(
          activity: activity,
          onTap: onActivityTap != null ? () => onActivityTap!(activity) : null,
          showDivider: !isLast,
        );
      }).toList(),
    );
  }
} 