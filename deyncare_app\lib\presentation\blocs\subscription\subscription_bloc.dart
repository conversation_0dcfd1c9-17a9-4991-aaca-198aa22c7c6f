import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/utils/api_toast_handler.dart';
import 'package:deyncare_app/domain/usecases/subscription/get_current_subscription_use_case.dart';
import 'package:deyncare_app/domain/usecases/subscription/get_available_plans_use_case.dart';
import 'package:deyncare_app/domain/usecases/subscription/upgrade_subscription_use_case.dart';
import 'package:deyncare_app/domain/repositories/subscription_repository.dart';
import 'subscription_event.dart';
import 'subscription_state.dart';

class SubscriptionBloc extends Bloc<SubscriptionEvent, SubscriptionState> {
  final GetCurrentSubscriptionUseCase getCurrentSubscriptionUseCase;
  final GetAvailablePlansUseCase getAvailablePlansUseCase;
  final RequestUpgradeUseCase requestUpgradeUseCase;
  final SubscriptionRepository subscriptionRepository;
  
  // Flag to prevent multiple simultaneous loads
  bool _isLoading = false;

  SubscriptionBloc({
    required this.getCurrentSubscriptionUseCase,
    required this.getAvailablePlansUseCase,
    required this.requestUpgradeUseCase,
    required this.subscriptionRepository,
  }) : super(SubscriptionInitial()) {
    on<LoadCurrentSubscription>(_onLoadCurrentSubscription);
    on<LoadAvailablePlans>(_onLoadAvailablePlans);
    on<UpgradeSubscription>(_onUpgradeSubscription);
    on<ChangePlan>(_onChangePlan);
    on<CancelSubscription>(_onCancelSubscription);
    on<UpdateAutoRenewal>(_onUpdateAutoRenewal);
    on<RenewSubscription>(_onRenewSubscription);
    on<PayWithEvc>(_onPayWithEvc);
    on<RecordOfflinePayment>(_onRecordOfflinePayment);
    on<LoadPaymentMethods>(_onLoadPaymentMethods);
    on<RefreshSubscription>(_onRefreshSubscription);
    on<ResetSubscriptionState>(_onResetSubscriptionState);
  }

  Future<void> _onLoadCurrentSubscription(
    LoadCurrentSubscription event,
    Emitter<SubscriptionState> emit,
  ) async {
    // Prevent multiple simultaneous loads
    if (_isLoading) return;
    _isLoading = true;
    
    emit(SubscriptionLoading());

    final result = await getCurrentSubscriptionUseCase();
    
    result.fold(
      (failure) {
        emit(SubscriptionError(
          message: failure.message,
          code: failure.code,
        ));
        ApiToastHandler.handleError(failure.message);
        _isLoading = false;
      },
      (subscription) {
        emit(SubscriptionLoaded(subscription: subscription));
        // Load additional data without affecting the main state
        _loadAdditionalData();
        _isLoading = false;
      },
    );
  }

  Future<void> _onLoadAvailablePlans(
    LoadAvailablePlans event,
    Emitter<SubscriptionState> emit,
  ) async {
    final result = await getAvailablePlansUseCase();
    
    result.fold(
      (failure) {
        // Don't emit error state for this, just keep current state
        ApiToastHandler.handleError(failure.message, showToast: false);
      },
      (plans) {
        if (state is SubscriptionLoaded) {
          final currentState = state as SubscriptionLoaded;
          emit(currentState.copyWith(availablePlans: plans));
        } else {
          emit(PlansLoaded(plans: plans));
        }
      },
    );
  }

  Future<void> _onUpgradeSubscription(
    UpgradeSubscription event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(const SubscriptionUpdating(operation: 'requesting_upgrade'));

    final params = RequestUpgradeParams(
      planType: event.planType,
      message: event.message,
    );

    final result = await requestUpgradeUseCase(params);
    
    result.fold(
      (failure) {
        emit(SubscriptionError(
          message: failure.message,
          code: failure.code,
        ));
        ApiToastHandler.handleError(failure.message);
      },
      (response) {
        // For upgrade requests, we don't get a subscription back, just confirmation
        emit(SubscriptionUpdated(
          subscription: null, // No subscription returned from request
          operation: 'upgrade_request',
          message: response['message'] ?? 'Upgrade request sent successfully',
        ));
        ApiToastHandler.handleSuccess(message: response['message'] ?? 'Upgrade request sent successfully');
        // Don't reload automatically - let user refresh if needed
      },
    );
  }

  Future<void> _onChangePlan(
    ChangePlan event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(const SubscriptionUpdating(operation: 'changing_plan'));

    final result = await subscriptionRepository.changePlan(
      planId: event.planId,
      planType: event.planType,
      prorated: event.prorated,
      paymentMethod: event.paymentMethod,
      paymentDetails: event.paymentDetails,
    );
    
    result.fold(
      (failure) {
        emit(SubscriptionError(
          message: failure.message,
          code: failure.code,
        ));
        ApiToastHandler.handleError(failure.message);
      },
      (subscription) {
        emit(SubscriptionUpdated(
          subscription: subscription,
          operation: 'change_plan',
          message: 'Plan changed successfully!',
        ));
        ApiToastHandler.handleSuccess(message: 'Plan changed successfully!');
        // Don't reload automatically - let user refresh if needed
      },
    );
  }

  Future<void> _onCancelSubscription(
    CancelSubscription event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(const SubscriptionUpdating(operation: 'canceling'));

    final result = await subscriptionRepository.cancelSubscription(
      reason: event.reason,
      feedback: event.feedback,
      immediateEffect: event.immediateEffect,
    );
    
    result.fold(
      (failure) {
        emit(SubscriptionError(
          message: failure.message,
          code: failure.code,
        ));
        ApiToastHandler.handleError(failure.message);
      },
      (subscription) {
        emit(SubscriptionUpdated(
          subscription: subscription,
          operation: 'cancel',
          message: 'Subscription canceled successfully.',
        ));
        ApiToastHandler.handleSuccess(message: 'Subscription canceled successfully.');
        // Don't reload automatically - let user refresh if needed
      },
    );
  }

  Future<void> _onUpdateAutoRenewal(
    UpdateAutoRenewal event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(const SubscriptionUpdating(operation: 'updating_auto_renewal'));

    final result = await subscriptionRepository.updateAutoRenewal(
      autoRenew: event.autoRenew,
    );
    
    result.fold(
      (failure) {
        emit(SubscriptionError(
          message: failure.message,
          code: failure.code,
        ));
        ApiToastHandler.handleError(failure.message);
      },
      (subscription) {
        emit(SubscriptionUpdated(
          subscription: subscription,
          operation: 'auto_renewal',
          message: 'Auto-renewal settings updated successfully.',
        ));
        ApiToastHandler.handleSuccess(message: 'Auto-renewal settings updated.');
        // Don't reload automatically - let user refresh if needed
      },
    );
  }

  Future<void> _onRenewSubscription(
    RenewSubscription event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(const SubscriptionUpdating(operation: 'renewing'));

    final result = await subscriptionRepository.renewSubscription(
      paymentMethod: event.paymentMethod,
      transactionId: event.transactionId,
      amount: event.amount,
      currency: event.currency,
      notes: event.notes,
    );
    
    result.fold(
      (failure) {
        emit(SubscriptionError(
          message: failure.message,
          code: failure.code,
        ));
        ApiToastHandler.handleError(failure.message);
      },
      (subscription) {
        emit(SubscriptionUpdated(
          subscription: subscription,
          operation: 'renew',
          message: 'Subscription renewed successfully!',
        ));
        ApiToastHandler.handleSuccess(message: 'Subscription renewed successfully!');
        // Don't reload automatically - let user refresh if needed
      },
    );
  }

  Future<void> _onPayWithEvc(
    PayWithEvc event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(const PaymentProcessing(paymentMethod: 'evc_plus'));

    final result = await subscriptionRepository.payWithEvc(
      subscriptionId: event.subscriptionId,
      phone: event.phone,
      amount: event.amount,
      planType: event.planType,
    );
    
    result.fold(
      (failure) {
        emit(PaymentFailed(
          message: failure.message,
          code: failure.code,
        ));
        ApiToastHandler.handleError(failure.message);
      },
      (paymentResult) {
        emit(PaymentCompleted(
          paymentResult: paymentResult,
          message: 'EVC payment processed successfully!',
        ));
        ApiToastHandler.handleSuccess(message: 'EVC payment processed successfully!');
        // Don't reload automatically - let user refresh if needed
      },
    );
  }

  Future<void> _onRecordOfflinePayment(
    RecordOfflinePayment event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(const PaymentProcessing(paymentMethod: 'offline'));

    final result = await subscriptionRepository.recordOfflinePayment(
      subscriptionId: event.subscriptionId,
      amount: event.amount,
      method: event.method,
      payerName: event.payerName,
      payerPhone: event.payerPhone,
      notes: event.notes,
      planType: event.planType,
      receiptPath: event.receiptPath,
    );
    
    result.fold(
      (failure) {
        emit(PaymentFailed(
          message: failure.message,
          code: failure.code,
        ));
        ApiToastHandler.handleError(failure.message);
      },
      (paymentResult) {
        emit(PaymentCompleted(
          paymentResult: paymentResult,
          message: 'Offline payment recorded successfully!',
        ));
        ApiToastHandler.handleSuccess(message: 'Offline payment recorded successfully!');
        // Don't reload automatically - let user refresh if needed
      },
    );
  }

  Future<void> _onLoadPaymentMethods(
    LoadPaymentMethods event,
    Emitter<SubscriptionState> emit,
  ) async {
    final result = await subscriptionRepository.getAvailablePaymentMethods();
    
    result.fold(
      (failure) {
        // Don't emit error state for this, just keep current state
        ApiToastHandler.handleError(failure.message, showToast: false);
      },
      (paymentMethods) {
        if (state is SubscriptionLoaded) {
          final currentState = state as SubscriptionLoaded;
          emit(currentState.copyWith(paymentMethods: paymentMethods));
        }
      },
    );
  }

  Future<void> _onRefreshSubscription(
    RefreshSubscription event,
    Emitter<SubscriptionState> emit,
  ) async {
    // Only refresh if not already loading
    if (!_isLoading) {
      add(LoadCurrentSubscription());
    }
  }

  Future<void> _onResetSubscriptionState(
    ResetSubscriptionState event,
    Emitter<SubscriptionState> emit,
  ) async {
    _isLoading = false;
    emit(SubscriptionInitial());
  }

  // Helper method to load additional data without affecting main state
  void _loadAdditionalData() {
    // Only load additional data if we have a successful subscription
    if (state is SubscriptionLoaded) {
      add(LoadAvailablePlans());
      add(LoadPaymentMethods());
    }
  }
} 