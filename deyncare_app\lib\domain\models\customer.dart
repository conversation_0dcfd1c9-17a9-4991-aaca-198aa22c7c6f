import 'package:equatable/equatable.dart';

/// Domain entity for Customer
class Customer extends Equatable {
  final String customerId;
  final String fullName;
  final String email;
  final String phone;
  final String? alternativePhone;
  final String? address;
  final String? city;
  final String? region;
  final String shopId;
  final CustomerStatus status;
  final RiskProfile riskProfile;
  final CustomerStats stats;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? notes;
  final List<String>? tags;

  const Customer({
    required this.customerId,
    required this.fullName,
    required this.email,
    required this.phone,
    this.alternativePhone,
    this.address,
    this.city,
    this.region,
    required this.shopId,
    required this.status,
    required this.riskProfile,
    required this.stats,
    required this.createdAt,
    required this.updatedAt,
    this.notes,
    this.tags,
  });

  @override
  List<Object?> get props => [
        customerId,
        fullName,
        email,
        phone,
        alternativePhone,
        address,
        city,
        region,
        shopId,
        status,
        riskProfile,
        stats,
        createdAt,
        updatedAt,
        notes,
        tags,
      ];

  Customer copyWith({
    String? customerId,
    String? fullName,
    String? email,
    String? phone,
    String? alternativePhone,
    String? address,
    String? city,
    String? region,
    String? shopId,
    CustomerStatus? status,
    RiskProfile? riskProfile,
    CustomerStats? stats,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? notes,
    List<String>? tags,
  }) {
    return Customer(
      customerId: customerId ?? this.customerId,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      alternativePhone: alternativePhone ?? this.alternativePhone,
      address: address ?? this.address,
      city: city ?? this.city,
      region: region ?? this.region,
      shopId: shopId ?? this.shopId,
      status: status ?? this.status,
      riskProfile: riskProfile ?? this.riskProfile,
      stats: stats ?? this.stats,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      notes: notes ?? this.notes,
      tags: tags ?? this.tags,
    );
  }
}

/// Customer status enumeration
enum CustomerStatus {
  active,
  inactive,
  suspended,
  blacklisted;

  String get displayName {
    switch (this) {
      case CustomerStatus.active:
        return 'Active';
      case CustomerStatus.inactive:
        return 'Inactive';
      case CustomerStatus.suspended:
        return 'Suspended';
      case CustomerStatus.blacklisted:
        return 'Blacklisted';
    }
  }

  bool get isActive => this == CustomerStatus.active;
}

/// Risk profile for customer assessment
class RiskProfile extends Equatable {
  final RiskLevel currentRiskLevel;
  final double riskScore;
  final String riskSource;
  final DateTime lastAssessment;
  final String? riskReason;
  final Map<String, dynamic>? mlData;

  const RiskProfile({
    required this.currentRiskLevel,
    required this.riskScore,
    required this.riskSource,
    required this.lastAssessment,
    this.riskReason,
    this.mlData,
  });

  @override
  List<Object?> get props => [
        currentRiskLevel,
        riskScore,
        riskSource,
        lastAssessment,
        riskReason,
        mlData,
      ];
}

/// Risk level enumeration
enum RiskLevel {
  low,
  medium,
  high,
  activeDebt;

  String get displayName {
    switch (this) {
      case RiskLevel.low:
        return 'Low Risk';
      case RiskLevel.medium:
        return 'Medium Risk';
      case RiskLevel.high:
        return 'High Risk';
      case RiskLevel.activeDebt:
        return 'Active Debt';
    }
  }

  bool get isHighRisk => this == RiskLevel.high;
}

/// Customer statistics
class CustomerStats extends Equatable {
  final int totalDebts;
  final double totalBorrowed;
  final double totalPaid;
  final double currentOutstanding;
  final int completedDebts;
  final int activeDebts;
  final int overdueDebts;
  final double averagePaymentDays;
  final PaymentReliability paymentReliability;

  const CustomerStats({
    required this.totalDebts,
    required this.totalBorrowed,
    required this.totalPaid,
    required this.currentOutstanding,
    required this.completedDebts,
    required this.activeDebts,
    required this.overdueDebts,
    required this.averagePaymentDays,
    required this.paymentReliability,
  });

  @override
  List<Object?> get props => [
        totalDebts,
        totalBorrowed,
        totalPaid,
        currentOutstanding,
        completedDebts,
        activeDebts,
        overdueDebts,
        averagePaymentDays,
        paymentReliability,
      ];
}

/// Payment reliability enumeration
enum PaymentReliability {
  excellent,
  good,
  fair,
  poor;

  String get displayName {
    switch (this) {
      case PaymentReliability.excellent:
        return 'Excellent';
      case PaymentReliability.good:
        return 'Good';
      case PaymentReliability.fair:
        return 'Fair';
      case PaymentReliability.poor:
        return 'Poor';
    }
  }
} 