import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_header.dart';

void main() {
  group('AuthHeader', () {
    testWidgets('renders with title correctly', (WidgetTester tester) async {
      const String testTitle = 'Welcome Back';
      
      // Build the widget
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: Center(
              child: AuthHeader(
                title: testTitle,
              ),
            ),
          ),
        ),
      );
      
      // Verify the title is displayed
      expect(find.text(testTitle), findsOneWidget);
      
      // Verify logo container is present (checking for Container)
      expect(find.byType(Container), findsWidgets);
      
      // Verify image is attempted to be loaded
      expect(find.byType(Image), findsOneWidget);
    });
    
    testWidgets('renders with title and subtitle correctly', (WidgetTester tester) async {
      const String testTitle = 'Create Account';
      const String testSubtitle = 'Sign up to get started';
      
      // Build the widget with subtitle
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: Center(
              child: AuthHeader(
                title: testTitle,
                subtitle: testSubtitle,
              ),
            ),
          ),
        ),
      );
      
      // Verify both title and subtitle are displayed
      expect(find.text(testTitle), findsOneWidget);
      expect(find.text(testSubtitle), findsOneWidget);
    });
    
    testWidgets('shows fallback icon when image fails to load', (WidgetTester tester) async {
      const String testTitle = 'Reset Password';
      
      // Build the widget (the errorBuilder will be triggered in test environment)
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: Center(
              child: AuthHeader(
                title: testTitle,
              ),
            ),
          ),
        ),
      );
      
      // Let the error builder do its work
      await tester.pump();
      
      // Verify the fallback icon is shown
      expect(find.byType(Icon), findsOneWidget);
    });
    
    testWidgets('has correct layout structure', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: Center(
              child: AuthHeader(
                title: 'Sign In',
                subtitle: 'Welcome back',
              ),
            ),
          ),
        ),
      );
      
      // Verify the layout structure
      expect(find.byType(Column), findsOneWidget);
      
      // These widgets should exist in the hierarchy
      expect(find.byType(Container), findsAtLeastNWidgets(1));
      expect(find.byType(SizedBox), findsAtLeastNWidgets(1));
      expect(find.byType(Text), findsAtLeastNWidgets(2));
    });
  });
}
