/// Debt Report Data Model - matches the debt report API response structure
/// This model is specifically designed for the /reports/debts/data endpoint
/// which returns debt information with customer details for reporting
class DebtReportData {
  final String debtId;
  final String customerId;
  final String customerName;
  final double debtAmount;
  final double outstandingDebt;
  final double paidAmount;
  final DateTime debtDate;
  final DateTime? dueDate;
  final double overdueDebt;
  final String paymentStatus;
  final int daysPastDue;
  final String customerType;

  const DebtReportData({
    required this.debtId,
    required this.customerId,
    required this.customerName,
    required this.debtAmount,
    required this.outstandingDebt,
    required this.paidAmount,
    required this.debtDate,
    this.dueDate,
    required this.overdueDebt,
    required this.paymentStatus,
    required this.daysPastDue,
    required this.customerType,
  });

  /// Factory constructor with null safety for API response parsing
  /// Provides sensible defaults for any null values from the API
  factory DebtReportData.fromJson(Map<String, dynamic> json) {
    final debtDate = json['debtDate'] != null
        ? DateTime.parse(json['debtDate'] as String)
        : DateTime.now();

    final dueDate = json['dueDate'] != null
        ? DateTime.parse(json['dueDate'] as String)
        : null;

    final outstandingDebt =
        (json['outstandingDebt'] as num?)?.toDouble() ?? 0.0;
    final overdueAmount = dueDate != null && DateTime.now().isAfter(dueDate)
        ? outstandingDebt
        : 0.0;

    final daysPastDue = dueDate != null && DateTime.now().isAfter(dueDate)
        ? DateTime.now().difference(dueDate).inDays
        : 0;

    return DebtReportData(
      debtId: json['debtId'] as String? ?? '',
      customerId: json['customerId'] as String? ?? '',
      customerName: json['customerName'] as String? ??
          json['CustomerName'] as String? ??
          '',
      debtAmount: (json['debtAmount'] as num?)?.toDouble() ??
          (json['DebtAmount'] as num?)?.toDouble() ??
          0.0,
      outstandingDebt: outstandingDebt,
      paidAmount: (json['paidAmount'] as num?)?.toDouble() ??
          (json['PaidAmount'] as num?)?.toDouble() ??
          0.0,
      debtDate: debtDate,
      dueDate: dueDate,
      overdueDebt: (json['overdueDebt'] as num?)?.toDouble() ?? overdueAmount,
      paymentStatus: json['paymentStatus'] as String? ??
          (outstandingDebt > 0 ? 'Outstanding' : 'Paid'),
      daysPastDue: json['daysPastDue'] as int? ?? daysPastDue,
      customerType: json['customerType'] as String? ??
          json['CustomerType'] as String? ??
          'Unknown',
    );
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'debtId': debtId,
      'customerId': customerId,
      'customerName': customerName,
      'debtAmount': debtAmount,
      'outstandingDebt': outstandingDebt,
      'paidAmount': paidAmount,
      'debtDate': debtDate.toIso8601String(),
      'dueDate': dueDate?.toIso8601String(),
      'overdueDebt': overdueDebt,
      'paymentStatus': paymentStatus,
      'daysPastDue': daysPastDue,
      'customerType': customerType,
    };
  }

  /// Create a copy with modified fields
  DebtReportData copyWith({
    String? debtId,
    String? customerId,
    String? customerName,
    double? debtAmount,
    double? outstandingDebt,
    double? paidAmount,
    DateTime? debtDate,
    DateTime? dueDate,
    double? overdueDebt,
    String? paymentStatus,
    int? daysPastDue,
    String? customerType,
  }) {
    return DebtReportData(
      debtId: debtId ?? this.debtId,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      debtAmount: debtAmount ?? this.debtAmount,
      outstandingDebt: outstandingDebt ?? this.outstandingDebt,
      paidAmount: paidAmount ?? this.paidAmount,
      debtDate: debtDate ?? this.debtDate,
      dueDate: dueDate ?? this.dueDate,
      overdueDebt: overdueDebt ?? this.overdueDebt,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      daysPastDue: daysPastDue ?? this.daysPastDue,
      customerType: customerType ?? this.customerType,
    );
  }

  /// Check if debt is overdue
  bool get isOverdue =>
      dueDate != null &&
      DateTime.now().isAfter(dueDate!) &&
      outstandingDebt > 0;

  /// Get formatted debt date
  String get formattedDebtDate =>
      '${debtDate.day}/${debtDate.month}/${debtDate.year}';

  /// Get formatted due date
  String get formattedDueDate => dueDate != null
      ? '${dueDate!.day}/${dueDate!.month}/${dueDate!.year}'
      : 'No Due Date';

  @override
  String toString() {
    return 'DebtReportData(debtId: $debtId, customerName: $customerName, debtAmount: $debtAmount, outstandingDebt: $outstandingDebt, paymentStatus: $paymentStatus, isOverdue: $isOverdue)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DebtReportData &&
        other.debtId == debtId &&
        other.customerId == customerId &&
        other.customerName == customerName &&
        other.debtAmount == debtAmount &&
        other.outstandingDebt == outstandingDebt &&
        other.paidAmount == paidAmount &&
        other.debtDate == debtDate &&
        other.dueDate == dueDate &&
        other.overdueDebt == overdueDebt &&
        other.paymentStatus == paymentStatus &&
        other.daysPastDue == daysPastDue &&
        other.customerType == customerType;
  }

  @override
  int get hashCode {
    return debtId.hashCode ^
        customerId.hashCode ^
        customerName.hashCode ^
        debtAmount.hashCode ^
        outstandingDebt.hashCode ^
        paidAmount.hashCode ^
        debtDate.hashCode ^
        dueDate.hashCode ^
        overdueDebt.hashCode ^
        paymentStatus.hashCode ^
        daysPastDue.hashCode ^
        customerType.hashCode;
  }
}
