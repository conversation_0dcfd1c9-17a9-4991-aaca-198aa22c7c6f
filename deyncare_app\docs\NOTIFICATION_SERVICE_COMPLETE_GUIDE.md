# 🔔 DeynCare Notification Service - Complete Guide

## ✅ Firebase Configuration Status: VERIFIED

Your Firebase configuration files have been successfully updated with real values from Firebase Console:

- **Project ID**: `deyncare-47d99`
- **Android Package**: `com.deyncare.app`
- **iOS Bundle**: `com.deyncare.app`
- **Firebase Admin SDK**: Already configured in backend

## 🎯 SuperAdmin Notification Management System

### Custom Notification Types

#### 1. 🏪 Shop-Targeted Notifications
Send notifications to specific shop admins.

**Endpoint:** `POST /api/admin/notifications/push/shops`

**Payload Example:**
```json
{
  "shopIds": ["SHOP001", "SHOP002"],
  "title": "Payment Update Required",
  "message": "Please update your payment method for continued service.",
  "priority": "high",
  "actionUrl": "/payments/update",
  "actionLabel": "Update Payment"
}
```

#### 2. 📢 Broadcast Notifications
Send to all admin users across all shops.

**Endpoint:** `POST /api/admin/notifications/push/broadcast`

**Payload Example:**
```json
{
  "title": "System Maintenance Notice",
  "message": "Scheduled maintenance on Jan 15, 2024 from 2-4 AM UTC.",
  "priority": "normal",
  "actionUrl": "/announcements/maintenance",
  "actionLabel": "View Details"
}
```

#### 3. 💰 Debt Reminder Notifications
Automated debt management notifications.

**Endpoint:** `POST /api/admin/notifications/push/debt-reminders`

**Payload Example:**
```json
{
  "reminderType": "7_days",
  "shopIds": ["SHOP001"]
}
```

**Reminder Types:**
- `7_days`: Debt due in 7 days
- `3_days`: Debt due in 3 days
- `overdue`: Overdue debts

## 🧪 Easy API Testing Commands

### 1. Get SuperAdmin Authentication
```bash
curl -X POST "https://your-backend-domain.com/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your-password"
  }'
```

### 2. Test Firebase Connection
```bash
curl -X GET "https://your-backend-domain.com/api/admin/notifications/push/test" \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN"
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Firebase connection successful"
}
```

### 3. Get Available Notification Targets
```bash
curl -X GET "https://your-backend-domain.com/api/admin/notifications/push/targets" \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN"
```

### 4. Send Test Notification to Specific Shop
```bash
curl -X POST "https://your-backend-domain.com/api/admin/notifications/push/shops" \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "shopIds": ["SHOP001"],
    "title": "🧪 Test Notification",
    "message": "This is a test notification to verify the system is working correctly.",
    "priority": "normal"
  }'
```

### 5. Send Test Broadcast
```bash
curl -X POST "https://your-backend-domain.com/api/admin/notifications/push/broadcast" \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "🚀 System Test",
    "message": "Testing broadcast notification functionality.",
    "priority": "normal"
  }'
```

## 📱 Mobile App Testing (Admin Users)

### 1. Register FCM Token
```bash
curl -X POST "https://your-backend-domain.com/api/fcm/register" \
  -H "Authorization: Bearer ADMIN_USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "fcm-token-from-mobile-app",
    "deviceInfo": {
      "platform": "android",
      "deviceId": "device-unique-id",
      "appVersion": "1.0.0",
      "osVersion": "Android 12"
    }
  }'
```

### 2. Send Test Notification to Self
```bash
curl -X POST "https://your-backend-domain.com/api/fcm/test" \
  -H "Authorization: Bearer ADMIN_USER_TOKEN"
```

## ✅ 100% Service Working Verification Checklist

### Backend Tests
- [ ] **Authentication**: SuperAdmin login returns valid JWT token
- [ ] **Firebase Connection**: Test endpoint returns success
- [ ] **Target Discovery**: Returns list of shops with admin users
- [ ] **Shop Notifications**: Successfully sends to specific shops
- [ ] **Broadcast Notifications**: Successfully sends to all admins
- [ ] **Debt Reminders**: Successfully triggers automated reminders

### Mobile App Tests
- [ ] **FCM Registration**: Admin users can register FCM tokens
- [ ] **Test Notifications**: Self-test notifications work
- [ ] **Notification Delivery**: Push notifications appear on devices
- [ ] **Action Buttons**: Notification actions work correctly
- [ ] **Deep Links**: App navigation works from notifications
- [ ] **Sound/Vibration**: Notification alerts work properly

### Real-World Scenarios
- [ ] **Shop Owner Onboarding**: Welcome notifications work
- [ ] **Payment Alerts**: High priority notifications work
- [ ] **System Announcements**: Broadcast notifications work
- [ ] **Debt Management**: Automated reminders work
- [ ] **Multi-Device**: Same user receives notifications on multiple devices

## 🔧 Firebase Push Notification Payload Structure

### Standard FCM Message
```json
{
  "notification": {
    "title": "Notification Title",
    "body": "Notification message content"
  },
  "data": {
    "type": "admin_communication",
    "priority": "high",
    "actionUrl": "/payments/update",
    "actionLabel": "Update Payment",
    "notificationId": "NOTIF_123456789",
    "shopId": "SHOP001",
    "category": "admin_communication"
  },
  "android": {
    "priority": "high",
    "notification": {
      "channel_id": "admin_notifications",
      "sound": "default",
      "click_action": "FLUTTER_NOTIFICATION_CLICK"
    }
  },
  "apns": {
    "payload": {
      "aps": {
        "alert": {
          "title": "Notification Title",
          "body": "Notification message content"
        },
        "sound": "default",
        "badge": 1
      }
    }
  }
}
```

## 📊 Monitoring and Analytics

### Get Notification Statistics
```bash
curl -X GET "https://your-backend-domain.com/api/admin/notifications/push/stats?days=30&shopId=SHOP001" \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN"
```

### Common Response Patterns

#### Success Response
```json
{
  "success": true,
  "message": "Notification sent to 2/2 shops",
  "data": {
    "totalShops": 2,
    "successful": 2,
    "failed": 0,
    "results": [
      {
        "shopId": "SHOP001",
        "shopName": "ABC Store",
        "notificationId": "NOTIF_123456789",
        "success": true
      }
    ]
  }
}
```

#### Error Response
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "title",
      "message": "Title is required"
    }
  ]
}
```

## 🚨 Troubleshooting Common Issues

### Firebase Connection Fails
1. Verify `google-services.json` and `GoogleService-Info.plist` are correctly placed
2. Check Firebase project configuration in Console
3. Ensure service account has proper permissions
4. Run `flutter clean && flutter pub get`

### No Notifications Received
1. Check FCM token registration in mobile app logs
2. Verify user role (must be admin/employee/superAdmin)
3. Check device notification permissions
4. Verify shop ID exists and user belongs to shop

### Authorization Errors
1. Confirm JWT token is valid and not expired
2. Verify user has correct role permissions
3. Check Authorization header format: `Bearer <token>`

## 🎯 Priority Levels and Categories

### Priority Levels
- **low**: General information
- **normal**: Standard notifications (default)
- **high**: Important updates requiring attention
- **urgent**: Critical notifications requiring immediate action

### Notification Categories
- **admin_communication**: Messages from SuperAdmin
- **system_broadcast**: System-wide announcements
- **debt_reminder**: Automated debt notifications
- **payment_alert**: Payment-related notifications
- **shop_management**: Shop operation notifications

## 🔄 Automated Notification Triggers

The system automatically sends notifications for:
1. **Debt Created**: When new debt is recorded
2. **Payment Recorded**: When payment is processed
3. **Debt Reminders**: Based on due dates (7-day, 3-day, overdue)
4. **User Status Changes**: Account suspension/activation
5. **System Events**: Maintenance, updates, alerts

## 🎨 Best Practices

### Message Content
- Keep titles under 40 characters
- Keep messages under 120 characters
- Use clear, actionable language
- Include emojis for better visual appeal

### Targeting
- Use shop-specific notifications for shop-related issues
- Use broadcast only for system-wide announcements
- Validate shop IDs before sending
- Avoid spam by batching similar notifications

### Timing
- Avoid notifications outside business hours
- Use `scheduledAt` for important announcements
- Consider time zones for global deployments

This comprehensive guide covers everything you need to test and verify that your notification service is working 100% correctly! 🎯 