import 'package:flutter_test/flutter_test.dart';
import 'package:deyncare_app/data/models/user_model.dart';
import 'package:deyncare_app/core/utils/permission_utils.dart';

void main() {
  group('UserModel Visibility Parsing', () {
    test('should correctly parse visibility from JSON like Carab user', () {
      // This is the JSON structure that should come from the backend for Carab
      final json = {
        'userId': 'carab123',
        'fullName': 'Carab',
        'email': '<EMAIL>',
        'phone': '+252619821173',
        'role': 'employee',
        'status': 'active',
        'registrationStatus': 'completed',
        'isEmailVerified': true,
        'isPaid': true,
        'isShopActive': true,
        'visibility': {
          'customerManagement': {
            'create': false,
            'update': false,
            'view': false,
            'delete': false,
          },
          'debtManagement': {
            'create': true,
            'update': true,
            'view': true,
            'delete': true,
          },
          'reportManagement': {
            'generate': true,
            'view': true,
            'delete': true,
          },
        },
      };

      // Parse the JSON into UserModel
      final userModel = UserModel.fromJson(json);
      
      // Verify basic fields
      expect(userModel.userId, 'carab123');
      expect(userModel.fullName, 'Carab');
      expect(userModel.email, '<EMAIL>');
      expect(userModel.role, 'employee');
      
      // Verify visibility field is parsed correctly
      expect(userModel.visibility, isNotNull);
      expect(userModel.visibility, isA<Map<String, dynamic>>());
      
      // Check customer management permissions
      final customerMgmt = userModel.visibility!['customerManagement'] as Map<String, dynamic>;
      expect(customerMgmt['create'], false);
      expect(customerMgmt['update'], false);
      expect(customerMgmt['view'], false);
      expect(customerMgmt['delete'], false);
      
      // Check debt management permissions (should all be true for Carab)
      final debtMgmt = userModel.visibility!['debtManagement'] as Map<String, dynamic>;
      expect(debtMgmt['create'], true);
      expect(debtMgmt['update'], true);
      expect(debtMgmt['view'], true);
      expect(debtMgmt['delete'], true);
      
      // Check report management permissions
      final reportMgmt = userModel.visibility!['reportManagement'] as Map<String, dynamic>;
      expect(reportMgmt['generate'], true);
      expect(reportMgmt['view'], true);
      expect(reportMgmt['delete'], true);
      
      print('UserModel visibility: ${userModel.visibility}');
    });

    test('should convert to domain User with visibility intact', () {
      final json = {
        'userId': 'carab123',
        'fullName': 'Carab',
        'email': '<EMAIL>',
        'role': 'employee',
        'status': 'active',
        'registrationStatus': 'completed',
        'isEmailVerified': true,
        'isPaid': true,
        'isShopActive': true,
        'visibility': {
          'customerManagement': {
            'create': false,
            'update': false,
            'view': false,
            'delete': false,
          },
          'debtManagement': {
            'create': true,
            'update': true,
            'view': true,
            'delete': true,
          },
          'reportManagement': {
            'generate': true,
            'view': true,
            'delete': true,
          },
        },
      };

      final userModel = UserModel.fromJson(json);
      final domainUser = userModel.toDomain();
      
      // Verify visibility is preserved in domain User
      expect(domainUser.visibility, isNotNull);
      expect(domainUser.visibility, equals(userModel.visibility));
      
      print('Domain User visibility: ${domainUser.visibility}');
    });

    test('should correctly check permissions for Carab-like user', () {
      final json = {
        'userId': 'carab123',
        'fullName': 'Carab',
        'email': '<EMAIL>',
        'role': 'employee',
        'status': 'active',
        'registrationStatus': 'completed',
        'isEmailVerified': true,
        'isPaid': true,
        'isShopActive': true,
        'visibility': {
          'customerManagement': {
            'create': false,
            'update': false,
            'view': false,
            'delete': false,
          },
          'debtManagement': {
            'create': true,
            'update': true,
            'view': true,
            'delete': true,
          },
          'reportManagement': {
            'generate': true,
            'view': true,
            'delete': true,
          },
        },
      };

      final userModel = UserModel.fromJson(json);
      final domainUser = userModel.toDomain();
      
      // Test permission checks
      print('Testing permissions for user: ${domainUser.fullName}');
      print('User visibility: ${domainUser.visibility}');
      print('User role: ${domainUser.role}');
      print('User isAdmin: ${domainUser.isAdmin}');
      print('User isEmployee: ${domainUser.isEmployee}');
      
      // Customer permissions (should be false)
      final canAccessCustomers = PermissionUtils.canAccessCustomers(domainUser);
      print('Can access customers: $canAccessCustomers');
      expect(canAccessCustomers, false);
      
      // Debt permissions (should be true)
      final canAccessDebts = PermissionUtils.canAccessDebts(domainUser);
      print('Can access debts: $canAccessDebts');
      expect(canAccessDebts, true);
      
      final canCreateDebts = PermissionUtils.canCreateDebts(domainUser);
      print('Can create debts: $canCreateDebts');
      expect(canCreateDebts, true);
      
      final canViewDebts = PermissionUtils.canViewDebts(domainUser);
      print('Can view debts: $canViewDebts');
      expect(canViewDebts, true);
      
      final canUpdateDebts = PermissionUtils.canUpdateDebts(domainUser);
      print('Can update debts: $canUpdateDebts');
      expect(canUpdateDebts, true);
      
      final canDeleteDebts = PermissionUtils.canDeleteDebts(domainUser);
      print('Can delete debts: $canDeleteDebts');
      expect(canDeleteDebts, true);
      
      // Report permissions (should be true)
      final canAccessReports = PermissionUtils.canAccessReports(domainUser);
      print('Can access reports: $canAccessReports');
      expect(canAccessReports, true);
      
      final canGenerateReports = PermissionUtils.canGenerateReports(domainUser);
      print('Can generate reports: $canGenerateReports');
      expect(canGenerateReports, true);
      
      final canViewReports = PermissionUtils.canViewReports(domainUser);
      print('Can view reports: $canViewReports');
      expect(canViewReports, true);
      
      final canDeleteReports = PermissionUtils.canDeleteReports(domainUser);
      print('Can delete reports: $canDeleteReports');
      expect(canDeleteReports, true);
    });
  });
}
