import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/utils/permission_utils.dart';
import 'package:deyncare_app/domain/models/customer.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_constants.dart';
import 'package:deyncare_app/presentation/widgets/modals/customer/customer_modal_handlers.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_state.dart';

/// Enhanced Customer Details View with Modern Design and Edit Functionality
class CustomerDetailsView extends StatelessWidget {
  final Customer customer;
  final bool showEditButton;

  const CustomerDetailsView({
    super.key, 
    required this.customer,
    this.showEditButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: ModalConstants.defaultPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildModernCustomerHeader(context),
          ModalConstants.largeSpacing,
          _buildModernBasicInformation(context),
          ModalConstants.largeSpacing,
          _buildModernContactInformation(context),
          ModalConstants.largeSpacing,
          _buildModernRiskProfileCard(context),
          if (customer.address?.isNotEmpty == true || 
              customer.notes?.isNotEmpty == true) ...[
            ModalConstants.largeSpacing,
            _buildModernAdditionalInformation(context),
          ],
          if (showEditButton) ...[
            ModalConstants.largeSpacing,
            _buildActionButtons(context),
          ],
          ModalConstants.massiveSpacing,
        ],
      ),
    );
  }

  /// Modern customer header with enhanced visual hierarchy
  Widget _buildModernCustomerHeader(BuildContext context) {
    return Container(
      decoration: ModalConstants.modernCardDecoration(),
      child: Padding(
        padding: ModalConstants.cardPadding,
        child: Column(
          children: [
            // Profile Avatar
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppThemes.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(ModalConstants.largeRadius),
                border: Border.all(
                  color: AppThemes.primaryColor.withOpacity(0.2),
                  width: 2,
                ),
              ),
              child: Icon(
                Icons.person_rounded,
                size: 40,
                color: AppThemes.primaryColor,
              ),
            ),
            ModalConstants.defaultSpacing,
            
            // Customer Name
            Text(
              customer.fullName,
              style: ModalConstants.sectionTitleStyle(context),
              textAlign: TextAlign.center,
            ),
            ModalConstants.smallSpacing,
            
            // Customer ID Badge
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppThemes.textSecondaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                'ID: ${customer.customerId}',
                style: ModalConstants.captionStyle(context),
              ),
            ),
            ModalConstants.defaultSpacing,
            
            // Status Badge
            ModalConstants.statusBadge(
              text: customer.status.displayName,
              color: AppThemes.primaryColor,
              icon: Icons.verified_user_rounded,
              isLarge: true,
            ),
          ],
        ),
      ),
    );
  }

  /// Modern basic information card
  Widget _buildModernBasicInformation(BuildContext context) {
    return Container(
      decoration: ModalConstants.modernCardDecoration(),
      child: Padding(
        padding: ModalConstants.cardPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppThemes.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.info_outline_rounded,
                    size: 20,
                    color: AppThemes.primaryColor,
                  ),
                ),
                ModalConstants.defaultHorizontalSpacing,
                Text(
                  'Basic Information',
                  style: ModalConstants.sectionTitleStyle(context),
                ),
              ],
            ),
            ModalConstants.largeSpacing,
            
            // Information Grid
            _buildModernInfoGrid(context, [
              _InfoItem(
                label: 'Full Name',
                value: customer.fullName,
                icon: Icons.person_outline_rounded,
              ),
              _InfoItem(
                label: 'Status',
                value: customer.status.displayName,
                icon: Icons.verified_user_outlined,
              ),
              _InfoItem(
                label: 'Shop ID',
                value: customer.shopId,
                icon: Icons.store_outlined,
              ),
              _InfoItem(
                label: 'Customer ID',
                value: customer.customerId,
                icon: Icons.tag_rounded,
              ),
            ]),
          ],
        ),
      ),
    );
  }

  /// Modern contact information card
  Widget _buildModernContactInformation(BuildContext context) {
    final contactItems = <_InfoItem>[
      _InfoItem(
        label: 'Phone',
        value: customer.phone,
        icon: Icons.phone_outlined,
      ),
      if (customer.email.isNotEmpty)
        _InfoItem(
          label: 'Email',
          value: customer.email,
          icon: Icons.email_outlined,
        ),
      if (customer.alternativePhone?.isNotEmpty == true)
        _InfoItem(
          label: 'Alternative Phone',
          value: customer.alternativePhone!,
          icon: Icons.phone_android_outlined,
        ),
      if (customer.address?.isNotEmpty == true)
        _InfoItem(
          label: 'Address',
          value: customer.address!,
          icon: Icons.location_on_outlined,
        ),
      if (customer.city?.isNotEmpty == true)
        _InfoItem(
          label: 'City',
          value: customer.city!,
          icon: Icons.location_city_outlined,
        ),
      if (customer.region?.isNotEmpty == true)
        _InfoItem(
          label: 'Region',
          value: customer.region!,
          icon: Icons.map_outlined,
        ),
    ];

    return Container(
      decoration: ModalConstants.modernCardDecoration(),
      child: Padding(
        padding: ModalConstants.cardPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppThemes.secondaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.contact_phone_rounded,
                    size: 20,
                    color: AppThemes.secondaryColor,
                  ),
                ),
                ModalConstants.defaultHorizontalSpacing,
                Text(
                  'Contact Information',
                  style: ModalConstants.sectionTitleStyle(context),
                ),
              ],
            ),
            ModalConstants.largeSpacing,
            
            // Contact Grid
            _buildModernInfoGrid(context, contactItems),
          ],
        ),
      ),
    );
  }

  /// Modern risk profile card with enhanced visual design
  Widget _buildModernRiskProfileCard(BuildContext context) {
    return Container(
      decoration: ModalConstants.modernCardDecoration(),
      child: Padding(
        padding: ModalConstants.cardPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getRiskLevelColor(customer.riskProfile.currentRiskLevel).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.security_rounded,
                    size: 20,
                    color: _getRiskLevelColor(customer.riskProfile.currentRiskLevel),
                  ),
                ),
                ModalConstants.defaultHorizontalSpacing,
                Expanded(
                  child: Text(
                    'Risk Profile & Debt Status',
                    style: ModalConstants.sectionTitleStyle(context),
                  ),
                ),
              ],
            ),
            ModalConstants.largeSpacing,
            
            // Risk Level Display
            _buildModernRiskLevelDisplay(context),
            ModalConstants.largeSpacing,
            
            // Debt Summary Card
            _buildModernDebtSummaryCard(context),
            ModalConstants.largeSpacing,
            
            // Risk Assessment Details
            _buildModernRiskAssessmentDetails(context),
          ],
        ),
      ),
    );
  }

  /// Modern risk level display
  Widget _buildModernRiskLevelDisplay(BuildContext context) {
    final riskColor = _getRiskLevelColor(customer.riskProfile.currentRiskLevel);
    final riskIcon = _getRiskLevelIcon(customer.riskProfile.currentRiskLevel);
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: riskColor.withOpacity(0.08),
        borderRadius: BorderRadius.circular(ModalConstants.defaultRadius),
        border: Border.all(
          color: riskColor.withOpacity(0.2),
          width: 1.5,
        ),
      ),
      child: Column(
        children: [
          // Risk Icon
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: riskColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(ModalConstants.defaultRadius),
            ),
            child: Icon(
              riskIcon,
              size: 32,
              color: riskColor,
            ),
          ),
          ModalConstants.defaultSpacing,
          
          // Risk Level Text
          Text(
            customer.riskProfile.currentRiskLevel.displayName,
            style: ModalConstants.sectionTitleStyle(context, color: riskColor),
          ),
          ModalConstants.smallSpacing,
          
          // Risk Score
          Text(
            'Risk Score: ${customer.riskProfile.riskScore.toStringAsFixed(1)}/100',
            style: ModalConstants.subtitleStyle(context),
          ),
        ],
      ),
    );
  }

  /// Modern debt summary card
  Widget _buildModernDebtSummaryCard(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppThemes.cardColor,
        borderRadius: BorderRadius.circular(ModalConstants.defaultRadius),
        border: Border.all(
          color: AppThemes.dividerColor,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Debt Summary',
            style: ModalConstants.subtitleStyle(context, color: AppThemes.textPrimaryColor),
          ),
          ModalConstants.defaultSpacing,
          
          // Debt Stats Grid
          Row(
            children: [
              Expanded(
                child: _buildModernStatCard(
                  context,
                  'Outstanding',
                  customer.stats.currentOutstanding > 0
                    ? '\$${customer.stats.currentOutstanding.toStringAsFixed(2)}'
                    : 'N/A',
                  Icons.account_balance_wallet_rounded,
                  AppThemes.warningColor,
                ),
              ),
              ModalConstants.defaultHorizontalSpacing,
              Expanded(
                child: _buildModernStatCard(
                  context,
                  'Active Debts',
                  customer.stats.activeDebts.toString(),
                  Icons.receipt_long_rounded,
                  AppThemes.primaryColor,
                ),
              ),
            ],
          ),
          ModalConstants.defaultSpacing,
          
          Row(
            children: [
              Expanded(
                child: _buildModernStatCard(
                  context,
                  'Total Debts',
                  customer.stats.totalDebts.toString(),
                  Icons.receipt_rounded,
                  AppThemes.textSecondaryColor,
                ),
              ),
              ModalConstants.defaultHorizontalSpacing,
              Expanded(
                child: _buildModernStatCard(
                  context,
                  'Reliability',
                  customer.stats.paymentReliability.displayName,
                  Icons.trending_up_rounded,
                  _getReliabilityColor(customer.stats.paymentReliability),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Modern stat card
  Widget _buildModernStatCard(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 24,
            color: color,
          ),
          ModalConstants.smallSpacing,
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
          ModalConstants.tinySpacing,
          Text(
            label,
            style: ModalConstants.captionStyle(context),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Modern risk assessment details
  Widget _buildModernRiskAssessmentDetails(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Risk Assessment Details',
          style: ModalConstants.subtitleStyle(context, color: AppThemes.textPrimaryColor),
        ),
        ModalConstants.defaultSpacing,
        
        _buildModernDetailItem(
          context,
          'Last Assessment',
          _formatDate(customer.riskProfile.lastAssessment),
          Icons.calendar_today_rounded,
        ),
        ModalConstants.smallSpacing,
        
        _buildModernDetailItem(
          context,
          'Risk Source',
          customer.riskProfile.riskSource,
          Icons.source_rounded,
        ),
        
        if (customer.riskProfile.riskReason?.isNotEmpty == true) ...[
          ModalConstants.smallSpacing,
          _buildModernDetailItem(
            context,
            'Risk Reason',
            customer.riskProfile.riskReason!,
            Icons.info_rounded,
          ),
        ],
        
        if (customer.riskProfile.mlData != null && customer.riskProfile.mlData!.isNotEmpty) ...[
          ModalConstants.defaultSpacing,
          _buildModernMLFactors(context),
        ],
      ],
    );
  }

  /// Modern ML factors display
  Widget _buildModernMLFactors(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppThemes.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppThemes.primaryColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.psychology_rounded,
                size: 20,
                color: AppThemes.primaryColor,
              ),
              ModalConstants.smallHorizontalSpacing,
              Text(
                'ML Risk Factors',
                style: ModalConstants.subtitleStyle(context, color: AppThemes.primaryColor),
              ),
            ],
          ),
          ModalConstants.defaultSpacing,
          
          ...customer.riskProfile.mlData!.entries.map((entry) => 
            Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color: AppThemes.primaryColor,
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                  ModalConstants.smallHorizontalSpacing,
                  Expanded(
                    child: Text(
                      '${entry.key}: ${entry.value}',
                      style: ModalConstants.captionStyle(context),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Modern additional information card
  Widget _buildModernAdditionalInformation(BuildContext context) {
    return Container(
      decoration: ModalConstants.modernCardDecoration(),
      child: Padding(
        padding: ModalConstants.cardPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppThemes.accentColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.note_add_rounded,
                    size: 20,
                    color: AppThemes.accentColor,
                  ),
                ),
                ModalConstants.defaultHorizontalSpacing,
                Text(
                  'Additional Information',
                  style: ModalConstants.sectionTitleStyle(context),
                ),
              ],
            ),
            ModalConstants.largeSpacing,
            
            if (customer.notes?.isNotEmpty == true) ...[
              _buildModernNotesSection(context),
              ModalConstants.defaultSpacing,
            ],
            
            if (customer.tags?.isNotEmpty == true) ...[
              _buildModernTagsSection(context),
            ],
          ],
        ),
      ),
    );
  }

  /// Modern notes section
  Widget _buildModernNotesSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppThemes.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppThemes.dividerColor,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.note_rounded,
                size: 16,
                color: AppThemes.textSecondaryColor,
              ),
              ModalConstants.smallHorizontalSpacing,
              Text(
                'Notes',
                style: ModalConstants.subtitleStyle(context),
              ),
            ],
          ),
          ModalConstants.smallSpacing,
          Text(
            customer.notes!,
            style: ModalConstants.bodyTextStyle(context),
          ),
        ],
      ),
    );
  }

  /// Modern tags section
  Widget _buildModernTagsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.local_offer_rounded,
              size: 16,
              color: AppThemes.textSecondaryColor,
            ),
            ModalConstants.smallHorizontalSpacing,
            Text(
              'Tags',
              style: ModalConstants.subtitleStyle(context),
            ),
          ],
        ),
        ModalConstants.smallSpacing,
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: customer.tags!.map((tag) => Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppThemes.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppThemes.primaryColor.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Text(
              tag,
              style: TextStyle(
                color: AppThemes.primaryColor,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          )).toList(),
        ),
      ],
    );
  }

  /// Modern info grid layout
  Widget _buildModernInfoGrid(BuildContext context, List<_InfoItem> items) {
    return Column(
      children: items.map((item) => 
        Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildModernDetailItem(
            context,
            item.label,
            item.value,
            item.icon,
          ),
        ),
      ).toList(),
    );
  }

  /// Modern detail item
  Widget _buildModernDetailItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppThemes.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppThemes.dividerColor,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppThemes.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 16,
              color: AppThemes.primaryColor,
            ),
          ),
          ModalConstants.defaultHorizontalSpacing,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: ModalConstants.captionStyle(context),
                ),
                ModalConstants.tinySpacing,
                Text(
                  value,
                  style: ModalConstants.bodyTextStyle(context),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Modern action buttons for customer operations
  Widget _buildActionButtons(BuildContext context) {
    return Container(
      decoration: ModalConstants.modernCardDecoration(),
      child: Padding(
        padding: ModalConstants.cardPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppThemes.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.settings_rounded,
                    size: 20,
                    color: AppThemes.primaryColor,
                  ),
                ),
                ModalConstants.defaultHorizontalSpacing,
                Text(
                  'Quick Actions',
                  style: ModalConstants.sectionTitleStyle(context),
                ),
              ],
            ),
            ModalConstants.largeSpacing,
            
            // Action Buttons Grid - Permission Aware
            BlocBuilder<AuthBloc, AuthState>(
              builder: (context, authState) {
                final user = authState is AuthAuthenticated ? authState.user : null;
                return _buildPermissionAwareActionButtons(context, user);
              },
            ),
            ModalConstants.defaultSpacing,
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    context: context,
                    icon: Icons.history_rounded,
                    label: 'View History',
                    color: AppThemes.infoColor,
                    onTap: () {
                      Navigator.of(context).pop();
                      // TODO: Implement customer history view
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('View history for ${customer.fullName} - Coming soon!'),
                          backgroundColor: AppThemes.infoColor,
                        ),
                      );
                    },
                  ),
                ),
                ModalConstants.defaultHorizontalSpacing,
                Expanded(
                  child: _buildActionButton(
                    context: context,
                    icon: Icons.delete_rounded,
                    label: 'Delete',
                    color: AppThemes.errorColor,
                    onTap: () {
                      Navigator.of(context).pop();
                      CustomerModalHandlers.showDeleteCustomerModal(context, customer);
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build permission-aware action buttons
  Widget _buildPermissionAwareActionButtons(BuildContext context, User? user) {
    final actionButtons = <Widget>[];

    // Edit Customer button - only if user can update customers
    if (PermissionUtils.canUpdateCustomers(user)) {
      actionButtons.add(
        Expanded(
          child: _buildActionButton(
            context: context,
            icon: Icons.edit_rounded,
            label: 'Edit Customer',
            color: AppThemes.primaryColor,
            onTap: () {
              Navigator.of(context).pop();
              CustomerModalHandlers.showEditCustomerModal(context, customer);
            },
          ),
        ),
      );
    }

    // Add Debt button - only if user can create debts
    if (PermissionUtils.canCreateDebts(user)) {
      if (actionButtons.isNotEmpty) {
        actionButtons.add(ModalConstants.defaultHorizontalSpacing);
      }
      actionButtons.add(
        Expanded(
          child: _buildActionButton(
            context: context,
            icon: Icons.add_rounded,
            label: 'Add Debt',
            color: AppThemes.successColor,
            onTap: () {
              Navigator.of(context).pop();
              // TODO: Implement debt creation with preselected customer
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Add debt for ${customer.fullName} - Coming soon!'),
                  backgroundColor: AppThemes.successColor,
                ),
              );
            },
          ),
        ),
      );
    }

    // If no buttons are available, show a message
    if (actionButtons.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppThemes.textSecondaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              Icons.lock_outline,
              color: AppThemes.textSecondaryColor,
              size: 20,
            ),
            ModalConstants.smallHorizontalSpacing,
            Expanded(
              child: Text(
                'No actions available with current permissions',
                style: ModalConstants.captionStyle(context),
              ),
            ),
          ],
        ),
      );
    }

    return Row(children: actionButtons);
  }

  /// Modern action button widget
  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: color.withOpacity(0.08),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                ModalConstants.smallSpacing,
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper methods (keeping existing logic)
  Color _getRiskLevelColor(RiskLevel riskLevel) {
    switch (riskLevel) {
      case RiskLevel.low:
        return AppThemes.successColor;
      case RiskLevel.medium:
        return AppThemes.warningColor;
      case RiskLevel.high:
        return AppThemes.errorColor;
      case RiskLevel.activeDebt:
        return AppThemes.primaryColor;
    }
  }

  IconData _getRiskLevelIcon(RiskLevel riskLevel) {
    switch (riskLevel) {
      case RiskLevel.low:
        return Icons.check_circle_rounded;
      case RiskLevel.medium:
        return Icons.warning_rounded;
      case RiskLevel.high:
        return Icons.error_rounded;
      case RiskLevel.activeDebt:
        return Icons.account_balance_wallet_rounded;
    }
  }

  Color _getReliabilityColor(PaymentReliability reliability) {
    switch (reliability) {
      case PaymentReliability.excellent:
        return AppThemes.successColor;
      case PaymentReliability.good:
        return AppThemes.primaryColor;
      case PaymentReliability.fair:
        return AppThemes.warningColor;
      case PaymentReliability.poor:
        return AppThemes.errorColor;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Helper class for info items
class _InfoItem {
  final String label;
  final String value;
  final IconData icon;

  _InfoItem({
    required this.label,
    required this.value,
    required this.icon,
  });
} 