import 'package:dartz/dartz.dart';
import 'package:deyncare_app/domain/repositories/debt_repository.dart';
import 'package:deyncare_app/domain/models/debt.dart';
import 'package:deyncare_app/core/errors/failures.dart';

/// Use case for updating existing debt - BACKEND ALIGNED
/// Matches PUT /api/debts/:debtId endpoint
/// Backend allows updating amount, dueDate and description
class UpdateDebtUseCase {
  final DebtRepository _repository;

  UpdateDebtUseCase(this._repository);

  /// Execute the use case to update debt
  /// Backend supports amount, dueDate and description updates
  Future<Either<Failure, Debt>> execute({
    required String debtId,
    double? amount,
    DateTime? dueDate,
    String? description,
  }) async {
    if (debtId.trim().isEmpty) {
      return Left(ValidationFailure(message: 'Debt ID is required'));
    }

    // Validate debt ID format
    final debtIdPattern = RegExp(r'^DEBT\d{3}$');
    if (!debtIdPattern.hasMatch(debtId)) {
      return Left(ValidationFailure(message: 'Invalid debt ID format. Expected format: DEBT001'));
    }

    // Validate due date if provided (backend doesn't enforce future date)
    // Note: Backend validation was removed to allow historical corrections

    // Validate amount if provided
    if (amount != null && amount <= 0) {
      return Left(ValidationFailure(message: 'Amount must be greater than 0'));
    }

    // Validate description length if provided
    if (description != null && description.length > 500) {
      return Left(ValidationFailure(message: 'Description must be less than 500 characters'));
    }

    // At least one field must be provided (matches backend validation)
    if (amount == null && dueDate == null && (description == null || description.trim().isEmpty)) {
      return Left(ValidationFailure(message: 'At least one field must be provided for update'));
    }

    return await _repository.updateDebt(
      debtId: debtId.trim(),
      amount: amount,
      dueDate: dueDate,
      description: description?.trim(),
      status: null, // Not allowed by backend
    );
  }
} 