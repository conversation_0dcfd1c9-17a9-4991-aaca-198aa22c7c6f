import 'package:dartz/dartz.dart';
import 'package:deyncare_app/domain/repositories/customer_repository.dart';
import 'package:deyncare_app/core/errors/failures.dart';

/// Use case for deleting a customer - BACKEND ALIGNED
class DeleteCustomerUseCase {
  final CustomerRepository _repository;

  DeleteCustomerUseCase(this._repository);

  /// Execute the use case to delete customer
  /// Supports force parameter for customers with debt history
  Future<Either<Failure, bool>> execute(
    String customerId, {
    bool force = false,
  }) async {
    if (customerId.trim().isEmpty) {
      return Left(ValidationFailure(message: 'Customer ID is required'));
    }

    // Validate customer ID format (matches backend validation)
    if (!RegExp(r'^CUST\d{3}$').hasMatch(customerId.trim())) {
      return Left(ValidationFailure(message: 'Invalid customer ID format. Expected: CUST001, CUST002, etc.'));
    }

    return await _repository.deleteCustomer(customerId.trim(), force: force);
  }
} 