import 'package:flutter/material.dart';
import 'package:deyncare_app/core/routes/app_router.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'dart:async';
import 'package:deyncare_app/domain/models/user.dart';

class PaymentSuccessScreen extends StatefulWidget {
  final User user;

  const PaymentSuccessScreen({super.key, required this.user});

  @override
  State<PaymentSuccessScreen> createState() => _PaymentSuccessScreenState();
}

class _PaymentSuccessScreenState extends State<PaymentSuccessScreen> {
  int _countdown = 5; // Countdown from 5 seconds
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startCountdown();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown == 0) {
        _timer?.cancel();
        // Redirect to login screen
        if (mounted) {
          AppRouter.navigateToLogin(context);
        }
      } else {
        setState(() {
          _countdown--;
        });
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeUtils.getBackgroundColor(context, type: BackgroundType.primary),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.check_circle_outline,
                color: ThemeUtils.getStatusColors(context).success,
                size: 100,
              ),
              const SizedBox(height: 32),
              Text(
                'Payment Successful!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: ThemeUtils.getTextColor(context, type: TextColorType.primary),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'Welcome to DeynCare, ${widget.user.fullName}! Your account has been successfully created and activated. Please login to start using all the features.',
                style: TextStyle(
                  fontSize: 16,
                  color: ThemeUtils.getTextColor(context, type: TextColorType.secondary),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              Text(
                'Redirecting to login in $_countdown seconds...',
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeUtils.getTextColor(context, type: TextColorType.hint),
                ),
              ),
              const SizedBox(height: 20),
              TextButton(
                onPressed: () {
                  _timer?.cancel();
                  AppRouter.navigateToLogin(context);
                },
                child: Text(
                  'Go to Login now',
                  style: TextStyle(
                    color: AppThemes.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 