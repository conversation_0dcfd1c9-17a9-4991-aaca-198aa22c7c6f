import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_strings.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';

/// Model class to store content for each onboarding page
class OnboardingContent {
  final String title;
  final String description;
  final String imagePath;
  final IconData icon;
  final Color iconColor;

  const OnboardingContent({
    required this.title,
    required this.description,
    required this.imagePath,
    required this.icon,
    required this.iconColor,
  });
}

/// List of all onboarding content pages
final List<OnboardingContent> onboardingContents = [
  // Welcome to DeynCare
  OnboardingContent(
    title: AppStrings.onboardingTitle1,
    description: AppStrings.onboardingDesc1,
    imagePath: 'assets/images/onb1.png',
    icon: Icons.handshake_outlined,
    iconColor: AppThemes.primaryColor,
  ),

  // Know Your Customers
  OnboardingContent(
    title: AppStrings.onboardingTitle2,
    description: AppStrings.onboardingDesc2,
    imagePath: 'assets/images/onb2.png',
    icon: Icons.people_outline,
    iconColor: AppThemes.secondaryColor,
  ),

  // Smart Risk Scoring
  OnboardingContent(
    title: AppStrings.onboardingTitle3,
    description: AppStrings.onboardingDesc3,
    imagePath: 'assets/images/onb3.png',
    icon: Icons.psychology_outlined,
    iconColor: AppThemes.accentColor,
  ),

  // Visual Reports Made Simple
  OnboardingContent(
    title: AppStrings.onboardingTitle4,
    description: AppStrings.onboardingDesc4,
    imagePath: 'assets/images/onb4.png',
    icon: Icons.analytics_outlined,
    iconColor: AppThemes.primaryColor,
  ),
];
