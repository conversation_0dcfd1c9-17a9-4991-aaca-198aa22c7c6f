import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:deyncare_app/domain/usecases/reports/generate_risk_report_use_case.dart';
import 'package:flutter/foundation.dart';

// Events
abstract class RiskReportEvent extends Equatable {
  const RiskReportEvent();

  @override
  List<Object?> get props => [];
}

class GenerateRiskReportEvent extends RiskReportEvent {
  final String? month;
  final String? year;
  final String? startDate;
  final String? endDate;
  final String dateRange;
  final String shopName;
  final String? shopLogo;

  const GenerateRiskReportEvent({
    this.month,
    this.year,
    this.startDate,
    this.endDate,
    this.dateRange = 'monthly',
    required this.shopName,
    this.shopLogo,
  });

  @override
  List<Object?> get props => [month, year, startDate, endDate, dateRange, shopName, shopLogo];
}

class ResetRiskReportEvent extends RiskReportEvent {}

// States
abstract class RiskReportState extends Equatable {
  const RiskReportState();

  @override
  List<Object?> get props => [];
}

class RiskReportInitial extends RiskReportState {}

class RiskReportGenerating extends RiskReportState {}

class RiskReportGenerated extends RiskReportState {
  final String pdfPath;
  final String reportPeriod;

  const RiskReportGenerated({
    required this.pdfPath,
    required this.reportPeriod,
  });

  @override
  List<Object?> get props => [pdfPath, reportPeriod];
}

class RiskReportError extends RiskReportState {
  final String message;

  const RiskReportError(this.message);

  @override
  List<Object?> get props => [message];
}

// BLoC
class RiskReportBloc extends Bloc<RiskReportEvent, RiskReportState> {
  final GenerateRiskReportUseCase generateRiskReportUseCase;

  RiskReportBloc({
    required this.generateRiskReportUseCase,
  }) : super(RiskReportInitial()) {
    on<GenerateRiskReportEvent>(_onGenerateRiskReport);
    on<ResetRiskReportEvent>(_onResetRiskReport);
  }

  Future<void> _onGenerateRiskReport(
    GenerateRiskReportEvent event,
    Emitter<RiskReportState> emit,
  ) async {
    if (kDebugMode) {
      print('🔍 RiskReportBloc: Starting risk report generation');
      print(
          '🔍 Event: month=${event.month}, year=${event.year}, startDate=${event.startDate}, endDate=${event.endDate}, dateRange=${event.dateRange}');
    }

    emit(RiskReportGenerating());

    try {
      if (kDebugMode) {
        print('🔍 RiskReportBloc: Calling risk report use case...');
      }

      final pdfPath = await generateRiskReportUseCase.execute(
        startDate: event.startDate,
        endDate: event.endDate,
        period: event.dateRange,
        shopName: event.shopName,
        shopLogo: event.shopLogo,
      );

      if (kDebugMode) {
        print('🔍 RiskReportBloc: Use case completed, PDF path: $pdfPath');
      }

      final reportPeriod = _getReportPeriodDescription(
        month: event.month,
        year: event.year,
        startDate: event.startDate,
        endDate: event.endDate,
        dateRange: event.dateRange,
      );

      if (kDebugMode) {
        print('🔍 RiskReportBloc: Report period: $reportPeriod');
        print('✅ RiskReportBloc: Emitting success state');
      }

      emit(RiskReportGenerated(
        pdfPath: pdfPath,
        reportPeriod: reportPeriod,
      ));
    } catch (error) {
      if (kDebugMode) {
        print('❌ RiskReportBloc Error: $error');
        print('❌ Error type: ${error.runtimeType}');
        print('❌ Stack trace: ${StackTrace.current}');
      }
      emit(RiskReportError(error.toString()));
    }
  }

  void _onResetRiskReport(
    ResetRiskReportEvent event,
    Emitter<RiskReportState> emit,
  ) {
    emit(RiskReportInitial());
  }

  String _getReportPeriodDescription({
    String? month,
    String? year,
    String? startDate,
    String? endDate,
    String dateRange = 'monthly',
  }) {
    if (month != null && year != null) {
      final monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      return '${monthNames[int.parse(month) - 1]} $year';
    }

    if (startDate != null && endDate != null) {
      return '$startDate to $endDate';
    }

    switch (dateRange) {
      case 'daily':
        return 'Today';
      case 'weekly':
        return 'This Week';
      case 'monthly':
        return 'This Month';
      case 'yearly':
        return 'This Year';
      default:
        return 'All Time';
    }
  }
}
