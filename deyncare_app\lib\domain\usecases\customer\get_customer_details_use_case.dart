import 'package:dartz/dartz.dart';
import 'package:deyncare_app/domain/repositories/customer_repository.dart';
import 'package:deyncare_app/data/models/customer_model.dart';
import 'package:deyncare_app/core/errors/failures.dart';

/// Use case for retrieving customer details by ID - BACKEND ALIGNED
class GetCustomerDetailsUseCase {
  final CustomerRepository _repository;

  GetCustomerDetailsUseCase(this._repository);

  /// Execute the use case to get customer details
  Future<Either<Failure, CustomerDetailResponse>> execute(String customerId) async {
    if (customerId.trim().isEmpty) {
      return Left(ValidationFailure(message: 'Customer ID is required'));
    }

    // Validate customer ID format (matches backend validation)
    if (!RegExp(r'^CUST\d{3}$').hasMatch(customerId.trim())) {
      return Left(ValidationFailure(message: 'Invalid customer ID format. Expected: CUST001, CUST002, etc.'));
    }

    return await _repository.getCustomerById(customerId.trim());
  }
} 