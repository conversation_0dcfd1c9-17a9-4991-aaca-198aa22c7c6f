import 'package:flutter/material.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'package:shimmer/shimmer.dart';

/// A performance-optimized skeleton loader widget
/// Follows best practices for memory efficiency and smooth animations
class SkeletonLoader extends StatelessWidget {
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final Color? baseColor;
  final Color? highlightColor;
  final bool enabled;

  const SkeletonLoader({
    super.key,
    this.width,
    this.height,
    this.borderRadius,
    this.baseColor,
    this.highlightColor,
    this.enabled = true,
  });

  const SkeletonLoader.circular({
    super.key,
    required double size,
    this.baseColor,
    this.highlightColor,
    this.enabled = true,
  }) : width = size,
       height = size,
       borderRadius = null;

  const SkeletonLoader.text({
    super.key,
    this.width,
    double? height,
    this.baseColor,
    this.highlightColor,
    this.enabled = true,
  }) : height = height ?? 16,
       borderRadius = const BorderRadius.all(Radius.circular(4));

  @override
  Widget build(BuildContext context) {
    if (!enabled) {
      return SizedBox(width: width, height: height);
    }

    final shimmerColors = ThemeUtils.getShimmerColors(context);

    return RepaintBoundary(
      child: Shimmer.fromColors(
        baseColor: baseColor ?? shimmerColors.baseColor,
        highlightColor: highlightColor ?? shimmerColors.highlightColor,
        period: const Duration(milliseconds: 1200),
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: baseColor ?? shimmerColors.baseColor,
            borderRadius: borderRadius ?? 
              (width == height 
                ? BorderRadius.circular(width! / 2) 
                : const BorderRadius.all(Radius.circular(8))),
          ),
        ),
      ),
    );
  }
}

/// Skeleton patterns for common UI components
class SkeletonPatterns {
  
  /// KPI Card skeleton with optimized layout and theme-aware colors
  static Widget kpiCard(BuildContext context, {bool enabled = true}) {
    return RepaintBoundary(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: ThemeUtils.getBackgroundColor(context, type: BackgroundType.card),
          borderRadius: BorderRadius.circular(16),
          boxShadow: ThemeUtils.getElevationShadow(context, elevation: 2),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                SkeletonLoader.circular(
                  size: 40,
                  enabled: enabled,
                ),
                const Spacer(),
                SkeletonLoader(
                  width: 30,
                  height: 20,
                  enabled: enabled,
                ),
              ],
            ),
            const SizedBox(height: 16),
            SkeletonLoader.text(
              width: 80,
              height: 24,
              enabled: enabled,
            ),
            const SizedBox(height: 8),
            SkeletonLoader.text(
              width: 60,
              height: 16,
              enabled: enabled,
            ),
          ],
        ),
      ),
    );
  }

  /// Activity item skeleton
  static Widget activityItem({bool enabled = true}) {
    return RepaintBoundary(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            SkeletonLoader.circular(
              size: 48,
              enabled: enabled,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SkeletonLoader.text(
                    width: double.infinity,
                    height: 18,
                    enabled: enabled,
                  ),
                  const SizedBox(height: 4),
                  SkeletonLoader.text(
                    width: 150,
                    height: 14,
                    enabled: enabled,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            SkeletonLoader.text(
              width: 60,
              height: 16,
              enabled: enabled,
            ),
          ],
        ),
      ),
    );
  }

  /// Chart skeleton placeholder with theme-aware colors
  static Widget chart(BuildContext context, {bool enabled = true, double? height}) {
    return RepaintBoundary(
      child: Container(
        height: height ?? 200,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: ThemeUtils.getBackgroundColor(context, type: BackgroundType.card),
          borderRadius: BorderRadius.circular(16),
          boxShadow: ThemeUtils.getElevationShadow(context, elevation: 2),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SkeletonLoader.text(
              width: 120,
              height: 20,
              enabled: enabled,
            ),
            const SizedBox(height: 16),
            Expanded(
              child: SkeletonLoader(
                width: double.infinity,
                height: double.infinity,
                borderRadius: BorderRadius.circular(8),
                enabled: enabled,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Header skeleton
  static Widget header({bool enabled = true}) {
    return RepaintBoundary(
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SkeletonLoader.text(
                  width: 120,
                  height: 24,
                  enabled: enabled,
                ),
                Row(
                  children: List.generate(3, (index) => Padding(
                    padding: EdgeInsets.only(left: index > 0 ? 8 : 0),
                    child: SkeletonLoader.circular(
                      size: 40,
                      enabled: enabled,
                    ),
                  )),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SkeletonLoader.text(
              width: 200,
              height: 28,
              enabled: enabled,
            ),
            const SizedBox(height: 8),
            SkeletonLoader.text(
              width: 150,
              height: 16,
              enabled: enabled,
            ),
          ],
        ),
      ),
    );
  }
}

/// Optimized skeleton list builder - only builds visible items
class SkeletonListView extends StatelessWidget {
  final Widget Function(int index) itemBuilder;
  final int itemCount;
  final EdgeInsets? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;

  const SkeletonListView({
    super.key,
    required this.itemBuilder,
    this.itemCount = 5, // Limited to 5 items for performance
    this.padding,
    this.physics,
    this.shrinkWrap = false,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: itemCount,
      padding: padding,
      physics: physics,
      shrinkWrap: shrinkWrap,
      itemBuilder: (context, index) => RepaintBoundary(
        child: itemBuilder(index),
      ),
    );
  }
}

/// Optimized skeleton grid builder - only builds visible items
class SkeletonGridView extends StatelessWidget {
  final Widget Function(int index) itemBuilder;
  final int itemCount;
  final int crossAxisCount;
  final double? childAspectRatio;
  final EdgeInsets? padding;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const SkeletonGridView({
    super.key,
    required this.itemBuilder,
    required this.crossAxisCount,
    this.itemCount = 4, // Limited for performance
    this.childAspectRatio,
    this.padding,
    this.mainAxisSpacing = 0.0,
    this.crossAxisSpacing = 0.0,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio ?? 1.0,
        mainAxisSpacing: mainAxisSpacing,
        crossAxisSpacing: crossAxisSpacing,
      ),
      itemCount: itemCount,
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics,
      itemBuilder: (context, index) => RepaintBoundary(
        child: itemBuilder(index),
      ),
    );
  }
}

/// Customer-specific skeleton patterns
class CustomerSkeletonPatterns {
  
  /// Customer list item skeleton with theme-aware colors
  static Widget customerListItem(BuildContext context, {bool enabled = true}) {
    return RepaintBoundary(
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: ThemeUtils.getBackgroundColor(context, type: BackgroundType.card),
          borderRadius: BorderRadius.circular(16),
          boxShadow: ThemeUtils.getElevationShadow(context, elevation: 2),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Avatar skeleton
              SkeletonLoader.circular(size: 48, enabled: enabled),
              const SizedBox(width: 16),
              
              // Content skeleton
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SkeletonLoader.text(width: double.infinity, height: 18, enabled: enabled),
                    const SizedBox(height: 4),
                    SkeletonLoader.text(width: 150, height: 14, enabled: enabled),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        SkeletonLoader.text(width: 60, height: 20, enabled: enabled),
                        const SizedBox(width: 8),
                        SkeletonLoader.text(width: 80, height: 16, enabled: enabled),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Actions skeleton
              Column(
                children: [
                  SkeletonLoader.circular(size: 32, enabled: enabled),
                  const SizedBox(height: 8),
                  SkeletonLoader.circular(size: 32, enabled: enabled),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Customer details header skeleton with theme-aware colors
  static Widget customerDetailsHeader(BuildContext context, {bool enabled = true}) {
    return RepaintBoundary(
      child: Container(
        decoration: BoxDecoration(
          color: ThemeUtils.getBackgroundColor(context, type: BackgroundType.card),
          borderRadius: BorderRadius.circular(16),
          boxShadow: ThemeUtils.getElevationShadow(context, elevation: 2),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              // Profile avatar skeleton
              SkeletonLoader.circular(size: 80, enabled: enabled),
              const SizedBox(height: 16),
              
              // Name skeleton
              SkeletonLoader.text(width: 150, height: 24, enabled: enabled),
              const SizedBox(height: 8),
              
              // ID badge skeleton
              SkeletonLoader.text(width: 100, height: 20, enabled: enabled),
              const SizedBox(height: 16),
              
              // Status badge skeleton
              SkeletonLoader.text(width: 80, height: 32, enabled: enabled),
            ],
          ),
        ),
      ),
    );
  }

  /// Customer info card skeleton
  static Widget customerInfoCard({bool enabled = true}) {
    return RepaintBoundary(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section header skeleton
              Row(
                children: [
                  SkeletonLoader.circular(size: 36, enabled: enabled),
                  const SizedBox(width: 12),
                  SkeletonLoader.text(width: 120, height: 20, enabled: enabled),
                ],
              ),
              const SizedBox(height: 20),
              
              // Info items skeleton
              ...List.generate(3, (index) => Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.grey.shade200,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      SkeletonLoader.circular(size: 32, enabled: enabled),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SkeletonLoader.text(width: 80, height: 14, enabled: enabled),
                            const SizedBox(height: 4),
                            SkeletonLoader.text(width: double.infinity, height: 16, enabled: enabled),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              )),
            ],
          ),
        ),
      ),
    );
  }

  /// Customer risk profile skeleton
  static Widget customerRiskProfile({bool enabled = true}) {
    return RepaintBoundary(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section header
              Row(
                children: [
                  SkeletonLoader.circular(size: 36, enabled: enabled),
                  const SizedBox(width: 12),
                  SkeletonLoader.text(width: 150, height: 20, enabled: enabled),
                ],
              ),
              const SizedBox(height: 20),
              
              // Risk display skeleton
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.grey.shade200,
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    SkeletonLoader.circular(size: 64, enabled: enabled),
                    const SizedBox(height: 16),
                    SkeletonLoader.text(width: 100, height: 20, enabled: enabled),
                    const SizedBox(height: 8),
                    SkeletonLoader.text(width: 120, height: 16, enabled: enabled),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              
              // Stats grid skeleton
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.grey.shade200,
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SkeletonLoader.text(width: 100, height: 16, enabled: enabled),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(child: _buildStatCardSkeleton(enabled: enabled)),
                        const SizedBox(width: 12),
                        Expanded(child: _buildStatCardSkeleton(enabled: enabled)),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(child: _buildStatCardSkeleton(enabled: enabled)),
                        const SizedBox(width: 12),
                        Expanded(child: _buildStatCardSkeleton(enabled: enabled)),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Customer action buttons skeleton
  static Widget customerActionButtons({bool enabled = true}) {
    return RepaintBoundary(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section header
              Row(
                children: [
                  SkeletonLoader.circular(size: 36, enabled: enabled),
                  const SizedBox(width: 12),
                  SkeletonLoader.text(width: 100, height: 20, enabled: enabled),
                ],
              ),
              const SizedBox(height: 20),
              
              // Action buttons grid
              Row(
                children: [
                  Expanded(child: _buildActionButtonSkeleton(enabled: enabled)),
                  const SizedBox(width: 12),
                  Expanded(child: _buildActionButtonSkeleton(enabled: enabled)),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(child: _buildActionButtonSkeleton(enabled: enabled)),
                  const SizedBox(width: 12),
                  Expanded(child: _buildActionButtonSkeleton(enabled: enabled)),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Customer form skeleton
  static Widget customerForm({bool enabled = true}) {
    return RepaintBoundary(
      child: Column(
        children: [
          // Header skeleton
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  SkeletonLoader.circular(size: 48, enabled: enabled),
                  const SizedBox(height: 16),
                  SkeletonLoader.text(width: 150, height: 24, enabled: enabled),
                  const SizedBox(height: 16),
                  SkeletonLoader.text(width: 200, height: 16, enabled: enabled),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          
          // Form fields skeleton
          ...List.generate(6, (index) => Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildFormFieldSkeleton(enabled: enabled),
          )),
          
          const SizedBox(height: 20),
          
          // Action buttons skeleton
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  SkeletonLoader(width: double.infinity, height: 48, enabled: enabled),
                  const SizedBox(height: 12),
                  SkeletonLoader(width: double.infinity, height: 48, enabled: enabled),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Private helper methods
  static Widget _buildStatCardSkeleton({bool enabled = true}) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          SkeletonLoader.circular(size: 24, enabled: enabled),
          const SizedBox(height: 8),
          SkeletonLoader.text(width: 40, height: 16, enabled: enabled),
          const SizedBox(height: 4),
          SkeletonLoader.text(width: 60, height: 12, enabled: enabled),
        ],
      ),
    );
  }

  static Widget _buildActionButtonSkeleton({bool enabled = true}) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          SkeletonLoader.circular(size: 48, enabled: enabled),
          const SizedBox(height: 8),
          SkeletonLoader.text(width: 60, height: 14, enabled: enabled),
        ],
      ),
    );
  }

  static Widget _buildFormFieldSkeleton({bool enabled = true}) {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            SkeletonLoader.circular(size: 20, enabled: enabled),
            const SizedBox(width: 12),
            Expanded(
              child: SkeletonLoader.text(width: double.infinity, height: 16, enabled: enabled),
            ),
          ],
        ),
      ),
    );
  }
} 