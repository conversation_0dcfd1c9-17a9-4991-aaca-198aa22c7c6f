import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:deyncare_app/core/utils/logger.dart';
import 'package:deyncare_app/data/models/plan_model.dart';
import 'package:deyncare_app/domain/repositories/plan_repository.dart';

part 'plan_event.dart';
part 'plan_state.dart';

class PlanBloc extends Bloc<PlanEvent, PlanState> {
  final PlanRepository _planRepository;

  PlanBloc({required PlanRepository planRepository})
      : _planRepository = planRepository,
        super(PlanInitial()) {
    logger.i('PlanBloc initialized');
    on<FetchPlans>(_onFetchPlans);
  }

  Future<void> _onFetchPlans(FetchPlans event, Emitter<PlanState> emit) async {
    logger.i('FetchPlans event received, emitting PlanLoading.');
    emit(PlanLoading());
    try {
      final plans = await _planRepository.getPlans();
      logger.i('Plans fetched successfully, emitting PlanLoaded.');
      emit(PlanLoaded(plans));
    } catch (e, stackTrace) {
      logger.e('Error fetching plans in PlanBloc', error: e, stackTrace: stackTrace);
      emit(const PlanError('Failed to fetch subscription plans. Please try again.'));
    }
  }
}
