import 'package:flutter_test/flutter_test.dart';
import 'package:deyncare_app/domain/models/auth_token.dart';

void main() {
  group('AuthToken', () {
    test('constructor creates token with correct properties', () {
      // Arrange
      final DateTime expiresAt = DateTime.now().add(const Duration(minutes: 15));
      
      // Act
      final token = AuthToken(
        accessToken: 'test_access_token',
        refreshToken: 'test_refresh_token',
        expiresAt: expiresAt,
      );
      
      // Assert
      expect(token.accessToken, 'test_access_token');
      expect(token.refreshToken, 'test_refresh_token');
      expect(token.expiresAt, expiresAt);
    });
    
    test('isExpired returns true for expired token', () {
      // Arrange - create a token that's already expired
      final token = AuthToken(
        accessToken: 'test_access_token',
        refreshToken: 'test_refresh_token',
        expiresAt: DateTime.now().subtract(const Duration(minutes: 5)),
      );
      
      // Assert
      expect(token.isExpired, isTrue);
    });
    
    test('isExpired returns false for valid token', () {
      // Arrange - create a token that expires in the future
      final token = AuthToken(
        accessToken: 'test_access_token',
        refreshToken: 'test_refresh_token',
        expiresAt: DateTime.now().add(const Duration(minutes: 5)),
      );
      
      // Assert
      expect(token.isExpired, isFalse);
    });
    
    test('copyWith returns new instance with updated values', () {
      // Arrange
      final DateTime originalExpiry = DateTime.now().add(const Duration(minutes: 15));
      final token = AuthToken(
        accessToken: 'original_access_token',
        refreshToken: 'original_refresh_token',
        expiresAt: originalExpiry,
      );
      
      // Act - update only the access token
      final updatedToken = token.copyWith(
        accessToken: 'updated_access_token',
      );
      
      // Assert - verify new token has updated access token but keeps original values for other fields
      expect(updatedToken.accessToken, 'updated_access_token');
      expect(updatedToken.refreshToken, 'original_refresh_token');
      expect(updatedToken.expiresAt, originalExpiry);
      
      // Original token should remain unchanged
      expect(token.accessToken, 'original_access_token');
      expect(token.refreshToken, 'original_refresh_token');
      expect(token.expiresAt, originalExpiry);
    });
    
    test('copyWith can update multiple properties at once', () {
      // Arrange
      final DateTime originalExpiry = DateTime.now().add(const Duration(minutes: 15));
      final DateTime newExpiry = DateTime.now().add(const Duration(minutes: 30));
      
      final token = AuthToken(
        accessToken: 'original_access_token',
        refreshToken: 'original_refresh_token',
        expiresAt: originalExpiry,
      );
      
      // Act - update multiple properties
      final updatedToken = token.copyWith(
        accessToken: 'updated_access_token',
        refreshToken: 'updated_refresh_token',
        expiresAt: newExpiry,
      );
      
      // Assert
      expect(updatedToken.accessToken, 'updated_access_token');
      expect(updatedToken.refreshToken, 'updated_refresh_token');
      expect(updatedToken.expiresAt, newExpiry);
    });
  });
}
