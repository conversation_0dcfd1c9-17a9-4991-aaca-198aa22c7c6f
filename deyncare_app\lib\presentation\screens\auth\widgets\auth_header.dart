import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/widgets/deyncare_logo.dart';

/// A reusable header component for authentication screens
/// Displays the DeynCare logo and screen title with responsive design
class AuthHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final bool showLogo;
  final DeynCareLogoVariant logoVariant;

  const AuthHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.showLogo = true,
    this.logoVariant = DeynCareLogoVariant.primary,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Logo container with responsive design
        if (showLogo) ...[
          Container(
            padding: EdgeInsets.all(isTablet ? 16 : 12),
            decoration: BoxDecoration(
              color: AppThemes.primaryColor.withValues(alpha: 0.05),
              shape: BoxShape.circle,
              border: Border.all(
                color: AppThemes.primaryColor.withValues(alpha: 0.1),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppThemes.primaryColor.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: DeynCareLogo.auth(
              variant: logoVariant,
              size: isTablet ? DeynCareLogoSize.large : DeynCareLogoSize.medium,
              showBorder: false,
              backgroundColor: Colors.transparent,
            ),
          ),
          SizedBox(height: isTablet ? 24 : 16),
        ],

        // Title with responsive typography
        Text(
          title,
          style: TextStyle(
            fontSize: isTablet ? 32 : 28,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
            letterSpacing: -0.5,
          ),
          textAlign: TextAlign.center,
        ),

        // Subtitle (if provided) with responsive typography
        if (subtitle != null) ...[
          SizedBox(height: isTablet ? 12 : 8),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: isTablet ? 32 : 16,
            ),
            child: Text(
              subtitle!,
              style: TextStyle(
                fontSize: isTablet ? 18 : 16,
                color: AppThemes.textSecondaryColor,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],

        // Brand tagline only for primary/SVG logo variants (not for icon)
        if (showLogo &&
            (logoVariant == DeynCareLogoVariant.primary ||
                logoVariant == DeynCareLogoVariant.svg)) ...[
          SizedBox(height: isTablet ? 16 : 12),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: isTablet ? 20 : 16,
              vertical: isTablet ? 10 : 8,
            ),
            decoration: BoxDecoration(
              color: AppThemes.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: AppThemes.primaryColor.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Text(
              'DEBT MANAGEMENT SYSTEM',
              style: TextStyle(
                fontSize: isTablet ? 12 : 10,
                fontWeight: FontWeight.w600,
                color: AppThemes.primaryColor,
                letterSpacing: 1.2,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ],
    );
  }
}
