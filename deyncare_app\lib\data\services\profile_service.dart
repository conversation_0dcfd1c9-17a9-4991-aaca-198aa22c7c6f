import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/data/services/auth_service.dart';

/// Profile service for managing user profile operations
class ProfileService {
  final AuthService _authService;

  ProfileService({AuthService? authService})
      : _authService = authService ?? AuthService();

  /// Get current user profile
  Future<User?> getCurrentUser() async {
    return await _authService.getCurrentUser();
  }

  /// Update user profile
  Future<User> updateProfile({
    required String fullName,
    required String phone,
  }) async {
    return await _authService.updateProfile(
      fullName: fullName,
      phone: phone,
    );
  }

  /// Change password
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    return await _authService.changePassword(
      currentPassword: currentPassword,
      newPassword: newPassword,
      confirmPassword: confirmPassword,
    );
  }
} 