import 'package:json_annotation/json_annotation.dart';
import 'package:deyncare_app/domain/models/payment.dart';

part 'payment_model.g.dart';

/// Data model for payment that matches backend API structure
@JsonSerializable()
class PaymentModel {
  @JsonKey(name: '_id')
  final String? id;
  
  final String paymentId;
  final double amount;
  final PaymentMethod paymentMethod;
  final PaymentStatus status;
  final PaymentContext context;
  final String referenceId;
  final String? description;
  final String? transactionReference;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? processedAt;
  final String? failureReason;
  final Map<String, dynamic>? metadata;
  final EvcPaymentDetailsModel? evcDetails;

  const PaymentModel({
    this.id,
    required this.paymentId,
    required this.amount,
    required this.paymentMethod,
    required this.status,
    required this.context,
    required this.referenceId,
    this.description,
    this.transactionReference,
    required this.createdAt,
    required this.updatedAt,
    this.processedAt,
    this.failureReason,
    this.metadata,
    this.evcDetails,
  });

  /// Factory constructor from JSON
  factory PaymentModel.fromJson(Map<String, dynamic> json) => _$PaymentModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$PaymentModelToJson(this);

  /// Convert to domain model
  Payment toDomain() => Payment(
        id: id ?? '',
        paymentId: paymentId,
        amount: amount,
        paymentMethod: paymentMethod,
        
        status: status,
        context: context,
        referenceId: referenceId,
        description: description,
        transactionReference: transactionReference,
        createdAt: createdAt,
        updatedAt: updatedAt,
        processedAt: processedAt,
        failureReason: failureReason,
        metadata: metadata,
        evcDetails: evcDetails?.toDomain(),
      );

  /// Create from domain model
  factory PaymentModel.fromDomain(Payment payment) => PaymentModel(
        id: payment.id.isNotEmpty ? payment.id : null,
        paymentId: payment.paymentId,
        amount: payment.amount,
        paymentMethod: payment.paymentMethod,
        status: payment.status,
        context: payment.context,
        referenceId: payment.referenceId,
        description: payment.description,
        transactionReference: payment.transactionReference,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
        processedAt: payment.processedAt,
        failureReason: payment.failureReason,
        metadata: payment.metadata,
        evcDetails: payment.evcDetails != null 
            ? EvcPaymentDetailsModel.fromDomain(payment.evcDetails!)
            : null,
      );

  PaymentModel copyWith({
    String? id,
    String? paymentId,
    double? amount,
    PaymentMethod? paymentMethod,
    PaymentStatus? status,
    PaymentContext? context,
    String? referenceId,
    String? description,
    String? transactionReference,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? processedAt,
    String? failureReason,
    Map<String, dynamic>? metadata,
    EvcPaymentDetailsModel? evcDetails,
  }) {
    return PaymentModel(
      id: id ?? this.id,
      paymentId: paymentId ?? this.paymentId,
      amount: amount ?? this.amount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      status: status ?? this.status,
      context: context ?? this.context,
      referenceId: referenceId ?? this.referenceId,
      description: description ?? this.description,
      transactionReference: transactionReference ?? this.transactionReference,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      processedAt: processedAt ?? this.processedAt,
      failureReason: failureReason ?? this.failureReason,
      metadata: metadata ?? this.metadata,
      evcDetails: evcDetails ?? this.evcDetails,
    );
  }
}

/// BACKEND-ALIGNED: Add Payment Request Model with Dual Time Tracking
/// Matches exactly: POST /api/debts/:debtId/payments
@JsonSerializable()
class AddPaymentRequest {
  final double amount;
  final DateTime? paymentDate; // Server time (official) - optional, server will set if not provided
  final DateTime? paidAtReal;  // Staff-recorded real payment time (when customer actually paid)
  final String? paymentMethod; // 'cash', 'bank_transfer', 'mobile_money', 'card', 'other'
  final String? notes;

  const AddPaymentRequest({
    required this.amount,
    this.paymentDate,
    this.paidAtReal,
    this.paymentMethod = 'cash',
    this.notes,
  });

  factory AddPaymentRequest.fromJson(Map<String, dynamic> json) => _$AddPaymentRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AddPaymentRequestToJson(this);
}

/// BACKEND-ALIGNED: Payment Response Model
/// Matches backend addPayment controller response structure
@JsonSerializable()
class PaymentResponse {
  final bool success;
  final String message;
  final PaymentResponseData data;

  const PaymentResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory PaymentResponse.fromJson(Map<String, dynamic> json) => _$PaymentResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentResponseToJson(this);
}

@JsonSerializable()
class PaymentResponseData {
  final PaymentInfo payment;
  final DebtInfo debt;
  final CustomerInfo customer;
  final MlEvaluationInfo mlEvaluation;
  final TimelineInfo timeline;
  final BusinessLogicInfo? businessLogic; // New enhanced business logic info

  const PaymentResponseData({
    required this.payment,
    required this.debt,
    required this.customer,
    required this.mlEvaluation,
    required this.timeline,
    this.businessLogic,
  });

  factory PaymentResponseData.fromJson(Map<String, dynamic> json) => _$PaymentResponseDataFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentResponseDataToJson(this);
}

@JsonSerializable()
class PaymentInfo {
  final String paymentId;
  final double amount;
  final DateTime? officialDate;    // Server time
  final DateTime? realPaymentDate; // Staff-recorded time
  final DateTime? date;            // Backward compatibility
  final String method;
  final String timing;
  final String status;
  final String? notes;

  const PaymentInfo({
    required this.paymentId,
    required this.amount,
    this.officialDate,
    this.realPaymentDate,
    this.date, // Backward compatibility
    required this.method,
    required this.timing,
    required this.status,
    this.notes,
  });

  factory PaymentInfo.fromJson(Map<String, dynamic> json) => _$PaymentInfoFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentInfoToJson(this);
}

@JsonSerializable()
class DebtInfo {
  final String debtId;
  @JsonKey(name: 'DebtID')
  final int debtID;
  final double originalAmount;
  final double outstandingDebt;
  final double paidAmount;
  final double debtPaidRatio;
  final String status;
  final String riskLevel;

  const DebtInfo({
    required this.debtId,
    required this.debtID,
    required this.originalAmount,
    required this.outstandingDebt,
    required this.paidAmount,
    required this.debtPaidRatio,
    required this.status,
    required this.riskLevel,
  });

  factory DebtInfo.fromJson(Map<String, dynamic> json) => _$DebtInfoFromJson(json);

  Map<String, dynamic> toJson() => _$DebtInfoToJson(this);
}

@JsonSerializable()
class CustomerInfo {
  final String name;
  final String type;
  final String currentRiskLevel;
  final double riskScore;

  const CustomerInfo({
    required this.name,
    required this.type,
    required this.currentRiskLevel,
    required this.riskScore,
  });

  factory CustomerInfo.fromJson(Map<String, dynamic> json) => _$CustomerInfoFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerInfoToJson(this);
}

@JsonSerializable()
class MlEvaluationInfo {
  final bool evaluated;
  final String? reason;          // Why ML was triggered
  final String? riskLevel;
  final double? riskScore;
  final List<String>? factors;   // Changed from Map to List for backend compatibility
  final String message;
  final Map<String, dynamic>? details; // For additional info like daysUntilDue
  final String? error;           // Error message if evaluation failed

  const MlEvaluationInfo({
    required this.evaluated,
    this.reason,
    this.riskLevel,
    this.riskScore,
    this.factors,
    required this.message,
    this.details,
    this.error,
  });

  factory MlEvaluationInfo.fromJson(Map<String, dynamic> json) => _$MlEvaluationInfoFromJson(json);

  Map<String, dynamic> toJson() => _$MlEvaluationInfoToJson(this);
}

@JsonSerializable()
class TimelineInfo {
  final bool dueDatePassed;
  final DateTime dueDate;
  final DateTime? officialPaymentDate; // Server time
  final DateTime? realPaymentDate;     // Staff-recorded time
  final DateTime? paymentDate;         // Backward compatibility
  final int paymentDelay;
  final int? daysUntilDue;

  const TimelineInfo({
    required this.dueDatePassed,
    required this.dueDate,
    this.officialPaymentDate,
    this.realPaymentDate,
    this.paymentDate, // Backward compatibility
    required this.paymentDelay,
    this.daysUntilDue,
  });

  factory TimelineInfo.fromJson(Map<String, dynamic> json) => _$TimelineInfoFromJson(json);

  Map<String, dynamic> toJson() => _$TimelineInfoToJson(this);
}

/// Enhanced Business Logic Information
@JsonSerializable()
class BusinessLogicInfo {
  final bool mlTriggered;
  final String triggerReason;
  final bool earlyPaymentSupport;
  final bool partialPaymentSupport;
  final bool dualTimeTracking;

  const BusinessLogicInfo({
    required this.mlTriggered,
    required this.triggerReason,
    required this.earlyPaymentSupport,
    required this.partialPaymentSupport,
    required this.dualTimeTracking,
  });

  factory BusinessLogicInfo.fromJson(Map<String, dynamic> json) => _$BusinessLogicInfoFromJson(json);

  Map<String, dynamic> toJson() => _$BusinessLogicInfoToJson(this);
}

/// EVC payment details model (kept for existing functionality)
@JsonSerializable()
class EvcPaymentDetailsModel {
  final String phoneNumber;
  final String? sessionId;
  final String? requestId;
  final String? transactionId;
  final String? providerResponse;
  final DateTime? requestedAt;
  final DateTime? confirmedAt;

  const EvcPaymentDetailsModel({
    required this.phoneNumber,
    this.sessionId,
    this.requestId,
    this.transactionId,
    this.providerResponse,
    this.requestedAt,
    this.confirmedAt,
  });

  factory EvcPaymentDetailsModel.fromJson(Map<String, dynamic> json) => 
      _$EvcPaymentDetailsModelFromJson(json);

  Map<String, dynamic> toJson() => _$EvcPaymentDetailsModelToJson(this);

  EvcPaymentDetails toDomain() => EvcPaymentDetails(
        phoneNumber: phoneNumber,
        sessionId: sessionId,
        requestId: requestId,
        transactionId: transactionId,
        providerResponse: providerResponse,
        requestedAt: requestedAt,
        confirmedAt: confirmedAt,
      );

  factory EvcPaymentDetailsModel.fromDomain(EvcPaymentDetails details) => 
      EvcPaymentDetailsModel(
        phoneNumber: details.phoneNumber,
        sessionId: details.sessionId,
        requestId: details.requestId,
        transactionId: details.transactionId,
        providerResponse: details.providerResponse,
        requestedAt: details.requestedAt,
        confirmedAt: details.confirmedAt,
      );
} 