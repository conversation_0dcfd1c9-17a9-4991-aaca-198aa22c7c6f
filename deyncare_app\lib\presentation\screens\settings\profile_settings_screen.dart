import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/common_app_bar.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_text_field.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_button.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_state.dart';
import 'package:deyncare_app/data/services/profile_service.dart';
import 'package:deyncare_app/domain/models/user.dart';

/// User profile settings screen for managing personal information and password
class ProfileSettingsScreen extends StatefulWidget {
  const ProfileSettingsScreen({super.key});

  @override
  State<ProfileSettingsScreen> createState() => _ProfileSettingsScreenState();
}

class _ProfileSettingsScreenState extends State<ProfileSettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  // Profile form controllers
  final _profileFormKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  
  // Password form controllers
  final _passwordFormKey = GlobalKey<FormState>();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  // Services
  final ProfileService _profileService = ProfileService();
  
  // State
  User? _currentUser;
  
  bool _isLoadingProfile = false;
  bool _isLoadingPassword = false;
  bool _showCurrentPassword = false;
  bool _showNewPassword = false;
  bool _showConfirmPassword = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadUserData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    try {
      setState(() {
        _isLoadingProfile = true;
      });

      // Get current user from profile service
      _currentUser = await _profileService.getCurrentUser();
      
      if (_currentUser != null) {
        _fullNameController.text = _currentUser!.fullName;
        _emailController.text = _currentUser!.email;
        _phoneController.text = _currentUser!.phone ?? '';
      }
    } catch (e) {
      // Handle error - show snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load user data: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingProfile = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CommonAppBar(
        title: 'Profile Settings',
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.person_outline, size: 20),
                  const SizedBox(width: 8),
                  const Text('Profile Info', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.lock_outline, size: 20),
                  const SizedBox(width: 8),
                  const Text('Password', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
                ],
              ),
            ),
          ],
          labelColor: AppThemes.primaryColor,
          unselectedLabelColor: AppThemes.textSecondaryColor,
          indicatorColor: AppThemes.primaryColor,
          indicatorWeight: 3,
          indicatorSize: TabBarIndicatorSize.tab,
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildProfileTab(),
          _buildPasswordTab(),
        ],
      ),
    );
  }

  Widget _buildProfileTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _profileFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile Header
            _buildProfileHeader(),
            
            const SizedBox(height: 32),
            
            // Personal Information Section
            _buildSectionTitle('Personal Information'),
            const SizedBox(height: 16),
            _buildPersonalInfoSection(),
            
            const SizedBox(height: 40),
            
            // Save Profile Button
            AuthButton(
              label: 'Update Profile',
              isLoading: _isLoadingProfile,
              onPressed: _updateProfile,
              icon: const Icon(Icons.save, color: Colors.white),
            ),
            
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _passwordFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Password Security Info
            _buildPasswordSecurityInfo(),
            
            const SizedBox(height: 32),
            
            // Change Password Section
            _buildSectionTitle('Change Password'),
            const SizedBox(height: 16),
            _buildPasswordChangeSection(),
            
            const SizedBox(height: 40),
            
            // Change Password Button
            AuthButton(
              label: 'Change Password',
              isLoading: _isLoadingPassword,
              onPressed: _changePassword,
              icon: const Icon(Icons.security, color: Colors.white),
            ),
            
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.onSurface,
      ),
    );
  }

  Widget _buildProfileHeader() {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final user = state is AuthAuthenticated ? state.user : _currentUser;
        
        return CommonCard(
          child: Row(
            children: [
              // Enhanced Profile Avatar
              Container(
                width: 90,
                height: 90,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      AppThemes.primaryColor,
                      AppThemes.primaryColor.withOpacity(0.7),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppThemes.primaryColor.withOpacity(0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    Center(
                      child: Text(
                        user?.fullName?.isNotEmpty == true 
                            ? user!.fullName.substring(0, 1).toUpperCase() 
                            : 'U',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 36,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: 2,
                      right: 2,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: AppThemes.successColor,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                        child: const Icon(
                          Icons.verified,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(width: 20),
              
              // Enhanced User Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            user?.fullName ?? 'User Name',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              fontSize: 20,
                            ),
                          ),
                        ),
                        Icon(
                          Icons.star,
                          color: AppThemes.warningColor,
                          size: 20,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppThemes.primaryColor.withOpacity(0.15),
                            AppThemes.primaryColor.withOpacity(0.05),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(15),
                        border: Border.all(
                          color: AppThemes.primaryColor.withOpacity(0.2),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.admin_panel_settings,
                            color: AppThemes.primaryColor,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            user?.role?.toUpperCase() ?? 'ADMIN',
                            style: TextStyle(
                              color: AppThemes.primaryColor,
                              fontSize: 13,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.email_outlined,
                          color: AppThemes.textSecondaryColor,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Text(
                            user?.email ?? '<EMAIL>',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppThemes.textSecondaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          color: AppThemes.textSecondaryColor,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'Last updated: ${DateTime.now().toString().split(' ')[0]}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppThemes.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPasswordSecurityInfo() {
    return CommonCard(
      backgroundColor: AppThemes.infoColor.withOpacity(0.1),
      borderColor: AppThemes.infoColor.withOpacity(0.3),
      child: Row(
        children: [
          Icon(
            Icons.security,
            color: AppThemes.infoColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Password Security',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppThemes.infoColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Keep your account secure by using a strong password and changing it regularly.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppThemes.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return CommonCard(
      child: Column(
        children: [
          AuthTextField(
            label: 'Full Name',
            hintText: 'Enter your full name',
            controller: _fullNameController,
            prefixIcon: Icon(Icons.person, color: AppThemes.primaryColor),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your full name';
              }
              if (value.length < 2) {
                return 'Name must be at least 2 characters';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 20),
          
          AuthTextField(
            label: 'Email Address',
            hintText: 'Enter your email address',
            controller: _emailController,
            prefixIcon: Icon(Icons.email, color: AppThemes.primaryColor),
            keyboardType: TextInputType.emailAddress,
            enabled: false, // Usually email cannot be changed
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your email address';
              }
              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                return 'Please enter a valid email';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 20),
          
          AuthTextField(
            label: 'Phone Number',
            hintText: '+252 61 000 0000',
            controller: _phoneController,
            prefixIcon: Icon(Icons.phone, color: AppThemes.primaryColor),
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your phone number';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordChangeSection() {
    return CommonCard(
      child: Column(
        children: [
          AuthTextField(
            label: 'Current Password',
            hintText: 'Enter your current password',
            controller: _currentPasswordController,
            prefixIcon: Icon(Icons.lock, color: AppThemes.primaryColor),
            obscureText: !_showCurrentPassword,
            suffixIcon: IconButton(
              icon: Icon(
                _showCurrentPassword ? Icons.visibility_off : Icons.visibility,
                color: AppThemes.textSecondaryColor,
              ),
              onPressed: () {
                setState(() {
                  _showCurrentPassword = !_showCurrentPassword;
                });
              },
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your current password';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 20),
          
          AuthTextField(
            label: 'New Password',
            hintText: 'Enter your new password',
            controller: _newPasswordController,
            prefixIcon: Icon(Icons.lock_outline, color: AppThemes.primaryColor),
            obscureText: !_showNewPassword,
            suffixIcon: IconButton(
              icon: Icon(
                _showNewPassword ? Icons.visibility_off : Icons.visibility,
                color: AppThemes.textSecondaryColor,
              ),
              onPressed: () {
                setState(() {
                  _showNewPassword = !_showNewPassword;
                });
              },
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a new password';
              }
              if (value.length < 8) {
                return 'Password must be at least 8 characters';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 20),
          
          AuthTextField(
            label: 'Confirm New Password',
            hintText: 'Confirm your new password',
            controller: _confirmPasswordController,
            prefixIcon: Icon(Icons.lock_outline, color: AppThemes.primaryColor),
            obscureText: !_showConfirmPassword,
            suffixIcon: IconButton(
              icon: Icon(
                _showConfirmPassword ? Icons.visibility_off : Icons.visibility,
                color: AppThemes.textSecondaryColor,
              ),
              onPressed: () {
                setState(() {
                  _showConfirmPassword = !_showConfirmPassword;
                });
              },
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please confirm your new password';
              }
              if (value != _newPasswordController.text) {
                return 'Passwords do not match';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  void _updateProfile() async {
    if (!_profileFormKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoadingProfile = true;
    });

    try {
      // Update profile via API
      _currentUser = await _profileService.updateProfile(
        fullName: _fullNameController.text.trim(),
        phone: _phoneController.text.trim(),
      );
      
      _showSuccessSnackBar('Profile updated successfully!');
      
    } catch (e) {
      _showErrorSnackBar('Failed to update profile: $e');
    } finally {
      setState(() {
        _isLoadingProfile = false;
      });
    }
  }

  void _changePassword() async {
    if (!_passwordFormKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoadingPassword = true;
    });

    try {
      // Change password via API
      await _profileService.changePassword(
        currentPassword: _currentPasswordController.text,
        newPassword: _newPasswordController.text,
        confirmPassword: _confirmPasswordController.text,
      );
      
      // Clear password fields after successful change
      _currentPasswordController.clear();
      _newPasswordController.clear();
      _confirmPasswordController.clear();
      
      _showSuccessSnackBar('Password changed successfully!');
      
    } catch (e) {
      _showErrorSnackBar('Failed to change password: $e');
    } finally {
      setState(() {
        _isLoadingPassword = false;
      });
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppThemes.successColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppThemes.errorColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
} 