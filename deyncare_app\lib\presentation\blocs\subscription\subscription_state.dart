import 'package:equatable/equatable.dart';
import 'package:deyncare_app/domain/models/subscription.dart';
import 'package:deyncare_app/domain/models/plan.dart';

abstract class SubscriptionState extends Equatable {
  const SubscriptionState();

  @override
  List<Object?> get props => [];
}

class SubscriptionInitial extends SubscriptionState {}

class SubscriptionLoading extends SubscriptionState {}

class SubscriptionLoaded extends SubscriptionState {
  final Subscription subscription;
  final List<Plan> availablePlans;
  final List<String> paymentMethods;

  const SubscriptionLoaded({
    required this.subscription,
    this.availablePlans = const [],
    this.paymentMethods = const [],
  });

  @override
  List<Object?> get props => [subscription, availablePlans, paymentMethods];

  SubscriptionLoaded copyWith({
    Subscription? subscription,
    List<Plan>? availablePlans,
    List<String>? paymentMethods,
  }) {
    return SubscriptionLoaded(
      subscription: subscription ?? this.subscription,
      availablePlans: availablePlans ?? this.availablePlans,
      paymentMethods: paymentMethods ?? this.paymentMethods,
    );
  }
}

class SubscriptionError extends SubscriptionState {
  final String message;
  final String? code;

  const SubscriptionError({
    required this.message,
    this.code,
  });

  @override
  List<Object?> get props => [message, code];
}

class SubscriptionUpdating extends SubscriptionState {
  final String operation; // 'upgrading', 'canceling', 'renewing', etc.

  const SubscriptionUpdating({required this.operation});

  @override
  List<Object?> get props => [operation];
}

class SubscriptionUpdated extends SubscriptionState {
  final Subscription? subscription; // Nullable for operations that don't return a subscription
  final String operation;
  final String message;

  const SubscriptionUpdated({
    this.subscription, // Made optional for operations like upgrade requests
    required this.operation,
    required this.message,
  });

  @override
  List<Object?> get props => [subscription, operation, message];
}

class PaymentProcessing extends SubscriptionState {
  final String paymentMethod;

  const PaymentProcessing({required this.paymentMethod});

  @override
  List<Object?> get props => [paymentMethod];
}

class PaymentCompleted extends SubscriptionState {
  final Map<String, dynamic> paymentResult;
  final String message;

  const PaymentCompleted({
    required this.paymentResult,
    required this.message,
  });

  @override
  List<Object?> get props => [paymentResult, message];
}

class PaymentFailed extends SubscriptionState {
  final String message;
  final String? code;

  const PaymentFailed({
    required this.message,
    this.code,
  });

  @override
  List<Object?> get props => [message, code];
}

class PlansLoading extends SubscriptionState {}

class PlansLoaded extends SubscriptionState {
  final List<Plan> plans;

  const PlansLoaded({required this.plans});

  @override
  List<Object?> get props => [plans];
}

class PlansError extends SubscriptionState {
  final String message;

  const PlansError({required this.message});

  @override
  List<Object?> get props => [message];
} 