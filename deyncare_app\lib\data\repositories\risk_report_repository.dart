import 'package:deyncare_app/data/network/clients/dio_client.dart';
import 'package:deyncare_app/data/models/risk_report_model.dart';
import 'package:flutter/foundation.dart';

/// Repository for risk report data operations
/// Handles API communication for risk report generation and statistics
class RiskReportRepository {
  final DioClient _dioClient;

  RiskReportRepository(this._dioClient);

  /// Get risk report data from the backend API
  /// Returns formatted risk assessment data suitable for PDF generation
  Future<Map<String, dynamic>> getRiskReportData({
    String? startDate,
    String? endDate,
    String period = 'all',
  }) async {
    try {
      if (kDebugMode) {
        print('📊 [RISK REPO] Fetching risk report data...');
        print('📊 [RISK REPO] Period: $period');
        print('📊 [RISK REPO] Date range: $startDate to $endDate');
      }

      // Build query parameters
      final Map<String, dynamic> queryParams = {
        'period': period,
      };

      if (startDate != null && endDate != null) {
        queryParams['startDate'] = startDate;
        queryParams['endDate'] = endDate;
      }

      if (kDebugMode) {
        print('📊 [RISK REPO] Query params: $queryParams');
      }

      // Make API call
      final response = await _dioClient.get(
        '/reports/risks/data',
        queryParameters: queryParams,
      );

      if (kDebugMode) {
        print('📊 [RISK REPO] API response type: ${response.runtimeType}');
      }

      // DioClient returns processed response data directly (not a Dio Response object)
      final responseData = response as Map<String, dynamic>;

      // Validate response structure
      if (!responseData.containsKey('data')) {
        throw Exception('Invalid API response: missing data field');
      }

      final data = responseData['data'] as Map<String, dynamic>;

      // Validate data structure
      if (!data.containsKey('risks')) {
        throw Exception('Invalid API response: missing risks field');
      }

      if (kDebugMode) {
        print('📊 [RISK REPO] Response data keys: ${data.keys.toList()}');
        print('📊 [RISK REPO] Risks count: ${(data['risks'] as List).length}');
      }

      // Parse risks using the report-specific model
      final List<RiskReportData> risks = (data['risks'] as List).map((json) {
        final riskJson = json as Map<String, dynamic>;
        if (kDebugMode) {
          print('🔍 Raw risk JSON keys: ${riskJson.keys.toList()}');
          print('🔍 Raw risk JSON: $riskJson');
          print('🔍 totalDebt field: ${riskJson['totalDebt']} (${riskJson['totalDebt'].runtimeType})');
          print('🔍 paidAmount field: ${riskJson['paidAmount']} (${riskJson['paidAmount'].runtimeType})');
          print('🔍 riskScore field: ${riskJson['riskScore']} (${riskJson['riskScore'].runtimeType})');
          print('🔍 customerName: ${riskJson['customerName']}');
          print('🔍 customerId: ${riskJson['customerId']}');
        }
        final risk = RiskReportData.fromJson(riskJson);
        if (kDebugMode) {
          print('🔍 Parsed risk data:');
          print('   - Customer: ${risk.customerName}');
          print('   - Risk Level: ${risk.riskLevel}');
          print('   - Risk Score: ${risk.riskScore}');
          print('   - Total Debt: ${risk.totalDebt}');
          print('   - Payment Delay: ${risk.paymentDelay} days');
        }
        return risk;
      }).toList();

      if (kDebugMode) {
        print('✅ Successfully parsed ${risks.length} risk assessments');
        if (risks.isNotEmpty) {
          final firstRisk = risks.first;
          print('🔍 First risk parsed data:');
          print('   - Customer: ${firstRisk.customerName}');
          print('   - Risk Level: ${firstRisk.riskLevel}');
          print('   - Risk Score: ${firstRisk.riskScore}');
          print('   - Total Debt: ${firstRisk.totalDebt}');
          print('   - Payment Delay: ${firstRisk.paymentDelay} days');
        }
      }

      // Extract summary data
      final summary = data['summary'] as Map<String, dynamic>? ?? {};

      if (kDebugMode) {
        print('📊 [RISK REPO] Summary data: $summary');
      }

      return {
        'risks': risks,
        'summary': summary,
        'period': data['period'] ?? period,
        'generatedAt': data['generatedAt'] ?? DateTime.now().toIso8601String(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [RISK REPO] Error fetching risk report data: $e');
      }
      rethrow;
    }
  }

  /// Get risk report statistics
  /// Returns aggregated statistics for risk reporting
  Future<Map<String, dynamic>> getRiskReportStats({
    String? startDate,
    String? endDate,
    String period = 'all',
  }) async {
    try {
      if (kDebugMode) {
        print('📊 [RISK REPO] Fetching risk report statistics...');
      }

      // For now, we'll use the same endpoint and extract summary
      final reportData = await getRiskReportData(
        startDate: startDate,
        endDate: endDate,
        period: period,
      );

      final summary = reportData['summary'] as Map<String, dynamic>;
      final risks = reportData['risks'] as List<RiskReportData>;

      // Calculate additional statistics
      final stats = {
        ...summary,
        'averageRiskScore': risks.isNotEmpty
            ? risks.fold<double>(0.0, (sum, risk) => sum + risk.riskScore) / risks.length
            : 0.0,
        'averagePaymentDelay': risks.isNotEmpty
            ? risks.fold<double>(0.0, (sum, risk) => sum + risk.paymentDelay) / risks.length
            : 0.0,
        'highRiskPercentage': risks.isNotEmpty
            ? (risks.where((risk) => risk.isHighRisk).length / risks.length) * 100
            : 0.0,
        'mediumRiskPercentage': risks.isNotEmpty
            ? (risks.where((risk) => risk.isMediumRisk).length / risks.length) * 100
            : 0.0,
        'lowRiskPercentage': risks.isNotEmpty
            ? (risks.where((risk) => risk.isLowRisk).length / risks.length) * 100
            : 0.0,
        'averagePaymentReliability': risks.isNotEmpty
            ? risks.fold<double>(0.0, (sum, risk) => sum + risk.paymentReliability) / risks.length
            : 100.0,
      };

      if (kDebugMode) {
        print('📊 [RISK REPO] Calculated statistics: $stats');
      }

      return stats;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [RISK REPO] Error fetching risk report statistics: $e');
      }
      rethrow;
    }
  }

  /// Get risk distribution data
  /// Returns risk level distribution for charts and analytics
  Future<Map<String, dynamic>> getRiskDistribution({
    String? startDate,
    String? endDate,
    String period = 'all',
  }) async {
    try {
      if (kDebugMode) {
        print('📊 [RISK REPO] Fetching risk distribution...');
      }

      final reportData = await getRiskReportData(
        startDate: startDate,
        endDate: endDate,
        period: period,
      );

      final risks = reportData['risks'] as List<RiskReportData>;

      // Calculate risk distribution
      final distribution = {
        'highRisk': {
          'count': risks.where((risk) => risk.isHighRisk).length,
          'percentage': risks.isNotEmpty
              ? (risks.where((risk) => risk.isHighRisk).length / risks.length) * 100
              : 0.0,
          'totalDebt': risks.where((risk) => risk.isHighRisk)
              .fold<double>(0.0, (sum, risk) => sum + risk.totalDebt),
        },
        'mediumRisk': {
          'count': risks.where((risk) => risk.isMediumRisk).length,
          'percentage': risks.isNotEmpty
              ? (risks.where((risk) => risk.isMediumRisk).length / risks.length) * 100
              : 0.0,
          'totalDebt': risks.where((risk) => risk.isMediumRisk)
              .fold<double>(0.0, (sum, risk) => sum + risk.totalDebt),
        },
        'lowRisk': {
          'count': risks.where((risk) => risk.isLowRisk).length,
          'percentage': risks.isNotEmpty
              ? (risks.where((risk) => risk.isLowRisk).length / risks.length) * 100
              : 0.0,
          'totalDebt': risks.where((risk) => risk.isLowRisk)
              .fold<double>(0.0, (sum, risk) => sum + risk.totalDebt),
        },
        'total': risks.length,
      };

      if (kDebugMode) {
        print('📊 [RISK REPO] Risk distribution: $distribution');
      }

      return distribution;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [RISK REPO] Error fetching risk distribution: $e');
      }
      rethrow;
    }
  }
}
