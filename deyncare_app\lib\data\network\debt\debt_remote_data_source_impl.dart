import 'package:deyncare_app/data/network/clients/dio_client.dart';
import 'package:deyncare_app/data/network/debt/debt_remote_data_source.dart';
import 'package:deyncare_app/data/models/debt_model.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';

/// Implementation of debt remote data source
/// Uses regular HTTP methods to match Customer CRUD pattern
class DebtRemoteDataSourceImpl implements DebtRemoteDataSource {
  final DioClient _dioClient;

  DebtRemoteDataSourceImpl(this._dioClient);

  @override
  Future<DebtModel> createDebt(Map<String, dynamic> debtData) async {
    try {
      // Use regular method like Customer CRUD
      final response = await _dioClient.post('/debts', data: debtData);
      
      // Response is already processed by DioClient/ResponseHandler
      final responseData = response as Map<String, dynamic>;
      
      // Extract debt and customer data from the enhanced response
      final data = responseData['data'] as Map<String, dynamic>;
      final debtInfo = data['debt'] as Map<String, dynamic>;
      final customerInfo = data['customer'] as Map<String, dynamic>? ?? {};
      final paymentInfo = data['payment'] as Map<String, dynamic>?;
      final notificationInfo = data['notification'] as Map<String, dynamic>?;
      final mlInfo = data['mlInfo'] as Map<String, dynamic>? ?? {};
      
      // Log notification status for debugging
      if (notificationInfo != null) {
        final notificationSent = notificationInfo['sent'] ?? false;
        final notificationError = notificationInfo['error'];
        final notificationTimedOut = notificationInfo['timedOut'] ?? false;
        
        print('📱 Debt creation notification status:');
        print('   Sent: $notificationSent');
        if (notificationError != null) {
          print('   Error: $notificationError');
        }
        if (notificationTimedOut) {
          print('   ⏰ Notification timed out (but debt was created successfully)');
        }
      }
      
      // Map the backend response to DebtModel structure with null safety
      final debtJson = {
        'debtId': debtInfo['debtId']?.toString() ?? 'UNKNOWN',
        'customerId': customerInfo['customerId']?.toString() ?? 'UNKNOWN',
        'shopId': 'SHOP001', // Default shop ID as not provided in response
        'CustomerName': debtInfo['customerName']?.toString() ?? 'Unknown Customer',
        'CustomerType': debtInfo['customerType']?.toString() ?? 'Unknown',
        'DebtAmount': _safeDouble(debtInfo['debtAmount']) ?? 0.0,
        'OutstandingDebt': _safeDouble(debtInfo['outstandingDebt']) ?? 0.0,
        'PaidAmount': paymentInfo != null ? _safeDouble(paymentInfo['amount']) ?? 0.0 : 0.0,
        'DebtPaidRatio': 0.0, // Will be calculated by backend
        'DebtCreationDate': DateTime.now().toIso8601String(), // Use current time
        'DueDate': _safeDateTime(debtInfo['dueDate']) ?? DateTime.now().toIso8601String(),
        'RiskLevel': debtInfo['riskStatus']?.toString() ?? 'Active Debt',
        'PaymentDelay': 0, // No delay for new debt
        'IsOnTime': true, // New debt is on time
        'RepaymentTime': _safeInt(debtInfo['repaymentTime']) ?? 0,
        'PaidDate': paymentInfo != null ? _safeDateTime(paymentInfo['date']) : null,
        'description': debtInfo['description']?.toString(),
        'status': debtInfo['status']?.toString() ?? 'active',
        'isDeleted': false,
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };
      
      return DebtModel.fromJson(debtJson);
    } on ApiException {
      rethrow;
    } catch (e) {
      throw ApiException(
        message: 'Failed to create debt: $e',
        statusCode: null,
      );
    }
  }

  @override
  Future<Map<String, dynamic>> getDebts(Map<String, dynamic> queryParams) async {
    try {
      final response = await _dioClient.get(
        '/debts', 
        queryParameters: queryParams,
      );
      
      // Response is already processed by DioClient/ResponseHandler
      return response as Map<String, dynamic>;
    } on ApiException {
      rethrow;
    } catch (e) {
      throw ApiException(
        message: 'Failed to fetch debts: $e',
        statusCode: null,
      );
    }
  }

  @override
  Future<DebtModel> getDebtById(String debtId) async {
    try {
      final response = await _dioClient.get('/debts/$debtId');
      
      // Response is already processed by DioClient/ResponseHandler
      final responseData = response as Map<String, dynamic>;
      // getDebtById returns complex structure, extract basic debt info for model
      final debtDetails = responseData['data'] as Map<String, dynamic>;
      
      // Safely extract nested data with null safety
      final basic = debtDetails['basic'] as Map<String, dynamic>? ?? {};
      final customer = debtDetails['customer'] as Map<String, dynamic>? ?? {};
      final financial = debtDetails['financial'] as Map<String, dynamic>? ?? {};
      final timeline = debtDetails['timeline'] as Map<String, dynamic>? ?? {};
      final risk = debtDetails['risk'] as Map<String, dynamic>? ?? {};
      
      // Map the complex response to simple debt structure with null safety
      final debtJson = {
        'debtId': basic['debtId']?.toString() ?? debtId,
        'customerId': customer['customerId']?.toString() ?? 'UNKNOWN',
        'shopId': 'SHOP001', // Default shop ID as not provided
        'CustomerName': customer['name']?.toString() ?? 'Unknown Customer',
        'CustomerType': customer['type']?.toString() ?? 'Unknown',
        'DebtAmount': _safeDouble(financial['debtAmount']) ?? 0.0,
        'OutstandingDebt': _safeDouble(financial['outstandingDebt']) ?? 0.0,
        'PaidAmount': _safeDouble(financial['paidAmount']) ?? 0.0,
        'DebtPaidRatio': _safeDouble(financial['debtPaidRatio']) ?? 0.0,
        'DebtCreationDate': _safeDateTime(basic['createdAt']) ?? DateTime.now().toIso8601String(),
        'DueDate': _safeDateTime(timeline['dueDate']) ?? DateTime.now().toIso8601String(),
        'RiskLevel': risk['currentLevel']?.toString() ?? 'Low Risk',
        'PaymentDelay': _safeInt(risk['paymentDelay']) ?? 0,
        'IsOnTime': risk['isOnTime'] == true,
        'RepaymentTime': _safeInt(timeline['repaymentTime']),
        'PaidDate': _safeDateTime(timeline['paidDate']),
        'description': basic['description']?.toString(),
        'status': basic['status']?.toString() ?? 'active',
        'isDeleted': false,
        'createdAt': _safeDateTime(basic['createdAt']) ?? DateTime.now().toIso8601String(),
        'updatedAt': _safeDateTime(basic['updatedAt']) ?? DateTime.now().toIso8601String(),
      };
      
      return DebtModel.fromJson(debtJson);
    } on ApiException {
      rethrow;
    } catch (e) {
      throw ApiException(
        message: 'Failed to fetch debt: $e',
        statusCode: null,
      );
    }
  }

  /// Safely convert dynamic value to double
  double? _safeDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  /// Safely convert dynamic value to int
  int? _safeInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.round();
    if (value is String) return int.tryParse(value);
    return null;
  }

  /// Safely convert dynamic value to DateTime string
  String? _safeDateTime(dynamic value) {
    if (value == null) return null;
    if (value is String) return value;
    if (value is DateTime) return value.toIso8601String();
    return null;
  }

  @override
  Future<DebtModel> updateDebt(String debtId, Map<String, dynamic> updateData) async {
    try {
      final response = await _dioClient.put('/debts/$debtId', data: updateData);
      
      // Response is already processed by DioClient/ResponseHandler
      final responseData = response as Map<String, dynamic>;
      final data = responseData['data'] as Map<String, dynamic>? ?? {};
      
      // Map the backend update response to DebtModel structure with proper null safety
      final debtJson = {
        'debtId': data['debtId']?.toString() ?? debtId,
        'customerId': data['customerId']?.toString() ?? 'UNKNOWN',
        'shopId': 'SHOP001', // Default shop ID if not provided
        'CustomerName': data['customerName']?.toString() ?? 'Unknown Customer',
        'CustomerType': data['customerType']?.toString() ?? 'New',
        'DebtAmount': _safeDouble(data['debtAmount']) ?? 0.0,
        'OutstandingDebt': _safeDouble(data['outstandingDebt']) ?? 0.0,
        'PaidAmount': _safeDouble(data['paidAmount']) ?? 0.0,
        'DebtPaidRatio': _safeDouble(data['debtPaidRatio']) != null 
            ? (_safeDouble(data['debtPaidRatio'])! / 100.0) // Convert percentage back to ratio
            : 0.0,
        'DebtCreationDate': _safeDateTime(data['createdAt']) ?? DateTime.now().toIso8601String(),
        'DueDate': _safeDateTime(data['dueDate']) ?? DateTime.now().add(const Duration(days: 30)).toIso8601String(),
        'RiskLevel': data['riskLevel']?.toString() ?? 'Active Debt',
        'PaymentDelay': _safeInt(data['paymentDelay']) ?? 0,
        'IsOnTime': data['isOnTime'] == true,
        'RepaymentTime': _safeInt(data['repaymentTime']) ?? 0,
        'PaidDate': _safeDateTime(data['paidDate']),
        'description': data['description']?.toString(),
        'status': data['status']?.toString() ?? 'active',
        'isDeleted': false,
        'createdAt': _safeDateTime(data['createdAt']) ?? DateTime.now().toIso8601String(),
        'updatedAt': _safeDateTime(data['updatedAt']) ?? DateTime.now().toIso8601String(),
      };
      
      return DebtModel.fromJson(debtJson);
    } on ApiException {
      rethrow;
    } catch (e) {
      throw ApiException(
        message: 'Failed to update debt: $e',
        statusCode: null,
      );
    }
  }

  @override
  Future<void> deleteDebt(String debtId) async {
    try {
      await _dioClient.delete('/debts/$debtId');
      // Response is already processed by DioClient/ResponseHandler
      // If we get here without exception, the deletion was successful
    } on ApiException {
      rethrow;
    } catch (e) {
      throw ApiException(
        message: 'Failed to delete debt: $e',
        statusCode: null,
      );
    }
  }

  @override
  Future<DebtModel> addPaymentToDebt(String debtId, Map<String, dynamic> paymentData) async {
    try {
      final response = await _dioClient.post('/debts/$debtId/payments', data: paymentData);
      
      // Response is already processed by DioClient/ResponseHandler
      final responseData = response as Map<String, dynamic>;
      
      // Based on backend addPayment.js analysis: data.debt structure
      final data = responseData['data'] as Map<String, dynamic>;
      final debtInfo = data['debt'] as Map<String, dynamic>;
      
      // Map the backend payment response to DebtModel structure
      final debtJson = {
        'debtId': debtInfo['debtId']?.toString() ?? debtId,
        'customerId': 'UNKNOWN', // Not provided in payment response
        'shopId': 'SHOP001', // Default shop ID
        'CustomerName': 'Unknown Customer', // Not provided in payment response
        'CustomerType': 'Unknown', // Not provided in payment response
        'DebtAmount': _safeDouble(debtInfo['originalAmount']) ?? 0.0,
        'OutstandingDebt': _safeDouble(debtInfo['outstandingDebt']) ?? 0.0,
        'PaidAmount': _safeDouble(debtInfo['paidAmount']) ?? 0.0,
        'DebtPaidRatio': _safeDouble(debtInfo['debtPaidRatio']) ?? 0.0,
        'DebtCreationDate': DateTime.now().toIso8601String(),
        'DueDate': DateTime.now().toIso8601String(),
        'RiskLevel': debtInfo['riskLevel']?.toString() ?? 'Active Debt',
        'PaymentDelay': 0,
        'IsOnTime': true,
        'RepaymentTime': 0,
        'PaidDate': DateTime.now().toIso8601String(),
        'description': '',
        'status': debtInfo['status']?.toString() ?? 'active',
        'isDeleted': false,
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };
      
      return DebtModel.fromJson(debtJson);
    } on ApiException {
      rethrow;
    } catch (e) {
      throw ApiException(
        message: 'Failed to add payment: $e',
        statusCode: null,
      );
    }
  }

  @override
  Future<Map<String, dynamic>> getDebtAnalytics(Map<String, dynamic> queryParams) async {
    try {
      final response = await _dioClient.get(
        '/debts/stats', 
        queryParameters: queryParams,
      );
      
      // Response is already processed by DioClient/ResponseHandler
      return response as Map<String, dynamic>;
    } on ApiException {
      rethrow;
    } catch (e) {
      throw ApiException(
        message: 'Failed to fetch debt analytics: $e',
        statusCode: null,
      );
    }
  }

  @override
  Future<List<DebtModel>> getCustomerDebts(String customerId) async {
    try {
      final response = await _dioClient.get('/customers/$customerId/debts');
      
      // Response is already processed by DioClient/ResponseHandler
      final responseData = response as Map<String, dynamic>;
      // Backend returns debt list in 'data' field for consistency
      final List<dynamic> debtsJson = responseData['data'] ?? responseData['debts'] ?? [];
      return debtsJson.map((json) => DebtModel.fromJson(json)).toList();
    } on ApiException {
      rethrow;
    } catch (e) {
      throw ApiException(
        message: 'Failed to fetch customer debts: $e',
        statusCode: null,
      );
    }
  }
} 