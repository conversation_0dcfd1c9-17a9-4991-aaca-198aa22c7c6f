import 'package:flutter/material.dart';
import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/core/utils/permission_utils.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/widgets/permission_denied_dialog.dart';

/// Permission-aware dashboard content that only shows accessible features
class PermissionAwareDashboardContent extends StatelessWidget {
  final User? user;
  final Widget Function()? buildCustomerContent;
  final Widget Function()? buildDebtContent;
  final Widget Function()? buildReportContent;
  final Widget Function()? buildDefaultContent;

  const PermissionAwareDashboardContent({
    super.key,
    required this.user,
    this.buildCustomerContent,
    this.buildDebtContent,
    this.buildReportContent,
    this.buildDefaultContent,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // KPI Cards Section - Permission-aware
        _buildPermissionAwareKPISection(context),

        const SizedBox(height: 24),

        // Quick Actions Section - Permission-aware
        _buildPermissionAwareQuickActions(context),

        const SizedBox(height: 24),

        // Recent Activity Section - Permission-aware
        _buildPermissionAwareActivity(context),
      ],
    );
  }

  Widget _buildPermissionAwareKPISection(BuildContext context) {
    final accessibleCards = <Widget>[];

    // Customer KPI - only if user has customer access
    if (PermissionUtils.canAccessCustomers(user)) {
      accessibleCards.add(_buildKPICard(
        context,
        title: 'Total Customers',
        value: '0', // Will be loaded from API
        icon: Icons.people_outline,
        color: AppThemes.primaryColor,
        onTap: () => _navigateToCustomers(context),
      ));
    } else {
      accessibleCards.add(_buildLockedKPICard(
        context,
        title: 'Total Customers',
        icon: Icons.people_outline,
        onTap: () => _showPermissionDenied(context, 'Customer Management'),
      ));
    }

    // Debt KPI - only if user has debt access
    if (PermissionUtils.canAccessDebts(user)) {
      accessibleCards.add(_buildKPICard(
        context,
        title: 'Outstanding Debts',
        value: '\$0.00', // Will be loaded from API
        icon: Icons.account_balance_wallet,
        color: AppThemes.warningColor,
        onTap: () => _navigateToDebts(context),
      ));
    } else {
      accessibleCards.add(_buildLockedKPICard(
        context,
        title: 'Outstanding Debts',
        icon: Icons.account_balance_wallet,
        onTap: () => _showPermissionDenied(context, 'Debt Management'),
      ));
    }

    // Reports KPI - only if user has report access
    if (PermissionUtils.canAccessReports(user)) {
      accessibleCards.add(_buildKPICard(
        context,
        title: 'Reports Generated',
        value: '0', // Will be loaded from API
        icon: Icons.analytics,
        color: AppThemes.infoColor,
        onTap: () => _navigateToReports(context),
      ));
    } else {
      accessibleCards.add(_buildLockedKPICard(
        context,
        title: 'Reports',
        icon: Icons.analytics,
        onTap: () => _showPermissionDenied(context, 'Report Management'),
      ));
    }

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: accessibleCards,
    );
  }

  Widget _buildKPICard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    VoidCallback? onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(icon, color: color, size: 20),
                  ),
                  const Spacer(),
                  if (onTap != null)
                    Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                value,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLockedKPICard(
    BuildContext context, {
    required String title,
    required IconData icon,
    VoidCallback? onTap,
  }) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.grey.withValues(alpha: 0.1),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(icon, color: Colors.grey, size: 20),
                  ),
                  const Spacer(),
                  Icon(Icons.lock_outline, size: 16, color: Colors.grey),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'Restricted',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey,
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey,
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPermissionAwareQuickActions(BuildContext context) {
    final actions = <Widget>[];

    if (PermissionUtils.canCreateCustomers(user)) {
      actions.add(_buildQuickActionButton(
        context,
        'Add Customer',
        Icons.person_add,
        AppThemes.primaryColor,
        () => _navigateToAddCustomer(context),
      ));
    }

    if (PermissionUtils.canCreateDebts(user)) {
      actions.add(_buildQuickActionButton(
        context,
        'Add Debt',
        Icons.add_card,
        AppThemes.warningColor,
        () => _navigateToAddDebt(context),
      ));
    }

    if (PermissionUtils.canGenerateReports(user)) {
      actions.add(_buildQuickActionButton(
        context,
        'Generate Report',
        Icons.assessment,
        AppThemes.infoColor,
        () => _navigateToGenerateReport(context),
      ));
    }

    if (actions.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(Icons.lock_outline, size: 48, color: Colors.grey),
              const SizedBox(height: 8),
              Text(
                'No Quick Actions Available',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.grey,
                    ),
              ),
              Text(
                'Contact your administrator for access',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey,
                    ),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: actions,
        ),
      ],
    );
  }

  Widget _buildQuickActionButton(
    BuildContext context,
    String label,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return ElevatedButton.icon(
      onPressed: onTap,
      icon: Icon(icon, size: 18),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Widget _buildPermissionAwareActivity(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Activity',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            Text(
              'Activity will be shown based on your permissions',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
          ],
        ),
      ),
    );
  }

  // Navigation methods
  void _navigateToCustomers(BuildContext context) {
    Navigator.pushNamed(context, '/customers');
  }

  void _navigateToDebts(BuildContext context) {
    Navigator.pushNamed(context, '/debts');
  }

  void _navigateToReports(BuildContext context) {
    Navigator.pushNamed(context, '/reports');
  }

  void _navigateToAddCustomer(BuildContext context) {
    Navigator.pushNamed(context, '/customers/add');
  }

  void _navigateToAddDebt(BuildContext context) {
    Navigator.pushNamed(context, '/debts/add');
  }

  void _navigateToGenerateReport(BuildContext context) {
    Navigator.pushNamed(context, '/reports/generate');
  }

  void _showPermissionDenied(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => PermissionDeniedDialog(
        featureName: feature,
        message: 'You need $feature permissions to access this feature.',
      ),
    );
  }
}
