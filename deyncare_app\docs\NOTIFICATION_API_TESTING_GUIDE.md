# 🧪 Notification API Testing Guide

## Quick Test Setup

### 1. Authentication
First, get your SuperAdmin JWT token by logging in:

```bash
POST https://your-backend-domain.com/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your-password"
}
```

**Save the token from response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": { "role": "superAdmin" }
}
```

### 2. Set Base Headers
For all requests, include:
```bash
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json
```

## 🚀 Quick Health Check

### Test Firebase Connection
**GET** `/api/admin/notifications/push/test`

```bash
curl -X GET "https://your-backend-domain.com/api/admin/notifications/push/test" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Firebase connection successful"
}
```

## 📱 Mobile App User Testing

### 1. Register FCM Token (Mobile App)
**POST** `/api/fcm/register`

```bash
curl -X POST "https://your-backend-domain.com/api/fcm/register" \
  -H "Authorization: Bearer ADMIN_USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "fcm-token-from-mobile-app",
    "deviceInfo": {
      "platform": "android",
      "deviceId": "samsung-galaxy-s21",
      "appVersion": "1.0.0",
      "osVersion": "Android 12"
    }
  }'
```

### 2. Send Test Notification to Yourself
**POST** `/api/fcm/test`

```bash
curl -X POST "https://your-backend-domain.com/api/fcm/test" \
  -H "Authorization: Bearer ADMIN_USER_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Test notification sent successfully",
  "data": {
    "totalSent": 1,
    "totalFailed": 0,
    "totalRecipients": 1
  }
}
```

## 🎯 SuperAdmin Custom Notifications

### 1. Send to Specific Shops
**POST** `/api/admin/notifications/push/shops`

```bash
curl -X POST "https://your-backend-domain.com/api/admin/notifications/push/shops" \
  -H "Authorization: Bearer SUPERADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "shopIds": ["SHOP001", "SHOP002"],
    "title": "📋 Weekly Report Available",
    "message": "Your weekly sales report is ready for review. Check it now!",
    "priority": "normal",
    "actionUrl": "/reports/weekly",
    "actionLabel": "View Report"
  }'
```

### 2. Broadcast to All Admins
**POST** `/api/admin/notifications/push/broadcast`

```bash
curl -X POST "https://your-backend-domain.com/api/admin/notifications/push/broadcast" \
  -H "Authorization: Bearer SUPERADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "🚀 New Feature: Mobile Payments",
    "message": "We have launched mobile payment support! Update your app to access new features.",
    "priority": "normal",
    "actionUrl": "/features/mobile-payments",
    "actionLabel": "Learn More"
  }'
```

### 3. Debt Reminder Notifications
**POST** `/api/admin/notifications/push/debt-reminders`

```bash
curl -X POST "https://your-backend-domain.com/api/admin/notifications/push/debt-reminders" \
  -H "Authorization: Bearer SUPERADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "reminderType": "7_days",
    "shopIds": ["SHOP001"]
  }'
```

## 📊 Monitoring & Analytics

### Get Notification Targets
**GET** `/api/admin/notifications/push/targets`

```bash
curl -X GET "https://your-backend-domain.com/api/admin/notifications/push/targets" \
  -H "Authorization: Bearer SUPERADMIN_TOKEN"
```

### Get Notification Statistics
**GET** `/api/admin/notifications/push/stats?days=7&shopId=SHOP001`

```bash
curl -X GET "https://your-backend-domain.com/api/admin/notifications/push/stats?days=7" \
  -H "Authorization: Bearer SUPERADMIN_TOKEN"
```

## 📋 Testing Checklist

### ✅ Basic Functionality
- [ ] Firebase connection test passes
- [ ] FCM token registration works from mobile app
- [ ] Test notification reaches mobile device
- [ ] SuperAdmin can send shop-targeted notifications
- [ ] SuperAdmin can send broadcast notifications

### ✅ Notification Delivery
- [ ] Notifications appear on Android devices
- [ ] Notifications appear on iOS devices
- [ ] Action buttons work correctly
- [ ] Deep links navigate to correct app screens
- [ ] Notification sounds play correctly

### ✅ Error Handling
- [ ] Invalid shop IDs return proper error
- [ ] Missing authentication returns 401
- [ ] Non-SuperAdmin users get 403 error
- [ ] Malformed JSON returns validation errors
- [ ] Firebase service errors are handled gracefully

### ✅ Edge Cases
- [ ] Notifications to shops with no active admins
- [ ] Notifications with very long titles/messages
- [ ] Scheduled notifications work correctly
- [ ] Notifications with special characters
- [ ] Multiple rapid notifications don't cause issues

## 🐛 Troubleshooting

### No Notifications Received
1. Check FCM token registration in mobile app
2. Verify user role (must be admin/employee)
3. Check Firebase connection health
4. Verify shop ID exists and is active
5. Check device notification permissions

### Firebase Connection Issues
1. Verify `google-services.json` is correctly placed
2. Check Firebase project configuration
3. Ensure service account has proper permissions
4. Verify FCM is enabled in Firebase Console

### Authorization Errors
1. Confirm JWT token is valid and not expired
2. Verify user has SuperAdmin role
3. Check if user account is active
4. Ensure proper Authorization header format

## 📱 Mobile App Testing Commands

### Test Different Notification Types
```javascript
// In Flutter app's notification test screen
await NotificationService.testNotification('debt_created');
await NotificationService.testNotification('payment_recorded');
await NotificationService.testNotification('debt_reminder');
await NotificationService.testNotification('admin_message');
```

### Verify Token Registration
```javascript
// Check if FCM token is registered
final tokens = await NotificationService.getStoredTokens();
print('Registered tokens: ${tokens.length}');
```

## 📈 Performance Testing

### Load Testing
```bash
# Send 10 notifications rapidly
for i in {1..10}; do
  curl -X POST "/api/admin/notifications/push/broadcast" \
    -H "Authorization: Bearer TOKEN" \
    -d '{"title":"Load Test '$i'","message":"Testing notification '$i'"}' &
done
wait
```

### Response Time Testing
```bash
# Measure response time
time curl -X POST "/api/admin/notifications/push/shops" \
  -H "Authorization: Bearer TOKEN" \
  -d '{"shopIds":["SHOP001"],"title":"Speed Test","message":"Testing response time"}'
```

This testing guide provides comprehensive coverage for validating that your notification system is working 100% correctly! 🎯 