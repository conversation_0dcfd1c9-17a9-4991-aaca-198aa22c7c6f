import 'package:equatable/equatable.dart';

/// Business validation utility that matches backend validation schemas 100%
/// Ensures consistency between Flutter and Node.js backend
class BusinessValidation {
  
  // ========================================
  // CUSTOMER VALIDATION RULES
  // ========================================
  
  /// Validates customer ID format (matches backend: /^CUST\d{3}$/)
  /// NOTE: Backend has inconsistent validation - schema expects /^CUST-[A-Z0-9]+$/ 
  /// but generator creates CUST001 format. Using actual generator format.
  static ValidationResult validateCustomerId(String customerId) {
    if (customerId.trim().isEmpty) {
      return ValidationResult.error('Customer ID is required');
    }
    
    final customerIdRegex = RegExp(r'^CUST\d{3}$');
    if (!customerIdRegex.hasMatch(customerId)) {
      return ValidationResult.error('Invalid customer ID format. Expected format: CUST001, CUST002, etc.');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates customer name (matches backend: 2-100 characters)
  static ValidationResult validateCustomerName(String name) {
    final trimmedName = name.trim();
    
    if (trimmedName.isEmpty) {
      return ValidationResult.error('Customer name is required');
    }
    
    if (trimmedName.length < 2) {
      return ValidationResult.error('Customer name must be at least 2 characters');
    }
    
    if (trimmedName.length > 100) {
      return ValidationResult.error('Customer name cannot exceed 100 characters');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates customer type (matches backend: 'new' | 'returning')
  static ValidationResult validateCustomerType(String customerType) {
    if (customerType.trim().isEmpty) {
      return ValidationResult.error('Customer type is required');
    }
    
    final validTypes = ['new', 'returning'];
    if (!validTypes.contains(customerType.toLowerCase())) {
      return ValidationResult.error('Customer type must be either "new" or "returning"');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates phone number (matches backend: /^[+]?[0-9\s\-\(\)]{10,15}$/)
  static ValidationResult validatePhone(String phone) {
    if (phone.trim().isEmpty) {
      return ValidationResult.error('Phone number is required');
    }
    
    final phoneRegex = RegExp(r'^[+]?[0-9\s\-\(\)]{10,15}$');
    if (!phoneRegex.hasMatch(phone.trim())) {
      return ValidationResult.error('Please provide a valid phone number (10-15 digits with optional formatting)');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates email format (optional field)
  static ValidationResult validateEmail(String? email) {
    if (email == null || email.trim().isEmpty) {
      return ValidationResult.valid(); // Email is optional
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(email.trim())) {
      return ValidationResult.error('Please provide a valid email address');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates address (matches backend: max 500 characters)
  static ValidationResult validateAddress(String? address) {
    if (address == null || address.trim().isEmpty) {
      return ValidationResult.valid(); // Address is optional
    }
    
    if (address.trim().length > 500) {
      return ValidationResult.error('Address cannot exceed 500 characters');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates credit limit (matches backend: must be non-negative)
  static ValidationResult validateCreditLimit(double? creditLimit) {
    if (creditLimit == null) {
      return ValidationResult.valid(); // Optional field
    }
    
    if (creditLimit < 0) {
      return ValidationResult.error('Credit limit cannot be negative');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates category (matches backend: 2-50 chars, alphanumeric + spaces, underscores, hyphens)
  static ValidationResult validateCategory(String? category) {
    if (category == null || category.trim().isEmpty) {
      return ValidationResult.valid(); // Optional field, defaults to 'regular'
    }
    
    final trimmedCategory = category.trim();
    
    if (trimmedCategory.length < 2) {
      return ValidationResult.error('Category must be at least 2 characters');
    }
    
    if (trimmedCategory.length > 50) {
      return ValidationResult.error('Category cannot exceed 50 characters');
    }
    
    final categoryRegex = RegExp(r'^[a-zA-Z0-9\s_-]+$');
    if (!categoryRegex.hasMatch(trimmedCategory)) {
      return ValidationResult.error('Category can only contain letters, numbers, spaces, underscores, and hyphens');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates notes (matches backend: max 1000 characters)
  static ValidationResult validateNotes(String? notes) {
    if (notes == null || notes.trim().isEmpty) {
      return ValidationResult.valid(); // Notes are optional
    }
    
    if (notes.trim().length > 1000) {
      return ValidationResult.error('Notes cannot exceed 1000 characters');
    }
    
    return ValidationResult.valid();
  }
  
  // ========================================
  // DEBT VALIDATION RULES
  // ========================================
  
  /// Validates debt ID format (matches backend: /^DEBT\d{3}$/)
  static ValidationResult validateDebtId(String debtId) {
    if (debtId.trim().isEmpty) {
      return ValidationResult.error('Debt ID is required');
    }
    
    final debtIdRegex = RegExp(r'^DEBT\d{3}$');
    if (!debtIdRegex.hasMatch(debtId)) {
      return ValidationResult.error('Invalid debt ID format. Expected format: DEBT001, DEBT002, etc.');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates debt amount (matches backend: must be positive)
  static ValidationResult validateDebtAmount(double? debtAmount) {
    if (debtAmount == null) {
      return ValidationResult.error('Debt amount is required');
    }
    
    if (debtAmount <= 0) {
      return ValidationResult.error('Debt amount must be a positive number');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates due date (matches backend: must be in future)
  static ValidationResult validateDueDate(DateTime? dueDate) {
    if (dueDate == null) {
      return ValidationResult.error('Due date is required');
    }
    
    final now = DateTime.now();
    final dueDateOnly = DateTime(dueDate.year, dueDate.month, dueDate.day);
    final nowOnly = DateTime(now.year, now.month, now.day);
    
    if (dueDateOnly.isBefore(nowOnly) || dueDateOnly.isAtSameMomentAs(nowOnly)) {
      return ValidationResult.error('Due date must be in the future');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates debt description (matches backend: max 1000 characters)
  static ValidationResult validateDebtDescription(String? description) {
    if (description == null || description.trim().isEmpty) {
      return ValidationResult.valid(); // Description is optional
    }
    
    if (description.trim().length > 1000) {
      return ValidationResult.error('Description cannot exceed 1000 characters');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates paid amount (matches backend: cannot exceed debt amount, cannot be negative)
  static ValidationResult validatePaidAmount(double? paidAmount, double debtAmount) {
    if (paidAmount == null) {
      return ValidationResult.valid(); // Paid amount is optional for initial debt creation
    }
    
    if (paidAmount < 0) {
      return ValidationResult.error('Paid amount cannot be negative');
    }
    
    if (paidAmount > debtAmount) {
      return ValidationResult.error('Paid amount cannot exceed debt amount');
    }
    
    return ValidationResult.valid();
  }
  
  // ========================================
  // PAYMENT VALIDATION RULES  
  // ========================================
  
  /// Validates payment amount (matches backend: must be positive)
  static ValidationResult validatePaymentAmount(double? paymentAmount) {
    if (paymentAmount == null) {
      return ValidationResult.error('Payment amount is required');
    }
    
    if (paymentAmount <= 0) {
      return ValidationResult.error('Payment amount must be positive');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates payment amount against outstanding debt
  static ValidationResult validatePaymentAgainstOutstanding(double paymentAmount, double outstandingAmount) {
    if (paymentAmount > outstandingAmount) {
      return ValidationResult.error('Payment amount (\$${paymentAmount.toStringAsFixed(2)}) cannot exceed outstanding debt (\$${outstandingAmount.toStringAsFixed(2)})');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates payment date (matches backend: cannot be in future)
  static ValidationResult validatePaymentDate(DateTime? paymentDate) {
    if (paymentDate == null) {
      return ValidationResult.valid(); // Will default to current date
    }
    
    final now = DateTime.now();
    if (paymentDate.isAfter(now)) {
      return ValidationResult.error('Payment date cannot be in the future');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates payment method (matches backend enum)
  static ValidationResult validatePaymentMethod(String? paymentMethod) {
    if (paymentMethod == null || paymentMethod.trim().isEmpty) {
      return ValidationResult.valid(); // Will default to 'cash'
    }
    
    final validMethods = ['cash', 'bank_transfer', 'mobile_money', 'card', 'other'];
    if (!validMethods.contains(paymentMethod.toLowerCase())) {
      return ValidationResult.error('Payment method must be one of: cash, bank_transfer, mobile_money, card, other');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates payment notes (matches backend: max 500 characters)
  static ValidationResult validatePaymentNotes(String? notes) {
    if (notes == null || notes.trim().isEmpty) {
      return ValidationResult.valid(); // Notes are optional
    }
    
    if (notes.trim().length > 500) {
      return ValidationResult.error('Payment notes cannot exceed 500 characters');
    }
    
    return ValidationResult.valid();
  }
  
  // ========================================
  // RISK ASSESSMENT VALIDATION
  // ========================================
  
  /// Validates risk level (matches backend enum)
  static ValidationResult validateRiskLevel(String? riskLevel) {
    if (riskLevel == null || riskLevel.trim().isEmpty) {
      return ValidationResult.valid(); // Will use default
    }
    
    final validLevels = ['Low Risk', 'Medium Risk', 'High Risk', 'Critical Risk', 'Active Debt'];
    if (!validLevels.contains(riskLevel)) {
      return ValidationResult.error('Risk level must be one of: Low Risk, Medium Risk, High Risk, Critical Risk, Active Debt');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates ML source (matches backend enum)
  static ValidationResult validateMlSource(String? mlSource) {
    if (mlSource == null || mlSource.trim().isEmpty) {
      return ValidationResult.valid(); // Will use default 'system'
    }
    
    final validSources = ['manual', 'ml_api', 'system'];
    if (!validSources.contains(mlSource.toLowerCase())) {
      return ValidationResult.error('ML source must be one of: manual, ml_api, system');
    }
    
    return ValidationResult.valid();
  }
  
  // ========================================
  // BUSINESS LOGIC RULES
  // ========================================
  
  /// Checks if customer can take new debt (business rule)
  static ValidationResult canCustomerTakeDebt(String customerId, double creditLimit, double outstandingBalance) {
    // Check if customer has exceeded credit limit
    if (creditLimit > 0 && outstandingBalance >= creditLimit) {
      return ValidationResult.error('Customer has exceeded credit limit (\$${creditLimit.toStringAsFixed(2)}). Outstanding balance: \$${outstandingBalance.toStringAsFixed(2)}');
    }
    
    return ValidationResult.valid();
  }
  
  /// Validates debt status progression (business rule)
  static ValidationResult validateDebtStatusTransition(String currentStatus, String newStatus) {
    final validTransitions = {
      'active': ['paid', 'overdue', 'partially_paid'],
      'partially_paid': ['paid', 'overdue'],
      'overdue': ['paid', 'partially_paid'],
      'paid': [], // No transitions from paid status
    };
    
    if (!validTransitions.containsKey(currentStatus)) {
      return ValidationResult.error('Invalid current debt status: $currentStatus');
    }
    
    if (!validTransitions[currentStatus]!.contains(newStatus)) {
      return ValidationResult.error('Invalid status transition from $currentStatus to $newStatus');
    }
    
    return ValidationResult.valid();
  }
}

/// Validation result class
class ValidationResult extends Equatable {
  final bool isValid;
  final String? errorMessage;
  
  const ValidationResult._(this.isValid, this.errorMessage);
  
  /// Creates a valid result
  factory ValidationResult.valid() => const ValidationResult._(true, null);
  
  /// Creates an error result
  factory ValidationResult.error(String message) => ValidationResult._(false, message);
  
  @override
  List<Object?> get props => [isValid, errorMessage];
}

/// Constants for business validation (matches backend exactly)
class BusinessConstants {
  // Customer validation constants
  static const int customerNameMinLength = 2;
  static const int customerNameMaxLength = 100;
  static const int addressMaxLength = 500;
  static const int categoryMinLength = 2;
  static const int categoryMaxLength = 50;
  static const int notesMaxLength = 1000;
  
  // Debt validation constants  
  static const int debtDescriptionMaxLength = 1000;
  static const int paymentNotesMaxLength = 500;
  
  // Regular expressions (matches backend)
  static const String customerIdPattern = r'^CUST\d{3}$';
  static const String debtIdPattern = r'^DEBT\d{3}$';
  static const String phonePattern = r'^[+]?[0-9\s\-\(\)]{10,15}$';
  static const String emailPattern = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
  static const String categoryPattern = r'^[a-zA-Z0-9\s_-]+$';
  
  // Valid enum values (matches backend)
  static const List<String> customerTypes = ['new', 'returning'];
  static const List<String> paymentMethods = ['cash', 'bank_transfer', 'mobile_money', 'card', 'other'];
  static const List<String> riskLevels = ['Low Risk', 'Medium Risk', 'High Risk', 'Critical Risk', 'Active Debt'];
  static const List<String> mlSources = ['manual', 'ml_api', 'system'];
  static const List<String> debtStatuses = ['active', 'paid', 'overdue', 'partially_paid'];
} 