import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/data/models/customer_model.dart' as data_models;
import 'package:deyncare_app/domain/models/customer.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_bloc.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_event.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_state.dart';
import 'package:deyncare_app/presentation/widgets/modals/customer/forms/edit_customer_form.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_constants.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_builders.dart';

/// Edit Customer By ID Form - Loads customer data first, then shows edit form
class EditCustomerByIdForm extends StatefulWidget {
  final String customerId;

  const EditCustomerByIdForm({
    super.key,
    required this.customerId,
  });

  @override
  State<EditCustomerByIdForm> createState() => _EditCustomerByIdFormState();
}

class _EditCustomerByIdFormState extends State<EditCustomerByIdForm> {
  @override
  void initState() {
    super.initState();
    // Load customer data when widget initializes
    context.read<CustomerBloc>().add(LoadCustomerDetails(widget.customerId));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CustomerBloc, CustomerState>(
      builder: (context, state) {
        if (state is CustomerDetailsLoading || state is CustomerLoading) {
          return ModalBuilders.buildModernLoadingState(
            context: context,
            message: 'Loading customer details...',
          );
        } else if (state is CustomerDetailsLoaded) {
          // Extract customer from the response
          final customerData = state.response.data?.customer;
          if (customerData != null) {
            // Convert CustomerBasicInfo to Customer domain model
            final customer = _convertToCustomer(customerData);
            return EditCustomerForm(
              customer: customer,
              parentContext: context,
            );
          } else {
            return ModalBuilders.buildModernEmptyState(
              context: context,
              title: 'Customer Data Missing',
              subtitle: 'Customer data is not available in the response.',
              icon: Icons.error_outline_rounded,
              action: ModalConstants.secondaryActionButton(
                text: 'Close',
                onPressed: () => Navigator.of(context).pop(),
                icon: Icons.close_rounded,
                context: context,
              ),
            );
          }
        } else if (state is CustomerDetailsError || state is CustomerError) {
          final errorMessage = state is CustomerDetailsError 
            ? state.message 
            : (state as CustomerError).message;
          
          return ModalBuilders.buildModernEmptyState(
            context: context,
            title: 'Error Loading Customer',
            subtitle: errorMessage,
            icon: Icons.error_outline_rounded,
            action: ModalConstants.primaryActionButton(
              text: 'Try Again',
              onPressed: () {
                context.read<CustomerBloc>().add(LoadCustomerDetails(widget.customerId));
              },
              icon: Icons.refresh_rounded,
              context: context,
            ),
          );
        } else {
          return ModalBuilders.buildModernEmptyState(
            context: context,
            title: 'Customer Not Found',
            subtitle: 'The requested customer could not be found.',
            icon: Icons.person_off_rounded,
            action: ModalConstants.secondaryActionButton(
              text: 'Close',
              onPressed: () => Navigator.of(context).pop(),
              icon: Icons.close_rounded,
              context: context,
            ),
          );
        }
      },
    );
  }

  /// Convert CustomerBasicInfo from API response to Customer domain model
  Customer _convertToCustomer(data_models.CustomerBasicInfo customerData) {
    final now = DateTime.now();
    return Customer(
      customerId: customerData.customerId ?? '',
      fullName: customerData.customerName ?? '',
      email: '', // Not available in basic info
      phone: customerData.phone ?? '',
      shopId: '', // Not available in basic info
      status: _mapStringToStatus(customerData.customerType),
      createdAt: customerData.createdAt ?? now,
      updatedAt: customerData.updatedAt ?? now,
      riskProfile: RiskProfile(
        currentRiskLevel: RiskLevel.low,
        riskScore: 0.0,
        riskSource: 'default',
        lastAssessment: now,
      ),
      stats: const CustomerStats(
        totalDebts: 0,
        totalBorrowed: 0.0,
        totalPaid: 0.0,
        currentOutstanding: 0.0,
        completedDebts: 0,
        activeDebts: 0,
        overdueDebts: 0,
        averagePaymentDays: 0.0,
        paymentReliability: PaymentReliability.excellent,
      ),
    );
  }

  /// Map string status to CustomerStatus enum
  CustomerStatus _mapStringToStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return CustomerStatus.active;
      case 'inactive':
        return CustomerStatus.inactive;
      case 'suspended':
        return CustomerStatus.suspended;
      default:
        return CustomerStatus.active;
    }
  }
} 