import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/domain/models/customer.dart';
import 'package:deyncare_app/domain/models/debt.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_bloc.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_builders.dart';
import 'package:deyncare_app/presentation/widgets/modals/debt/forms/add_debt_form.dart';
import 'package:deyncare_app/presentation/widgets/modals/debt/forms/edit_debt_form.dart';
import 'package:deyncare_app/presentation/widgets/modals/debt/forms/edit_debt_by_id_form.dart';
import 'package:deyncare_app/presentation/widgets/modals/debt/forms/add_payment_form.dart';
import 'package:deyncare_app/presentation/widgets/modals/debt/forms/delete_debt_form.dart';
import 'package:deyncare_app/presentation/widgets/modals/debt/views/debt_details_view.dart';
import 'package:deyncare_app/presentation/widgets/modals/debt/views/debt_details_by_id_view.dart';

/// Enhanced Debt CRUD Modal Handlers using Modern Wolt Modal Sheet
/// Provides Add, Edit, View, Delete, and Payment functionality with modern UI/UX
class DebtModalHandlers {
  
  /// Show Add Debt Modal with Modern Design
  static void showAddDebtModal(BuildContext context, {Customer? preselectedCustomer}) {
    final debtBloc = context.read<DebtBloc>();
    final page = ModalBuilders.buildModernPage(
      context: context,
      title: 'Add New Debt',
      subtitle: 'Create a new debt record for customer',
      child: BlocProvider.value(
        value: debtBloc,
        child: AddDebtForm(preselectedCustomer: preselectedCustomer),
      ),
    );
    
    ModalBuilders.showModernModal(context: context, page: page);
  }

  /// Show Edit Debt Modal with Modern Design
  static void showEditDebtModal(BuildContext context, Debt debt) {
    final debtBloc = context.read<DebtBloc>();
    final page = ModalBuilders.buildModernPage(
      context: context,
      title: 'Edit Debt',
      subtitle: 'Modify debt details and information',
      child: BlocProvider.value(
        value: debtBloc,
        child: EditDebtForm(debt: debt),
      ),
    );
    
    ModalBuilders.showModernModal(context: context, page: page);
  }

  /// Show Edit Debt Modal By ID with Modern Design
  static void showEditDebtById(BuildContext context, String debtId) {
    final page = ModalBuilders.buildModernPage(
      context: context,
      title: 'Edit Debt',
      subtitle: 'Loading debt details...',
      child: BlocProvider.value(
        value: context.read<DebtBloc>(),
        child: EditDebtByIdForm(debtId: debtId),
      ),
    );
    
    ModalBuilders.showModernModal(context: context, page: page);
  }

  /// Show View Debt Details Modal with Modern Design
  static void showViewDebtModal(BuildContext context, Debt debt) {
    final actions = [
      if (debt.remainingAmount > 0) ...[
        Container(
          decoration: BoxDecoration(
            color: AppThemes.successColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: IconButton(
            icon: Icon(Icons.payment_rounded, color: AppThemes.successColor),
            onPressed: () {
              Navigator.of(context).pop();
              showAddPaymentModal(context, debt);
            },
            tooltip: 'Add Payment',
          ),
        ),
      ],
      Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: IconButton(
          icon: Icon(Icons.edit_rounded, color: Theme.of(context).colorScheme.primary),
          onPressed: () {
            Navigator.of(context).pop();
            showEditDebtModal(context, debt);
          },
          tooltip: 'Edit Debt',
        ),
      ),
    ];

    final page = ModalBuilders.buildModernPageWithActions(
      context: context,
      title: 'Debt Details',
      subtitle: 'View complete debt information',
      actions: actions,
      child: DebtDetailsView(debt: debt),
    );
    
    ModalBuilders.showModernModal(context: context, page: page);
  }

  /// Show View Debt Details By ID Modal with Modern Design
  static void showViewDebtByIdModal(BuildContext context, String debtId) {
    final debtBloc = context.read<DebtBloc>();
    final page = ModalBuilders.buildModernPage(
      context: context,
      title: 'Debt Details',
      subtitle: 'Loading debt information...',
      child: BlocProvider.value(
        value: debtBloc,
        child: DebtDetailsByIdView(debtId: debtId),
      ),
    );
    
    ModalBuilders.showModernModal(context: context, page: page);
  }

  /// Show Add Payment Modal with Modern Design
  static void showAddPaymentModal(BuildContext context, Debt debt) {
    final debtBloc = context.read<DebtBloc>();
    final page = ModalBuilders.buildModernPage(
      context: context,
      title: 'Add Payment',
      subtitle: 'Record a new payment for this debt',
      titleColor: AppThemes.successColor,
      child: BlocProvider.value(
        value: debtBloc,
        child: AddPaymentForm(debt: debt),
      ),
    );
    
    ModalBuilders.showModernModal(context: context, page: page);
  }

  /// Show Delete Debt Confirmation Modal with Modern Design
  static void showDeleteDebtModal(BuildContext context, Debt debt) {
    final debtBloc = context.read<DebtBloc>();
    final page = ModalBuilders.buildModernPage(
      context: context,
      title: 'Delete Debt',
      subtitle: 'This action cannot be undone',
      titleColor: AppThemes.errorColor,
      child: BlocProvider.value(
        value: debtBloc,
        child: DeleteDebtForm(debt: debt),
      ),
    );
    
    ModalBuilders.showModernModal(context: context, page: page);
  }

  /// Show Modern Bottom Sheet for Quick Actions
  static void showDebtQuickActionsBottomSheet(BuildContext context, Debt debt) {
    final page = ModalBuilders.buildModernBottomSheet(
      context: context,
      title: 'Debt Actions',
      subtitle: 'Choose an action for this debt',
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (debt.remainingAmount > 0) ...[
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppThemes.successColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.payment_rounded,
                  color: AppThemes.successColor,
                ),
              ),
              title: const Text('Add Payment'),
              subtitle: const Text('Record a new payment'),
              onTap: () {
                Navigator.of(context).pop();
                showAddPaymentModal(context, debt);
              },
            ),
            const Divider(),
          ],
          ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppThemes.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.visibility_rounded,
                color: AppThemes.primaryColor,
              ),
            ),
            title: const Text('View Details'),
            subtitle: const Text('See complete debt information'),
            onTap: () {
              Navigator.of(context).pop();
              showViewDebtModal(context, debt);
            },
          ),
          const Divider(),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppThemes.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.edit_rounded,
                color: AppThemes.primaryColor,
              ),
            ),
            title: const Text('Edit Debt'),
            subtitle: const Text('Modify debt details'),
            onTap: () {
              Navigator.of(context).pop();
              showEditDebtModal(context, debt);
            },
          ),
          const Divider(),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppThemes.errorColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.delete_rounded,
                color: AppThemes.errorColor,
              ),
            ),
            title: const Text('Delete Debt'),
            subtitle: const Text('Remove this debt record'),
            onTap: () {
              Navigator.of(context).pop();
              showDeleteDebtModal(context, debt);
            },
          ),
        ],
      ),
    );
    
    ModalBuilders.showModernBottomSheet(context: context, page: page);
  }
} 