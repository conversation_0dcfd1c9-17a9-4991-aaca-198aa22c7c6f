import 'dart:io';
import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/blocs/plan/plan_bloc.dart';
import 'package:deyncare_app/data/services/plan/plan_remote_source.dart';
import 'package:deyncare_app/data/services/plan/plan_repository_impl.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/presentation/screens/auth/register/widgets/registration_stepper.dart';
import 'package:deyncare_app/core/utils/toast_util.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_event.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_state.dart';
import 'package:deyncare_app/presentation/screens/auth/register/widgets/registration_step_1.dart';
import 'package:deyncare_app/presentation/screens/auth/register/widgets/registration_step_2.dart';
import 'package:deyncare_app/presentation/screens/auth/register/widgets/registration_step_3.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/error_retry_widget.dart';

class RegistrationForm extends StatefulWidget {
  const RegistrationForm({super.key});

  @override
  State<RegistrationForm> createState() => _RegistrationFormState();
}

class _RegistrationFormState extends State<RegistrationForm> {
  int _currentStep = 0;

  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _shopNameController = TextEditingController();
  final _shopAddressController = TextEditingController();

  // GlobalKey to access RegistrationStep1 state
  final GlobalKey<RegistrationStep1State> _step1Key = GlobalKey<RegistrationStep1State>();

  String _selectedPlan = 'trial';
  String _selectedPaymentMethod = 'offline';
  bool _termsAccepted = false;
  bool _initialPaymentRequired = false;
  File? _selectedLogo;

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _shopNameController.dispose();
    _shopAddressController.dispose();
    super.dispose();
  }

  void _nextStep() {
    // Validate current step before advancing
    if (_currentStep == 0) {
      // Validate step 1 form
      if (_step1Key.currentState?.validate() != true) {
        return;
      }
    }
    
    if (_currentStep < 2) {
      setState(() {
        _currentStep += 1;
      });
    }
  }

  void _prevStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep -= 1;
      });
    }
  }

  void _handleLogoSelected(File? logo) {
    setState(() {
      _selectedLogo = logo;
    });
  }

  void _handlePlanSelected(String plan) {
    setState(() {
      _selectedPlan = plan;
      // Recalculate payment requirement when plan changes
      _initialPaymentRequired = (_selectedPaymentMethod == 'EVC Plus' && plan != 'trial');
    });
    
    // Provide immediate feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${plan.toUpperCase()} plan selected'),
        duration: const Duration(milliseconds: 800),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _handlePaymentMethodSelected(String method) {
    setState(() {
      _selectedPaymentMethod = method;
      // Only require initial payment for EVC Plus AND non-trial plans
      _initialPaymentRequired = (method == 'EVC Plus' && _selectedPlan != 'trial');
    });
  }

  void _handleTermsAccepted(bool accepted) {
    setState(() {
      _termsAccepted = accepted;
    });
  }

  void _handleRegister(BuildContext context) {
    // Basic validation before dispatching event
    if (_currentStep == 0) {
      if (_fullNameController.text.isEmpty ||
          _emailController.text.isEmpty ||
          _phoneController.text.isEmpty ||
          _passwordController.text.isEmpty ||
          _confirmPasswordController.text.isEmpty) {
        ToastUtil.showError('Please fill all fields in Personal Info');
        return;
      }
      // Add password confirmation validation here if needed
    } else if (_currentStep == 1) {
      if (_shopNameController.text.isEmpty ||
          _shopAddressController.text.isEmpty) {
        ToastUtil.showError('Please fill all fields in Shop Details');
        return;
      }
    } else if (_currentStep == 2) {
      if (!_termsAccepted) {
        ToastUtil.showWarning('Please accept the terms and conditions');
        return;
      }
    }

    // Dispatch InitRegistrationRequested event if on the last step (Step 3)
    if (_currentStep == 2) {
      // Get the complete phone number from step 1
      final completePhoneNumber = _step1Key.currentState?.completePhoneNumber ?? _phoneController.text;
      
      context.read<AuthBloc>().add(
        InitRegistrationRequested(
          fullName: _fullNameController.text.trim(),
          email: _emailController.text.replaceAll(' ', '').toLowerCase().trim(),
          phone: completePhoneNumber,
          password: _passwordController.text,
          shopName: _shopNameController.text.trim(),
          shopAddress: _shopAddressController.text.trim(),
          planType: _selectedPlan,
          paymentMethod: _selectedPaymentMethod,
          initialPaid: _initialPaymentRequired,
        ),
      );
    } else {
      _nextStep();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        // Handle state changes and show appropriate messages
        if (state is AuthFailure) {
          // CRITICAL: Show clear error message and allow retry
          ToastUtil.showError(state.message.isEmpty ? 'Registration failed. Please try again.' : state.message);
        } else if (state is AuthEmailVerificationPending) {
          // Registration successful, navigate to verification
          ToastUtil.showSuccess('Registration successful! Please check your email for verification code.');
        }
      },
      builder: (context, state) {
        // Determine if we're in a loading state
        final isLoading = state is AuthLoading;
        
        // Determine if we can interact with the form
        final canInteract = !isLoading;
        
        const steps = ['Personal Info', 'Shop Details', 'Subscription'];

        return Column(
          children: <Widget>[
            // Error Banner (if in failure state)
            if (state is AuthFailure)
              ErrorRetryBanner(
                message: state.message,
                onRetry: () {
                  // Clear error state by triggering a retry
                  setState(() {
                    // This will rebuild and remove the error banner
                  });
                },
                onDismiss: () {
                  // Clear error state by triggering a retry
                  setState(() {
                    // This will rebuild and remove the error banner
                  });
                },
              ),

            // Stepper
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: RegistrationStepper(currentStep: _currentStep, steps: steps),
            ),

            // Form content
            Expanded(
              child: IndexedStack(
                index: _currentStep,
                children: <Widget>[
                  RegistrationStep1(
                    fullNameController: _fullNameController,
                    emailController: _emailController,
                    phoneController: _phoneController,
                    passwordController: _passwordController,
                    confirmPasswordController: _confirmPasswordController,
                    key: _step1Key,
                  ),
                  RegistrationStep2(
                    shopNameController: _shopNameController,
                    shopAddressController: _shopAddressController,
                    onLogoSelected: _handleLogoSelected,
                    selectedLogo: _selectedLogo,
                  ),
                  BlocProvider(
                    create: (context) => PlanBloc(
                      planRepository: PlanRepositoryImpl(
                        remoteSource: PlanRemoteSource(),
                      ),
                    )..add(FetchPlans()),
                    child: RegistrationStep3(
                      onPlanSelected: _handlePlanSelected,
                      onPaymentMethodSelected: _handlePaymentMethodSelected,
                      onTermsAccepted: _handleTermsAccepted,
                      selectedPlan: _selectedPlan,
                      selectedPaymentMethod: _selectedPaymentMethod,
                      termsAccepted: _termsAccepted,
                    ),
                  ),
                ],
              ),
            ),

            // Navigation buttons
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 12, 24, 12),
              child: Row(
                children: <Widget>[
                  if (_currentStep > 0)
                    Expanded(
                      child: OutlinedButton(
                        onPressed: canInteract ? _prevStep : null,
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          side: BorderSide(
                            color: canInteract 
                                ? AppThemes.primaryColor 
                                : AppThemes.primaryColor.withOpacity(0.5),
                          ),
                        ),
                        child: const Text('BACK'),
                      ),
                    ),
                  if (_currentStep > 0) const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: canInteract ? () {
                        if (_currentStep < 2) {
                          _nextStep();
                        } else {
                          _handleRegister(context);
                        }
                      } : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: canInteract 
                            ? AppThemes.primaryColor 
                            : AppThemes.primaryColor.withOpacity(0.7),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: isLoading && _currentStep == 2
                          ? Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white, 
                                    strokeWidth: 2,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                const Text('Registering...'),
                              ],
                            )
                          : Text(_currentStep == 2 ? 'REGISTER' : 'NEXT'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
} 