import 'package:deyncare_app/domain/models/auth_token.dart';
import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/domain/repositories/auth_repository.dart';

/// Use case for user login authentication
///
/// This class is responsible for authenticating users with their
/// email and password, returning user data and authentication tokens.
class LoginUseCase {
  final AuthRepository _repository;

  /// Creates a new instance with the required repository
  LoginUseCase(this._repository);

  /// Executes the login use case with email and password
  ///
  /// Returns a tuple containing User information and AuthToken
  /// Throws exceptions for authentication failures
  Future<(User, AuthToken)> execute(String email, String password) async {
    return await _repository.login(email, password);
  }
}
