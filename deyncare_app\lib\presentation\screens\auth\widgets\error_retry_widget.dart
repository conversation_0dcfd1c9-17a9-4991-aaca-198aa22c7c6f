import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';

/// A reusable widget for displaying errors with retry functionality
class ErrorRetryWidget extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final bool showRetryButton;
  final bool showDismissButton;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? borderColor;
  final Color? textColor;

  const ErrorRetryWidget({
    super.key,
    required this.title,
    required this.message,
    this.onRetry,
    this.onDismiss,
    this.showRetryButton = true,
    this.showDismissButton = true,
    this.icon,
    this.backgroundColor,
    this.borderColor,
    this.textColor,
  });

  /// Factory constructor for network errors
  factory ErrorRetryWidget.network({
    required VoidCallback onRetry,
    VoidCallback? onDismiss,
    String? customMessage,
  }) {
    return ErrorRetryWidget(
      title: 'Connection Error',
      message: customMessage ?? 'Unable to connect to server. Please check your internet connection and try again.',
      onRetry: onRetry,
      onDismiss: onDismiss,
      icon: Icons.wifi_off,
      backgroundColor: Colors.orange.withOpacity(0.1),
      borderColor: Colors.orange.withOpacity(0.3),
      textColor: Colors.orange.shade700,
    );
  }

  /// Factory constructor for server errors
  factory ErrorRetryWidget.server({
    required VoidCallback onRetry,
    VoidCallback? onDismiss,
    String? customMessage,
  }) {
    return ErrorRetryWidget(
      title: 'Server Error',
      message: customMessage ?? 'The server is currently unavailable. Please try again in a moment.',
      onRetry: onRetry,
      onDismiss: onDismiss,
      icon: Icons.error_outline,
      backgroundColor: Colors.red.withOpacity(0.1),
      borderColor: Colors.red.withOpacity(0.3),
      textColor: Colors.red.shade700,
    );
  }

  /// Factory constructor for validation errors
  factory ErrorRetryWidget.validation({
    required String message,
    VoidCallback? onRetry,
    VoidCallback? onDismiss,
  }) {
    return ErrorRetryWidget(
      title: 'Validation Error',
      message: message,
      onRetry: onRetry,
      onDismiss: onDismiss,
      showRetryButton: onRetry != null,
      icon: Icons.warning_outlined,
      backgroundColor: Colors.amber.withOpacity(0.1),
      borderColor: Colors.amber.withOpacity(0.3),
      textColor: Colors.amber.shade700,
    );
  }

  /// Factory constructor for general errors
  factory ErrorRetryWidget.general({
    required String message,
    required VoidCallback onRetry,
    VoidCallback? onDismiss,
    String? title,
  }) {
    return ErrorRetryWidget(
      title: title ?? 'Error',
      message: message,
      onRetry: onRetry,
      onDismiss: onDismiss,
      icon: Icons.error_outline,
      backgroundColor: Colors.red.withOpacity(0.1),
      borderColor: Colors.red.withOpacity(0.3),
      textColor: Colors.red.shade700,
    );
  }

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor = backgroundColor ?? Colors.red.withOpacity(0.1);
    final effectiveBorderColor = borderColor ?? Colors.red.withOpacity(0.3);
    final effectiveTextColor = textColor ?? Colors.red.shade700;
    final effectiveIcon = icon ?? Icons.error_outline;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: effectiveBorderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon and title
          Row(
            children: [
              Icon(
                effectiveIcon,
                color: effectiveTextColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
              if (showDismissButton && onDismiss != null)
                IconButton(
                  onPressed: onDismiss,
                  icon: Icon(
                    Icons.close,
                    color: effectiveTextColor,
                    size: 20,
                  ),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Error message
          Text(
            message,
            style: TextStyle(
              color: effectiveTextColor,
              fontSize: 14,
            ),
          ),
          
          // Action buttons
          if (showRetryButton && onRetry != null) ...[
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: onRetry,
                    icon: const Icon(Icons.refresh, size: 18),
                    label: const Text('Try Again'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppThemes.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}

/// A banner version of the error widget for inline display
class ErrorRetryBanner extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? borderColor;
  final Color? textColor;

  const ErrorRetryBanner({
    super.key,
    required this.message,
    this.onRetry,
    this.onDismiss,
    this.icon,
    this.backgroundColor,
    this.borderColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor = backgroundColor ?? Colors.red.withOpacity(0.1);
    final effectiveBorderColor = borderColor ?? Colors.red.withOpacity(0.3);
    final effectiveTextColor = textColor ?? Colors.red.shade700;
    final effectiveIcon = icon ?? Icons.error_outline;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: effectiveBorderColor),
      ),
      child: Row(
        children: [
          Icon(
            effectiveIcon,
            color: effectiveTextColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: 13,
              ),
            ),
          ),
          if (onRetry != null) ...[
            const SizedBox(width: 8),
            TextButton(
              onPressed: onRetry,
              style: TextButton.styleFrom(
                foregroundColor: effectiveTextColor,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: const Text(
                'Retry',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
          if (onDismiss != null) ...[
            const SizedBox(width: 4),
            IconButton(
              onPressed: onDismiss,
              icon: Icon(
                Icons.close,
                color: effectiveTextColor,
                size: 16,
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ],
        ],
      ),
    );
  }
} 