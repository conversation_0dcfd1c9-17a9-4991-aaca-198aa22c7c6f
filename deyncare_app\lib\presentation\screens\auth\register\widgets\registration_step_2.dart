import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_text_field.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';

/// Step 2 of registration: Business Information
class RegistrationStep2 extends StatefulWidget {
  final TextEditingController shopNameController;
  final TextEditingController shopAddressController;
  final Function(File?) onLogoSelected;
  final File? selectedLogo;

  const RegistrationStep2({
    super.key,
    required this.shopNameController,
    required this.shopAddressController,
    required this.onLogoSelected,
    this.selectedLogo,
  });
  
  @override
  State<RegistrationStep2> createState() => _RegistrationStep2State();
}

class _RegistrationStep2State extends State<RegistrationStep2> {
  final _formKey = GlobalKey<FormState>();
  final ImagePicker _imagePicker = ImagePicker();
  final List<String> _businessCategories = [
    'Retail Store',
    'Supermarket',
    'Restaurant',
    'Pharmacy',
    'Clothing Store',
    'Electronics Store',
    'Other'
  ];
  
  String _selectedCategory = 'Retail Store';
  
  // Validation for shop name
  String? _validateShopName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Shop name is required';
    }
    if (value.length < 2) {
      return 'Shop name must be at least 2 characters';
    }
    return null;
  }
  
  // Validation for shop address
  String? _validateShopAddress(String? value) {
    if (value == null || value.isEmpty) {
      return 'Shop address is required';
    }
    if (value.length < 5) {
      return 'Shop address must be at least 5 characters';
    }
    return null;
  }
  
  // Handle image selection
  Future<void> _selectImage(ImageSource source) async {
    try {
      final XFile? pickedFile = await _imagePicker.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );
      
      if (pickedFile != null) {
        widget.onLogoSelected(File(pickedFile.path));
      }
    } catch (e) {
      // Show error dialog
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Error'),
            content: const Text('Failed to pick image. Please try again.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          );
        },
      );
    }
  }
  
  // Show image source selection dialog
  void _showImageSourceDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Logo Source'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: const Text('Take a photo'),
                onTap: () {
                  Navigator.pop(context);
                  _selectImage(ImageSource.camera);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Choose from gallery'),
                onTap: () {
                  Navigator.pop(context);
                  _selectImage(ImageSource.gallery);
                },
              ),
            ],
          ),
        );
      },
    );
  }
  

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Business Information',
            style: TextStyle(
              fontSize: 26,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tell us more about your business.',
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 32),
          Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Shop Logo
                Center(
                  child: Column(
                    children: [
                      GestureDetector(
                        onTap: _showImageSourceDialog,
                        child: Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppThemes.textFieldBackgroundColor,
                            border: Border.all(
                              color: AppThemes.dividerColor,
                              width: 2,
                            ),
                            image: widget.selectedLogo != null
                                ? DecorationImage(
                                    image: FileImage(widget.selectedLogo!),
                                    fit: BoxFit.cover,
                                  )
                                : null,
                          ),
                          child: widget.selectedLogo == null
                              ? Icon(
                                  Icons.add_a_photo,
                                  size: 40,
                                  color: AppThemes.textSecondaryColor,
                                )
                              : null,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Shop Logo',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppThemes.textSecondaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Tap to upload',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppThemes.textSecondaryColor.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 32),

                // Shop Name
                AuthTextField(
                  label: 'Shop Name',
                  hintText: 'Enter your shop name',
                  controller: widget.shopNameController,
                  validator: _validateShopName,
                  textInputAction: TextInputAction.next,
                  keyboardType: TextInputType.text,
                  prefixIcon: const Icon(Icons.store_outlined),
                ),
                const SizedBox(height: 20),

                // Shop Address
                AuthTextField(
                  label: 'Shop Address',
                  hintText: 'Enter your shop address',
                  controller: widget.shopAddressController,
                  validator: _validateShopAddress,
                  textInputAction: TextInputAction.next,
                  keyboardType: TextInputType.streetAddress,
                  prefixIcon: const Icon(Icons.location_on_outlined),
                ),
                const SizedBox(height: 20),

                // Business Category
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Business Category',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Theme.of(context).dividerColor,
                          width: 1,
                        ),
                        color: Theme.of(context).colorScheme.surface,
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: _selectedCategory,
                          isExpanded: true,
                          icon: const Icon(Icons.arrow_drop_down),
                          style: TextStyle(
                            fontSize: 16,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                          onChanged: (String? newValue) {
                            if (newValue != null) {
                              setState(() {
                                _selectedCategory = newValue;
                              });
                            }
                          },
                          items: _businessCategories
                              .map<DropdownMenuItem<String>>((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(value),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
