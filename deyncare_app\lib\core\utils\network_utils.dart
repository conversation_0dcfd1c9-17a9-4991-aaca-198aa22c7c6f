import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:async';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

/// Utility class for network-related operations
class NetworkUtils {
  static final Connectivity _connectivity = Connectivity();
  
  /// Check if the device has an active internet connection
  /// This does a more thorough check by actually making a small request
  static Future<bool> isConnected() async {
    try {
      // First check if we have WiFi/mobile data connectivity
      final connectivityResult = await _connectivity.checkConnectivity();
      final hasConnectivity = connectivityResult.contains(ConnectivityResult.mobile) || 
                              connectivityResult.contains(ConnectivityResult.wifi) ||
                              connectivityResult.contains(ConnectivityResult.ethernet);
                              
      if (!hasConnectivity) {
        return false; // No need to check further if we don't have connectivity
      }
      
      // Do a ping test to verify actual internet connectivity
      bool hasInternet = await _hasActualInternetConnection();
      return hasInternet;
    } catch (e) {
      return false;
    }
  }
  
  /// Check if there's actual internet connection by pinging reliable services
  static Future<bool> _hasActualInternetConnection() async {
    try {
      // Try multiple reliable services to check for connectivity
      List<String> reliableDomains = [
        'google.com',
        'cloudflare.com',
        'microsoft.com',
        'apple.com',
        'amazon.com'
      ];

      // First try DNS lookup - fast and reliable
      int successfulLookups = 0;
      const int requiredLookups = 1; // Only need one successful lookup
      
      for (String domain in reliableDomains) {
        try {
          final result = await InternetAddress.lookup(domain)
              .timeout(const Duration(seconds: 4)); // Increased timeout
          if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
            successfulLookups++;
            if (successfulLookups >= requiredLookups) {
              return true;
            }
          }
        } catch (e) {
          // Log the specific error for debugging
          if (kDebugMode) {
            print('DNS lookup failed for $domain: $e');
          }
          continue;
        }
      }

      // If DNS lookups fail, try HTTP requests as a last resort
      final List<String> httpEndpoints = [
        'http://www.google.com/generate_204',
        'http://httpbin.org/status/200',
        'https://www.cloudflare.com/cdn-cgi/trace',
      ];
      
      for (String endpoint in httpEndpoints) {
        try {
          final isHttps = endpoint.startsWith('https');
          final timeout = Duration(seconds: isHttps ? 6 : 4);
          
          final response = await http.get(
            Uri.parse(endpoint),
            headers: {'User-Agent': 'DeynCare-Mobile/1.0'},
          ).timeout(timeout);
          
          if (response.statusCode >= 200 && response.statusCode < 400) {
            return true;
          }
        } catch (e) {
          if (kDebugMode) {
            print('HTTP check failed for $endpoint: $e');
          }
          continue;
        }
      }
      
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Internet connection check failed: $e');
      }
      return false;
    }
  }
  
  /// Stream of connectivity changes
  static Stream<ConnectivityResult> get connectivityStream => 
      _connectivity.onConnectivityChanged.map((result) => result.first);
      
  /// Check if the error is a network connectivity error
  static bool isNetworkError(dynamic error) {
    final errorMessage = error.toString().toLowerCase();
    return errorMessage.contains('socketexception') ||
           errorMessage.contains('network is unreachable') ||
           errorMessage.contains('connection refused') ||
           errorMessage.contains('connection timed out') ||
           errorMessage.contains('no route to host') ||
           errorMessage.contains('network error') ||
           errorMessage.contains('connection error') ||
           errorMessage.contains('network_error') ||
           errorMessage.contains('connection_error');
  }
}
