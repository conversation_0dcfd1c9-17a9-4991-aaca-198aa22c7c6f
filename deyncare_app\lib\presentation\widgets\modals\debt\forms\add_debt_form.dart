import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/domain/models/customer.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_bloc.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_event.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_state.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_bloc.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_event.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_state.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_constants.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/form_mixins.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/loading_states.dart';
import 'package:deyncare_app/injection_container.dart' as di;

/// Add Debt Form Widget - Updated for new debt architecture
class AddDebtForm extends StatefulWidget {
  final Customer? preselectedCustomer;

  const AddDebtForm({super.key, this.preselectedCustomer});

  @override
  State<AddDebtForm> createState() => _AddDebtFormState();
}

class _AddDebtFormState extends State<AddDebtForm> 
    with ModalFormMixin<AddDebtForm>, BlocListenerMixin<AddDebtForm> {
  
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  Customer? _selectedCustomer;
  DateTime? _selectedDueDate;
  List<Customer> _customers = [];
  bool _loadingCustomers = false;

  @override
  void initState() {
    super.initState();
    _selectedCustomer = widget.preselectedCustomer;
    _selectedDueDate = DateTime.now().add(const Duration(days: 30)); // Default 30 days
    _loadCustomers();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadCustomers() async {
    if (widget.preselectedCustomer != null) return; // Skip if preselected
    
    setState(() => _loadingCustomers = true);
    
    // Get customer bloc and load customers
    final customerBloc = di.sl<CustomerBloc>();
    customerBloc.add(const LoadCustomers(page: 1, limit: 100)); // Load first 100 customers
    
    // Listen to customer bloc state changes
    customerBloc.stream.listen((state) {
      if (mounted) {
        if (state is CustomerListLoaded) {
          setState(() {
            // Convert CustomerSummary to Customer for dropdown selection
            // Using minimal Customer objects for form purposes
            final customers = state.response.data?.customers ?? [];
            _customers = customers.map((summary) => Customer(
              customerId: summary.customerId,
              fullName: summary.customerName,
              email: '',
              phone: summary.phone,
              shopId: 'default',
              status: CustomerStatus.active,
              riskProfile: RiskProfile(
                currentRiskLevel: RiskLevel.low,
                riskScore: 0.0,
                riskSource: 'default',
                lastAssessment: DateTime.now(),
              ),
              // Using minimal stats for dropdown selection only
              stats: const CustomerStats(
                totalDebts: 0,
                totalBorrowed: 0.0,
                totalPaid: 0.0,
                currentOutstanding: 0.0,
                completedDebts: 0,
                activeDebts: 0,
                overdueDebts: 0,
                averagePaymentDays: 0.0,
                paymentReliability: PaymentReliability.good,
              ),
              createdAt: summary.createdAt,
              updatedAt: summary.updatedAt,
            )).toList();
            _loadingCustomers = false;
          });
        } else if (state is CustomerListError) {
          setState(() => _loadingCustomers = false);
          if (mounted) {
            handleError('Error loading customers: ${state.message}');
          }
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return buildBlocListener<DebtBloc, DebtState>(
      listener: (context, state) {
        if (state is DebtCreated) {
          handleSuccess('Debt created successfully!');
        } else if (state is DebtError) {
          handleError(state.message);
        }
      },
      child: BlocBuilder<DebtBloc, DebtState>(
        builder: (context, state) {
          // Show skeleton loader during creation
          if (state is DebtCreating) {
            return ModalLoadingStates.formSkeleton(fieldCount: 4);
          }
          
          return Padding(
            padding: ModalConstants.defaultPadding,
            child: Form(
              key: formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildCustomerSelection(),
                  ModalConstants.defaultSpacing,
                  _buildAmountField(),
                  ModalConstants.defaultSpacing,
                  _buildDueDateField(),
                  ModalConstants.defaultSpacing,
                  _buildDescriptionField(),
                  ModalConstants.largeSpacing,
                  _buildSubmitButton(state),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCustomerSelection() {
    if (widget.preselectedCustomer != null) {
      return CommonCard(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Customer',
                style: ModalConstants.sectionTitleStyle(context),
              ),
              ModalConstants.defaultSpacing,
              Text(
                widget.preselectedCustomer!.fullName,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              Text(
                'ID: ${widget.preselectedCustomer!.customerId}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppThemes.textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Customer',
          style: ModalConstants.sectionTitleStyle(context),
        ),
        ModalConstants.defaultSpacing,
        buildDropdownField<Customer>(
          value: _selectedCustomer,
          items: _customers.map((customer) {
            return DropdownMenuItem(
              value: customer,
              child: Text(customer.fullName),
            );
          }).toList(),
          labelText: 'Customer',
          hintText: _loadingCustomers ? 'Loading customers...' : 'Choose customer',
          onChanged: (customer) {
            if (!_loadingCustomers) {
            setState(() => _selectedCustomer = customer);
            }
          },
          validator: (value) {
            if (value == null) return 'Please select a customer';
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildAmountField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Debt Amount (\$)',
          style: ModalConstants.sectionTitleStyle(context),
        ),
        ModalConstants.defaultSpacing,
        buildFormField(
          controller: _amountController,
          labelText: 'Amount',
          hintText: 'Enter debt amount',
          prefixIcon: const Icon(Icons.attach_money),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter debt amount';
            }
            final amount = double.tryParse(value);
            if (amount == null || amount <= 0) {
              return 'Please enter a valid amount greater than 0';
            }
            if (amount > 999999.99) {
              return 'Amount cannot exceed \$999,999.99';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildDueDateField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Due Date',
          style: ModalConstants.sectionTitleStyle(context),
        ),
        ModalConstants.defaultSpacing,
        buildDatePickerField(
          selectedDate: _selectedDueDate,
          labelText: 'due date',
          onDateSelected: (date) {
            setState(() => _selectedDueDate = date);
          },
          firstDate: DateTime.now(),
          lastDate: DateTime.now().add(const Duration(days: 365)),
        ),
      ],
    );
  }

  Widget _buildDescriptionField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Description (Optional)',
          style: ModalConstants.sectionTitleStyle(context),
        ),
        ModalConstants.defaultSpacing,
        buildFormField(
          controller: _descriptionController,
          labelText: 'Description',
          hintText: 'Enter description (optional)',
          maxLines: 3,
          validator: (value) {
            if (value != null && value.length > 500) {
              return 'Description must be less than 500 characters';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildSubmitButton(DebtState state) {
    return buildActionButtons(
      primaryButtonText: 'Create Debt',
      onPrimaryPressed: _handleSubmit,
    );
  }

  void _handleSubmit() {
    if (!validateForm()) return;
    if (_selectedCustomer == null && widget.preselectedCustomer == null) {
      handleError('Please select a customer');
      return;
    }
    if (_selectedDueDate == null) {
      handleError('Please select a due date');
      return;
    }

    // Prevent multiple submissions
    if (isLoading) {
      return;
    }

    final customer = _selectedCustomer ?? widget.preselectedCustomer!;
    final amount = double.parse(_amountController.text);
    final description = _descriptionController.text.trim().isEmpty 
        ? null 
        : _descriptionController.text.trim();

    // Basic business validation before submission
    if (_selectedDueDate!.isBefore(DateTime.now().subtract(const Duration(days: 1)))) {
      handleError('Due date cannot be in the past');
      return;
    }

    setLoading(true);

    context.read<DebtBloc>().add(CreateDebt(
      customerId: customer.customerId,
      debtAmount: amount,
      dueDate: _selectedDueDate!,
      description: description,
    ));
  }
} 