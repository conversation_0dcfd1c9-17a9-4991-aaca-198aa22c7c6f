import 'package:deyncare_app/domain/models/payment.dart';

/// Repository interface for debt management
/// Matches backend debt API endpoints
abstract class DebtRepository {
  /// Create a new debt
  /// POST /api/debts
  Future<Debt> createDebt({
    required String customerId,
    required double amount,
    required DateTime dueDate,
    String? description,
    double? paidAmount,
    DateTime? paidDate,
    PaymentMethod? paymentMethod,
  });

  /// Get debts with pagination and filters
  /// GET /api/debts
  Future<DebtListResult> getDebts({
    int page = 1,
    int limit = 20,
    DebtStatus? status,
    RiskLevel? riskLevel,
    String? customerType,
    String? search,
    String? sortBy,
    bool ascending = false,
  });

  /// Get debt by ID
  /// GET /api/debts/:debtId
  Future<Debt> getDebtById(String debtId);

  /// Update existing debt
  /// PUT /api/debts/:debtId
  Future<Debt> updateDebt({
    required String debtId,
    double? amount,
    DateTime? dueDate,
    String? description,
    DebtStatus? status,
  });

  /// Delete debt
  /// DELETE /api/debts/:debtId
  Future<void> deleteDebt(String debtId);

  /// Add payment to debt
  /// POST /api/debts/:debtId/payments
  Future<Debt> addPaymentToDebt({
    required String debtId,
    required double amount,
    required PaymentMethod paymentMethod,
    String? notes,
    DateTime? paymentDate,
  });

  /// Get debt analytics and statistics
  /// GET /api/debts/stats
  Future<DebtAnalytics> getDebtAnalytics({
    DateTime? startDate,
    DateTime? endDate,
    String? shopId,
  });

  /// Get customer debts (for customer detail view)
  /// GET /api/customers/:customerId/debts
  Future<List<Debt>> getCustomerDebts(String customerId);
}

/// Result containing debts list and pagination info
class DebtListResult {
  final List<Debt> debts;
  final int totalCount;
  final int currentPage;
  final int totalPages;
  final bool hasNextPage;
  final bool hasPreviousPage;

  const DebtListResult({
    required this.debts,
    required this.totalCount,
    required this.currentPage,
    required this.totalPages,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });
}

/// Debt statistics
class DebtStats {
  final int totalDebts;
  final double totalAmount;
  final double totalPaid;
  final double totalOutstanding;
  final int activeDebts;
  final int overdueDebts;
  final int completedDebts;
  final double averageDebtAmount;
  final double collectionRate;

  const DebtStats({
    required this.totalDebts,
    required this.totalAmount,
    required this.totalPaid,
    required this.totalOutstanding,
    required this.activeDebts,
    required this.overdueDebts,
    required this.completedDebts,
    required this.averageDebtAmount,
    required this.collectionRate,
  });
}

/// Debt analytics and statistics
class DebtAnalytics {
  final DebtStats stats;
  final List<RiskDistribution> riskDistribution;
  final List<StatusDistribution> statusDistribution;
  final List<MonthlyTrend> monthlyTrends;
  final PaymentMetrics paymentMetrics;

  const DebtAnalytics({
    required this.stats,
    required this.riskDistribution,
    required this.statusDistribution,
    required this.monthlyTrends,
    required this.paymentMetrics,
  });
}

/// Risk distribution data
class RiskDistribution {
  final RiskLevel riskLevel;
  final int count;
  final double percentage;

  const RiskDistribution({
    required this.riskLevel,
    required this.count,
    required this.percentage,
  });
}

/// Status distribution data
class StatusDistribution {
  final DebtStatus status;
  final int count;
  final double percentage;

  const StatusDistribution({
    required this.status,
    required this.count,
    required this.percentage,
  });
}

/// Monthly trend data
class MonthlyTrend {
  final DateTime month;
  final double totalAmount;
  final int debtCount;
  final double collectionRate;

  const MonthlyTrend({
    required this.month,
    required this.totalAmount,
    required this.debtCount,
    required this.collectionRate,
  });
}

/// Payment metrics
class PaymentMetrics {
  final double averagePaymentTime;
  final double onTimePaymentRate;
  final double defaultRate;
  final PaymentMethod mostUsedPaymentMethod;

  const PaymentMetrics({
    required this.averagePaymentTime,
    required this.onTimePaymentRate,
    required this.defaultRate,
    required this.mostUsedPaymentMethod,
  });
}
