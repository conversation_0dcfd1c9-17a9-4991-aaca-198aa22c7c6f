import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:deyncare_app/core/utils/api_toast_handler.dart';

/// Processes API responses according to DeynCare backend format
class ResponseHandler {
  /// Process response according to DeynCare backend format
  /// Set [showSuccessToast] to true to show a toast on successful responses
  static dynamic processResponse(Response response, {bool showSuccessToast = false}) {
    // Check if automatic toasts are disabled for this request
    final showAutoToast = response.requestOptions.extra['showAutoToast'] ?? true;
    
    // Check for success/error in DeynCare format
    if (response.data is Map) {
      // Safely cast to Map<String, dynamic> to avoid type errors
      final Map<String, dynamic> data;
      try {
        if (response.data is Map<String, dynamic>) {
          data = response.data as Map<String, dynamic>;
        } else {
          // Convert other Map types to Map<String, dynamic>
          data = Map<String, dynamic>.from(response.data as Map);
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error casting response data to Map<String, dynamic>: $e');
          print('❌ Response data type: ${response.data.runtimeType}');
        }
        // Return data as-is if casting fails
        return response.data;
      }
      
      // Check for success flag
      if (data.containsKey('success')) {
        if (data['success'] == true) {
          // Successful response
          // Only show success toast if automatic toasts are enabled AND explicitly requested
          if (showAutoToast && showSuccessToast && data.containsKey('message')) {
            // Show success toast if requested and message is available
            ApiToastHandler.handleSuccess(message: data['message']);
          } else if (showAutoToast && showSuccessToast) {
            // Show generic success toast if no specific message
            ApiToastHandler.handleSuccess(message: 'Operation completed successfully');
          }
          
          // Log success in debug mode
          if (kDebugMode) {
            print('✅ API Success: ${response.requestOptions.path}');
          }
          
          return data;
        } else {
          // Error response
          final message = data['message'] ?? 'Unknown error';
          final errorCode = data['errorCode'] ?? 'unknown_error';
          
          final exception = ApiException(
            message: message,
            code: errorCode,
            statusCode: response.statusCode,
            data: data,
          );
          
          // Only show error toast if automatic toasts are enabled
          if (showAutoToast) {
          ApiToastHandler.handleError(exception);
          }
          
          throw exception;
        }
      }
    }
    
    // Non-standard response, return as-is
    return response.data;
  }
}
