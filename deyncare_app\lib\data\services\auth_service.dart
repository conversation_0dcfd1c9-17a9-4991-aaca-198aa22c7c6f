import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:deyncare_app/core/config/env_config.dart';

// Domain Models
import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/domain/models/auth_token.dart';
import 'package:deyncare_app/domain/models/registration_progress.dart';

// Data Layer Components
import 'package:deyncare_app/data/network/clients/dio_client.dart';
import 'package:deyncare_app/data/network/token/token_manager.dart';
import 'package:deyncare_app/data/repositories/auth_repository_impl.dart';
import 'package:deyncare_app/data/services/auth/auth_remote_source.dart';
import 'package:deyncare_app/data/services/auth/auth_utils.dart';
import 'package:deyncare_app/data/services/auth/auth_deep_link_handler.dart';

/// Authentication service for interacting with DeynCare backend auth endpoints
///
/// This is the main service class that integrates all authentication operations
/// into a single interface for use by the presentation layer.
class AuthService {
  // Internal components
  final AuthRepositoryImpl _repository;
  late final AuthDeepLinkHandler _deepLinkHandler;

  /// Creates a new AuthService with required dependencies
  ///
  /// If dependencies are not provided, they will be created internally
  AuthService({
    DioClient? dioClient,
    TokenManager? tokenManager,
    FlutterSecureStorage? secureStorage,
    AuthRepositoryImpl? repository,
    AuthDeepLinkHandler? deepLinkHandler,
  }) : _repository = repository ??
            AuthRepositoryImpl(
              remoteSource: AuthRemoteSource(dioClient: dioClient ?? DioClient()),
              tokenManager:
                  tokenManager ?? TokenManager(secureStorage: secureStorage),
              authUtils: AuthUtils(secureStorage: secureStorage),
              dioClient: dioClient ?? DioClient(),
            ) {
    _deepLinkHandler = deepLinkHandler ?? AuthDeepLinkHandler(authService: this);
  }

  /// Initialize the auth service
  ///
  /// This should be called early in the app lifecycle
  Future<void> init(BuildContext context) async {
    await _deepLinkHandler.initialize(context);
  }

  /// Dispose resources
  void dispose() {
    _deepLinkHandler.dispose();
  }

  /// Login with email and password
  ///
  /// Returns a tuple containing the user and auth token
  Future<(User, AuthToken)> login(String email, String password) async {
    return await _repository.login(email, password);
  }

  /// Register new user with shop (admin)
  Future<(User, RegistrationProgress)> register({
    required String fullName,
    required String email,
    required String phone,
    required String password,
    required String shopName,
    required String shopAddress,
    File? shopLogo,
    String planType = 'trial',
    String paymentMethod = 'offline',
    bool initialPaid = false,
    String? discountCode,
  }) async {
    return await _repository.register(
      fullName: fullName,
      email: email,
      phone: phone,
      password: password,
      shopName: shopName,
      shopAddress: shopAddress,
      shopLogo: shopLogo,
      planType: planType,
      paymentMethod: paymentMethod,
      initialPaid: initialPaid,
      discountCode: discountCode,
    );
  }

  /// Create employee user (requires admin privileges)
  Future<User> createEmployee({
    required String fullName,
    required String email,
    required String phone,
    String? password,
    bool generatePassword = true,
    List<String>? permissions,
    String? position,
    String? note,
  }) async {
    return await _repository.createEmployee(
      fullName: fullName,
      email: email,
      phone: phone,
      password: password,
      generatePassword: generatePassword,
      permissions: permissions,
      position: position,
      note: note,
    );
  }

  /// Check if an email already exists in the system
  ///
  /// Returns true if the email exists, false otherwise
  Future<bool> checkEmailExists(String email) async {
    try {
      return await _repository.checkEmailExists(email);
    } catch (e) {
      // If there's an error checking, assume it might exist to be safe
      rethrow;
    }
  }

  /// Process subscription payment
  Future<(User, RegistrationProgress)> processPayment({
    required String userId,
    required String shopId,
    required String planId,
    required String paymentMethod,
    required String phoneNumber,
    String? discountCode,
  }) async {
    return await _repository.processPayment(
      userId: userId,
      shopId: shopId,
      planId: planId,
      paymentMethod: paymentMethod,
      phoneNumber: phoneNumber,
      discountCode: discountCode,
    );
  }

  /// Verify email with verification code
  Future<(User, RegistrationProgress)> verifyEmail(
      String email, String verificationCode) async {
    return await _repository.verifyEmail(email, verificationCode);
  }

  /// Resend verification code
  Future<void> resendVerification(String email) async {
    await _repository.resendVerification(email);
  }

  /// Request password reset
  ///
  /// Sends a reset link to the user's email
  Future<void> forgotPassword(String email) async {
    await _repository.forgotPassword(email);
  }

  /// Reset password with token
  Future<void> resetPassword({
    required String token,
    required String newPassword,
    required String confirmPassword,
  }) async {
    await _repository.resetPassword(
      token: token,
      newPassword: newPassword,
      confirmPassword: confirmPassword,
    );
  }

  /// Open reset password page in browser
  ///
  /// This is used as a fallback when deep linking fails
  Future<bool> openResetPasswordPage(String token) async {
    final url = '${EnvConfig.webAppUrl}/reset-password?token=$token';
    final uri = Uri.parse(url);
    return await launchUrl(uri, mode: LaunchMode.externalApplication);
  }

  /// Logout current user
  Future<void> logout() async {
    await _repository.logout();
  }

  /// Logout from all devices
  Future<void> logoutAll() async {
    await _repository.logoutAll();
  }

  /// Refresh auth token
  ///
  /// If refreshToken is provided, it will be used instead of the stored token
  Future<AuthToken> refreshToken(String refreshToken) async {
    return await _repository.refreshToken(refreshToken);
  }

  /// Change password while logged in
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    await _repository.changePassword(
      currentPassword: currentPassword,
      newPassword: newPassword,
      confirmPassword: confirmPassword,
    );
  }

  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    return await _repository.isLoggedIn();
  }

  /// Get current authenticated user
  Future<User?> getCurrentUser() async {
    return await _repository.getCurrentUser();
  }

  /// Update user profile
  Future<User> updateProfile({
    required String fullName,
    required String phone,
  }) async {
    return await _repository.updateProfile(
      fullName: fullName,
      phone: phone,
    );
  }
}
