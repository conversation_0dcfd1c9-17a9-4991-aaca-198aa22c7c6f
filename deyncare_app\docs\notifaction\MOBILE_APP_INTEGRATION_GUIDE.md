# 📱 DeynCare Mobile App Push Notification Integration Guide

## Overview

Complete integration guide for connecting Flutter mobile app to DeynCare's Firebase-based push notification system.

**Target Users**: Ad<PERSON>, SuperAdmin, Employee roles only (Customers do NOT use mobile app)

## 🚀 Quick Start

### 1. Firebase Project Setup

#### Download Configuration Files
- **Android**: Download `google-services.json` from Firebase Console → Place in `android/app/`
- **iOS**: Download `GoogleService-Info.plist` from Firebase Console → Place in `ios/Runner/`

#### Firebase Project Details
```
Project ID: deyncare-47d99
Project Number: 1089951025738
Web API Key: AIzaSyCYP-Wb642o1uCI3BmjWU97jE_VWHope5k
```

### 2. Flutter Dependencies

```yaml
# pubspec.yaml
dependencies:
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10
  firebase_analytics: ^10.7.4
  flutter_local_notifications: ^16.3.2
  dio: ^5.4.0
  provider: ^6.1.1
  flutter_secure_storage: ^9.0.0
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
```

### 3. Android Configuration

#### `android/app/build.gradle`
```gradle
dependencies {
    implementation 'com.google.firebase:firebase-messaging:23.1.0'
    implementation 'com.google.firebase:firebase-analytics:21.2.0'
}

apply plugin: 'com.google.gms.google-services'
```

#### `android/build.gradle`
```gradle
dependencies {
    classpath 'com.google.gms:google-services:4.3.15'
}
```

### 4. iOS Configuration

#### `ios/Runner/Info.plist`
```xml
<key>FirebaseMessagingAutoInitEnabled</key>
<true/>
<key>FirebaseAnalyticsCollectionEnabled</key>
<true/>
```

## 🔧 Core Implementation

### Firebase Service (`lib/services/firebase_service.dart`)

```dart
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/foundation.dart';

class FirebaseService {
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications = 
      FlutterLocalNotificationsPlugin();
  
  static String? _fcmToken;
  static bool _initialized = false;

  /// Initialize Firebase and FCM
  static Future<void> initialize() async {
    if (_initialized) return;
    
    try {
      await Firebase.initializeApp();
      await _requestPermissions();
      await _initializeLocalNotifications();
      await _getFCMToken();
      _setupMessageHandlers();
      
      _initialized = true;
      debugPrint('🔥 Firebase Service initialized');
    } catch (e) {
      debugPrint('❌ Firebase initialization failed: $e');
      rethrow;
    }
  }

  /// Request notification permissions
  static Future<void> _requestPermissions() async {
    NotificationSettings settings = await _messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );

    if (settings.authorizationStatus != AuthorizationStatus.authorized) {
      throw Exception('Notification permissions not granted');
    }
  }

  /// Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    const AndroidInitializationSettings androidSettings = 
        AndroidInitializationSettings('@mipmap/ic_launcher');
    
    const DarwinInitializationSettings iosSettings = 
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings settings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      settings,
      onDidReceiveNotificationResponse: _onNotificationTap,
    );
  }

  /// Get FCM token
  static Future<String?> _getFCMToken() async {
    try {
      _fcmToken = await _messaging.getToken();
      debugPrint('🔑 FCM Token: $_fcmToken');
      return _fcmToken;
    } catch (e) {
      debugPrint('❌ Failed to get FCM token: $e');
      return null;
    }
  }

  /// Setup message handlers
  static void _setupMessageHandlers() {
    // Foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
    
    // Notification taps
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
    
    // Token refresh
    _messaging.onTokenRefresh.listen(_onTokenRefresh);
  }

  /// Handle foreground messages
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    debugPrint('📨 Foreground: ${message.notification?.title}');
    await _showLocalNotification(message);
  }

  /// Handle background messages
  @pragma('vm:entry-point')
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    debugPrint('📨 Background: ${message.notification?.title}');
  }

  /// Handle notification tap
  static void _handleNotificationTap(RemoteMessage message) {
    debugPrint('👆 Notification tapped: ${message.data}');
    _navigateToScreen(message.data);
  }

  /// Handle local notification tap
  static void _onNotificationTap(NotificationResponse response) {
    debugPrint('👆 Local notification tapped');
  }

  /// Handle token refresh
  static void _onTokenRefresh(String newToken) {
    debugPrint('🔄 Token refreshed: $newToken');
    _fcmToken = newToken;
    ApiService.registerFCMToken(newToken);
  }

  /// Show local notification
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'deyncare_channel',
      'DeynCare Notifications',
      importance: Importance.high,
      priority: Priority.high,
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'DeynCare',
      message.notification?.body ?? 'New notification',
      details,
    );
  }

  /// Navigate based on notification data
  static void _navigateToScreen(Map<String, dynamic> data) {
    final type = data['type'];
    
    switch (type) {
      case 'debt_created':
        // Navigate to debt details
        break;
      case 'payment_recorded':
        // Navigate to payment details
        break;
      case 'debt_reminder':
        // Navigate to debt list
        break;
      default:
        // Navigate to home
        break;
    }
  }

  /// Get current FCM token
  static String? get fcmToken => _fcmToken;
  static bool get isInitialized => _initialized;
}
```

### API Service (`lib/services/api_service.dart`)

```dart
import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

class ApiService {
  static late Dio _dio;
  static const String baseUrl = 'https://your-backend-url.com/api';
  // Local: 'http://localhost:5000/api'
  
  static String? _authToken;
  static Map<String, String> _deviceInfo = {};

  static Future<void> initialize() async {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    _dio.interceptors.add(_AuthInterceptor());
    await _getDeviceInfo();
  }

  /// Get device information
  static Future<void> _getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      final packageInfo = await PackageInfo.fromPlatform();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        _deviceInfo = {
          'platform': 'android',
          'deviceId': androidInfo.id,
          'osVersion': androidInfo.version.release,
          'appVersion': packageInfo.version,
        };
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        _deviceInfo = {
          'platform': 'ios',
          'deviceId': iosInfo.identifierForVendor ?? 'unknown',
          'osVersion': iosInfo.systemVersion,
          'appVersion': packageInfo.version,
        };
      }
    } catch (e) {
      print('Failed to get device info: $e');
    }
  }

  static void setAuthToken(String token) => _authToken = token;
  static void clearAuthToken() => _authToken = null;

  /// Register FCM token
  static Future<Map<String, dynamic>> registerFCMToken(String fcmToken) async {
    final response = await _dio.post('/fcm/register', data: {
      'token': fcmToken,
      'deviceInfo': _deviceInfo,
    });
    return response.data;
  }

  /// Send test notification
  static Future<Map<String, dynamic>> sendTestNotification() async {
    final response = await _dio.post('/fcm/test');
    return response.data;
  }

  /// Login
  static Future<Map<String, dynamic>> login(String email, String password) async {
    final response = await _dio.post('/auth/login', data: {
      'email': email,
      'password': password,
    });

    if (response.data['success'] == true) {
      setAuthToken(response.data['data']['token']);
    }
    return response.data;
  }

  /// Logout
  static Future<void> logout() async {
    try {
      await _dio.post('/auth/logout');
    } finally {
      clearAuthToken();
    }
  }
}

class _AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (ApiService._authToken != null) {
      options.headers['Authorization'] = 'Bearer ${ApiService._authToken}';
    }
    handler.next(options);
  }
}
```

### Main App (`main.dart`)

```dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'services/firebase_service.dart';
import 'services/api_service.dart';
import 'providers/auth_provider.dart';
import 'providers/notification_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize services
  await FirebaseService.initialize();
  await ApiService.initialize();
  
  runApp(const DeynCareApp());
}

class DeynCareApp extends StatelessWidget {
  const DeynCareApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => NotificationProvider()),
      ],
      child: MaterialApp(
        title: 'DeynCare Admin',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          useMaterial3: true,
        ),
        home: const LoginScreen(),
      ),
    );
  }
}
```

## 🔗 API Endpoints

### FCM Token Registration
```http
POST /api/fcm/register
Authorization: Bearer <token>

{
  "token": "fcm_token_here",
  "deviceInfo": {
    "platform": "android|ios",
    "deviceId": "unique_device_id",
    "appVersion": "1.0.0",
    "osVersion": "13.0"
  }
}
```

### Test Notification
```http
POST /api/fcm/test
Authorization: Bearer <token>
```

### Authentication
```http
POST /api/auth/login

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

## 📊 Notification Types

The app will receive these notification types:

### 1. Debt Creation Notification
```json
{
  "type": "debt_created",
  "title": "💰 New Debt Created",
  "body": "Ahmed Hassan - $150.00 due Dec 28, 2024",
  "data": {
    "debtId": "debt_123",
    "customerId": "customer_456",
    "amount": "150.00",
    "dueDate": "2024-12-28"
  }
}
```

### 2. Payment Recording Notification
```json
{
  "type": "payment_recorded",
  "title": "💳 Payment Recorded",
  "body": "Ahmed Hassan paid $75.00 - Risk: Low ✅",
  "data": {
    "paymentId": "payment_789",
    "customerId": "customer_456",
    "amount": "75.00",
    "riskStatus": "low"
  }
}
```

### 3. Debt Reminder Notification
```json
{
  "type": "debt_reminder",
  "title": "⏰ Debt Reminder",
  "body": "3 debts due in 3 days - Total: $450.00",
  "data": {
    "reminderType": "upcoming",
    "debtCount": 3,
    "totalAmount": "450.00"
  }
}
```

### 4. Custom Notification
```json
{
  "type": "custom",
  "title": "📢 System Update",
  "body": "New features available in your DeynCare app",
  "data": {
    "actionUrl": "/updates",
    "priority": "normal"
  }
}
```

## 🧪 Testing Guide

### 1. Setup Test Environment

```dart
// Test Screen
class NotificationTestScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Notification Test')),
      body: Column(
        children: [
          // FCM Status Card
          Card(
            child: ListTile(
              title: Text('FCM Status'),
              subtitle: Text('Token: ${FirebaseService.fcmToken}'),
              trailing: Icon(
                FirebaseService.isInitialized ? Icons.check : Icons.error,
                color: FirebaseService.isInitialized ? Colors.green : Colors.red,
              ),
            ),
          ),
          
          // Test Buttons
          ElevatedButton(
            onPressed: () => NotificationProvider.of(context).registerFCMToken(),
            child: Text('Register FCM Token'),
          ),
          ElevatedButton(
            onPressed: () => NotificationProvider.of(context).sendTestNotification(),
            child: Text('Send Test Notification'),
          ),
        ],
      ),
    );
  }
}
```

### 2. Testing Checklist

#### ✅ Basic Setup
- [ ] Firebase configuration files added
- [ ] Dependencies installed
- [ ] App builds successfully
- [ ] No compilation errors

#### ✅ FCM Integration
- [ ] FCM token generated
- [ ] Token registered with backend
- [ ] Permissions granted
- [ ] Test notification received

#### ✅ Authentication
- [ ] Login successful
- [ ] Token stored securely
- [ ] API calls authenticated
- [ ] Logout clears token

#### ✅ Notification Handling
- [ ] Foreground notifications shown
- [ ] Background notifications received
- [ ] Notification tap navigation works
- [ ] Token refresh handled

### 3. Debug Commands

```bash
# Flutter
flutter doctor
flutter clean && flutter pub get
flutter run --debug

# Android Logs
adb logcat | grep -i firebase
adb logcat | grep -i fcm

# iOS Logs (use Xcode console)
```

## 🚀 Deployment

### Android Release Build

```bash
# Build APK
flutter build apk --release

# Build App Bundle
flutter build appbundle --release
```

### iOS Release Build

```bash
# Build iOS
flutter build ios --release

# Archive in Xcode for App Store
```

### Backend Configuration

Ensure these environment variables are set:

```env
# Firebase
FIREBASE_PROJECT_ID=deyncare-47d99
FIREBASE_PROJECT_NUMBER=1089951025738
FIREBASE_WEB_API_KEY=AIzaSyCYP-Wb642o1uCI3BmjWU97jE_VWHope5k
GOOGLE_APPLICATION_CREDENTIALS=./deyncare-47d99-firebase-adminsdk-fbsvc-cea556463f.json

# Notifications
PUSH_NOTIFICATIONS_ENABLED=true
DEBT_REMINDERS_ENABLED=true
NOTIFICATION_BATCH_SIZE=500
```

## 🔍 Troubleshooting

### Common Issues

#### 1. FCM Token Not Generated
**Solution:**
- Check Firebase configuration files
- Verify package/bundle ID matches Firebase project
- Ensure permissions granted

#### 2. Notifications Not Received
**Solution:**
- Test with Firebase Console first
- Check device notification settings
- Verify token registration with backend
- Check network connectivity

#### 3. Authentication Failures
**Solution:**
- Verify API endpoint URLs
- Check CORS configuration
- Validate SSL certificates
- Test with Postman first

#### 4. Build Errors
**Solution:**
- Run `flutter clean && flutter pub get`
- Check dependency versions
- Verify configuration files

### Debug Steps

1. **Check Firebase Status:**
   ```dart
   print('Firebase initialized: ${FirebaseService.isInitialized}');
   print('FCM Token: ${FirebaseService.fcmToken}');
   ```

2. **Test API Connection:**
   ```bash
   curl -X GET https://your-backend-url.com/api/health
   ```

3. **Verify Token Registration:**
   ```bash
   curl -X POST https://your-backend-url.com/api/fcm/register \
     -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d '{"token":"test_token","deviceInfo":{}}'
   ```

## 📞 Support Resources

- **Backend Health Check**: `/api/health`
- **Firebase Console**: [console.firebase.google.com/project/deyncare-47d99](https://console.firebase.google.com/project/deyncare-47d99)
- **Flutter Firebase Docs**: [firebase.flutter.dev](https://firebase.flutter.dev)
- **FCM Documentation**: [firebase.google.com/docs/cloud-messaging](https://firebase.google.com/docs/cloud-messaging)

---

## 🎯 Next Phase: Web Dashboard

Once mobile integration is complete, we'll build the SuperAdmin web dashboard for:
- Sending custom notifications
- Managing notification templates
- Viewing delivery statistics
- Configuring notification settings

**Ready to connect your mobile app!** 🚀 