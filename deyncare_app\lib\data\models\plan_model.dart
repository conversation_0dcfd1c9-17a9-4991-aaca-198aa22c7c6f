import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'plan_model.g.dart';

@JsonSerializable()
class PlanModel extends Equatable {
  @Json<PERSON>ey(name: 'planId')
  final String id;
  final String name;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'displayName')
  final String displayName;
  final String type; // 'trial', 'monthly', 'yearly'
  final String? description;
  @JsonKey(name: 'pricing')
  final PlanPricingModel pricing;
  final int trialDays;
  final Map<String, dynamic> features;
  final Map<String, dynamic> limits;
  final bool isActive;
  final bool isPopular;
  final int displayOrder;
  final DateTime createdAt;
  final DateTime updatedAt;

  const PlanModel({
    required this.id,
    required this.name,
    required this.displayName,
    required this.type,
    this.description,
    required this.pricing,
    required this.trialDays,
    required this.features,
    required this.limits,
    required this.isActive,
    required this.isPopular,
    required this.displayOrder,
    required this.createdAt,
    required this.updatedAt,
  });

  // Legacy constructor for backward compatibility
  factory PlanModel.fromJson(Map<String, dynamic> json) {
    return PlanModel(
      id: json['id'] as String? ?? json['planId'] as String,
      name: json['name'] as String,
      displayName: json['displayName'] as String? ?? json['name'] as String,
      type: json['type'] as String,
      description: json['description'] as String?,
      pricing: json['pricing'] != null 
          ? PlanPricingModel.fromJson(json['pricing'] as Map<String, dynamic>)
          : PlanPricingModel(
              basePrice: (json['price'] as num?)?.toDouble() ?? 0.0,
              currency: json['currency'] as String? ?? 'USD',
              billingCycle: json['billingCycle'] as String? ?? json['type'] as String,
            ),
      trialDays: json['trialDays'] as int? ?? 
                 (json['features'] as Map<String, dynamic>?)?['trialDays'] as int? ?? 0,
      features: json['features'] as Map<String, dynamic>? ?? {},
      limits: json['limits'] as Map<String, dynamic>? ?? {},
      isActive: json['isActive'] as bool? ?? true,
      isPopular: json['isPopular'] as bool? ?? false,
      displayOrder: json['displayOrder'] as int? ?? 0,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt'] as String)
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt'] as String)
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() => _$PlanModelToJson(this);

  // Legacy getters for backward compatibility
  double get price => pricing.basePrice;
  String get currency => pricing.currency;
  String get billingCycle => pricing.billingCycle;

  @override
  List<Object?> get props => [
        id,
        name,
        displayName,
        type,
        description,
        pricing,
        trialDays,
        features,
        limits,
        isActive,
        isPopular,
        displayOrder,
        createdAt,
        updatedAt,
      ];
}

@JsonSerializable()
class PlanPricingModel extends Equatable {
  @JsonKey(name: 'basePrice')
  final double basePrice;
  final String currency;
  @JsonKey(name: 'billingCycle')
  final String billingCycle;

  const PlanPricingModel({
    required this.basePrice,
    required this.currency,
    required this.billingCycle,
  });

  factory PlanPricingModel.fromJson(Map<String, dynamic> json) => 
      _$PlanPricingModelFromJson(json);

  Map<String, dynamic> toJson() => _$PlanPricingModelToJson(this);

  @override
  List<Object?> get props => [basePrice, currency, billingCycle];
} 