import 'package:flutter/material.dart';

// Import implemented screens
import 'package:deyncare_app/presentation/screens/splash/splash_screen.dart';
import 'package:deyncare_app/presentation/screens/onboarding/onboarding_screen.dart';
import 'package:deyncare_app/presentation/screens/auth/login/login_screen.dart';
import 'package:deyncare_app/presentation/screens/auth/register/register_screen.dart';
import 'package:deyncare_app/presentation/screens/auth/forgot_password/forgot_password_screen.dart';
import 'package:deyncare_app/presentation/screens/auth/verification/verification_screen.dart';
import 'package:deyncare_app/presentation/screens/auth/reset_password/reset_password_screen.dart';
import 'package:deyncare_app/presentation/screens/auth/reset_success/reset_success_screen.dart';
import 'package:deyncare_app/presentation/screens/dashboard/dashboard_screen.dart';
import 'package:deyncare_app/presentation/screens/auth/payment_success_screen.dart';
import 'package:deyncare_app/presentation/screens/auth/payment/payment_screen.dart';

// Import new business screens
import 'package:deyncare_app/presentation/screens/customer/customer_list_screen.dart';
import 'package:deyncare_app/presentation/screens/customer/create_customer_screen.dart';
import 'package:deyncare_app/presentation/screens/debt/debt_list_screen.dart';
import 'package:deyncare_app/presentation/screens/debt/create_debt_screen.dart';
import 'package:deyncare_app/presentation/screens/payment/payment_history_screen.dart';

// Import settings screens
import 'package:deyncare_app/presentation/screens/settings/shop_settings_screen.dart';
import 'package:deyncare_app/presentation/screens/settings/profile_settings_screen.dart';
import 'package:deyncare_app/presentation/screens/settings/notification_settings_screen.dart';

// Import subscription screens
import 'package:deyncare_app/presentation/screens/subscription/subscription_screen.dart';

import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/domain/models/auth_token.dart';

// Add imports
import 'package:deyncare_app/presentation/screens/reports/reports_main_screen.dart';
import 'package:deyncare_app/presentation/screens/reports/customer/customer_report_screen.dart';
import 'package:deyncare_app/presentation/screens/reports/debt/debt_report_screen.dart';
import 'package:deyncare_app/presentation/screens/reports/risk/risk_report_screen.dart';

// User role management imports
import 'package:deyncare_app/presentation/screens/user_role/user_role_list_screen.dart';
import 'package:deyncare_app/presentation/screens/user_role/add_user_role_screen.dart';
import 'package:deyncare_app/presentation/screens/user_role/edit_user_role_screen.dart';

/// Handles routing throughout the app
class AppRouter {
  // Route names - constants for all app routes
  static const String splashRoute = '/';
  static const String onboardingRoute = '/onboarding';
  static const String loginRoute = '/login';
  static const String registerRoute = '/register';
  static const String forgotPasswordRoute = '/forgot-password';
  static const String resetPasswordRoute = '/reset-password';
  static const String resetSuccessRoute = '/reset-success';
  static const String verificationRoute = '/verification';
  static const String dashboardRoute = '/dashboard';
  static const String paymentSuccessRoute = '/payment-success';
  static const String paymentRoute = '/payment';

  // Business screen routes
  static const String customerListRoute = '/customers';
  static const String createCustomerRoute = '/customers/create';
  static const String customerDetailsRoute = '/customers/details';
  static const String editCustomerRoute = '/customers/edit';

  static const String debtListRoute = '/debts';
  static const String createDebtRoute = '/debts/create';
  static const String debtDetailsRoute = '/debts/details';
  static const String editDebtRoute = '/debts/edit';

  static const String paymentHistoryRoute = '/payments/history';

  // Settings screen routes
  static const String shopSettingsRoute = '/settings/shop';
  static const String profileSettingsRoute = '/settings/profile';
  static const String notificationSettingsRoute = '/settings/notifications';

  // Subscription routes
  static const String subscriptionRoute = '/subscription';

  // Add route constants
  static const String reportsRoute = '/reports';
  static const String customerReportRoute = '/reports/customer';
  static const String debtReportRoute = '/reports/debt';
  static const String riskReportRoute = '/reports/risk';

  // User role management routes
  static const String userRoleListRoute = '/user-role-list';
  static const String addUserRoleRoute = '/user-role-add';
  static const String editUserRoleRoute = '/user-role-edit';

  // onGenerateRoute method for MaterialApp
  static Route<dynamic> onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case splashRoute:
        return MaterialPageRoute(
          builder: (_) => const SplashScreen(),
        );

      case onboardingRoute:
        return MaterialPageRoute(
          builder: (_) => const OnboardingScreen(),
        );

      case loginRoute:
        // Extract email argument if it exists
        final Map<String, dynamic>? args =
            settings.arguments as Map<String, dynamic>?;
        final String? email = args != null && args.containsKey('email')
            ? args['email'] as String
            : null;

        return MaterialPageRoute(
          builder: (_) => LoginScreen(prefilledEmail: email),
        );

      case registerRoute:
        return MaterialPageRoute(
          builder: (_) => const RegisterScreen(),
        );

      case forgotPasswordRoute:
        return MaterialPageRoute(
          builder: (_) => const ForgotPasswordScreen(),
        );

      case resetPasswordRoute:
        final Map<String, dynamic>? args =
            settings.arguments as Map<String, dynamic>?;
        final String? token = args != null && args.containsKey('token')
            ? args['token'] as String
            : null;

        if (token == null) {
          // If no token provided, redirect to login
          return MaterialPageRoute(
            builder: (_) => const LoginScreen(),
          );
        }

        return MaterialPageRoute(
          builder: (_) => ResetPasswordScreen(token: token),
        );

      case resetSuccessRoute:
        return MaterialPageRoute(
          builder: (_) => const ResetSuccessScreen(),
        );

      case verificationRoute:
        final Map<String, dynamic>? args =
            settings.arguments as Map<String, dynamic>?;
        final User? user = args != null && args.containsKey('user')
            ? args['user'] as User
            : null;
        final String? selectedPlanId =
            args != null && args.containsKey('selectedPlanId')
                ? args['selectedPlanId'] as String
                : null;
        final DateTime? expiresAt =
            args != null && args.containsKey('expiresAt')
                ? args['expiresAt'] as DateTime
                : null;

        if (user == null) {
          // If no user provided, redirect to login
          return MaterialPageRoute(
            builder: (_) => const LoginScreen(),
          );
        }

        return MaterialPageRoute(
          builder: (_) => VerificationScreen(
            user: user,
            selectedPlanId: selectedPlanId,
            expiresAt: expiresAt,
          ),
        );

      case paymentSuccessRoute:
        final Map<String, dynamic> args =
            settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => PaymentSuccessScreen(
            user: args['user'] as User,
          ),
        );

      case paymentRoute:
        final Map<String, dynamic>? args =
            settings.arguments as Map<String, dynamic>?;
        if (args == null ||
            !args.containsKey('user') ||
            args['user'] is! User ||
            !args.containsKey('selectedPlanId') ||
            args['selectedPlanId'] is! String) {
          // Handle invalid arguments: navigate to login or show error
          return MaterialPageRoute(
            builder: (_) => const LoginScreen(),
          );
        }
        return MaterialPageRoute(
          builder: (_) => PaymentScreen(
            user: args['user'] as User,
            token: args['token'] as AuthToken?,
            selectedPlanId: args['selectedPlanId'] as String,
          ),
        );

      case dashboardRoute:
        return MaterialPageRoute(
          builder: (_) => const DashboardScreen(),
        );

      // Business screen routes
      case customerListRoute:
        return MaterialPageRoute(
          builder: (_) => const CustomerListScreen(),
        );

      case createCustomerRoute:
        return MaterialPageRoute(
          builder: (_) => const CreateCustomerScreen(),
        );

      case customerDetailsRoute:
        final Map<String, dynamic>? args =
            settings.arguments as Map<String, dynamic>?;
        final String? customerId = args?['customerId'] as String?;

        if (customerId == null) {
          return MaterialPageRoute(
            builder: (_) => const CustomerListScreen(),
          );
        }

        // TODO: Implement CustomerDetailsScreen
        return MaterialPageRoute(
          builder: (_) => const Scaffold(
            body: Center(child: Text('Customer Details - Coming Soon')),
          ),
        );

      case editCustomerRoute:
        final Map<String, dynamic>? args =
            settings.arguments as Map<String, dynamic>?;
        final String? customerId = args?['customerId'] as String?;

        if (customerId == null) {
          return MaterialPageRoute(
            builder: (_) => const CustomerListScreen(),
          );
        }

        // TODO: Implement EditCustomerScreen
        return MaterialPageRoute(
          builder: (_) => const Scaffold(
            body: Center(child: Text('Edit Customer - Coming Soon')),
          ),
        );

      case debtListRoute:
        return MaterialPageRoute(
          builder: (_) => const DebtListScreen(),
        );

      case createDebtRoute:
        final Map<String, dynamic>? args =
            settings.arguments as Map<String, dynamic>?;
        final String? customerId = args?['customerId'] as String?;

        return MaterialPageRoute(
          builder: (_) => CreateDebtScreen(preSelectedCustomerId: customerId),
        );

      case debtDetailsRoute:
        final Map<String, dynamic>? args =
            settings.arguments as Map<String, dynamic>?;
        final String? debtId = args?['debtId'] as String?;

        if (debtId == null) {
          return MaterialPageRoute(
            builder: (_) => const DebtListScreen(),
          );
        }

        // TODO: Implement DebtDetailsScreen
        return MaterialPageRoute(
          builder: (_) => const Scaffold(
            body: Center(child: Text('Debt Details - Coming Soon')),
          ),
        );

      case editDebtRoute:
        final Map<String, dynamic>? args =
            settings.arguments as Map<String, dynamic>?;
        final String? debtId = args?['debtId'] as String?;

        if (debtId == null) {
          return MaterialPageRoute(
            builder: (_) => const DebtListScreen(),
          );
        }

        // TODO: Implement EditDebtScreen
        return MaterialPageRoute(
          builder: (_) => const Scaffold(
            body: Center(child: Text('Edit Debt - Coming Soon')),
          ),
        );

      case paymentHistoryRoute:
        final Map<String, dynamic>? args =
            settings.arguments as Map<String, dynamic>?;
        final String? debtId = args?['debtId'] as String?;
        final String? customerName = args?['customerName'] as String?;

        if (debtId == null || customerName == null) {
          return MaterialPageRoute(
            builder: (_) => const DebtListScreen(),
          );
        }

        return MaterialPageRoute(
          builder: (_) => PaymentHistoryScreen(
            debtId: debtId,
            customerName: customerName,
          ),
        );

      case shopSettingsRoute:
        return MaterialPageRoute(
          builder: (_) => const ShopSettingsScreen(),
        );

      case profileSettingsRoute:
        return MaterialPageRoute(
          builder: (_) => const ProfileSettingsScreen(),
        );

      case notificationSettingsRoute:
        return MaterialPageRoute(
          builder: (_) => const NotificationSettingsScreen(),
        );

      case subscriptionRoute:
        return MaterialPageRoute(
          builder: (_) => const SubscriptionScreen(),
        );

      case reportsRoute:
        return MaterialPageRoute(
          builder: (_) => const ReportsMainScreen(),
          settings: settings,
        );

      case customerReportRoute:
        return MaterialPageRoute(
          builder: (_) => const CustomerReportScreen(),
          settings: settings,
        );

      case debtReportRoute:
        return MaterialPageRoute(
          builder: (_) => const DebtReportScreen(),
          settings: settings,
        );

      case riskReportRoute:
        return MaterialPageRoute(
          builder: (_) => const RiskReportScreen(),
          settings: settings,
        );

      case userRoleListRoute:
        return MaterialPageRoute(
          builder: (_) => const UserRoleListScreen(),
          settings: settings,
        );

      case addUserRoleRoute:
        return MaterialPageRoute(
          builder: (_) => const AddUserRoleScreen(),
          settings: settings,
        );

      case editUserRoleRoute:
        final Map<String, dynamic>? args =
            settings.arguments as Map<String, dynamic>?;
        final employee = args?['employee'];

        if (employee == null) {
          return MaterialPageRoute(
            builder: (_) => const Scaffold(
              body: Center(child: Text('Employee data not found')),
            ),
          );
        }

        return MaterialPageRoute(
          builder: (_) => EditUserRoleScreen(employee: employee),
          settings: settings,
        );

      default:
        // If the route is not defined, redirect to splash screen
        return MaterialPageRoute(
          builder: (_) => const Scaffold(
            body: Center(child: Text('Route not found')),
          ),
        );
    }
  }

  // Navigation helper methods for business screens
  static void navigateToCustomerList(BuildContext context,
      {bool replace = false}) {
    if (replace) {
      Navigator.pushReplacementNamed(context, customerListRoute);
    } else {
      Navigator.pushNamed(context, customerListRoute);
    }
  }

  static void navigateToCreateCustomer(BuildContext context) {
    Navigator.pushNamed(context, createCustomerRoute);
  }

  static void navigateToCustomerDetails(BuildContext context,
      {required String customerId}) {
    Navigator.pushNamed(
      context,
      customerDetailsRoute,
      arguments: {'customerId': customerId},
    );
  }

  static void navigateToEditCustomer(BuildContext context,
      {required String customerId}) {
    Navigator.pushNamed(
      context,
      editCustomerRoute,
      arguments: {'customerId': customerId},
    );
  }

  static void navigateToDebtList(BuildContext context, {bool replace = false}) {
    if (replace) {
      Navigator.pushReplacementNamed(context, debtListRoute);
    } else {
      Navigator.pushNamed(context, debtListRoute);
    }
  }

  static void navigateToCreateDebt(BuildContext context, {String? customerId}) {
    Navigator.pushNamed(
      context,
      createDebtRoute,
      arguments: customerId != null ? {'customerId': customerId} : null,
    );
  }

  static void navigateToDebtDetails(BuildContext context,
      {required String debtId}) {
    Navigator.pushNamed(
      context,
      debtDetailsRoute,
      arguments: {'debtId': debtId},
    );
  }

  static void navigateToEditDebt(BuildContext context,
      {required String debtId}) {
    Navigator.pushNamed(
      context,
      editDebtRoute,
      arguments: {'debtId': debtId},
    );
  }

  static void navigateToPaymentHistory(
    BuildContext context, {
    required String debtId,
    required String customerName,
  }) {
    Navigator.pushNamed(
      context,
      paymentHistoryRoute,
      arguments: {
        'debtId': debtId,
        'customerName': customerName,
      },
    );
  }

  // Existing navigation helper methods
  static void navigateToSplash(BuildContext context) {
    Navigator.pushReplacementNamed(context, splashRoute);
  }

  static void navigateToOnboarding(BuildContext context) {
    Navigator.pushReplacementNamed(context, onboardingRoute);
  }

  static void navigateToLogin(BuildContext context, {String? email}) {
    Navigator.pushReplacementNamed(
      context,
      loginRoute,
      arguments: email != null ? {'email': email} : null,
    );
  }

  static void navigateToRegister(BuildContext context) {
    Navigator.pushNamed(context, registerRoute);
  }

  static void navigateToForgotPassword(BuildContext context) {
    Navigator.pushNamed(context, forgotPasswordRoute);
  }

  static void navigateToResetPassword(BuildContext context,
      {required String token}) {
    Navigator.pushNamed(
      context,
      resetPasswordRoute,
      arguments: {'token': token},
    );
  }

  static void navigateToResetSuccess(BuildContext context) {
    Navigator.pushReplacementNamed(context, resetSuccessRoute);
  }

  static void navigateToVerification(BuildContext context,
      {required User user,
      String? selectedPlanId,
      DateTime? expiresAt,
      bool replace = false}) {
    if (replace) {
      Navigator.pushReplacementNamed(
        context,
        verificationRoute,
        arguments: {
          'user': user,
          if (selectedPlanId != null) 'selectedPlanId': selectedPlanId,
          if (expiresAt != null) 'expiresAt': expiresAt,
        },
      );
    } else {
      Navigator.pushNamed(
        context,
        verificationRoute,
        arguments: {
          'user': user,
          if (selectedPlanId != null) 'selectedPlanId': selectedPlanId,
          if (expiresAt != null) 'expiresAt': expiresAt,
        },
      );
    }
  }

  static void navigateToDashboard(BuildContext context,
      {bool replace = false}) {
    if (replace) {
      Navigator.pushReplacementNamed(context, dashboardRoute);
    } else {
      Navigator.pushNamed(context, dashboardRoute);
    }
  }

  static void navigateToPaymentSuccess(BuildContext context,
      {required User user, bool replace = false}) {
    if (replace) {
      Navigator.pushReplacementNamed(
        context,
        paymentSuccessRoute,
        arguments: {'user': user},
      );
    } else {
      Navigator.pushNamed(
        context,
        paymentSuccessRoute,
        arguments: {'user': user},
      );
    }
  }

  static void navigateToPayment(BuildContext context,
      {required User user,
      AuthToken? token,
      required String selectedPlanId,
      bool replace = false}) {
    final arguments = {
      'user': user,
      'token': token,
      'selectedPlanId': selectedPlanId,
    };

    if (replace) {
      Navigator.pushReplacementNamed(context, paymentRoute,
          arguments: arguments);
    } else {
      Navigator.pushNamed(context, paymentRoute, arguments: arguments);
    }
  }

  // Settings navigation helpers
  static void navigateToShopSettings(BuildContext context) {
    Navigator.pushNamed(context, shopSettingsRoute);
  }

  static void navigateToProfileSettings(BuildContext context) {
    Navigator.pushNamed(context, profileSettingsRoute);
  }

  static void navigateToNotificationSettings(BuildContext context) {
    Navigator.pushNamed(context, notificationSettingsRoute);
  }

  // Subscription navigation helpers
  static void navigateToSubscription(BuildContext context) {
    Navigator.pushNamed(context, subscriptionRoute);
  }

  // Add navigation helpers
  static void navigateToReports(BuildContext context) {
    Navigator.pushNamed(context, reportsRoute);
  }

  static void navigateToCustomerReport(BuildContext context) {
    Navigator.pushNamed(context, customerReportRoute);
  }

  static void navigateToDebtReport(BuildContext context) {
    Navigator.pushNamed(context, debtReportRoute);
  }

  static void navigateToRiskReport(BuildContext context) {
    Navigator.pushNamed(context, riskReportRoute);
  }

  // User role management navigation helpers
  static void navigateToUserRoleList(BuildContext context) {
    Navigator.pushNamed(context, userRoleListRoute);
  }

  static void navigateToAddUserRole(BuildContext context) {
    Navigator.pushNamed(context, addUserRoleRoute);
  }

  static Future<dynamic> navigateToEditUserRole(
      BuildContext context, dynamic employee) {
    return Navigator.pushNamed(
      context,
      editUserRoleRoute,
      arguments: {'employee': employee},
    );
  }
}
