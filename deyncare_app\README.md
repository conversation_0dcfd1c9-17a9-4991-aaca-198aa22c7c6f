# deyncare_app

A new Flutter project.

## Getting Started

Before Run the Application you Must DO the 
Following Step 

1. it Will Connectign the Flutter and Backend

C:\platform-tools\adb.exe reverse tcp:5000 tcp:5000

2. testing the Connection is 100% is working by Visting the Api Health in the backend 

C:\platform-tools\adb.exe shell curl http://127.0.0.1:5000/api/health

Or 

 curl http://127.0.0.1:5000/api/health

Response it Will Look Like 

health
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed
100   141  100   141    0     0   4862      0 --:--:-- --:--:-- --:--:--  5035
{"status":"success","message":"DeynCare API is running","environment":"development","timestamp":"2025-06-08T01:41:35.268Z","version":"1.0.0"}

and you Succes to Connecting you Api and <PERSON>utter App !!