import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/blocs/subscription/subscription_bloc.dart';
import 'package:deyncare_app/presentation/blocs/subscription/subscription_event.dart';
import 'package:deyncare_app/presentation/blocs/subscription/subscription_state.dart';
import 'package:deyncare_app/presentation/widgets/common_app_bar.dart';
import 'package:deyncare_app/presentation/widgets/skeleton_loader.dart';
import 'package:deyncare_app/core/utils/api_toast_handler.dart';
import 'package:deyncare_app/domain/models/subscription.dart';
import 'package:deyncare_app/domain/models/plan.dart';

class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  State<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen> {
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeSubscription();
  }

  void _initializeSubscription() {
    if (!_isInitialized) {
      _isInitialized = true;
      context.read<SubscriptionBloc>().add(LoadCurrentSubscription());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppThemes.backgroundColor,
      appBar: const CommonAppBar(
        title: 'My Subscription',
      ),
      body: BlocConsumer<SubscriptionBloc, SubscriptionState>(
        listener: _handleStateChanges,
        builder: (context, state) {
          return RefreshIndicator(
            onRefresh: () async {
              // Add a small delay to prevent rapid refreshes
              await Future.delayed(const Duration(milliseconds: 300));
              context.read<SubscriptionBloc>().add(RefreshSubscription());
            },
            child: _buildBody(context, state),
          );
        },
      ),
    );
  }

  void _handleStateChanges(BuildContext context, SubscriptionState state) {
    if (state is SubscriptionUpdated) {
      ApiToastHandler.handleSuccess(message: state.message);
      
      // Different delays based on operation type
      int delayMs = 500; // Default delay
      if (state.operation == 'cancel') {
        // Cancellation needs more time as backend creates new trial subscription
        delayMs = 2000; // 2 seconds for cancellation
      }
      
      // After a successful update, refresh the subscription data
      Future.delayed(Duration(milliseconds: delayMs), () {
        if (mounted) {
          context.read<SubscriptionBloc>().add(LoadCurrentSubscription());
        }
      });
    } else if (state is PaymentCompleted) {
      ApiToastHandler.handleSuccess(message: state.message);
      // After a successful payment, refresh the subscription data
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          context.read<SubscriptionBloc>().add(LoadCurrentSubscription());
        }
      });
    } else if (state is SubscriptionError) {
      ApiToastHandler.handleError(state.message);
    } else if (state is PaymentFailed) {
      ApiToastHandler.handleError(state.message);
    }
  }

  Widget _buildBody(BuildContext context, SubscriptionState state) {
    if (state is SubscriptionLoading) {
      return _buildLoadingState();
    } else if (state is SubscriptionError) {
      return _buildErrorState(context, state);
    } else if (state is SubscriptionLoaded) {
      return _buildLoadedState(context, state);
    } else if (state is SubscriptionUpdating) {
      return _buildUpdatingState(context, state);
    } else if (state is PaymentProcessing) {
      return _buildPaymentProcessingState(context, state);
    }

    return _buildInitialState(context);
  }

  Widget _buildLoadingState() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          SkeletonLoader(height: 200, borderRadius: BorderRadius.circular(16)),
          const SizedBox(height: 16),
          SkeletonLoader(height: 150, borderRadius: BorderRadius.circular(16)),
          const SizedBox(height: 16),
          SkeletonLoader(height: 300, borderRadius: BorderRadius.circular(16)),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, SubscriptionError state) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to Load Subscription',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              state.message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    context.read<SubscriptionBloc>().add(LoadCurrentSubscription());
                  },
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                ),
                OutlinedButton.icon(
                  onPressed: () {
                    context.read<SubscriptionBloc>().add(ResetSubscriptionState());
                    Future.delayed(const Duration(milliseconds: 100), () {
                      context.read<SubscriptionBloc>().add(LoadCurrentSubscription());
                    });
                  },
                  icon: const Icon(Icons.restart_alt),
                  label: const Text('Reset'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadedState(BuildContext context, SubscriptionLoaded state) {
    final subscription = state.subscription;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Subscription Overview
          _buildSubscriptionOverview(context, subscription),
          const SizedBox(height: 16),

          // Plan Features
          _buildPlanFeatures(context, subscription),
          const SizedBox(height: 16),

          // Subscription Actions
          _buildSubscriptionActions(context, subscription),
          const SizedBox(height: 16),

          // Available Plans (if can upgrade)
          if (subscription.canUpgrade) ...[
            _buildAvailablePlans(context, subscription, state.availablePlans),
            const SizedBox(height: 16),
          ],

          // Change Plan (for active subscriptions)
          if (subscription.status == 'active' && subscription.planType != 'yearly') ...[
            _buildChangePlanSection(context, subscription, state.availablePlans),
            const SizedBox(height: 16),
          ],

          // Additional space at bottom
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSubscriptionOverview(BuildContext context, Subscription subscription) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatusColor(subscription.status),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    subscription.statusDisplayText,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  subscription.planDisplayName,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (subscription.isActive) ...[
              Text(
                subscription.daysRemainingText,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppThemes.textSecondaryColor,
                ),
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: subscription.percentageUsed / 100,
                backgroundColor: AppThemes.dividerColor,
                valueColor: AlwaysStoppedAnimation<Color>(
                  subscription.isExpiringSoon ? AppThemes.warningColor : AppThemes.primaryColor,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subscription.progressText,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppThemes.textTertiaryColor,
                ),
              ),
            ],

            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Price',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppThemes.textSecondaryColor,
                        ),
                      ),
                      Text(
                        '\$${subscription.totalPrice.toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Billing',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppThemes.textSecondaryColor,
                        ),
                      ),
                      Text(
                        subscription.billingCycle.toUpperCase(),
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Expires',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppThemes.textSecondaryColor,
                        ),
                      ),
                      Text(
                        _formatDate(subscription.endDate),
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlanFeatures(BuildContext context, Subscription subscription) {
    final features = _getPlanFeatures(subscription.planType);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Plan Features',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            ...features.map((feature) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    size: 20,
                    color: AppThemes.successColor,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      feature,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildSubscriptionActions(BuildContext context, Subscription subscription) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Subscription Settings',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            // Auto Renewal Toggle
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Auto Renewal',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        'Automatically renew when subscription expires',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppThemes.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: subscription.autoRenew,
                  onChanged: (value) {
                    context.read<SubscriptionBloc>().add(
                      UpdateAutoRenewal(autoRenew: value),
                    );
                  },
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            
            // Action Buttons
            if (subscription.canCancel) ...[
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: () => _showCancelSubscriptionDialog(context),
                  icon: const Icon(Icons.cancel_outlined),
                  label: const Text('Cancel Subscription'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppThemes.errorColor,
                    side: BorderSide(color: AppThemes.errorColor),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAvailablePlans(BuildContext context, Subscription subscription, List<Plan> plans) {
    // Only show upgrade options if subscription is in trial
    if (subscription.status != 'trial') {
      return const SizedBox.shrink();
    }
    
    final availablePlans = plans.where((plan) => 
      plan.isActive && plan.type != subscription.planType).toList();
    
    if (availablePlans.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Upgrade Your Plan',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            ...availablePlans.map((plan) => _buildPlanCard(context, plan, subscription)),
          ],
        ),
      ),
    );
  }

  Widget _buildChangePlanSection(BuildContext context, Subscription subscription, List<Plan> plans) {
    // Only show yearly plan for active monthly subscriptions
    final yearlyPlan = plans.where((plan) => 
      plan.isActive && plan.type == 'yearly').firstOrNull;
    
    if (yearlyPlan == null) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Upgrade to Yearly Plan',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Save money with annual billing',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppThemes.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 16),
            _buildPlanCard(context, yearlyPlan, subscription),
          ],
        ),
      ),
    );
  }

  Widget _buildPlanCard(BuildContext context, Plan plan, Subscription currentSubscription) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          plan.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          plan.priceDisplay,
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ],
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () => _showUpgradeDialog(context, plan),
                    child: const Text('Upgrade'),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                plan.description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppThemes.textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUpdatingState(BuildContext context, SubscriptionUpdating state) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              _getUpdatingMessage(state.operation),
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentProcessingState(BuildContext context, PaymentProcessing state) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'Processing ${state.paymentMethod.toUpperCase()} Payment...',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInitialState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.subscriptions_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Text(
            'Loading Subscription...',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _showUpgradeDialog(BuildContext context, Plan plan) {
    final subscription = (context.read<SubscriptionBloc>().state as SubscriptionLoaded).subscription;
    
    String title;
    String message;
    String actionText;
    
    if (subscription.status == 'trial') {
      title = 'Upgrade to ${plan.name}';
      message = 'Would you like to upgrade to ${plan.name} for ${plan.priceDisplay}?';
      actionText = 'Upgrade';
    } else if (subscription.status == 'active') {
      title = 'Change to ${plan.name}';
      message = 'Would you like to change your plan to ${plan.name} for ${plan.priceDisplay}?';
      actionText = 'Change Plan';
    } else {
      title = 'Plan Change';
      message = 'Plan changes are not available for your current subscription status.';
      actionText = 'OK';
    }
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          if (subscription.status == 'trial' || subscription.status == 'active')
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _initiateUpgrade(context, plan);
              },
              child: Text(actionText),
            ),
        ],
      ),
    );
  }

  void _showCancelSubscriptionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return _CancelSubscriptionDialog();
      },
    );
  }

  void _initiateUpgrade(BuildContext context, Plan plan) {
    final subscription = (context.read<SubscriptionBloc>().state as SubscriptionLoaded).subscription;
    
    if (subscription.status == 'trial') {
      // Request upgrade from trial
      context.read<SubscriptionBloc>().add(
        UpgradeSubscription(
          planType: plan.type,
          message: 'Requesting upgrade to ${plan.name} plan. Current plan: ${subscription.planType}',
        ),
      );
    } else if (subscription.status == 'active') {
      // Change plan for active subscription
      context.read<SubscriptionBloc>().add(
        ChangePlan(
          planId: plan.id,
          planType: plan.type,
          paymentMethod: 'offline',
          paymentDetails: {
            'amount': plan.price,
            'currency': plan.currency,
            'notes': 'Change to ${plan.name}',
          },
        ),
      );
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'trial':
        return AppThemes.warningColor;
      case 'active':
        return AppThemes.successColor;
      case 'past_due':
        return AppThemes.errorColor;
      case 'canceled':
        return AppThemes.textTertiaryColor;
      case 'expired':
        return AppThemes.errorColor;
      default:
        return AppThemes.textSecondaryColor;
    }
  }

  List<String> _getPlanFeatures(String planType) {
    switch (planType) {
      case 'trial':
        return [
          'Up to 10 Customers',
          'Up to 20 Debts',
          '1 User',
          'Basic Analytics',
          'Email Support',
        ];
      case 'monthly':
        return [
          'Up to 100 Customers',
          'Up to 500 Debts',
          '3 Users',
          'AI Risk Analysis',
          'SMS & Email Notifications',
          'Advanced Analytics',
          'Bulk Operations',
          'Data Export',
          'Priority Support',
        ];
      case 'yearly':
        return [
          'Unlimited Customers',
          'Unlimited Debts',
          '10 Users',
          'AI Risk Analysis',
          'SMS & Email Notifications',
          'Advanced Analytics',
          'API Access',
          'Priority Support',
          'Custom Branding',
          'Bulk Operations',
          'Data Export',
        ];
      default:
        return [];
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getUpdatingMessage(String operation) {
    switch (operation) {
      case 'upgrading':
        return 'Upgrading your subscription...';
      case 'changing_plan':
        return 'Changing your plan...';
      case 'canceling':
        return 'Canceling subscription...';
      case 'renewing':
        return 'Renewing subscription...';
      case 'updating_auto_renewal':
        return 'Updating auto-renewal settings...';
      default:
        return 'Updating subscription...';
    }
  }
}

/// Dynamic cancellation dialog with user input and validation
class _CancelSubscriptionDialog extends StatefulWidget {
  @override
  _CancelSubscriptionDialogState createState() => _CancelSubscriptionDialogState();
}

class _CancelSubscriptionDialogState extends State<_CancelSubscriptionDialog> {
  final _feedbackController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  String _selectedReason = 'other';
  bool _isLoading = false;

  // Valid cancellation reasons from backend enum
  static const Map<String, String> _cancellationReasons = {
    'cost': 'Too expensive',
    'features': 'Missing features',
    'competitor': 'Found better alternative',
    'usability': 'Hard to use',
    'support': 'Poor customer support',
    'other': 'Other reason',
  };

  @override
  void dispose() {
    _feedbackController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Cancel Subscription'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'We\'re sorry to see you go! Please help us improve by telling us why you\'re canceling.',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              
              // Reason Selection
              Text(
                'Reason for cancellation:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<String>(
                value: _selectedReason,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: _cancellationReasons.entries.map((entry) {
                  return DropdownMenuItem<String>(
                    value: entry.key,
                    child: Text(entry.value),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedReason = value ?? 'other';
                  });
                },
              ),
              const SizedBox(height: 16),
              
              // Additional Feedback
              Text(
                'Additional feedback (optional):',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _feedbackController,
                maxLines: 4,
                maxLength: 500,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  hintText: 'Tell us more about your experience or what we could improve...',
                  contentPadding: EdgeInsets.all(12),
                ),
                validator: (value) {
                  // Only validate if user entered something
                  if (value != null && value.trim().isNotEmpty && value.trim().length < 10) {
                    return 'Please provide at least 10 characters for meaningful feedback';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 8),
              
              Text(
                '⚠️ This action cannot be undone. Your subscription will remain active until the end of your current billing period.',
                style: TextStyle(
                  fontSize: 12,
                  color: AppThemes.warningColor,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Keep Subscription'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _handleCancellation,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppThemes.errorColor,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('Cancel Subscription'),
        ),
      ],
    );
  }

  void _handleCancellation() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final feedback = _feedbackController.text.trim();
      
      Navigator.of(context).pop();
      
      context.read<SubscriptionBloc>().add(
        CancelSubscription(
          reason: _selectedReason,
          feedback: feedback.isNotEmpty ? feedback : null,
          immediateEffect: false,
        ),
      );
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      // Error handling is done by the bloc
    }
  }
} 