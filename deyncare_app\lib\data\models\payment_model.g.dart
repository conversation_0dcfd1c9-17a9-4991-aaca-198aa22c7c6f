// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaymentModel _$PaymentModelFromJson(Map<String, dynamic> json) => PaymentModel(
      id: json['_id'] as String?,
      paymentId: json['paymentId'] as String,
      amount: (json['amount'] as num).toDouble(),
      paymentMethod: $enumDecode(_$PaymentMethodEnumMap, json['paymentMethod']),
      status: $enumDecode(_$PaymentStatusEnumMap, json['status']),
      context: $enumDecode(_$PaymentContextEnumMap, json['context']),
      referenceId: json['referenceId'] as String,
      description: json['description'] as String?,
      transactionReference: json['transactionReference'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      processedAt: json['processedAt'] == null
          ? null
          : DateTime.parse(json['processedAt'] as String),
      failureReason: json['failureReason'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      evcDetails: json['evcDetails'] == null
          ? null
          : EvcPaymentDetailsModel.fromJson(
              json['evcDetails'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PaymentModelToJson(PaymentModel instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'paymentId': instance.paymentId,
      'amount': instance.amount,
      'paymentMethod': _$PaymentMethodEnumMap[instance.paymentMethod]!,
      'status': _$PaymentStatusEnumMap[instance.status]!,
      'context': _$PaymentContextEnumMap[instance.context]!,
      'referenceId': instance.referenceId,
      'description': instance.description,
      'transactionReference': instance.transactionReference,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'processedAt': instance.processedAt?.toIso8601String(),
      'failureReason': instance.failureReason,
      'metadata': instance.metadata,
      'evcDetails': instance.evcDetails,
    };

const _$PaymentMethodEnumMap = {
  PaymentMethod.cash: 'cash',
  PaymentMethod.bankTransfer: 'bankTransfer',
  PaymentMethod.mobileMoney: 'mobileMoney',
  PaymentMethod.card: 'card',
  PaymentMethod.other: 'other',
};

const _$PaymentStatusEnumMap = {
  PaymentStatus.pending: 'pending',
  PaymentStatus.completed: 'completed',
  PaymentStatus.failed: 'failed',
  PaymentStatus.cancelled: 'cancelled',
};

const _$PaymentContextEnumMap = {
  PaymentContext.debt: 'debt',
  PaymentContext.subscription: 'subscription',
  PaymentContext.pos: 'pos',
};

AddPaymentRequest _$AddPaymentRequestFromJson(Map<String, dynamic> json) =>
    AddPaymentRequest(
      amount: (json['amount'] as num).toDouble(),
      paymentDate: json['paymentDate'] == null
          ? null
          : DateTime.parse(json['paymentDate'] as String),
      paidAtReal: json['paidAtReal'] == null
          ? null
          : DateTime.parse(json['paidAtReal'] as String),
      paymentMethod: json['paymentMethod'] as String? ?? 'cash',
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$AddPaymentRequestToJson(AddPaymentRequest instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'paymentDate': instance.paymentDate?.toIso8601String(),
      'paidAtReal': instance.paidAtReal?.toIso8601String(),
      'paymentMethod': instance.paymentMethod,
      'notes': instance.notes,
    };

PaymentResponse _$PaymentResponseFromJson(Map<String, dynamic> json) =>
    PaymentResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: PaymentResponseData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PaymentResponseToJson(PaymentResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };

PaymentResponseData _$PaymentResponseDataFromJson(Map<String, dynamic> json) =>
    PaymentResponseData(
      payment: PaymentInfo.fromJson(json['payment'] as Map<String, dynamic>),
      debt: DebtInfo.fromJson(json['debt'] as Map<String, dynamic>),
      customer: CustomerInfo.fromJson(json['customer'] as Map<String, dynamic>),
      mlEvaluation: MlEvaluationInfo.fromJson(
          json['mlEvaluation'] as Map<String, dynamic>),
      timeline: TimelineInfo.fromJson(json['timeline'] as Map<String, dynamic>),
      businessLogic: json['businessLogic'] == null
          ? null
          : BusinessLogicInfo.fromJson(
              json['businessLogic'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PaymentResponseDataToJson(
        PaymentResponseData instance) =>
    <String, dynamic>{
      'payment': instance.payment,
      'debt': instance.debt,
      'customer': instance.customer,
      'mlEvaluation': instance.mlEvaluation,
      'timeline': instance.timeline,
      'businessLogic': instance.businessLogic,
    };

PaymentInfo _$PaymentInfoFromJson(Map<String, dynamic> json) => PaymentInfo(
      paymentId: json['paymentId'] as String,
      amount: (json['amount'] as num).toDouble(),
      officialDate: json['officialDate'] == null
          ? null
          : DateTime.parse(json['officialDate'] as String),
      realPaymentDate: json['realPaymentDate'] == null
          ? null
          : DateTime.parse(json['realPaymentDate'] as String),
      date:
          json['date'] == null ? null : DateTime.parse(json['date'] as String),
      method: json['method'] as String,
      timing: json['timing'] as String,
      status: json['status'] as String,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$PaymentInfoToJson(PaymentInfo instance) =>
    <String, dynamic>{
      'paymentId': instance.paymentId,
      'amount': instance.amount,
      'officialDate': instance.officialDate?.toIso8601String(),
      'realPaymentDate': instance.realPaymentDate?.toIso8601String(),
      'date': instance.date?.toIso8601String(),
      'method': instance.method,
      'timing': instance.timing,
      'status': instance.status,
      'notes': instance.notes,
    };

DebtInfo _$DebtInfoFromJson(Map<String, dynamic> json) => DebtInfo(
      debtId: json['debtId'] as String,
      debtID: (json['DebtID'] as num).toInt(),
      originalAmount: (json['originalAmount'] as num).toDouble(),
      outstandingDebt: (json['outstandingDebt'] as num).toDouble(),
      paidAmount: (json['paidAmount'] as num).toDouble(),
      debtPaidRatio: (json['debtPaidRatio'] as num).toDouble(),
      status: json['status'] as String,
      riskLevel: json['riskLevel'] as String,
    );

Map<String, dynamic> _$DebtInfoToJson(DebtInfo instance) => <String, dynamic>{
      'debtId': instance.debtId,
      'DebtID': instance.debtID,
      'originalAmount': instance.originalAmount,
      'outstandingDebt': instance.outstandingDebt,
      'paidAmount': instance.paidAmount,
      'debtPaidRatio': instance.debtPaidRatio,
      'status': instance.status,
      'riskLevel': instance.riskLevel,
    };

CustomerInfo _$CustomerInfoFromJson(Map<String, dynamic> json) => CustomerInfo(
      name: json['name'] as String,
      type: json['type'] as String,
      currentRiskLevel: json['currentRiskLevel'] as String,
      riskScore: (json['riskScore'] as num).toDouble(),
    );

Map<String, dynamic> _$CustomerInfoToJson(CustomerInfo instance) =>
    <String, dynamic>{
      'name': instance.name,
      'type': instance.type,
      'currentRiskLevel': instance.currentRiskLevel,
      'riskScore': instance.riskScore,
    };

MlEvaluationInfo _$MlEvaluationInfoFromJson(Map<String, dynamic> json) =>
    MlEvaluationInfo(
      evaluated: json['evaluated'] as bool,
      reason: json['reason'] as String?,
      riskLevel: json['riskLevel'] as String?,
      riskScore: (json['riskScore'] as num?)?.toDouble(),
      factors:
          (json['factors'] as List<dynamic>?)?.map((e) => e as String).toList(),
      message: json['message'] as String,
      details: json['details'] as Map<String, dynamic>?,
      error: json['error'] as String?,
    );

Map<String, dynamic> _$MlEvaluationInfoToJson(MlEvaluationInfo instance) =>
    <String, dynamic>{
      'evaluated': instance.evaluated,
      'reason': instance.reason,
      'riskLevel': instance.riskLevel,
      'riskScore': instance.riskScore,
      'factors': instance.factors,
      'message': instance.message,
      'details': instance.details,
      'error': instance.error,
    };

TimelineInfo _$TimelineInfoFromJson(Map<String, dynamic> json) => TimelineInfo(
      dueDatePassed: json['dueDatePassed'] as bool,
      dueDate: DateTime.parse(json['dueDate'] as String),
      officialPaymentDate: json['officialPaymentDate'] == null
          ? null
          : DateTime.parse(json['officialPaymentDate'] as String),
      realPaymentDate: json['realPaymentDate'] == null
          ? null
          : DateTime.parse(json['realPaymentDate'] as String),
      paymentDate: json['paymentDate'] == null
          ? null
          : DateTime.parse(json['paymentDate'] as String),
      paymentDelay: (json['paymentDelay'] as num).toInt(),
      daysUntilDue: (json['daysUntilDue'] as num?)?.toInt(),
    );

Map<String, dynamic> _$TimelineInfoToJson(TimelineInfo instance) =>
    <String, dynamic>{
      'dueDatePassed': instance.dueDatePassed,
      'dueDate': instance.dueDate.toIso8601String(),
      'officialPaymentDate': instance.officialPaymentDate?.toIso8601String(),
      'realPaymentDate': instance.realPaymentDate?.toIso8601String(),
      'paymentDate': instance.paymentDate?.toIso8601String(),
      'paymentDelay': instance.paymentDelay,
      'daysUntilDue': instance.daysUntilDue,
    };

BusinessLogicInfo _$BusinessLogicInfoFromJson(Map<String, dynamic> json) =>
    BusinessLogicInfo(
      mlTriggered: json['mlTriggered'] as bool,
      triggerReason: json['triggerReason'] as String,
      earlyPaymentSupport: json['earlyPaymentSupport'] as bool,
      partialPaymentSupport: json['partialPaymentSupport'] as bool,
      dualTimeTracking: json['dualTimeTracking'] as bool,
    );

Map<String, dynamic> _$BusinessLogicInfoToJson(BusinessLogicInfo instance) =>
    <String, dynamic>{
      'mlTriggered': instance.mlTriggered,
      'triggerReason': instance.triggerReason,
      'earlyPaymentSupport': instance.earlyPaymentSupport,
      'partialPaymentSupport': instance.partialPaymentSupport,
      'dualTimeTracking': instance.dualTimeTracking,
    };

EvcPaymentDetailsModel _$EvcPaymentDetailsModelFromJson(
        Map<String, dynamic> json) =>
    EvcPaymentDetailsModel(
      phoneNumber: json['phoneNumber'] as String,
      sessionId: json['sessionId'] as String?,
      requestId: json['requestId'] as String?,
      transactionId: json['transactionId'] as String?,
      providerResponse: json['providerResponse'] as String?,
      requestedAt: json['requestedAt'] == null
          ? null
          : DateTime.parse(json['requestedAt'] as String),
      confirmedAt: json['confirmedAt'] == null
          ? null
          : DateTime.parse(json['confirmedAt'] as String),
    );

Map<String, dynamic> _$EvcPaymentDetailsModelToJson(
        EvcPaymentDetailsModel instance) =>
    <String, dynamic>{
      'phoneNumber': instance.phoneNumber,
      'sessionId': instance.sessionId,
      'requestId': instance.requestId,
      'transactionId': instance.transactionId,
      'providerResponse': instance.providerResponse,
      'requestedAt': instance.requestedAt?.toIso8601String(),
      'confirmedAt': instance.confirmedAt?.toIso8601String(),
    };
