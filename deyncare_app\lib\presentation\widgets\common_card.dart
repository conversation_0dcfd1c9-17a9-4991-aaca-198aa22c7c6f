import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';

/// Enhanced Common Card Widget with Modern Design
class CommonCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? borderRadius;
  final bool hasShadow;
  final bool isInteractive;
  final VoidCallback? onTap;
  final double? elevation;
  final EdgeInsets? margin;

  const CommonCard({
    super.key,
    required this.child,
    this.padding,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius,
    this.hasShadow = true,
    this.isInteractive = false,
    this.onTap,
    this.elevation,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveBorderRadius = borderRadius ?? 16.0;
    final effectivePadding = padding ?? const EdgeInsets.all(20.0);
    final effectiveBackgroundColor = backgroundColor ?? Theme.of(context).colorScheme.surface;
    final effectiveBorderColor = borderColor ?? Theme.of(context).colorScheme.outline.withOpacity(0.2);

    Widget cardContent = Container(
      margin: margin,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(effectiveBorderRadius),
        border: Border.all(
          color: effectiveBorderColor,
          width: 1,
        ),
        boxShadow: hasShadow ? [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withOpacity(0.08),
            blurRadius: elevation ?? 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withOpacity(0.04),
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ] : null,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(effectiveBorderRadius),
        child: Padding(
          padding: effectivePadding,
          child: child,
        ),
      ),
    );

    if (isInteractive && onTap != null) {
      return Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(effectiveBorderRadius),
          child: cardContent,
        ),
      );
    }

    return cardContent;
  }
}

/// Enhanced Card with Header
class CommonCardWithHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget child;
  final IconData? headerIcon;
  final Color? headerIconColor;
  final List<Widget>? headerActions;
  final EdgeInsets? padding;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? borderRadius;
  final bool hasShadow;
  final bool isInteractive;
  final VoidCallback? onTap;

  const CommonCardWithHeader({
    super.key,
    required this.title,
    required this.child,
    this.subtitle,
    this.headerIcon,
    this.headerIconColor,
    this.headerActions,
    this.padding,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius,
    this.hasShadow = true,
    this.isInteractive = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveBorderRadius = borderRadius ?? 16.0;
    final effectivePadding = padding ?? const EdgeInsets.all(20.0);
    final effectiveBackgroundColor = backgroundColor ?? Theme.of(context).colorScheme.surface;
    final effectiveBorderColor = borderColor ?? Theme.of(context).colorScheme.outline.withOpacity(0.2);

    return CommonCard(
      padding: EdgeInsets.zero,
      backgroundColor: effectiveBackgroundColor,
      borderColor: effectiveBorderColor,
      borderRadius: effectiveBorderRadius,
      hasShadow: hasShadow,
      isInteractive: isInteractive,
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Section
          Container(
            padding: effectivePadding,
            decoration: BoxDecoration(
              color: effectiveBackgroundColor,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                if (headerIcon != null) ...[
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: (headerIconColor ?? Theme.of(context).colorScheme.primary).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      headerIcon,
                      size: 20,
                      color: headerIconColor ?? Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                          letterSpacing: -0.3,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          subtitle!,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                            height: 1.4,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                if (headerActions != null) ...[
                  const SizedBox(width: 12),
                  ...headerActions!,
                ],
              ],
            ),
          ),
          
          // Content Section
          Padding(
            padding: effectivePadding,
            child: child,
          ),
        ],
      ),
    );
  }
}

/// Enhanced Expandable Card
class CommonExpandableCard extends StatefulWidget {
  final String title;
  final String? subtitle;
  final Widget child;
  final IconData? headerIcon;
  final Color? headerIconColor;
  final bool initiallyExpanded;
  final EdgeInsets? padding;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? borderRadius;
  final bool hasShadow;

  const CommonExpandableCard({
    super.key,
    required this.title,
    required this.child,
    this.subtitle,
    this.headerIcon,
    this.headerIconColor,
    this.initiallyExpanded = false,
    this.padding,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius,
    this.hasShadow = true,
  });

  @override
  State<CommonExpandableCard> createState() => _CommonExpandableCardState();
}

class _CommonExpandableCardState extends State<CommonExpandableCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  late bool _isExpanded;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    if (_isExpanded) {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final effectiveBorderRadius = widget.borderRadius ?? 16.0;
    final effectivePadding = widget.padding ?? const EdgeInsets.all(20.0);
    final effectiveBackgroundColor = widget.backgroundColor ?? AppThemes.cardColor;
    final effectiveBorderColor = widget.borderColor ?? AppThemes.dividerColor;

    return CommonCard(
      padding: EdgeInsets.zero,
      backgroundColor: effectiveBackgroundColor,
      borderColor: effectiveBorderColor,
      borderRadius: effectiveBorderRadius,
      hasShadow: widget.hasShadow,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Section
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _toggleExpansion,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(effectiveBorderRadius),
                topRight: Radius.circular(effectiveBorderRadius),
                bottomLeft: _isExpanded ? Radius.zero : Radius.circular(effectiveBorderRadius),
                bottomRight: _isExpanded ? Radius.zero : Radius.circular(effectiveBorderRadius),
              ),
              child: Container(
                padding: effectivePadding,
                decoration: BoxDecoration(
                  border: _isExpanded ? Border(
                    bottom: BorderSide(
                      color: AppThemes.dividerColor,
                      width: 1,
                    ),
                  ) : null,
                ),
                child: Row(
                  children: [
                    if (widget.headerIcon != null) ...[
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: (widget.headerIconColor ?? Theme.of(context).colorScheme.primary).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(24),
                        ),
                        child: Icon(
                          widget.headerIcon,
                          size: 24,
                          color: widget.headerIconColor ?? Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const SizedBox(width: 12),
                    ],
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.title,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                              letterSpacing: -0.3,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          if (widget.subtitle != null) ...[
                            const SizedBox(height: 4),
                            Text(
                              widget.subtitle!,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                                height: 1.4,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    const SizedBox(width: 12),
                    AnimatedRotation(
                      turns: _isExpanded ? 0.5 : 0,
                      duration: const Duration(milliseconds: 300),
                      child: Icon(
                        Icons.expand_more_rounded,
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Content Section
          SizeTransition(
            sizeFactor: _animation,
            child: Padding(
              padding: effectivePadding,
              child: widget.child,
            ),
          ),
        ],
      ),
    );
  }
}

/// Status card for displaying status information
class StatusCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;
  final String? subtitle;
  final bool isCompact;

  const StatusCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.onTap,
    this.subtitle,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: EdgeInsets.all(isCompact ? 12 : 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon and arrow row
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(isCompact ? 8 : 12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            color.withValues(alpha: 0.15),
                            color.withValues(alpha: 0.08),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: color.withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        icon,
                        color: color,
                        size: isCompact ? 20 : 24,
                      ),
                    ),
                    const Spacer(),
                    if (onTap != null)
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.05),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Icon(
                          Icons.arrow_forward_ios_rounded,
                          size: 12,
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4),
                        ),
                      ),
                  ],
                ),
                
                SizedBox(height: isCompact ? 8 : 12),
                
                // Value with animated effect
                Text(
                  value,
                  style: TextStyle(
                    fontSize: isCompact ? 20 : 28,
                    fontWeight: FontWeight.w700,
                    color: Theme.of(context).colorScheme.onSurface,
                    letterSpacing: -0.5,
                  ),
                ),
                
                SizedBox(height: isCompact ? 2 : 4),
                
                // Title
                Text(
                  title,
                  style: TextStyle(
                    fontSize: isCompact ? 12 : 14,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    letterSpacing: 0.1,
                  ),
                ),
                
                // Optional subtitle
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle!,
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w400,
                      color: color.withValues(alpha: 0.8),
                      letterSpacing: 0.1,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
} 