import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:deyncare_app/domain/models/shop.dart';
import 'package:deyncare_app/data/models/shop_model.dart';
import 'package:deyncare_app/data/services/api_client.dart';

/// Shop repository implementation for API operations
class ShopRepositoryImpl {
  final ApiClient _apiClient;

  ShopRepositoryImpl({ApiClient? apiClient})
      : _apiClient = apiClient ?? ApiClient();

  /// Get shop by ID from API
  Future<Shop> getShopById(String shopId) async {
    try {
      final response = await _apiClient.get('/shops/$shopId');
      
      if (response is Map<String, dynamic> && 
          response['success'] == true && 
          response['data'] != null) {
        final shopModel = ShopModel.fromJson(response['data']);
        return shopModel.toDomain();
      } else {
        throw ApiException(
          message: 'Failed to load shop data',
          statusCode: 500,
          code: 'shop_load_error',
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: 'Failed to load shop: ${e.toString()}',
        statusCode: 500,
        code: 'shop_load_error',
      );
    }
  }

  /// Update shop information via API
  Future<Shop> updateShop(String shopId, Map<String, dynamic> updateData) async {
    try {
      print('🔍 DEYNCARE_DEBUG: ShopRepository: Updating shop $shopId with data: $updateData');
      final response = await _apiClient.put('/shops/$shopId', data: updateData);
      print('🔍 DEYNCARE_DEBUG: ShopRepository: Response received: $response');
      
      if (response is Map<String, dynamic> && 
          response['success'] == true && 
          response['data'] != null) {
        final shopModel = ShopModel.fromJson(response['data']);
        return shopModel.toDomain();
      } else {
        throw ApiException(
          message: 'Failed to update shop',
          statusCode: 500,
          code: 'shop_update_error',
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: 'Failed to update shop: ${e.toString()}',
        statusCode: 500,
        code: 'shop_update_error',
      );
    }
  }

  /// Upload shop logo via API
  Future<Shop> uploadShopLogo(String shopId, File logoFile) async {
    try {
      final response = await _apiClient.uploadFile(
        '/shops/$shopId/logo',
        file: logoFile,
        fieldName: 'logo',
      );
      
      if (response is Map<String, dynamic> && 
          response['success'] == true && 
          response['data'] != null) {
        final shopModel = ShopModel.fromJson(response['data']);
        return shopModel.toDomain();
      } else {
        throw ApiException(
          message: 'Failed to upload shop logo',
          statusCode: 500,
          code: 'logo_upload_error',
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: 'Failed to upload logo: ${e.toString()}',
        statusCode: 500,
        code: 'logo_upload_error',
      );
    }
  }
} 