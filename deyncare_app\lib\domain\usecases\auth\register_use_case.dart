import 'dart:io';
import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/domain/repositories/auth_repository.dart';
import 'package:deyncare_app/domain/models/registration_progress.dart';

/// Use case for registering a new shop owner (admin) user
///
/// This class handles the registration of a new shop owner with all
/// required information for both user and shop creation.
class RegisterUseCase {
  final AuthRepository _repository;

  /// Creates a new instance with the required repository
  RegisterUseCase(this._repository);

  /// Executes the registration with all required parameters
  ///
  /// Returns the created User object and RegistrationProgress upon successful registration
  /// Throws exceptions for registration failures
  Future<(User, RegistrationProgress)> execute({
    required String fullName,
    required String email,
    required String phone,
    required String password,
    required String shopName,
    required String shopAddress,
    File? shopLogo,
    String planType = 'trial',
    String paymentMethod = 'offline',
    bool initialPaid = false,
    String? discountCode,
  }) async {
    return await _repository.register(
      fullName: fullName,
      email: email,
      phone: phone,
      password: password,
      shopName: shopName,
      shopAddress: shopAddress,
      shopLogo: shopLogo,
      planType: planType,
      paymentMethod: paymentMethod,
      initialPaid: initialPaid,
      discountCode: discountCode,
    );
  }
}
