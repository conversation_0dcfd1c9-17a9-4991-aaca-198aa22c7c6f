# Enhanced Payment Recording Business Logic Tests

## Test Scenarios Overview

New Business Logic Features Implemented:
1. Early Payment ML Evaluation - ML triggers for full payments before due date
2. Partial Payment ML Evaluation - ML triggers for ≥50% payments  
3. Dual Time Tracking - Server time + staff-recorded real payment time
4. Enhanced Risk Assessment - Supports all payment scenarios

## Postman Test Setup

### Get Authentication Token
POST http://localhost:3000/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your_password"
}

## Test Scenarios Using Your Actual Data

### Scenario 1: Early Full Payment (DEBT004 - Carab)
Test ML evaluation for early full payment

POST http://localhost:3000/api/debts/DEBT004/payments
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "amount": 1100,
  "paymentDate": "2025-01-15T10:00:00Z",
  "paidAtReal": "2025-01-14T16:30:00Z",
  "paymentMethod": "cash",
  "notes": "Early payment - customer paid yesterday evening"
}

Expected Result:
- ML Evaluation: mlTriggered: true
- Trigger Reason: "Early full payment completed"
- Risk Level: "Low Risk" (early full payment)
- Dual Time Tracking: Both officialDate and realPaymentDate

### Scenario 2: Significant Partial Payment (DEBT005 - Ikran)
Test ML evaluation for 50%+ partial payment

POST http://localhost:3000/api/debts/DEBT005/payments
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "amount": 350,
  "paymentMethod": "mobile_money",
  "notes": "First payment - 58% of total debt"
}

Expected Result:
- ML Evaluation: mlTriggered: true
- Trigger Reason: "Significant partial payment (≥50%)"
- Payment Status: "Significant Partial Payment ⚠️"
- Debt Paid Ratio: 0.58

### Scenario 3: Minimal Partial Payment (DEBT006 - Khalid)
Test no ML evaluation for small partial payment

POST http://localhost:3000/api/debts/DEBT006/payments
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "amount": 150,
  "paymentMethod": "cash",
  "notes": "Small payment - 30% of total debt"
}

Expected Result:
- ML Evaluation: mlTriggered: false
- Message: "ML evaluation pending"
- Payment Status: "Minimal Payment 💰"
- Amount needed for ML: $100 (to reach 50%)

### Scenario 4: Late Full Payment (DEBT007 - Naji)
Test ML evaluation for late payment after due date

POST http://localhost:3000/api/debts/DEBT007/payments
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "amount": 800,
  "paymentDate": "2025-08-05T14:00:00Z",
  "paidAtReal": "2025-08-05T09:30:00Z",
  "paymentMethod": "bank_transfer",
  "notes": "Late payment - after due date (2025-07-30)"
}

Expected Result:
- ML Evaluation: mlTriggered: true
- Trigger Reason: "Late full payment completed"
- Payment Delay: 6 days late
- Risk Level: Based on ML evaluation

### Scenario 5: Dual Time Tracking Test
Test real vs official payment time difference

POST http://localhost:3000/api/debts/DEBT009/payments
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "amount": 450,
  "paymentDate": "2025-01-15T08:00:00Z",
  "paidAtReal": "2025-01-12T17:45:00Z",
  "paymentMethod": "mobile_money",
  "notes": "Customer paid 3 days ago, recording now"
}

Expected Result:
- Official Date: 2025-01-15T08:00:00Z (server time)
- Real Payment Date: 2025-01-12T17:45:00Z (business logic time)
- Payment Delay calculated using real time
- Dual Time Tracking: true

## ML Service Confirmation

ML API Call Verification:
The backend will call the ML service with these 4 core fields:

{
  "DebtPaidRatio": 1.0,
  "PaymentDelay": -1,
  "OutstandingDebt": 0,
  "DebtAmount": 1100
}

ML Evaluation Triggers:
1. Full Payment (any time): DebtPaidRatio >= 1.0
2. Significant Partial: DebtPaidRatio >= 0.5 && < 1.0
3. Due Date Passed: now > dueDate

## Test Checklist

- Early payment triggers ML evaluation
- Partial payment (≥50%) triggers ML evaluation
- Minimal payment (<50%) does NOT trigger ML
- Dual time tracking works correctly
- Payment timing calculated using real payment time
- Risk levels update based on ML results
- Business logic info included in response
- Server time vs real time properly separated

Ready to test! Start with Scenario 1 (Early Payment) and work through each scenario to verify the enhanced business logic. 