import 'package:deyncare_app/data/models/payment_model.dart';

/// Remote data source interface for payment API calls
abstract class PaymentRemoteDataSource {
  /// Create a new payment
  Future<PaymentModel> createPayment(Map<String, dynamic> paymentData);

  /// Get payments with pagination and filters
  Future<Map<String, dynamic>> getPayments(Map<String, dynamic> queryParams);

  /// Get payment by ID
  Future<PaymentModel> getPaymentById(String paymentId);

  /// Confirm payment
  Future<PaymentModel> confirmPayment(String paymentId, Map<String, dynamic> confirmationData);

  /// Process EVC mobile money payment
  Future<Map<String, dynamic>> processEvcPayment(Map<String, dynamic> evcData);

  /// Verify payment status
  Future<PaymentModel> verifyPayment(String paymentId);

  /// Refund payment
  Future<PaymentModel> refundPayment(String paymentId, Map<String, dynamic> refundData);

  /// Get payment analytics
  Future<Map<String, dynamic>> getPaymentAnalytics(Map<String, dynamic> queryParams);

  /// Get payment history for specific reference
  Future<List<PaymentModel>> getPaymentHistory(String referenceId, String context);
} 