import 'package:deyncare_app/domain/models/auth_token.dart';

/// Data model for authentication tokens with JSON serialization/deserialization
class AuthTokenModel extends AuthToken {
  AuthTokenModel({
    required super.accessToken,
    required super.refreshToken,
    required super.expiresAt,
  });

  /// Create AuthTokenModel from JSON map
  factory AuthTokenModel.fromJson(Map<String, dynamic> json) {
    // Calculate expires at - default to 2 hours if not provided to match backend
    final int expiresIn = json['expiresIn'] ?? 7200; // 2 hours in seconds
    final DateTime expiresAt = DateTime.now().add(Duration(seconds: expiresIn));

    return AuthTokenModel(
      accessToken: json['accessToken'] ?? '',
      refreshToken: json['refreshToken'] ?? '',
      expiresAt: expiresAt,
    );
  }

  /// Convert AuthTokenModel to JSON map
  Map<String, dynamic> toJson() {
    return {
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'expiresAt': expiresAt.toIso8601String(),
    };
  }

  /// Convert to domain AuthToken entity
  AuthToken toDomain() {
    return AuthToken(
      accessToken: accessToken,
      refreshToken: refreshToken,
      expiresAt: expiresAt,
    );
  }

  /// Create a copy with updated values
  @override
  AuthTokenModel copyWith({
    String? accessToken,
    String? refreshToken,
    DateTime? expiresAt,
  }) {
    return AuthTokenModel(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }
}
