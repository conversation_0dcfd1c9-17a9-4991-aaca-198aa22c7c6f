import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';

import 'package:deyncare_app/presentation/screens/auth/login/login_screen.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_state.dart';

// Create a mock AuthBloc class for our tests
class MockAuthBloc extends Mock implements AuthBloc {}

void main() {
  late MockAuthBloc mockAuthBloc;

  setUp(() {
    // Initialize mock auth bloc
    mockAuthBloc = MockAuthBloc();
    
    // Set up default behavior
    when(mockAuthBloc.state).thenReturn(AuthUnauthenticated());
  });

  tearDown(() {
    // Clean up resources
    reset(mockAuthBloc);
  });
  
  group('LoginScreen', () {
    testWidgets('renders correctly with auth header and form', (WidgetTester tester) async {
      // Create a mock AuthBloc to avoid dependency issues
      final mockAuthBloc = MockAuthBloc();
      
      // Stub common states and methods
      when(mockAuthBloc.state).thenReturn(AuthUnauthenticated());
      
      // Build login screen with the mock bloc
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AuthBloc>.value(
            value: mockAuthBloc,
            child: const LoginScreen(),
          ),
        ),
      );
      
      // Verify login screen renders properly
      expect(find.byType(LoginScreen), findsOneWidget);
      
      // App Header should be shown
      expect(find.text('Login'), findsOneWidget);
      
      // Form fields should be present
      expect(find.byType(TextFormField), findsWidgets);
      
      // Login button should be present
      expect(find.byType(ElevatedButton), findsWidgets);
    });
    
    testWidgets('form validates input correctly', (WidgetTester tester) async {
      // Create a mock AuthBloc to avoid dependency issues
      final mockAuthBloc = MockAuthBloc();
      
      // Stub common states and methods
      when(mockAuthBloc.state).thenReturn(AuthUnauthenticated());
      
      // Build login screen with the mock bloc
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AuthBloc>.value(
            value: mockAuthBloc,
            child: const LoginScreen(),
          ),
        ),
      );
      
      // Find the login form and submit it without entering any data
      final loginButton = find.byType(ElevatedButton);
      await tester.tap(loginButton);
      await tester.pump();
      
      // Check for validation messages (empty fields should trigger validation)
      expect(find.text('Email is required'), findsOneWidget);
      expect(find.text('Password is required'), findsOneWidget);
    });
    
    testWidgets('can enter email and password', (WidgetTester tester) async {
      // Create a mock AuthBloc to avoid dependency issues
      final mockAuthBloc = MockAuthBloc();
      
      // Stub common states and methods
      when(mockAuthBloc.state).thenReturn(AuthUnauthenticated());
      
      // Build login screen with the mock bloc
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AuthBloc>.value(
            value: mockAuthBloc,
            child: const LoginScreen(),
          ),
        ),
      );
      
      // Find email and password fields
      final emailField = find.byType(TextFormField).at(0);
      final passwordField = find.byType(TextFormField).at(1);
      
      // Enter valid credentials
      await tester.enterText(emailField, '<EMAIL>');
      await tester.enterText(passwordField, 'password123');
      await tester.pump();
      
      // Verify text was entered correctly
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.text('password123'), findsOneWidget);
    });
    
    testWidgets('shows forgot password option', (WidgetTester tester) async {
      // Create a mock AuthBloc to avoid dependency issues
      final mockAuthBloc = MockAuthBloc();
      
      // Stub common states and methods
      when(mockAuthBloc.state).thenReturn(AuthUnauthenticated());
      
      // Build login screen with the mock bloc
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AuthBloc>.value(
            value: mockAuthBloc,
            child: const LoginScreen(),
          ),
        ),
      );
      
      // Look for forgot password text
      expect(find.textContaining('Forgot'), findsOneWidget);
    });
    
    testWidgets('shows registration link', (WidgetTester tester) async {
      // Create a mock AuthBloc to avoid dependency issues
      final mockAuthBloc = MockAuthBloc();
      
      // Stub common states and methods
      when(mockAuthBloc.state).thenReturn(AuthUnauthenticated());
      
      // Build login screen with the mock bloc
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AuthBloc>.value(
            value: mockAuthBloc,
            child: const LoginScreen(),
          ),
        ),
      );
      
      // Look for registration link text
      expect(find.textContaining('Sign up'), findsOneWidget);
    });
  });
}
