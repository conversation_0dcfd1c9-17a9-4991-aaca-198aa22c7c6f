import 'package:deyncare_app/domain/repositories/auth_repository.dart';

/// Use case for logging out a user
///
/// This class is responsible for signing out the current user
/// and clearing authentication tokens.
class LogoutUseCase {
  final AuthRepository _repository;

  /// Creates a new instance with the required repository
  LogoutUseCase(this._repository);

  /// Executes the logout use case
  ///
  /// Returns a Future<void> that completes when the logout is successful
  /// Throws exceptions if logout fails
  Future<void> execute() async {
    await _repository.logout();
  }
  
  /// Executes logout from all devices
  ///
  /// Clears all active sessions for the current user across all devices
  Future<void> executeLogoutAll() async {
    await _repository.logoutAll();
  }
}
