import 'package:deyncare_app/domain/repositories/auth_repository.dart';
import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/domain/models/registration_progress.dart';

/// Use case for email verification
///
/// This class is responsible for verifying a user's email address
/// using the verification code sent to them during registration.
class VerifyEmailUseCase {
  final AuthRepository _repository;

  /// Creates a new instance with the required repository
  VerifyEmailUseCase(this._repository);

  /// Executes the email verification with email and verification code
  ///
  /// Returns a Future<(User, RegistrationProgress)> that completes when verification is successful
  /// Throws exceptions if verification fails (invalid/expired code)
  Future<(User, RegistrationProgress)> execute(String email, String verificationCode) async {
    return await _repository.verifyEmail(email, verificationCode);
  }
  
  /// Resends verification code to the user's email
  ///
  /// Returns a Future<void> that completes when a new code is sent
  Future<void> resendVerification(String email) async {
    await _repository.resendVerification(email);
  }
}
