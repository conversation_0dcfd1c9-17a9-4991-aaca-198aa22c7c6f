import 'package:equatable/equatable.dart';

/// Domain entity for Payment
class Payment extends Equatable {
  final String id;
  final String paymentId;
  final double amount;
  final PaymentMethod paymentMethod;
  final PaymentStatus status;
  final PaymentContext context;
  final String referenceId;
  final String? description;
  final String? transactionReference;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? processedAt;
  final String? failureReason;
  final Map<String, dynamic>? metadata;
  final EvcPaymentDetails? evcDetails;

  const Payment({
    required this.id,
    required this.paymentId,
    required this.amount,
    required this.paymentMethod,
    required this.status,
    required this.context,
    required this.referenceId,
    this.description,
    this.transactionReference,
    required this.createdAt,
    required this.updatedAt,
    this.processedAt,
    this.failureReason,
    this.metadata,
    this.evcDetails,
  });

  @override
  List<Object?> get props => [
        id,
        paymentId,
        amount,
        paymentMethod,
        status,
        context,
        referenceId,
        description,
        transactionReference,
        createdAt,
        updatedAt,
        processedAt,
        failureReason,
        metadata,
        evcDetails,
      ];

  bool get isSuccessful => status == PaymentStatus.completed;
  bool get isPending => status == PaymentStatus.pending;
  bool get isFailed => status == PaymentStatus.failed;

  Payment copyWith({
    String? id,
    String? paymentId,
    double? amount,
    PaymentMethod? paymentMethod,
    PaymentStatus? status,
    PaymentContext? context,
    String? referenceId,
    String? description,
    String? transactionReference,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? processedAt,
    String? failureReason,
    Map<String, dynamic>? metadata,
    EvcPaymentDetails? evcDetails,
  }) {
    return Payment(
      id: id ?? this.id,
      paymentId: paymentId ?? this.paymentId,
      amount: amount ?? this.amount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      status: status ?? this.status,
      context: context ?? this.context,
      referenceId: referenceId ?? this.referenceId,
      description: description ?? this.description,
      transactionReference: transactionReference ?? this.transactionReference,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      processedAt: processedAt ?? this.processedAt,
      failureReason: failureReason ?? this.failureReason,
      metadata: metadata ?? this.metadata,
      evcDetails: evcDetails ?? this.evcDetails,
    );
  }
}

/// EVC payment details domain entity
class EvcPaymentDetails extends Equatable {
  final String phoneNumber;
  final String? sessionId;
  final String? requestId;
  final String? transactionId;
  final String? providerResponse;
  final DateTime? requestedAt;
  final DateTime? confirmedAt;

  const EvcPaymentDetails({
    required this.phoneNumber,
    this.sessionId,
    this.requestId,
    this.transactionId,
    this.providerResponse,
    this.requestedAt,
    this.confirmedAt,
  });

  @override
  List<Object?> get props => [
        phoneNumber,
        sessionId,
        requestId,
        transactionId,
        providerResponse,
        requestedAt,
        confirmedAt,
      ];
}

/// Payment method enumeration - BACKEND ALIGNED
enum PaymentMethod {
  cash,
  bankTransfer,
  mobileMoney,
  card,
  other;

  String get displayName {
    switch (this) {
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethod.mobileMoney:
        return 'Mobile Money';
      case PaymentMethod.card:
        return 'Card';
      case PaymentMethod.other:
        return 'Other';
    }
  }

  String get apiValue {
    switch (this) {
      case PaymentMethod.cash:
        return 'cash';
      case PaymentMethod.bankTransfer:
        return 'bank_transfer';
      case PaymentMethod.mobileMoney:
        return 'mobile_money';
      case PaymentMethod.card:
        return 'card';
      case PaymentMethod.other:
        return 'other';
    }
  }

  String get value => apiValue;

  static PaymentMethod fromApiValue(String apiValue) {
    switch (apiValue) {
      case 'cash':
        return PaymentMethod.cash;
      case 'bank_transfer':
        return PaymentMethod.bankTransfer;
      case 'mobile_money':
        return PaymentMethod.mobileMoney;
      case 'card':
        return PaymentMethod.card;
      case 'other':
        return PaymentMethod.other;
      default:
        return PaymentMethod.other;
    }
  }
}

/// Payment status enumeration
enum PaymentStatus {
  pending,
  completed,
  failed,
  cancelled;

  String get displayName {
    switch (this) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.completed:
        return 'Completed';
      case PaymentStatus.failed:
        return 'Failed';
      case PaymentStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get apiValue {
    switch (this) {
      case PaymentStatus.pending:
        return 'pending';
      case PaymentStatus.completed:
        return 'completed';
      case PaymentStatus.failed:
        return 'failed';
      case PaymentStatus.cancelled:
        return 'cancelled';
    }
  }

  bool get isSuccessful => this == PaymentStatus.completed;
  bool get isFinal => this == PaymentStatus.completed || this == PaymentStatus.failed || this == PaymentStatus.cancelled;
}

/// Payment context enumeration
enum PaymentContext {
  debt,
  subscription,
  pos;

  String get displayName {
    switch (this) {
      case PaymentContext.debt:
        return 'Debt Payment';
      case PaymentContext.subscription:
        return 'Subscription Payment';
      case PaymentContext.pos:
        return 'POS Payment';
    }
  }

  String get apiValue {
    switch (this) {
      case PaymentContext.debt:
        return 'debt';
      case PaymentContext.subscription:
        return 'subscription';
      case PaymentContext.pos:
        return 'pos';
    }
  }
} 