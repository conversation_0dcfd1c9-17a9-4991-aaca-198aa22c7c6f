# DeynCare Mobile App - Hybrid Password Reset Implementation

## Overview

This document outlines the hybrid approach for implementing password reset functionality in the DeynCare mobile application. Instead of implementing the complete password reset flow within the app, we leverage the existing web implementation with deep linking for a more secure and consistent user experience.

## Architecture

The hybrid password reset flow involves three components:

1. **Mobile App**: Initiates the password reset request and handles deep link returns
2. **Backend API**: Processes requests and sends emails with reset links
3. **Web Frontend**: Handles the actual password reset UI and redirects back to the app

```
┌─────────────┐    1. Reset Request     ┌─────────────┐
│  Mobile App │───────────────────────▶│   Backend   │
└─────────────┘                         └─────────────┘
       ▲                                       │
       │                                       │ 2. Email with
       │                                       │    Reset Link
       │                                       ▼
       │                                ┌─────────────┐
       │ 4. Deep Link Return           │     User    │
       └───────────────────────────────│    Email    │
                                        └─────────────┘
                                               │
                                               │ 3. Click Link
                                               ▼
                                        ┌─────────────┐
                                        │     Web     │
                                        │  Frontend   │
                                        └─────────────┘
```

## Workflow

1. **User initiates password reset** in the mobile app by entering their email
2. **Backend sends an email** with a link to the web frontend password reset page
   - Link includes `platform=mobile` parameter to indicate mobile flow
3. **User completes password reset** in their browser
4. **Web frontend redirects** back to the app using a deep link
5. **Mobile app shows success screen** and guides user to login with new password

## Implementation Components

### 1. Mobile App Configuration

#### Android (AndroidManifest.xml)

```xml
<intent-filter>
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="deyncare" android:host="auth" />
</intent-filter>
```

#### iOS (Info.plist)

```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleTypeRole</key>
        <string>Editor</string>
        <key>CFBundleURLName</key>
        <string>com.deyncare.app</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>deyncare</string>
        </array>
    </dict>
</array>
```

### 2. Mobile App Components

#### Deep Link Service

A Flutter service to handle deep links in the app:

```dart
class DeepLinkService {
  static Future<void> initDeepLinks(BuildContext context) async {
    // Handle initial link (app opened from link)
    final initialLink = await getInitialLink();
    if (initialLink != null) {
      _handleDeepLink(context, initialLink);
    }

    // Handle links when app is running
    linkStream.listen((String? link) {
      if (link != null) {
        _handleDeepLink(context, link);
      }
    });
  }

  static void _handleDeepLink(BuildContext context, String link) {
    final uri = Uri.parse(link);
    
    // Handle auth deep links
    if (uri.scheme == 'deyncare' && uri.host == 'auth') {
      if (uri.path == '/reset-success') {
        // Navigate to reset success screen
        Navigator.pushReplacementNamed(context, '/reset-success');
      }
    }
  }
}
```

#### Password Reset Success Screen

A screen to display when the user returns to the app after successfully resetting their password:

```dart
class ResetSuccessScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          children: [
            Icon(Icons.check_circle_outline),
            Text('Password Reset Successful!'),
            Text('You can now log in with your new password.'),
            ElevatedButton(
              onPressed: () => Navigator.pushReplacementNamed(context, '/login'),
              child: Text('Login Now'),
            ),
          ],
        ),
      ),
    );
  }
}
```

### 3. Web Frontend Integration

The web frontend needs to detect mobile app flows and redirect back to the app:

```javascript
// In reset password page
useEffect(() => {
  // Get platform parameter from URL
  const platform = searchParams.get("platform");
  setIsMobileFlow(platform === "mobile");
}, [searchParams]);

// After successful reset
if (success && isMobileFlow) {
  // Redirect to mobile app
  window.location.href = "deyncare://auth/reset-success";
  
  // Fallback button in case automatic redirect fails
  return (
    <Button onClick={() => window.location.href = "deyncare://auth/reset-success"}>
      Return to App
    </Button>
  );
}
```

### 4. API Integration

The forgot password API endpoint doesn't need modification, but we need to ensure the email template includes the platform parameter when sent from the mobile app:

```javascript
// In AuthService.forgotPassword
const resetLink = `https://app.deyncare.com/reset-password?token=${resetToken}`;

// Add platform parameter if request came from mobile app
if (source === 'mobile_app') {
  resetLink += '&platform=mobile';
}

// Send email with the reset link
await EmailService.sendPasswordResetEmail(email, resetLink);
```

## User Experience

1. **Mobile App**: User enters email and submits
2. **Mobile App**: Shows confirmation message explaining they will receive an email
3. **Email**: User receives email with reset link
4. **Web Browser**: User clicks link and sets new password
5. **Web Browser**: After success, redirects to mobile app
6. **Mobile App**: Shows success screen and option to login
7. **Mobile App**: User logs in with new password

## Security Considerations

This approach offers several security advantages:

1. **Token Security**: Reset tokens are not stored or processed in the mobile app
2. **Expiration Handling**: Web implementation handles token validation and expiration
3. **Consistent UI**: Password requirements and validation are unified in one codebase
4. **Compatibility**: Works even if the user doesn't have access to the device they're resetting from

## Testing Requirements

1. **Deep Link Testing**: Verify that the app can be opened via `deyncare://auth/reset-success`
2. **Platform Detection**: Ensure web frontend correctly identifies mobile flows
3. **Fallback Handling**: Test the behavior when deep linking fails (manual button)
4. **Error States**: Verify how invalid or expired tokens are handled

## Dependencies

Required Flutter packages:
- `uni_links: ^0.5.1` - For handling deep links

## Conclusion

The hybrid approach leverages existing web infrastructure for password reset while providing a seamless user experience. It follows security best practices by keeping sensitive operations in secure environments while using deep linking to maintain the perception of a native app experience.

By using this approach, we avoid duplicating complex functionality in the mobile app while still providing a user-friendly password reset flow.
