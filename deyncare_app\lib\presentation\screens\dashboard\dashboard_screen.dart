import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:deyncare_app/core/constants/app_strings.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/providers/theme_provider.dart';
import 'package:deyncare_app/core/routes/app_router.dart';
import 'package:deyncare_app/core/utils/permission_utils.dart';
import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/core/services/navigation_guard_service.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_event.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_state.dart';
import 'package:deyncare_app/presentation/blocs/dashboard/dashboard_bloc.dart';
import 'package:deyncare_app/presentation/blocs/dashboard/dashboard_event.dart';
import 'package:deyncare_app/presentation/blocs/dashboard/dashboard_state.dart';
import 'package:deyncare_app/presentation/screens/dashboard/widgets/index.dart';
import 'package:deyncare_app/presentation/screens/dashboard/widgets/speed_dial_fab.dart';
import 'package:deyncare_app/presentation/widgets/skeleton_loader.dart';

// Import our business screens
import 'package:deyncare_app/presentation/screens/customer/customer_list_screen.dart';
import 'package:deyncare_app/presentation/screens/debt/debt_list_screen.dart';
import 'package:deyncare_app/presentation/screens/dashboard/menu_screen.dart';

/// Main dashboard screen with modern UI/UX design
class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen>
    with AutomaticKeepAliveClientMixin {
  int _currentIndex = 0;

  @override
  bool get wantKeepAlive => true; // Preserve state during parent rebuilds

  @override
  void initState() {
    super.initState();
    // Initialize dashboard data
    context.read<DashboardBloc>().add(DashboardInitialized());
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        // If user becomes unauthenticated, navigate back to login
        if (state is AuthUnauthenticated || state is AuthInitial) {
          AppRouter.navigateToLogin(context);
        }
      },
      builder: (context, authState) {
        // Get user info if authenticated
        final user = authState is AuthAuthenticated ? authState.user : null;

        return Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          body: _buildCurrentScreen(user),
          floatingActionButton: _buildSpeedDial(context),
          floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
          bottomNavigationBar: DashboardBottomNavigation(
            currentIndex: _currentIndex,
            disabledTabs: [
              false, // Dashboard - always accessible
              !PermissionUtils.canAccessCustomers(user), // Customers
              !PermissionUtils.canAccessDebts(user), // Debts
              false, // Menu - always accessible
            ],
            onTap: (index) {
              // Check permissions before allowing navigation
              if (NavigationGuardService.shouldBlockNavigation(user, index)) {
                // Show permission denied feedback
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Row(
                      children: [
                        const Icon(Icons.lock_outline,
                            color: Colors.white, size: 20),
                        const SizedBox(width: 8),
                        Text(NavigationGuardService.getBlockedNavigationMessage(
                            index)),
                      ],
                    ),
                    backgroundColor: AppThemes.warningColor,
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8)),
                    margin: const EdgeInsets.all(16),
                    duration: const Duration(seconds: 2),
                  ),
                );
                return; // Don't change the tab
              }

              setState(() {
                _currentIndex = index;
              });
              // Handle navigation events
              context.read<DashboardBloc>().add(
                    NavigationTabChanged(tabIndex: index),
                  );
            },
          ),
        );
      },
    );
  }

  Widget _buildCurrentScreen(dynamic user) {
    switch (_currentIndex) {
      case 0:
        return _buildDashboardTab(user);
      case 1:
        return _buildCustomersTab(); // Customer tab
      case 2:
        return _buildDebtTab(); // Debt tab (shifted from index 3)
      case 3:
        return MenuScreen(user: user); // Menu tab with My Account and settings
      default:
        return _buildDashboardTab(user);
    }
  }

  Widget _buildDashboardTab(dynamic user) {
    return BlocBuilder<DashboardBloc, DashboardState>(
      builder: (context, state) {
        return RefreshIndicator(
          onRefresh: () async {
            context.read<DashboardBloc>().add(PullToRefreshTriggered());
          },
          child: Stack(
            children: [
              // Dashboard Header (Fixed at top)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: DashboardHeader(
                  userName: user?.fullName ?? 'User',
                  userRole: user?.role ?? 'Manager',
                  shopName: _getShopName(user),
                  lastLoginTime: _formatLastLogin(),
                  onProfileTap: () => _navigateToProfile(),
                ),
              ),

              // Scrollable Content with overlapping KPI cards
              Positioned(
                top: 240, // Start content 40px before header ends (280 - 40)
                left: 0,
                right: 0,
                bottom: 0,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    children: [
                      // KPI Cards Section (overlapping with header)
                      _buildKPISection(state),
                      const SizedBox(height: 24),

                      // Charts Section
                      _buildChartsSection(state),
                      const SizedBox(height: 24),

                      // Recent Activity Section
                      _buildActivitySection(state),
                      const SizedBox(height: 100), // Extra space for FAB
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildKPISection(DashboardState state) {
    if (state is DashboardLoaded) {
      return _buildKPICards(state.kpiData);
    } else {
      return _buildKPILoadingState();
    }
  }

  Widget _buildKPICards(KPIData kpiData) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, authState) {
        final user = authState is AuthAuthenticated ? authState.user : null;

        return Container(
          // Add shadow and elevation for overlapping effect
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, -5),
                spreadRadius: 0,
              ),
            ],
          ),
          child: GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.1,
            children: [
              // Today's Payments - Disable for employees without debt permissions
              KPICard(
                title: 'Today\'s Payments',
                value: '\$${kpiData.todayPayments.toStringAsFixed(2)}',
                icon: Icons.payment,
                iconColor: AppThemes.successColor,
                trend: 'up',
                trendValue: kpiData.paymentsGrowth,
                onTap: PermissionUtils.canAccessDebts(user)
                    ? () => _navigateToPaymentsDetails()
                    : null, // Disable clicking for employees without debt permissions
              ),
              // Outstanding Debts - Disable for employees without debt permissions
              KPICard(
                title: 'Outstanding Debts',
                value: '\$${kpiData.outstandingDebts.toStringAsFixed(2)}',
                icon: Icons.warning,
                iconColor: AppThemes.warningColor,
                trend: kpiData.overdueDebts > 10 ? 'up' : 'down',
                trendValue: kpiData.overdueDebts.toDouble(),
                unit: 'items',
                onTap: PermissionUtils.canAccessDebts(user)
                    ? () => _navigateToDebtsDetails()
                    : null, // Disable clicking for employees without debt permissions
              ),
              // New Customers - Disable for employees without customer permissions
              KPICard(
                title: 'New Customers',
                value: kpiData.newCustomers.toString(),
                icon: Icons.person_add,
                iconColor: AppThemes.primaryColor,
                trend: 'up',
                trendValue: 15.0,
                onTap: PermissionUtils.canAccessCustomers(user)
                    ? () => _navigateToCustomersDetails()
                    : null, // Disable clicking for employees without customer permissions
              ),
              // Risk Level - Disable for employees without report permissions
              KPICard(
                title: 'Risk Level',
                value: kpiData.riskLevel,
                icon: Icons.security,
                iconColor: _getRiskColor(kpiData.riskLevel),
                subtitle: kpiData.riskTrend,
                showTrend: false,
                onTap: PermissionUtils.canAccessReports(user)
                    ? () => _navigateToRiskAnalysis()
                    : null, // Disable clicking for employees without report permissions
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildKPILoadingState() {
    return RepaintBoundary(
      child: Container(
        // Add shadow and elevation for overlapping effect
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, -5),
              spreadRadius: 0,
            ),
          ],
        ),
        child: GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.1,
          children: List.generate(
            4,
            (index) => RepaintBoundary(
              child: SkeletonPatterns.kpiCard(context),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedSectionHeader({
    required String title,
    required IconData icon,
    String? subtitle,
    Color? iconColor,
    VoidCallback? onMoreTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          // Icon container with gradient background
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  (iconColor ?? AppThemes.primaryColor).withValues(alpha: 0.1),
                  (iconColor ?? AppThemes.primaryColor).withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: (iconColor ?? AppThemes.primaryColor)
                    .withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Icon(
              icon,
              color: iconColor ?? AppThemes.primaryColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          // Title and subtitle
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                        letterSpacing: -0.5,
                      ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withValues(alpha: 0.7),
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                ],
              ],
            ),
          ),
          // More button if provided
          if (onMoreTap != null)
            Container(
              decoration: BoxDecoration(
                color: AppThemes.backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppThemes.textLightColor.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: IconButton(
                onPressed: onMoreTap,
                icon: Icon(
                  Icons.more_horiz,
                  color: AppThemes.textSecondaryColor,
                ),
                iconSize: 20,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildChartsSection(DashboardState state) {
    if (state is DashboardLoaded) {
      return _buildChartsContent(state);
    } else {
      return _buildChartsLoadingState();
    }
  }

  Widget _buildChartsContent(DashboardLoaded state) {
    return UnifiedChartWidget(
      kpiData: state.kpiData,
    );
  }

  Widget _buildChartsLoadingState() {
    return RepaintBoundary(
      child: Container(
        height: 400,
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(16),
        ),
        child: SkeletonPatterns.chart(context, height: 400),
      ),
    );
  }

  Widget _buildActivitySection(DashboardState state) {
    if (state is DashboardLoaded) {
      return _buildActivityContent(state.recentActivity);
    } else {
      return _buildActivityLoadingState();
    }
  }

  Widget _buildActivityContent(List<ActivityItem> activities) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: activities.isEmpty
          ? _buildEmptyActivityState()
          : _buildActivityList(activities),
    );
  }

  Widget _buildEmptyActivityState() {
    return SizedBox(
      height: 200,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: Theme.of(context).textTheme.bodySmall?.color,
            ),
            const SizedBox(height: 16),
            Text(
              'No recent activity',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Activity will appear here as you use the app',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityList(List<ActivityItem> activities) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: activities.length,
      itemBuilder: (context, index) {
        final activity = activities[index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: AppThemes.primaryColor.withValues(alpha: 0.1),
            child: Icon(
              _getActivityIcon(activity.type),
              color: AppThemes.primaryColor,
            ),
          ),
          title: Text(
            activity.title,
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
          subtitle: Text(activity.subtitle),
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                activity.amount,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppThemes.successColor,
                ),
              ),
              Text(
                _formatTime(activity.timestamp),
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
          onTap: () => _handleActivityTap(activity),
        );
      },
    );
  }

  Widget _buildActivityLoadingState() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: 5,
        itemBuilder: (context, index) => SkeletonPatterns.activityItem(),
      ),
    );
  }

  IconData _getActivityIcon(String type) {
    switch (type) {
      case 'payment':
        return Icons.payment;
      case 'debt':
        return Icons.warning;
      case 'customer':
        return Icons.person;
      default:
        return Icons.info;
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  Widget _buildCustomersTab() {
    // Use our real CustomerListScreen
    return const CustomerListScreen();
  }

  Widget _buildDebtTab() {
    // Use our real DebtListScreen
    return const DebtListScreen();
  }

  Widget _buildProfileTab(dynamic user) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const SizedBox(height: 40),
        // Enhanced Profile Header
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppThemes.primaryColor,
                AppThemes.primaryLight,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: AppThemes.primaryColor.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            children: [
              // Profile Avatar
              Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 3,
                      ),
                    ),
                  ),
                  CircleAvatar(
                    radius: 45,
                    backgroundColor: Colors.white.withValues(alpha: 0.2),
                    child: Text(
                      user?.fullName?.isNotEmpty == true
                          ? user!.fullName!.substring(0, 1).toUpperCase()
                          : 'U',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 36,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // User Name (Prominent)
              Text(
                user?.fullName ?? 'User Name',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      letterSpacing: -0.5,
                    ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 8),

              // User Role (Prominent)
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  user?.role ?? 'Manager',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                        letterSpacing: 0.5,
                      ),
                ),
              ),

              const SizedBox(height: 12),

              // Shop Name (Smaller)
              Text(
                user?.shopName ?? 'DeynCare Store',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontWeight: FontWeight.w500,
                    ),
              ),

              // Email (Smaller)
              Text(
                user?.email ?? '<EMAIL>',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 32),

        // Profile Options
        _buildProfileOption(
          icon: Icons.person,
          title: 'Edit Profile',
          onTap: () => _navigateToEditProfile(),
        ),
        _buildThemeToggleOption(),
        _buildProfileOption(
          icon: Icons.settings,
          title: 'Settings',
          onTap: () => _navigateToSettings(),
        ),
        _buildProfileOption(
          icon: Icons.help,
          title: 'Help & Support',
          onTap: () => _navigateToHelp(),
        ),
        _buildProfileOption(
          icon: Icons.info,
          title: 'About',
          onTap: () => _showAboutDialog(),
        ),
        const SizedBox(height: 16),
        _buildProfileOption(
          icon: Icons.logout,
          title: 'Logout',
          textColor: AppThemes.errorColor,
          onTap: () => _showLogoutDialog(),
        ),
      ],
    );
  }

  Widget _buildProfileOption({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: textColor ?? Theme.of(context).textTheme.bodyLarge?.color,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: textColor ?? Theme.of(context).textTheme.bodyLarge?.color,
            fontWeight: FontWeight.w500,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Theme.of(context).textTheme.bodySmall?.color,
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildThemeToggleOption() {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final isDark = Theme.of(context).brightness == Brightness.dark;

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: isDark
                    ? Colors.black.withValues(alpha: 0.3)
                    : Colors.black.withValues(alpha: 0.05),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ListTile(
            leading: Icon(
              themeProvider.isDarkMode ? Icons.light_mode : Icons.dark_mode,
              color: AppThemes.primaryColor,
            ),
            title: Text(
              themeProvider.isDarkMode ? 'Light Mode' : 'Dark Mode',
              style: TextStyle(
                color: Theme.of(context).textTheme.bodyLarge?.color,
                fontWeight: FontWeight.w500,
              ),
            ),
            trailing: Switch(
              value: themeProvider.isDarkMode,
              onChanged: (bool value) {
                _toggleTheme(value);
              },
              activeColor: AppThemes.primaryColor,
              inactiveThumbColor: isDark
                  ? Theme.of(context).colorScheme.outline
                  : Theme.of(context).colorScheme.outline,
              inactiveTrackColor: isDark
                  ? Theme.of(context).colorScheme.outlineVariant
                  : Theme.of(context).colorScheme.outlineVariant,
            ),
          ),
        );
      },
    );
  }

  void _toggleTheme(bool isDark) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    themeProvider.setDarkMode(isDark);

    // Show feedback to user
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isDark ? '🌙 Dark mode enabled' : '☀️ Light mode enabled',
        ),
        backgroundColor: AppThemes.primaryColor,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  // Helper methods
  String _formatLastLogin() {
    final now = DateTime.now();
    return '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
  }

  /// Get shop name with proper fallback handling
  String _getShopName(User? user) {
    if (user?.shopName != null && user!.shopName!.isNotEmpty) {
      return user.shopName!;
    }

    // Fallback based on user role and name
    if (user?.isAdmin == true) {
      return '${user?.fullName ?? 'Admin'}\'s Shop';
    } else if (user?.isEmployee == true) {
      return 'DeynCare Store'; // Generic name for employees
    }

    return 'DeynCare Store'; // Default fallback
  }

  Color _getRiskColor(String riskLevel) {
    switch (riskLevel.toLowerCase()) {
      case 'low':
        return AppThemes.successColor;
      case 'medium':
        return AppThemes.warningColor;
      case 'high':
        return AppThemes.errorColor;
      default:
        return AppThemes.textSecondaryColor;
    }
  }

  // Navigation methods - Fixed to avoid skeleton loading loops
  void _navigateToProfile() {
    setState(() {
      _currentIndex = 3;
    });
  }

  void _navigateToSettings() {
    // Simple navigation without BLoC events to avoid loading loops
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Settings - Coming Soon'),
        backgroundColor: AppThemes.primaryColor,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _navigateToPaymentsDetails() {
    // Navigate to debt tab
    setState(() {
      _currentIndex = 2;
    });
  }

  void _navigateToDebtsDetails() {
    // Navigate to debt tab
    setState(() {
      _currentIndex = 2;
    });
  }

  void _navigateToCustomersDetails() {
    // Navigate to customer tab
    setState(() {
      _currentIndex = 1;
    });
  }

  void _navigateToRiskAnalysis() {
    // Simple navigation without BLoC events to avoid loading loops
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Risk Analysis - Coming Soon'),
        backgroundColor: AppThemes.infoColor,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _navigateToEditProfile() {
    AppRouter.navigateToProfileSettings(context);
  }

  void _navigateToHelp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Help Center - Coming Soon'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _handleActivityTap(ActivityItem activity) {
    // Simple navigation without BLoC events to avoid loading loops
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Activity: ${activity.title}'),
        backgroundColor: AppThemes.primaryColor,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: AppStrings.appName,
      applicationVersion: 'v${AppStrings.appVersion}',
      applicationLegalese: '© 2025 DeynCare. All rights reserved.',
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              context.read<AuthBloc>().add(LoggedOut());
              Navigator.pop(context);
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  Widget _buildSpeedDial(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, authState) {
        // Get user info if authenticated
        final user = authState is AuthAuthenticated ? authState.user : null;

        // Build permission-aware children list
        final children = _buildPermissionAwareSpeedDialChildren(context, user);

        // Don't show FAB if user has no permissions for any actions
        if (children.isEmpty) {
          return const SizedBox.shrink();
        }

        return SpeedDialFAB(
          animatedIcon: AnimatedIcons.add_event,
          backgroundColor: AppThemes.primaryColor,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          curve: Curves.bounceIn,
          children: children,
        );
      },
    );
  }

  List<SpeedDialChild> _buildPermissionAwareSpeedDialChildren(BuildContext context, User? user) {
    final children = <SpeedDialChild>[];

    // Add Customer button - only if user can create customers
    if (PermissionUtils.canCreateCustomers(user)) {
      children.add(SpeedDialChild(
        child: Icon(Icons.person_add,
            color: Theme.of(context).colorScheme.onPrimary),
        backgroundColor: AppThemes.primaryColor,
        label: 'Add Customer',
        labelStyle: TextStyle(
          fontSize: 16.0,
          color: Theme.of(context).textTheme.bodyMedium?.color,
        ),
        onTap: () => _handleQuickAction('add_customer'),
      ));
    }

    // Collect Payment button - only if user can access debts
    if (PermissionUtils.canAccessDebts(user)) {
      children.add(SpeedDialChild(
        child: Icon(Icons.payment,
            color: Theme.of(context).colorScheme.onPrimary),
        backgroundColor: AppThemes.infoColor,
        label: 'Collect Payment',
        labelStyle: TextStyle(
          fontSize: 16.0,
          color: Theme.of(context).textTheme.bodyMedium?.color,
        ),
        onTap: () => _handleQuickAction('collect_payment'),
      ));
    }

    // View Reports button - only if user can access reports
    if (PermissionUtils.canAccessReports(user)) {
      children.add(SpeedDialChild(
        child: Icon(Icons.analytics,
            color: Theme.of(context).colorScheme.onPrimary),
        backgroundColor: AppThemes.secondaryColor,
        label: 'View Reports',
        labelStyle: TextStyle(
          fontSize: 16.0,
          color: Theme.of(context).textTheme.bodyMedium?.color,
        ),
        onTap: () => _handleQuickAction('view_reports'),
      ));
    }

    return children;
  }

  void _handleQuickAction(String action) {
    // Get current user for permission checks
    final authState = context.read<AuthBloc>().state;
    final user = authState is AuthAuthenticated ? authState.user : null;

    // Perform permission checks before executing actions
    switch (action) {
      case 'add_customer':
        if (!PermissionUtils.canCreateCustomers(user)) {
          _showPermissionDeniedMessage('create customers');
          return;
        }
        // Navigate to create customer screen
        AppRouter.navigateToCreateCustomer(context);
        break;
      case 'collect_payment':
        if (!PermissionUtils.canAccessDebts(user)) {
          _showPermissionDeniedMessage('access debt management');
          return;
        }
        // Navigate to debt tab where users can add payments
        setState(() {
          _currentIndex = 2;
        });
        break;
      case 'view_reports':
        if (!PermissionUtils.canAccessReports(user)) {
          _showPermissionDeniedMessage('access reports');
          return;
        }
        // Show analytics in dashboard
        setState(() {
          _currentIndex = 0;
        });
        break;
    }

    // Log the action
    context.read<DashboardBloc>().add(QuickActionTapped(actionType: action));

    // Show success feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_getActionMessage(action)),
        duration: const Duration(seconds: 2),
        backgroundColor: AppThemes.primaryColor,
      ),
    );
  }

  void _showPermissionDeniedMessage(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('You don\'t have permission to $feature'),
        duration: const Duration(seconds: 3),
        backgroundColor: AppThemes.errorColor,
        action: SnackBarAction(
          label: 'Contact Admin',
          textColor: Colors.white,
          onPressed: () {
            // Could navigate to contact/support screen
          },
        ),
      ),
    );
  }

  String _getActionMessage(String action) {
    switch (action) {
      case 'add_customer':
        return 'Opening Add Customer Form...';
      case 'collect_payment':
        return 'Opening Debt Management...';
      case 'view_reports':
        return 'Showing Dashboard Analytics...';
      default:
        return 'Quick action: $action';
    }
  }
}
