import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/domain/models/debt.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_bloc.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_event.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_state.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_constants.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/form_mixins.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/loading_states.dart';

/// Delete Debt Form Widget - Updated to match Customer design pattern
class DeleteDebtForm extends StatefulWidget {
  final Debt debt;

  const DeleteDebtForm({super.key, required this.debt});

  @override
  State<DeleteDebtForm> createState() => _DeleteDebtFormState();
}

class _DeleteDebtFormState extends State<DeleteDebtForm> 
    with ModalFormMixin<DeleteDebtForm>, BlocListenerMixin<DeleteDebtForm> {

  @override
  Widget build(BuildContext context) {
    return buildBlocListener<DebtBloc, DebtState>(
      listener: (context, state) {
        if (state is DebtDeleted) {
          handleSuccess('Debt deleted successfully!');
        } else if (state is DebtError) {
          handleError('Error: ${state.message}');
        }
      },
      child: BlocBuilder<DebtBloc, DebtState>(
        builder: (context, state) {
          // Show skeleton loader during deletion
          if (state is DebtDeleting) {
            return ModalLoadingStates.deleteSkeleton();
          }
          
          return Padding(
            padding: ModalConstants.defaultPadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildWarningHeader(),
                ModalConstants.largeSpacing,
                _buildDebtInformation(),
                ModalConstants.defaultSpacing,
                _buildWarningMessage(),
                ModalConstants.largeSpacing,
                _buildActionButtons(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildWarningHeader() {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.warning_rounded,
            size: 64,
            color: AppThemes.errorColor,
          ),
          ModalConstants.defaultSpacing,
          Text(
            'Delete Debt',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppThemes.errorColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDebtInformation() {
    return CommonCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Debt Details',
              style: ModalConstants.sectionTitleStyle(context),
            ),
            const SizedBox(height: 12),
            _buildDetailRow('Debt ID:', widget.debt.debtId),
            _buildDetailRow('Customer:', widget.debt.customerName),
            _buildDetailRow('Total Amount:', '\$${widget.debt.amount.toStringAsFixed(2)}'),
            _buildDetailRow('Paid Amount:', '\$${widget.debt.totalPaid.toStringAsFixed(2)}'),
            _buildDetailRow('Remaining:', '\$${widget.debt.remainingAmount.toStringAsFixed(2)}'),
            _buildDetailRow('Status:', widget.debt.status.displayName),
            _buildDetailRow('Due Date:', _formatDate(widget.debt.dueDate)),
            if (widget.debt.description?.isNotEmpty == true)
              _buildDetailRow('Description:', widget.debt.description!),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: AppThemes.textSecondaryColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWarningMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppThemes.warningColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppThemes.warningColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '⚠️ WARNING',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              color: AppThemes.warningColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          ModalConstants.defaultSpacing,
          Text(
            'This action cannot be undone. The debt record and all associated payment history will be permanently deleted from the system.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppThemes.warningColor,
            ),
          ),
          if (widget.debt.totalPaid > 0) ...[
            ModalConstants.defaultSpacing,
            Text(
              'Note: This debt has existing payments (\$${widget.debt.totalPaid.toStringAsFixed(2)}). Deleting will remove all payment records.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppThemes.warningColor,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ModalConstants.outlinedButtonStyle(),
            child: const Text('Cancel'),
          ),
        ),
        ModalConstants.defaultSpacing,
        Expanded(
          child: ElevatedButton(
            onPressed: !isLoading ? _handleDelete : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppThemes.errorColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(ModalConstants.defaultRadius),
              ),
            ),
            child: isLoading
                ? ModalLoadingStates.buttonLoading(context)
                : const Text(
                    'Delete Debt',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _handleDelete() {
    setLoading(true);
    
    context.read<DebtBloc>().add(DeleteDebt(widget.debt.debtId));
    
    setLoading(false);
  }
} 