# User Role Management - PRD UI/UX Documentation

## 📋 **Feature Overview**
This document outlines the Product Requirements and UI/UX specifications for the User Role Management feature in the DeynCare Flutter mobile app.

---

## 🎯 **Objectives**
- Enable Shop Admins to manage employee users within their shop
- Provide granular permission control for employee access
- Maintain security and role-based access control
- Deliver intuitive user experience for user management

---

## 👥 **Target Users**
- **Primary**: Shop Admins (role: 'admin')
- **Secondary**: Employees (limited view of their own profile)

---

## 🏗️ **Feature Architecture**

### **Backend Integration**
- ✅ Employee role properly defined in backend
- ✅ Ad<PERSON> can create, list, update, delete employees
- ✅ Shop-scoped access control implemented
- ✅ Granular permissions via visibility field
- ✅ Authorization middleware covers employee role

### **Flutter Implementation**
```
lib/
├── data/
│   ├── models/
│   │   └── employee_model.dart ✅
│   └── services/
│       └── user_management_service.dart ✅
├── domain/
│   └── models/
│       └── employee.dart ✅
└── presentation/
    └── screens/
        └── user_role/
            ├── user_role_list_screen.dart ✅
            └── add_user_role_screen.dart 🔄
```

---

## 🎨 **UI/UX Specifications**

### **1. Menu Integration**
**Location**: Dashboard → Menu → Shop Management Section

**Design Requirements**:
- Add "User Role" menu item after "Shop Settings"
- Icon: `Icons.people_outline`
- Color: Accent color with 15% opacity background
- Visibility: Only show for Admin users (`role == 'admin'`)

**Implementation Status**: ✅ **COMPLETED**

### **2. User Role List Screen**

**Screen Purpose**: Display all shop users with management options

**Layout Components**:
1. **App Bar**
   - Title: "User Role Management"
   - Back button for navigation

2. **Search and Action Section**
   - Search field with placeholder: "Search users..."
   - Clear button when text is entered
   - "Add New User Role" button (primary action)

3. **User List**
   - Cards displaying user information
   - Empty state when no users found

**User Card Design**:
```
┌─────────────────────────────────────┐
│ [Avatar] Name                    [⋮] │
│         <EMAIL>           │
│         [Admin/Employee] [Active]   │
└─────────────────────────────────────┘
```

**Interactive Elements**:
- Tap card to edit employee (not available for admin)
- Three-dot menu for employees:
  - "Edit Permissions"
  - "Delete User" (red color)
- Admin users show admin panel settings icon

**Implementation Status**: ✅ **COMPLETED**

### **3. Add New User Role Screen**

**Screen Purpose**: Create new employee with granular permissions

**Form Structure**:

1. **Basic Information Section**
   ```
   ┌─────────────────────────────────────┐
   │ Basic Information                   │
   ├─────────────────────────────────────┤
   │ Full Name: [________________]       │
   │ Email Address: [________________]   │
   │ Password: [________________]        │
   │ Confirm Password: [________________]│
   └─────────────────────────────────────┘
   ```

2. **Permissions Section**
   ```
   ┌─────────────────────────────────────┐
   │ Permissions                         │
   ├─────────────────────────────────────┤
   │ ☐ Check All                         │
   │                                     │
   │ Customer Management                 │
   │ ☐ Create  ☐ Update  ☐ View  ☐ Delete│
   │                                     │
   │ Debt Management                     │
   │ ☐ Create  ☐ Update  ☐ View  ☐ Delete│
   │                                     │
   │ Report Management                   │
   │ ☐ Generate  ☐ View  ☐ Delete        │
   └─────────────────────────────────────┘
   ```

3. **Action Buttons**
   ```
   ┌─────────────────────────────────────┐
   │ [Cancel]           [Create Employee]│
   └─────────────────────────────────────┘
   ```

**Validation Rules**:
- Full Name: Required, 3-100 characters
- Email: Required, valid email format
- Password: Required, minimum 6 characters
- Confirm Password: Must match password
- Permissions: At least one permission must be selected

**Implementation Status**: 🔄 **IN PROGRESS**

### **4. Edit User Role Screen**

**Screen Purpose**: Modify existing employee permissions

**Design**: Similar to Add User screen but:
- Pre-populated form data
- Password fields hidden (separate change password option)
- "Update Employee" button instead of "Create"
- Option to view current permissions

**Implementation Status**: ⏳ **PENDING**

---

## 🔐 **Security & Permissions Matrix**

### **Admin Capabilities**
| Action | Admin | Employee |
|--------|--------|----------|
| View User Role Menu | ✅ | ❌ |
| List All Shop Users | ✅ | ❌ |
| Create New Employee | ✅ | ❌ |
| Edit Employee Permissions | ✅ | ❌ |
| Delete Employee | ✅ | ❌ |
| View Own Profile | ✅ | ✅ |

### **Employee Permission Modules**
| Module | Actions | Description |
|--------|---------|-------------|
| Customer Management | Create, Update, View, Delete | Customer CRUD operations |
| Debt Management | Create, Update, View, Delete | Debt record CRUD operations |
| Report Management | Generate, View, Delete | Report operations |

---

## 📱 **User Experience Flow**

### **Primary Flow: Add New Employee**
1. Admin opens Menu → User Role
2. Views current user list
3. Taps "Add New User Role"
4. Fills basic information form
5. Selects appropriate permissions
6. Submits form
7. New employee appears in list
8. Success confirmation shown

### **Secondary Flow: Manage Existing Employee**
1. Admin views user list
2. Taps on employee card or menu
3. Selects "Edit Permissions"
4. Modifies permission checkboxes
5. Saves changes
6. Updated permissions reflected immediately

### **Error Handling**
- Form validation with inline error messages
- Network error handling with retry options
- Loading states during API operations
- Success/failure feedback via SnackBar

---

## 🎯 **Success Metrics**
- ✅ Admin can successfully create employees
- ✅ Granular permissions work as expected
- ✅ UI is intuitive and follows app design patterns
- ✅ No security vulnerabilities in role management
- ✅ Performance: List loads within 2 seconds

---

## 🔄 **Implementation Status**

### **Completed ✅**
- [x] Backend Employee role analysis and verification
- [x] Employee domain and data models
- [x] User management service with API integration
- [x] Menu integration with admin-only visibility
- [x] User Role List Screen with search and actions
- [x] User card design with role/status badges
- [x] Delete user functionality with confirmation

### **In Progress 🔄**
- [ ] Add New User Role Screen with permissions
- [ ] Form validation and submission
- [ ] Permission checkbox "Check All" functionality

### **Pending ⏳**
- [ ] Edit User Role Screen
- [ ] Route configuration in AppRouter
- [ ] Integration with UserManagementService
- [ ] Error handling and loading states
- [ ] Unit tests for user management features

---

## 🚀 **Next Steps**

1. **Complete Add User Screen**: Implement permission checkboxes with backend payload structure
2. **Route Integration**: Add proper navigation routes in AppRouter
3. **Service Integration**: Connect UI with UserManagementService
4. **Testing**: Add comprehensive tests for user management flows
5. **Polish**: Implement loading states, error handling, and animations

---

## 📝 **Notes**
- Backend API endpoints match expected structure
- Permission structure aligns with backend visibility field
- UI follows existing app design patterns
- Security measures prevent unauthorized access
- Feature is scoped specifically for shop-level user management

**Document Version**: 1.0  
**Last Updated**: 2025-01-27  
**Status**: Active Development 