import 'package:deyncare_app/data/network/clients/dio_client.dart';
import 'package:deyncare_app/data/models/customer_report_model.dart';
import 'package:flutter/foundation.dart';

class CustomerReportRepository {
  final DioClient _dioClient;

  CustomerReportRepository({required DioClient dioClient})
      : _dioClient = dioClient;

  /// Fetch customer report data from backend
  Future<Map<String, dynamic>> getCustomerReportData({
    String? month,
    String? year,
    String? startDate,
    String? endDate,
    String dateRange = 'monthly',
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'dateRange': dateRange,
      };

      if (month != null) queryParams['month'] = month;
      if (year != null) queryParams['year'] = year;
      if (startDate != null) queryParams['startDate'] = startDate;
      if (endDate != null) queryParams['endDate'] = endDate;

      if (kDebugMode) {
        print(
            '🔍 CustomerReportRepository: Making API call to /reports/customers/data');
        print('🔍 Query params: $queryParams');
      }

      final response = await _dioClient.get(
        '/reports/customers/data',
        queryParameters: queryParams,
      );

      if (kDebugMode) {
        print('🔍 DioClient response type: ${response.runtimeType}');
        print('🔍 Raw response: $response');
      }

      // DioClient returns processed response data directly (not a Dio Response object)
      // Check if response is null
      if (response == null) {
        if (kDebugMode) {
          print('❌ Response is null');
        }
        throw Exception('API response is null');
      }

      // Try to safely cast response to Map
      Map<String, dynamic> responseData;
      try {
        if (response is Map<String, dynamic>) {
          responseData = response;
        } else if (response is Map) {
          responseData = Map<String, dynamic>.from(response as Map);
        } else {
          if (kDebugMode) {
            print('❌ Response is not a Map. Type: ${response.runtimeType}');
            print('❌ Response content: $response');
          }
          throw Exception(
              'API response is not a Map. Type: ${response.runtimeType}');
        }
      } catch (castError) {
        if (kDebugMode) {
          print('❌ Error casting response: $castError');
          print('❌ Response: $response');
        }
        throw Exception('Failed to cast response to Map: $castError');
      }

      if (kDebugMode) {
        print('🔍 Response keys: ${responseData.keys.toList()}');
        print('🔍 Response structure: $responseData');
      }

      // Validate response structure
      if (!responseData.containsKey('data')) {
        if (kDebugMode) {
          print('❌ Response missing "data" field');
          print('❌ Available keys: ${responseData.keys.toList()}');
        }
        throw Exception(
            'Invalid API response: missing data field. Available keys: ${responseData.keys.toList()}');
      }

      final dataField = responseData['data'];
      if (kDebugMode) {
        print('🔍 Data field type: ${dataField.runtimeType}');
        print('🔍 Data field content: $dataField');
      }

      // Try to safely cast data field to Map
      Map<String, dynamic> data;
      try {
        if (dataField is Map<String, dynamic>) {
          data = dataField;
        } else if (dataField is Map) {
          data = Map<String, dynamic>.from(dataField as Map);
        } else {
          if (kDebugMode) {
            print('❌ Data field is not a Map. Type: ${dataField.runtimeType}');
          }
          throw Exception(
              'Data field is not a Map. Type: ${dataField.runtimeType}');
        }
      } catch (castError) {
        if (kDebugMode) {
          print('❌ Error casting data field: $castError');
        }
        throw Exception('Failed to cast data field to Map: $castError');
      }

      // Validate data structure
      if (!data.containsKey('customers')) {
        if (kDebugMode) {
          print('❌ Data missing "customers" field');
          print('❌ Available data keys: ${data.keys.toList()}');
        }
        throw Exception(
            'Invalid API response: missing customers field. Available keys: ${data.keys.toList()}');
      }

      if (kDebugMode) {
        print('🔍 Customers field type: ${data['customers'].runtimeType}');
        print('🔍 Number of customers: ${(data['customers'] as List).length}');
      }

      // Parse customers using the report-specific model
      final List<CustomerReportData> customers =
          (data['customers'] as List).map((json) {
        final customerJson = json as Map<String, dynamic>;
        if (kDebugMode) {
          print('🔍 Raw customer JSON keys: ${customerJson.keys.toList()}');
          print('🔍 Raw customer JSON: $customerJson');
          print(
              '🔍 totalDebt field: ${customerJson['totalDebt']} (${customerJson['totalDebt'].runtimeType})');
          print(
              '🔍 outstandingDebt field: ${customerJson['outstandingDebt']} (${customerJson['outstandingDebt'].runtimeType})');
          print(
              '🔍 paidAmount field: ${customerJson['paidAmount']} (${customerJson['paidAmount'].runtimeType})');
          print('🔍 CustomerName: ${customerJson['CustomerName']}');
          print('🔍 customerId: ${customerJson['customerId']}');
        }
        final customer = CustomerReportData.fromJson(customerJson);
        if (kDebugMode) {
          print('🔍 Parsed customer data:');
          print('   - Name: ${customer.customerName}');
          print('   - Total Debt: ${customer.totalDebt}');
          print('   - Outstanding Debt: ${customer.outstandingDebt}');
          print('   - Paid Amount: ${customer.paidAmount}');
        }
        return customer;
      }).toList();

      if (kDebugMode) {
        print('✅ Successfully parsed ${customers.length} customers');
        if (customers.isNotEmpty) {
          final firstCustomer = customers.first;
          print('🔍 First customer parsed data:');
          print('   - Name: ${firstCustomer.customerName}');
          print('   - Total Debt: ${firstCustomer.totalDebt}');
          print('   - Outstanding Debt: ${firstCustomer.outstandingDebt}');
          print('   - Paid Amount: ${firstCustomer.paidAmount}');
        }
      }

      return {
        'customers': customers,
        'summary': data['summary'] ?? {},
        'metadata': data['metadata'] ?? {},
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ CustomerReportRepository Error: $e');
        print('❌ Error type: ${e.runtimeType}');
        print('❌ Stack trace: ${StackTrace.current}');
      }
      // Add more specific error information
      throw Exception('Failed to fetch customer report data: ${e.toString()}');
    }
  }

  /// Get customer report statistics
  Future<Map<String, dynamic>> getCustomerReportStats({
    String? month,
    String? year,
    String? startDate,
    String? endDate,
    String dateRange = 'monthly',
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'dateRange': dateRange,
      };

      if (month != null) queryParams['month'] = month;
      if (year != null) queryParams['year'] = year;
      if (startDate != null) queryParams['startDate'] = startDate;
      if (endDate != null) queryParams['endDate'] = endDate;

      if (kDebugMode) {
        print(
            '🔍 CustomerReportRepository: Making API call to /reports/customers/stats');
        print('🔍 Query params: $queryParams');
      }

      final response = await _dioClient.get(
        '/reports/customers/stats',
        queryParameters: queryParams,
      );

      if (kDebugMode) {
        print('🔍 Stats DioClient response type: ${response.runtimeType}');
        print('🔍 Stats Raw response: $response');
      }

      // DioClient returns processed response data directly (not a Dio Response object)
      final responseData = response as Map<String, dynamic>;

      if (kDebugMode) {
        print('🔍 Stats Response keys: ${responseData.keys.toList()}');
      }

      // Validate response structure
      if (!responseData.containsKey('data')) {
        if (kDebugMode) {
          print('❌ Stats Response missing "data" field');
          print('❌ Available keys: ${responseData.keys.toList()}');
        }
        throw Exception('Invalid API response: missing data field');
      }

      final result = responseData['data'] as Map<String, dynamic>;

      if (kDebugMode) {
        print('✅ Successfully fetched customer report stats');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ CustomerReportRepository Stats Error: $e');
        print('❌ Error type: ${e.runtimeType}');
      }
      // Add more specific error information
      throw Exception('Failed to fetch customer report stats: ${e.toString()}');
    }
  }
}
