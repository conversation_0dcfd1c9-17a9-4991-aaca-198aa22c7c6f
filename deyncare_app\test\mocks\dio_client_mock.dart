import 'dart:io';
import 'package:dio/dio.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:deyncare_app/data/network/clients/dio_client.dart';

// Mock DioClient for testing auth service
class MockDioClient extends DioClient {
  // Mock response data for predictable testing
  final Map<String, dynamic> _mockUserData = {
    'id': '1',
    'name': 'Test User',
    'email': '<EMAIL>',
    'role': 'customer',
  };

  // Constructor - use a mock Dio instance to avoid real network calls
  MockDioClient() : super(dio: Dio());

  // Override HTTP methods to avoid real network calls
  @override
  Future<dynamic> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
    bool retry = true,
    bool showSuccessToast = false,
  }) async {
    if (path.contains('/auth/me')) {
      return {
        'success': true,
        'data': {'user': _mockUserData},
      };
    }
    throw ApiException(message: 'Not implemented in mock', code: 'mock_error');
  }

  @override
  Future<dynamic> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    bool retry = true,
    bool showSuccessToast = false,
  }) async {
    // Simulate auth endpoints
    if (path.contains('/auth/login')) {
      final credentials = data as Map<String, dynamic>;
      final email = credentials['email'] as String?;
      final password = credentials['password'] as String?;

      if (email == '<EMAIL>' && password == 'password') {
        return {
          'success': true,
          'data': {
            'accessToken': 'mock_access_token',
            'refreshToken': 'mock_refresh_token',
            'user': _mockUserData
          }
        };
      } else {
        throw ApiException(
          message: 'Invalid credentials',
          code: 'auth_error',
        );
      }
    } else if (path.contains('/auth/register')) {
      final userData = data as Map<String, dynamic>;

      return {
        'success': true,
        'data': {
          'accessToken': 'mock_access_token',
          'refreshToken': 'mock_refresh_token',
          'user': {
            'id': '1',
            'name': userData['fullName'],
            'email': userData['email'],
            'role': 'customer',
          }
        }
      };
    } else if (path.contains('/auth/logout')) {
      return {
        'success': true, 
        'message': 'Logged out successfully'
      };
    } else if (path.contains('/auth/refresh-token')) {
      return {
        'success': true,
        'data': {
          'accessToken': 'new_mock_access_token',
          'refreshToken': 'new_mock_refresh_token',
          'expiresIn': 900,
        }
      };
    } else if (path.contains('/auth/forgot-password')) {
      return {
        'success': true,
        'message': 'Password reset email sent successfully'
      };
    } else if (path.contains('/auth/verify-email')) {
      return {
        'success': true,
        'message': 'Email verified successfully'
      };
    }

    throw ApiException(message: 'Endpoint not mocked: $path', code: 'mock_error');
  }

  @override
  Future<dynamic> uploadFile(
    String path, {
    required File file,
    required String fieldName,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    bool retry = true,
    bool showSuccessToast = false,
  }) async {
    // Simulate file upload for registration with shop logo
    if (path.contains('/auth/register')) {
      final userData = data as Map<String, dynamic>;

      return {
        'success': true,
        'data': {
          'accessToken': 'mock_access_token',
          'refreshToken': 'mock_refresh_token',
          'user': {
            'id': '1',
            'name': userData['fullName'],
            'email': userData['email'],
            'role': 'customer',
            'shopLogo': 'http://example.com/logo.png'
          }
        }
      };
    }

    throw ApiException(message: 'File upload endpoint not mocked: $path', code: 'mock_error');
  }
}
