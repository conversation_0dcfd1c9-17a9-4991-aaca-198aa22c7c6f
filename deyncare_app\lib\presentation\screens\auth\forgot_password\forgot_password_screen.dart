import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_header.dart';
import 'package:deyncare_app/presentation/screens/auth/forgot_password/widgets/forgot_password_form.dart';
import 'package:deyncare_app/presentation/widgets/deyncare_logo.dart';

/// Screen for requesting a password reset
class ForgotPasswordScreen extends StatelessWidget {
  const ForgotPasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    
    return Scaffold(
      backgroundColor: ThemeUtils.getBackgroundColor(context, type: BackgroundType.primary),
      appBar: AppBar(
        title: const Text('Reset Password'),
        backgroundColor: AppThemes.primaryColor,
        foregroundColor: ThemeUtils.getTextColor(context, type: TextColorType.primary),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: isTablet ? 48.0 : 24.0,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                SizedBox(height: isTablet ? 48 : 32),
                
                // Header with SVG logo
                const AuthHeader(
                  title: 'Forgot Password?',
                  subtitle: 'Don\'t worry, we\'ll help you reset it',
                  showLogo: true,
                  logoVariant: DeynCareLogoVariant.svg,
                ),
                
                SizedBox(height: isTablet ? 56 : 40),
                
                // Form in a clean card
                Card(
                  elevation: isTablet ? 8 : 4,
                  shadowColor: ThemeUtils.getShadowColor(context, type: ShadowType.light),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(isTablet ? 24 : 16),
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(isTablet ? 32.0 : 24.0),
                    child: const ForgotPasswordForm(),
                  ),
                ),
                
                SizedBox(height: isTablet ? 48 : 32),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
