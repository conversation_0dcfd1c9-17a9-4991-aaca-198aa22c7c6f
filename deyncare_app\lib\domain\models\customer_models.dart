// Import needed for extensions
import 'package:deyncare_app/data/models/customer_model.dart';

// Export all customer-related domain models and types
export 'customer.dart';

// Re-export commonly used types from data models
export 'package:deyncare_app/data/models/customer_model.dart' show 
  CreateCustomerRequest,
  UpdateCustomerRequest,
  CustomerListResponse,
  CustomerSummary,
  CustomerDetailResponse,
  PaginationInfo,
  FinancialSummary;

// Customer Type enumeration that matches backend validation
enum CustomerType {
  new_,
  returning;

  String get value {
    switch (this) {
      case CustomerType.new_:
        return 'new';
      case CustomerType.returning:
        return 'returning';
    }
  }

  String get displayName {
    switch (this) {
      case CustomerType.new_:
        return 'New';
      case CustomerType.returning:
        return 'Returning';
    }
  }

  static CustomerType fromValue(String value) {
    switch (value.toLowerCase()) {
      case 'new':
        return CustomerType.new_;
      case 'returning':
        return CustomerType.returning;
      default:
        return CustomerType.new_;
    }
  }
}

// Extensions to add missing properties
extension CustomerSummaryExtensions on CustomerSummary {
  /// Get total debt from financial summary
  double get totalDebt => financialSummary.totalOutstanding;
}

extension CustomerListResponseExtensions on CustomerListResponse {
  /// Get hasNextPage from pagination data
  bool get hasNextPage => data.pagination.hasNextPage;
  
  /// Get currentPage from pagination data
  int get currentPage => data.pagination.currentPage;
} 