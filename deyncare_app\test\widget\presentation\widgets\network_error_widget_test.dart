import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:deyncare_app/presentation/widgets/network_error_widget.dart';

void main() {
  group('NetworkErrorWidget Tests', () {
    testWidgets('should display correct message and icon', (WidgetTester tester) async {
      // Define test variables
      const testMessage = 'Test error message';
      bool retryPressed = false;
      
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: NetworkErrorWidget(
              message: testMessage,
              onRetry: () {
                retryPressed = true;
              },
              icon: Icons.error,
            ),
          ),
        ),
      );
      
      // Verify displayed content
      expect(find.text(testMessage), findsOneWidget);
      expect(find.byIcon(Icons.error), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
      
      // Test retry button functionality
      await tester.tap(find.text('Retry'));
      await tester.pump();
      
      // Verify callback was triggered
      expect(retryPressed, isTrue);
    });
    
    testWidgets('OfflineWidget should display correct message', (WidgetTester tester) async {
      // Build the widget
      bool retryPressed = false;
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: OfflineWidget(
              onRetry: () {
                retryPressed = true;
              },
            ),
          ),
        ),
      );
      
      // Verify specific offline message content
      expect(find.text('You appear to be offline.\nPlease check your internet connection.'), findsOneWidget);
      expect(find.byIcon(Icons.wifi_off), findsOneWidget);
      
      // Test retry button
      await tester.tap(find.text('Retry'));
      await tester.pump();
      
      // Verify callback was triggered
      expect(retryPressed, isTrue);
    });
    
    testWidgets('TimeoutErrorWidget should display correct message', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TimeoutErrorWidget(
              onRetry: () {},
            ),
          ),
        ),
      );
      
      // Verify specific timeout message content
      expect(find.text('Connection timed out.\nPlease try again.'), findsOneWidget);
      expect(find.byIcon(Icons.timer_off), findsOneWidget);
    });
    
    testWidgets('ServerErrorWidget should display correct message', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServerErrorWidget(
              onRetry: () {},
            ),
          ),
        ),
      );
      
      // Verify specific server error message content
      expect(find.text('Server error occurred.\nOur team has been notified.'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });
  });
}
