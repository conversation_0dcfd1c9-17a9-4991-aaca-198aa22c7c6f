import 'package:flutter/material.dart';
import 'package:deyncare_app/core/routes/app_router.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_button.dart';

/// Screen shown when user returns to the app after successfully resetting password on web
class ResetSuccessScreen extends StatelessWidget {
  const ResetSuccessScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeUtils.getBackgroundColor(context, type: BackgroundType.primary),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Success icon
              Icon(
                Icons.check_circle_outline,
                size: 100,
                color: ThemeUtils.getStatusColors(context).success,
              ),
              const SizedBox(height: 32),
              
              // Success title
              Text(
                'Password Reset Successful!',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: ThemeUtils.getStatusColors(context).success,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              
              // Success message
              Text(
                'Your password has been reset successfully. You can now log in to your account with your new password.',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: ThemeUtils.getTextColor(context, type: TextColorType.secondary),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 48),
              
              // Login button
              AuthButton(
                label: 'Login Now',
                onPressed: () {
                  AppRouter.navigateToLogin(context);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
