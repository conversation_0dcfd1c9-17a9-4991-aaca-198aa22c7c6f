import 'package:dartz/dartz.dart';
import 'package:deyncare_app/core/errors/failures.dart';
import 'package:deyncare_app/core/utils/business_validation.dart';
import 'package:deyncare_app/domain/repositories/customer_repository.dart';

/// Use case for customer business logic validation
/// Implements business rules that match backend exactly
class CustomerBusinessLogicUseCase {
  final CustomerRepository _repository;

  CustomerBusinessLogicUseCase(this._repository);

  /// Checks if customer can take new debt based on credit limit business rule
  /// Matches backend logic in customer.model.js hasExceededCreditLimit method
  Future<Either<Failure, CustomerDebtEligibilityResult>> checkDebtEligibility({
    required String customerId,
    required double newDebtAmount,
  }) async {
    try {
      // Validate customer ID format (business validation)
      final customerIdValidation = BusinessValidation.validateCustomerId(customerId);
      if (!customerIdValidation.isValid) {
        return Left(ValidationFailure(message: customerIdValidation.errorMessage!));
      }

      // Validate debt amount (business validation)
      final debtAmountValidation = BusinessValidation.validateDebtAmount(newDebtAmount);
      if (!debtAmountValidation.isValid) {
        return Left(ValidationFailure(message: debtAmountValidation.errorMessage!));
      }

      // Get current customer details
      final customerResult = await _repository.getCustomerById(customerId);
      return customerResult.fold(
        (failure) => Left(failure),
        (customerResponse) {
          final customerData = customerResponse.data;
          if (customerData == null) {
            return Left(ServerFailure(message: 'No customer data found'));
          }
          final customer = customerData.customer;
          final financials = customerData.statistics?.financials;
          final riskProfile = customerData.riskProfile;
          
          // Get financial data with fallbacks
          final currentOutstanding = financials?.totalOutstanding ?? 0.0;
          final creditLimit = 0.0; // TODO: Add creditLimit to backend customer model
          final riskLevel = riskProfile?.currentRiskLevel ?? 'Not Assessed';
          
          // Calculate new total outstanding after this debt
          final newTotalOutstanding = currentOutstanding + newDebtAmount;

          // Apply business rule: can customer take new debt?
          final businessRuleValidation = BusinessValidation.canCustomerTakeDebt(
            customerId,
            creditLimit,
            newTotalOutstanding, // Use projected outstanding, not current
          );

          if (!businessRuleValidation.isValid) {
            return Right(CustomerDebtEligibilityResult(
              canTakeDebt: false,
              customerId: customerId,
              customerName: customer.customerName ?? 'Unknown Customer',
              currentOutstanding: currentOutstanding,
              creditLimit: creditLimit,
              requestedDebtAmount: newDebtAmount,
              projectedTotalOutstanding: newTotalOutstanding,
              availableCredit: creditLimit > 0 
                ? (creditLimit - currentOutstanding).clamp(0, double.infinity)
                : double.infinity,
              businessRuleMessage: businessRuleValidation.errorMessage!,
              riskLevel: riskLevel,
            ));
          }

          // Customer is eligible for new debt
          return Right(CustomerDebtEligibilityResult(
            canTakeDebt: true,
            customerId: customerId,
            customerName: customer.customerName ?? 'Unknown Customer',
            currentOutstanding: currentOutstanding,
            creditLimit: creditLimit,
            requestedDebtAmount: newDebtAmount,
            projectedTotalOutstanding: newTotalOutstanding,
            availableCredit: creditLimit > 0 
              ? (creditLimit - currentOutstanding).clamp(0, double.infinity)
              : double.infinity,
            businessRuleMessage: 'Customer is eligible for new debt',
            riskLevel: riskLevel,
          ));
        },
      );

    } catch (e) {
      return Left(ServerFailure(message: 'Failed to check debt eligibility: ${e.toString()}'));
    }
  }

  /// Validates customer data before update/create operations
  /// Ensures all business rules are enforced
  Future<Either<Failure, CustomerValidationResult>> validateCustomerData({
    required String customerName,
    required String customerType,
    required String phone,
    String? email,
    String? address,
    double? creditLimit,
    String? category,
    String? notes,
  }) async {
    // Validate all fields using business validation
    final validations = [
      BusinessValidation.validateCustomerName(customerName),
      BusinessValidation.validateCustomerType(customerType),
      BusinessValidation.validatePhone(phone),
      BusinessValidation.validateEmail(email),
      BusinessValidation.validateAddress(address),
      BusinessValidation.validateCreditLimit(creditLimit),
      BusinessValidation.validateCategory(category),
      BusinessValidation.validateNotes(notes),
    ];

    // Check for any validation errors
    for (final validation in validations) {
      if (!validation.isValid) {
        return Left(ValidationFailure(message: validation.errorMessage!));
      }
    }

    return Right(CustomerValidationResult(
      isValid: true,
      validationMessage: 'All customer data is valid',
    ));
  }
}

/// Result of customer debt eligibility check
class CustomerDebtEligibilityResult {
  final bool canTakeDebt;
  final String customerId;
  final String customerName;
  final double currentOutstanding;
  final double creditLimit;
  final double requestedDebtAmount;
  final double projectedTotalOutstanding;
  final double availableCredit;
  final String businessRuleMessage;
  final String riskLevel;

  const CustomerDebtEligibilityResult({
    required this.canTakeDebt,
    required this.customerId,
    required this.customerName,
    required this.currentOutstanding,
    required this.creditLimit,
    required this.requestedDebtAmount,
    required this.projectedTotalOutstanding,
    required this.availableCredit,
    required this.businessRuleMessage,
    required this.riskLevel,
  });
}

/// Result of customer validation
class CustomerValidationResult {
  final bool isValid;
  final String validationMessage;

  const CustomerValidationResult({
    required this.isValid,
    required this.validationMessage,
  });
} 