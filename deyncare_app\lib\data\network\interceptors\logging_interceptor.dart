import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// Custom logging interceptor for better debugging
class LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (kDebugMode) {
      print('🌐 REQUEST[${options.method}] => PATH: ${options.path}');
      print('🌐 Headers: ${options.headers}');
      print('🌐 Query Parameters: ${options.queryParameters}');
      
      if (options.data != null) {
        print('🌐 Body: ${_truncateIfNeeded(options.data.toString())}');
      }
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (kDebugMode) {
      print('✅ RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}');
      print('✅ Data: ${_truncateIfNeeded(response.data.toString())}');
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (kDebugMode) {
      print('❌ ERROR[${err.response?.statusCode}] => PATH: ${err.requestOptions.path}');
      print('❌ Error message: ${err.message}');
      if (err.response?.data != null) {
        print('❌ Response data: ${_truncateIfNeeded(err.response!.data.toString())}');
      }
    }
    super.onError(err, handler);
  }
  
  // Truncate long responses for better logging
  String _truncateIfNeeded(String data, {int maxLength = 500}) {
    if (data.length <= maxLength) {
      return data;
    }
    return '${data.substring(0, maxLength)}... [truncated ${data.length - maxLength} chars]';
  }
}
