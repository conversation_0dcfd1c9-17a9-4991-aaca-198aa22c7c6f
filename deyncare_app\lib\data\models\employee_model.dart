import 'package:deyncare_app/domain/models/employee.dart';

/// Data model for employee/user role management with JSON serialization
class EmployeeModel extends Employee {
  EmployeeModel({
    required super.userId,
    required super.fullName,
    required super.email,
    super.phone,
    required super.role,
    super.shopId,
    required super.status,
    super.visibility,
    super.createdAt,
    super.updatedAt,
    // Additional fields
    super.userTitle,
    super.isSuspended = false,
    super.suspensionReason,
    super.suspendedAt,
    super.suspendedBy,
    super.verified = false,
    super.emailVerified = false,
    super.isPaid = false,
    super.isActivated = false,
    super.lastLoginAt,
    super.profilePicture,
    super.isDeleted = false,
    super.deletedAt,
  });

  /// Create EmployeeModel from JSON map
  factory EmployeeModel.fromJson(Map<String, dynamic> json) {
    return EmployeeModel(
      userId: json['userId'] ?? '',
      fullName: json['fullName'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'],
      role: json['role'] ?? 'employee',
      shopId: json['shopId'],
      status: json['status'] ?? 'active',
      visibility: json['visibility'] != null
          ? EmployeeVisibilityModel.fromJson(json['visibility'])
          : null,
      createdAt: json['createdAt'] != null
          ? DateTime.tryParse(json['createdAt'])
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.tryParse(json['updatedAt'])
          : null,
      // Additional fields from backend
      userTitle: json['userTitle'],
      isSuspended: json['isSuspended'] ?? false,
      suspensionReason: json['suspensionReason'],
      suspendedAt: json['suspendedAt'] != null
          ? DateTime.tryParse(json['suspendedAt'])
          : null,
      suspendedBy: json['suspendedBy'],
      verified: json['verified'] ?? false,
      emailVerified: json['emailVerified'] ?? false,
      isPaid: json['isPaid'] ?? false,
      isActivated: json['isActivated'] ?? false,
      lastLoginAt: json['lastLoginAt'] != null
          ? DateTime.tryParse(json['lastLoginAt'])
          : null,
      profilePicture: json['profilePicture'],
      isDeleted: json['isDeleted'] ?? false,
      deletedAt: json['deletedAt'] != null
          ? DateTime.tryParse(json['deletedAt'])
          : null,
    );
  }

  /// Convert EmployeeModel to JSON map
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'fullName': fullName,
      'email': email,
      'phone': phone,
      'role': role,
      'shopId': shopId,
      'status': status,
      'visibility': visibility is EmployeeVisibilityModel
          ? (visibility as EmployeeVisibilityModel).toJson()
          : null,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      // Additional fields
      'userTitle': userTitle,
      'isSuspended': isSuspended,
      'suspensionReason': suspensionReason,
      'suspendedAt': suspendedAt?.toIso8601String(),
      'suspendedBy': suspendedBy,
      'verified': verified,
      'emailVerified': emailVerified,
      'isPaid': isPaid,
      'isActivated': isActivated,
      'lastLoginAt': lastLoginAt?.toIso8601String(),
      'profilePicture': profilePicture,
      'isDeleted': isDeleted,
      'deletedAt': deletedAt?.toIso8601String(),
    };
  }

  /// Convert to domain Employee entity
  Employee toDomain() {
    return Employee(
      userId: userId,
      fullName: fullName,
      email: email,
      phone: phone,
      role: role,
      shopId: shopId,
      status: status,
      visibility: visibility,
      createdAt: createdAt,
      updatedAt: updatedAt,
      userTitle: userTitle,
      isSuspended: isSuspended,
      suspensionReason: suspensionReason,
      suspendedAt: suspendedAt,
      suspendedBy: suspendedBy,
      verified: verified,
      emailVerified: emailVerified,
      isPaid: isPaid,
      isActivated: isActivated,
      lastLoginAt: lastLoginAt,
      profilePicture: profilePicture,
      isDeleted: isDeleted,
      deletedAt: deletedAt,
    );
  }

  /// Create a copy with some fields replaced
  @override
  EmployeeModel copyWith({
    String? userId,
    String? fullName,
    String? email,
    String? phone,
    String? role,
    String? shopId,
    String? status,
    EmployeeVisibility? visibility,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? userTitle,
    bool? isSuspended,
    String? suspensionReason,
    DateTime? suspendedAt,
    String? suspendedBy,
    bool? verified,
    bool? emailVerified,
    bool? isPaid,
    bool? isActivated,
    DateTime? lastLoginAt,
    String? profilePicture,
    bool? isDeleted,
    DateTime? deletedAt,
  }) {
    return EmployeeModel(
      userId: userId ?? this.userId,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      shopId: shopId ?? this.shopId,
      status: status ?? this.status,
      visibility: visibility ?? this.visibility,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      userTitle: userTitle ?? this.userTitle,
      isSuspended: isSuspended ?? this.isSuspended,
      suspensionReason: suspensionReason ?? this.suspensionReason,
      suspendedAt: suspendedAt ?? this.suspendedAt,
      suspendedBy: suspendedBy ?? this.suspendedBy,
      verified: verified ?? this.verified,
      emailVerified: emailVerified ?? this.emailVerified,
      isPaid: isPaid ?? this.isPaid,
      isActivated: isActivated ?? this.isActivated,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      profilePicture: profilePicture ?? this.profilePicture,
      isDeleted: isDeleted ?? this.isDeleted,
      deletedAt: deletedAt ?? this.deletedAt,
    );
  }
}

/// Employee visibility/permissions model
class EmployeeVisibilityModel extends EmployeeVisibility {
  EmployeeVisibilityModel({
    required super.customerManagement,
    required super.debtManagement,
    required super.reportManagement,
  });

  factory EmployeeVisibilityModel.fromJson(Map<String, dynamic> json) {
    return EmployeeVisibilityModel(
      customerManagement:
          ModulePermissionsModel.fromJson(json['customerManagement'] ?? {}),
      debtManagement:
          ModulePermissionsModel.fromJson(json['debtManagement'] ?? {}),
      reportManagement:
          ReportPermissionsModel.fromJson(json['reportManagement'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'customerManagement':
          (customerManagement as ModulePermissionsModel).toJson(),
      'debtManagement': (debtManagement as ModulePermissionsModel).toJson(),
      'reportManagement': (reportManagement as ReportPermissionsModel).toJson(),
    };
  }

  /// Create default permissions (all false)
  factory EmployeeVisibilityModel.defaultPermissions() {
    return EmployeeVisibilityModel(
      customerManagement: ModulePermissionsModel.defaultPermissions(),
      debtManagement: ModulePermissionsModel.defaultPermissions(),
      reportManagement: ReportPermissionsModel.defaultPermissions(),
    );
  }

  /// Create full access permissions (all true)
  factory EmployeeVisibilityModel.fullAccess() {
    return EmployeeVisibilityModel(
      customerManagement: ModulePermissionsModel.fullAccess(),
      debtManagement: ModulePermissionsModel.fullAccess(),
      reportManagement: ReportPermissionsModel.fullAccess(),
    );
  }
}

/// Module permissions for customer and debt management
class ModulePermissionsModel extends ModulePermissions {
  ModulePermissionsModel({
    required super.create,
    required super.update,
    required super.view,
    required super.delete,
  });

  factory ModulePermissionsModel.fromJson(Map<String, dynamic> json) {
    return ModulePermissionsModel(
      create: json['create'] ?? false,
      update: json['update'] ?? false,
      view: json['view'] ?? false,
      delete: json['delete'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'create': create,
      'update': update,
      'view': view,
      'delete': delete,
    };
  }

  factory ModulePermissionsModel.defaultPermissions() {
    return ModulePermissionsModel(
      create: false,
      update: false,
      view: false,
      delete: false,
    );
  }

  factory ModulePermissionsModel.fullAccess() {
    return ModulePermissionsModel(
      create: true,
      update: true,
      view: true,
      delete: true,
    );
  }
}

/// Report permissions model
class ReportPermissionsModel extends ReportPermissions {
  ReportPermissionsModel({
    required super.generate,
    required super.delete,
    required super.view,
  });

  factory ReportPermissionsModel.fromJson(Map<String, dynamic> json) {
    return ReportPermissionsModel(
      generate: json['generate'] ?? false,
      delete: json['delete'] ?? false,
      view: json['view'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'generate': generate,
      'delete': delete,
      'view': view,
    };
  }

  factory ReportPermissionsModel.defaultPermissions() {
    return ReportPermissionsModel(
      generate: false,
      delete: false,
      view: false,
    );
  }

  factory ReportPermissionsModel.fullAccess() {
    return ReportPermissionsModel(
      generate: true,
      delete: true,
      view: true,
    );
  }
}

/// Employee creation request model
class CreateEmployeeRequestModel {
  final String fullName;
  final String userTitle;
  final String email;
  final String phone;
  final String password;
  final String confirmPassword;
  final Map<String, dynamic> visibility;

  CreateEmployeeRequestModel({
    required this.fullName,
    required this.userTitle,
    required this.email,
    required this.phone,
    required this.password,
    required this.confirmPassword,
    required this.visibility,
  });

  Map<String, dynamic> toJson() {
    return {
      'fullName': fullName,
      'userTitle': userTitle,
      'email': email,
      'phone': phone,
      'password': password,
      'confirmPassword': confirmPassword,
      'visibility': visibility,
    };
  }
}

/// Employee update request model
class UpdateEmployeeRequestModel {
  final String? fullName;
  final EmployeeVisibilityModel? visibility;

  UpdateEmployeeRequestModel({
    this.fullName,
    this.visibility,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (fullName != null) data['userTitle'] = fullName;
    if (visibility != null) data['visibility'] = visibility!.toJson();
    return data;
  }
}
