import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:deyncare_app/core/routes/app_router.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/constants/app_strings.dart';
import 'package:deyncare_app/core/providers/theme_provider.dart';
import 'package:deyncare_app/presentation/screens/splash/splash_screen.dart';
import 'package:deyncare_app/injection_container.dart' as di;
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_event.dart';
import 'package:deyncare_app/presentation/blocs/dashboard/dashboard_bloc.dart';
import 'package:deyncare_app/presentation/blocs/notification/notification_bloc.dart';
import 'package:deyncare_app/presentation/blocs/subscription/subscription_bloc.dart';
import 'package:deyncare_app/data/services/deep_link_service.dart';
import 'package:deyncare_app/data/services/firebase_service.dart';
import 'package:deyncare_app/presentation/widgets/connectivity_banner.dart';

// New BLoCs added
import 'package:deyncare_app/presentation/blocs/customer/customer_bloc.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_bloc.dart';
import 'package:deyncare_app/presentation/blocs/payment/payment_bloc.dart';
import 'package:deyncare_app/presentation/blocs/reports/customer_report_bloc.dart';
import 'package:deyncare_app/presentation/blocs/reports/debt_report_bloc.dart';
import 'package:deyncare_app/presentation/blocs/reports/risk_report_bloc.dart';

// Auth lifecycle management imports
import 'package:deyncare_app/data/repositories/auth_repository_impl.dart';
import 'package:deyncare_app/core/utils/auth_debug_logger.dart';
import 'package:deyncare_app/data/network/token/token_manager.dart';
import 'package:deyncare_app/data/network/clients/dio_client.dart';
import 'package:deyncare_app/data/services/auth/auth_utils.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependency injection
  await di.init();

  // Initialize Firebase for push notifications
  try {
    await FirebaseService.initialize();
    if (kDebugMode) {
      print('🔥 Firebase initialized successfully');
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Firebase initialization failed: $e');
    }
    // Continue app initialization even if Firebase fails
  }

  runApp(const DeynCareApp());
}

class DeynCareApp extends StatefulWidget {
  static final GlobalKey<NavigatorState> _navigatorKey =
      GlobalKey<NavigatorState>();

  static GlobalKey<NavigatorState> get navigatorKey => _navigatorKey;

  const DeynCareApp({super.key});

  @override
  State<DeynCareApp> createState() => _DeynCareAppState();
}

class _DeynCareAppState extends State<DeynCareApp> with WidgetsBindingObserver {
  bool _isThemeInitialized = false;
  late ThemeProvider _themeProvider;

  // Auth lifecycle management components
  late AuthRepositoryImpl _authRepository;
  late TokenManager _tokenManager;
  late DioClient _dioClient;
  late AuthUtils _authUtils;

  @override
  void initState() {
    super.initState();

    // Initialize auth components for lifecycle management
    _initAuthComponents();

    // Register for app lifecycle events
    WidgetsBinding.instance.addObserver(this);

    _initializeTheme();
  }

  /// Initialize authentication components for lifecycle management
  void _initAuthComponents() {
    try {
      _authRepository = di.sl<AuthRepositoryImpl>();
      _tokenManager = di.sl<TokenManager>();
      _dioClient = di.sl<DioClient>();
      _authUtils = di.sl<AuthUtils>();

      if (kDebugMode) {
        print('🔐 Auth lifecycle components initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error initializing auth components: $e');
      }
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (kDebugMode) {
      print('📱 App lifecycle changed: $state');
    }

    // Handle authentication-related lifecycle events
    _handleAuthLifecycleEvent(state);
  }

  /// Handle authentication-related app lifecycle events
  void _handleAuthLifecycleEvent(AppLifecycleState state) async {
    try {
      switch (state) {
        case AppLifecycleState.resumed:
          if (kDebugMode) {
            print('🔄 App resumed - validating session');
          }

          // Log current auth state for debugging
          await AuthDebugLogger.logAuthState(
            tokenManager: _tokenManager,
            dioClient: _dioClient,
            authUtils: _authUtils,
            context: 'App Resumed',
          );

          // Handle session validation on app resume
          await _authRepository.handleAppLifecycleChange('resumed');

          AuthDebugLogger.logAppLifecycle('resumed',
              authAction: 'Session validation completed');
          break;

        case AppLifecycleState.paused:
          if (kDebugMode) {
            print('⏸️ App paused - preserving session');
          }

          await _authRepository.handleAppLifecycleChange('paused');

          AuthDebugLogger.logAppLifecycle('paused',
              authAction: 'Session state preserved');
          break;

        case AppLifecycleState.detached:
          if (kDebugMode) {
            print('🔒 App detached');
          }

          await _authRepository.handleAppLifecycleChange('detached');

          AuthDebugLogger.logAppLifecycle('detached',
              authAction: 'App terminating');
          break;

        case AppLifecycleState.inactive:
          if (kDebugMode) {
            print('💤 App inactive');
          }
          // No specific auth action needed for inactive state
          break;

        case AppLifecycleState.hidden:
          if (kDebugMode) {
            print('🙈 App hidden');
          }
          // No specific auth action needed for hidden state
          break;
      }
    } catch (e) {
      AuthDebugLogger.logAuthError('App Lifecycle Handling', e,
          context: 'State: $state');
    }
  }

  Future<void> _initializeTheme() async {
    _themeProvider = ThemeProvider();
    await _themeProvider.initializeTheme();
    if (mounted) {
      setState(() {
        _isThemeInitialized = true;
      });
      // Initialize deep links after theme is ready and first frame is rendered
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _initDeepLinks();
      });
    }
  }

  // Initialize deep link handling
  Future<void> _initDeepLinks() async {
    if (DeynCareApp._navigatorKey.currentContext != null) {
      await DeepLinkService.initDeepLinks(
          DeynCareApp._navigatorKey.currentContext!);
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    DeepLinkService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Show loading screen only during initial theme loading
    if (!_isThemeInitialized) {
      return MaterialApp(
        home: Scaffold(
          backgroundColor: Colors.white,
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  color: AppThemes.primaryColor,
                ),
                const SizedBox(height: 16),
                Text(
                  'Loading...',
                  style: TextStyle(
                    color: AppThemes.primaryColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // Theme initialized, build main app with efficient provider
    return MultiBlocProvider(
      providers: [
        // Core BLoCs
        BlocProvider<AuthBloc>(
          create: (context) {
            final authBloc = di.sl<AuthBloc>();

            // Log initial auth state for debugging
            AuthDebugLogger.logAuthFlow('AuthBloc Creation',
                details: 'Initial auth state check starting');

            authBloc.add(AppStarted());
            return authBloc;
          },
        ),
        BlocProvider<DashboardBloc>(
          create: (context) => di.sl<DashboardBloc>(),
        ),
        BlocProvider<NotificationBloc>(
          create: (context) => di.sl<NotificationBloc>(),
        ),
        BlocProvider<SubscriptionBloc>(
          create: (context) => di.sl<SubscriptionBloc>(),
        ),

        // Business Logic BLoCs
        BlocProvider<CustomerBloc>(
          create: (context) => di.sl<CustomerBloc>(),
        ),
        BlocProvider<DebtBloc>(
          create: (context) => di.sl<DebtBloc>(),
        ),
        BlocProvider<PaymentBloc>(
          create: (context) => di.sl<PaymentBloc>(),
        ),

        // Reports BLoCs
        BlocProvider<CustomerReportBloc>(
          create: (context) => di.sl<CustomerReportBloc>(),
        ),
        BlocProvider<DebtReportBloc>(
          create: (context) => di.sl<DebtReportBloc>(),
        ),
        BlocProvider<RiskReportBloc>(
          create: (context) => di.sl<RiskReportBloc>(),
        ),
      ],
      child: ChangeNotifierProvider.value(
        value: _themeProvider,
        child: Consumer<ThemeProvider>(
          builder: (context, themeProvider, child) {
            if (kDebugMode) {
              print(
                  '🎨 Building app with theme mode: ${themeProvider.themeMode}');
            }

            return MaterialApp(
              navigatorKey: DeynCareApp._navigatorKey,
              title: AppStrings.appName,
              debugShowCheckedModeBanner: false,
              theme: themeProvider.lightTheme,
              darkTheme: themeProvider.darkTheme,
              themeMode: themeProvider.themeMode,
              onGenerateRoute: AppRouter.onGenerateRoute,
              initialRoute: AppRouter.splashRoute,
              home: const SplashScreen(),
              builder: (context, child) {
                return Stack(
                  children: [
                    child!,
                    const Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      child: ConnectivityBanner(),
                    ),
                  ],
                );
              },
            );
          },
        ),
      ),
    );
  }
}

// Clean architecture implementation - No template code needed
