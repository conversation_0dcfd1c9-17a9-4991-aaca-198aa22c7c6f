// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in deyncare_app/test/unit/data/repositories/auth_repository_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;
import 'dart:io' as _i6;

import 'package:deyncare_app/data/models/user_model.dart' as _i3;
import 'package:deyncare_app/data/network/token/token_manager.dart' as _i7;
import 'package:deyncare_app/data/services/auth/auth_remote_source.dart' as _i4;
import 'package:deyncare_app/data/services/auth/auth_utils.dart' as _i8;
import 'package:deyncare_app/domain/models/user.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i9;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUser_0 extends _i1.SmartFake implements _i2.User {
  _FakeUser_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserModel_1 extends _i1.SmartFake implements _i3.UserModel {
  _FakeUserModel_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AuthRemoteSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRemoteSource extends _i1.Mock implements _i4.AuthRemoteSource {
  MockAuthRemoteSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<Map<String, dynamic>> login(String? email, String? password) =>
      (super.noSuchMethod(
            Invocation.method(#login, [email, password]),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<Map<String, dynamic>> register({
    required String? fullName,
    required String? email,
    required String? phone,
    required String? password,
    required String? shopName,
    required String? shopAddress,
    _i6.File? shopLogo,
    String? planType = 'trial',
    String? paymentMethod = 'offline',
    bool? initialPaid = false,
    String? discountCode,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#register, [], {
              #fullName: fullName,
              #email: email,
              #phone: phone,
              #password: password,
              #shopName: shopName,
              #shopAddress: shopAddress,
              #shopLogo: shopLogo,
              #planType: planType,
              #paymentMethod: paymentMethod,
              #initialPaid: initialPaid,
              #discountCode: discountCode,
            }),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<Map<String, dynamic>> createEmployee({
    required String? fullName,
    required String? email,
    required String? phone,
    String? password,
    bool? generatePassword = true,
    List<String>? permissions,
    String? position,
    String? note,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createEmployee, [], {
              #fullName: fullName,
              #email: email,
              #phone: phone,
              #password: password,
              #generatePassword: generatePassword,
              #permissions: permissions,
              #position: position,
              #note: note,
            }),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<Map<String, dynamic>> verifyEmail(
    String? email,
    String? verificationCode,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#verifyEmail, [email, verificationCode]),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<void> resendVerification(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#resendVerification, [email]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<Map<String, dynamic>> processPayment({
    required String? userId,
    required String? shopId,
    required String? planId,
    required String? paymentMethod,
    required String? phoneNumber,
    required double? amount,
    String? discountCode,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#processPayment, [], {
              #userId: userId,
              #shopId: shopId,
              #planId: planId,
              #paymentMethod: paymentMethod,
              #phoneNumber: phoneNumber,
              #amount: amount,
              #discountCode: discountCode,
            }),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<Map<String, dynamic>> checkEmailExists(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#checkEmailExists, [email]),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<Map<String, dynamic>> forgotPassword(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#forgotPassword, [email]),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<Map<String, dynamic>> resetPassword({
    required String? token,
    required String? newPassword,
    required String? confirmPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#resetPassword, [], {
              #token: token,
              #newPassword: newPassword,
              #confirmPassword: confirmPassword,
            }),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<void> logout() =>
      (super.noSuchMethod(
            Invocation.method(#logout, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logoutAll() =>
      (super.noSuchMethod(
            Invocation.method(#logoutAll, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<Map<String, dynamic>> refreshToken(String? refreshToken) =>
      (super.noSuchMethod(
            Invocation.method(#refreshToken, [refreshToken]),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<Map<String, dynamic>> changePassword({
    required String? currentPassword,
    required String? newPassword,
    required String? confirmPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#changePassword, [], {
              #currentPassword: currentPassword,
              #newPassword: newPassword,
              #confirmPassword: confirmPassword,
            }),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<Map<String, dynamic>> getProfile() =>
      (super.noSuchMethod(
            Invocation.method(#getProfile, []),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<List<String>> getAvailablePaymentMethods(String? context) =>
      (super.noSuchMethod(
            Invocation.method(#getAvailablePaymentMethods, [context]),
            returnValue: _i5.Future<List<String>>.value(<String>[]),
          )
          as _i5.Future<List<String>>);
}

/// A class which mocks [TokenManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockTokenManager extends _i1.Mock implements _i7.TokenManager {
  MockTokenManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<String?> getAccessToken() =>
      (super.noSuchMethod(
            Invocation.method(#getAccessToken, []),
            returnValue: _i5.Future<String?>.value(),
          )
          as _i5.Future<String?>);

  @override
  _i5.Future<String?> getRefreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#getRefreshToken, []),
            returnValue: _i5.Future<String?>.value(),
          )
          as _i5.Future<String?>);

  @override
  _i5.Future<void> saveTokens({
    required String? accessToken,
    required String? refreshToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#saveTokens, [], {
              #accessToken: accessToken,
              #refreshToken: refreshToken,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> deleteTokens() =>
      (super.noSuchMethod(
            Invocation.method(#deleteTokens, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> clearTokens() =>
      (super.noSuchMethod(
            Invocation.method(#clearTokens, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<bool> isTokenExpired({
    Duration? buffer = const Duration(minutes: 2),
  }) =>
      (super.noSuchMethod(
            Invocation.method(#isTokenExpired, [], {#buffer: buffer}),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> shouldRefreshToken({
    Duration? refreshBuffer = const Duration(minutes: 5),
  }) =>
      (super.noSuchMethod(
            Invocation.method(#shouldRefreshToken, [], {
              #refreshBuffer: refreshBuffer,
            }),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> hasValidRefreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasValidRefreshToken, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> validateTokenIntegrity(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#validateTokenIntegrity, [token]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<Duration?> getTimeUntilExpiry() =>
      (super.noSuchMethod(
            Invocation.method(#getTimeUntilExpiry, []),
            returnValue: _i5.Future<Duration?>.value(),
          )
          as _i5.Future<Duration?>);

  @override
  _i5.Future<Map<String, dynamic>> getTokenInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getTokenInfo, []),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);
}

/// A class which mocks [AuthUtils].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthUtils extends _i1.Mock implements _i8.AuthUtils {
  MockAuthUtils() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<void> saveUserData(_i3.UserModel? user) =>
      (super.noSuchMethod(
            Invocation.method(#saveUserData, [user]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<_i3.UserModel?> getUserData() =>
      (super.noSuchMethod(
            Invocation.method(#getUserData, []),
            returnValue: _i5.Future<_i3.UserModel?>.value(),
          )
          as _i5.Future<_i3.UserModel?>);

  @override
  _i5.Future<void> clearUserData() =>
      (super.noSuchMethod(
            Invocation.method(#clearUserData, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  String getDeviceName() =>
      (super.noSuchMethod(
            Invocation.method(#getDeviceName, []),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.method(#getDeviceName, []),
            ),
          )
          as String);
}

/// A class which mocks [UserModel].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserModel extends _i1.Mock implements _i3.UserModel {
  MockUserModel() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get userId =>
      (super.noSuchMethod(
            Invocation.getter(#userId),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.getter(#userId),
            ),
          )
          as String);

  @override
  String get fullName =>
      (super.noSuchMethod(
            Invocation.getter(#fullName),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.getter(#fullName),
            ),
          )
          as String);

  @override
  String get email =>
      (super.noSuchMethod(
            Invocation.getter(#email),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.getter(#email),
            ),
          )
          as String);

  @override
  String get role =>
      (super.noSuchMethod(
            Invocation.getter(#role),
            returnValue: _i9.dummyValue<String>(this, Invocation.getter(#role)),
          )
          as String);

  @override
  String get status =>
      (super.noSuchMethod(
            Invocation.getter(#status),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.getter(#status),
            ),
          )
          as String);

  @override
  String get registrationStatus =>
      (super.noSuchMethod(
            Invocation.getter(#registrationStatus),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.getter(#registrationStatus),
            ),
          )
          as String);

  @override
  bool get isEmailVerified =>
      (super.noSuchMethod(
            Invocation.getter(#isEmailVerified),
            returnValue: false,
          )
          as bool);

  @override
  bool get isPaid =>
      (super.noSuchMethod(Invocation.getter(#isPaid), returnValue: false)
          as bool);

  @override
  bool get isShopActive =>
      (super.noSuchMethod(Invocation.getter(#isShopActive), returnValue: false)
          as bool);

  @override
  bool get isSuspended =>
      (super.noSuchMethod(Invocation.getter(#isSuspended), returnValue: false)
          as bool);

  @override
  bool get isAdmin =>
      (super.noSuchMethod(Invocation.getter(#isAdmin), returnValue: false)
          as bool);

  @override
  bool get isShopAdmin =>
      (super.noSuchMethod(Invocation.getter(#isShopAdmin), returnValue: false)
          as bool);

  @override
  bool get isSuperAdmin =>
      (super.noSuchMethod(Invocation.getter(#isSuperAdmin), returnValue: false)
          as bool);

  @override
  bool get isEmployee =>
      (super.noSuchMethod(Invocation.getter(#isEmployee), returnValue: false)
          as bool);

  @override
  bool get isActive =>
      (super.noSuchMethod(Invocation.getter(#isActive), returnValue: false)
          as bool);

  @override
  bool get isAccountSuspended =>
      (super.noSuchMethod(
            Invocation.getter(#isAccountSuspended),
            returnValue: false,
          )
          as bool);

  @override
  bool get canAccessMobileApp =>
      (super.noSuchMethod(
            Invocation.getter(#canAccessMobileApp),
            returnValue: false,
          )
          as bool);

  @override
  bool get shouldBeBlocked =>
      (super.noSuchMethod(
            Invocation.getter(#shouldBeBlocked),
            returnValue: false,
          )
          as bool);

  @override
  Map<String, dynamic> toJson() =>
      (super.noSuchMethod(
            Invocation.method(#toJson, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  _i2.User toDomain() =>
      (super.noSuchMethod(
            Invocation.method(#toDomain, []),
            returnValue: _FakeUser_0(this, Invocation.method(#toDomain, [])),
          )
          as _i2.User);

  @override
  _i3.UserModel copyWith({
    String? userId,
    String? fullName,
    String? email,
    String? phone,
    String? role,
    String? shopId,
    String? status,
    List<String>? permissions,
    String? registrationStatus,
    bool? isEmailVerified,
    bool? isPaid,
    DateTime? emailVerifiedAt,
    DateTime? paymentCompletedAt,
    String? verificationCode,
    DateTime? verificationCodeExpiresAt,
    String? shopName,
    String? shopStatus,
    bool? isShopActive,
    String? accessToken,
    String? refreshToken,
    DateTime? tokenExpiresAt,
    bool? isSuspended,
    String? suspensionReason,
    DateTime? suspendedAt,
    String? suspendedBy,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#copyWith, [], {
              #userId: userId,
              #fullName: fullName,
              #email: email,
              #phone: phone,
              #role: role,
              #shopId: shopId,
              #status: status,
              #permissions: permissions,
              #registrationStatus: registrationStatus,
              #isEmailVerified: isEmailVerified,
              #isPaid: isPaid,
              #emailVerifiedAt: emailVerifiedAt,
              #paymentCompletedAt: paymentCompletedAt,
              #verificationCode: verificationCode,
              #verificationCodeExpiresAt: verificationCodeExpiresAt,
              #shopName: shopName,
              #shopStatus: shopStatus,
              #isShopActive: isShopActive,
              #accessToken: accessToken,
              #refreshToken: refreshToken,
              #tokenExpiresAt: tokenExpiresAt,
              #isSuspended: isSuspended,
              #suspensionReason: suspensionReason,
              #suspendedAt: suspendedAt,
              #suspendedBy: suspendedBy,
            }),
            returnValue: _FakeUserModel_1(
              this,
              Invocation.method(#copyWith, [], {
                #userId: userId,
                #fullName: fullName,
                #email: email,
                #phone: phone,
                #role: role,
                #shopId: shopId,
                #status: status,
                #permissions: permissions,
                #registrationStatus: registrationStatus,
                #isEmailVerified: isEmailVerified,
                #isPaid: isPaid,
                #emailVerifiedAt: emailVerifiedAt,
                #paymentCompletedAt: paymentCompletedAt,
                #verificationCode: verificationCode,
                #verificationCodeExpiresAt: verificationCodeExpiresAt,
                #shopName: shopName,
                #shopStatus: shopStatus,
                #isShopActive: isShopActive,
                #accessToken: accessToken,
                #refreshToken: refreshToken,
                #tokenExpiresAt: tokenExpiresAt,
                #isSuspended: isSuspended,
                #suspensionReason: suspensionReason,
                #suspendedAt: suspendedAt,
                #suspendedBy: suspendedBy,
              }),
            ),
          )
          as _i3.UserModel);

  @override
  bool hasPermission(String? permission) =>
      (super.noSuchMethod(
            Invocation.method(#hasPermission, [permission]),
            returnValue: false,
          )
          as bool);
}
