import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/utils/logger.dart';
import 'package:deyncare_app/core/utils/permission_utils.dart';
import 'package:deyncare_app/presentation/widgets/common_app_bar.dart' hide CommonFAB;
import 'package:deyncare_app/presentation/widgets/common_list_item.dart';
import 'package:deyncare_app/presentation/widgets/common_button.dart';
import 'package:deyncare_app/presentation/widgets/skeleton_loader.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_bloc.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_state.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_event.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_state.dart';
import 'package:deyncare_app/presentation/screens/debt/widgets/debt_search_bar.dart';
import 'package:deyncare_app/presentation/screens/debt/widgets/debt_stats_header.dart';
import 'package:deyncare_app/presentation/screens/debt/widgets/debt_filter_sheet.dart';
import 'package:deyncare_app/presentation/screens/debt/widgets/add_payment_dialog.dart';
import 'package:deyncare_app/presentation/widgets/modals/debt/debt_modal_handlers.dart';
import 'package:deyncare_app/presentation/widgets/permission_denied_dialog.dart';
import 'package:deyncare_app/injection_container.dart' as di;

/// Main debt list screen displaying all debts with search and filter
class DebtListScreen extends StatelessWidget {
  const DebtListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: di.sl<DebtBloc>()
        ..add(const LoadDebts())
        ..add(const LoadDebtStats()),
      child: const DebtListView(),
    );
  }
}

class DebtListView extends StatefulWidget {
  const DebtListView({super.key});

  @override
  State<DebtListView> createState() => _DebtListViewState();
}

class _DebtListViewState extends State<DebtListView> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Remove infinite scroll listener
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // Remove the infinite scroll method
  // void _onScroll() { ... }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, authState) {
        final user = authState is AuthAuthenticated ? authState.user : null;

        return Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          appBar: CommonAppBar(
            title: 'Debts',
            actions: _buildPermissionAwareActions(context, user),
          ),
      body: Column(
        children: [
          // Stats header
          const DebtStatsHeader(),
          
          // Search bar
          DebtSearchBar(
            controller: _searchController,
            onChanged: _onSearchChanged,
          ),
          
          // Debt list with pagination
          Expanded(
            child: BlocConsumer<DebtBloc, DebtState>(
              buildWhen: (previous, current) =>
                  current is DebtListLoaded ||
                  current is DebtListLoading ||
                  current is DebtListError,
              listenWhen: (previous, current) =>
                  current is DebtError ||
                  current is DebtCreated ||
                  current is DebtUpdated ||
                  current is PaymentAdded ||
                  current is DebtDeleted,
              listener: _handleStateChanges,
              builder: (context, state) => _buildBody(context, state),
            ),
          ),
          
          // Pagination controls (will be shown only when list is loaded)
          BlocBuilder<DebtBloc, DebtState>(
            buildWhen: (previous, current) => current is DebtListLoaded,
            builder: (context, state) {
              if (state is DebtListLoaded) {
                return _buildPaginationControls(context, state);
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      );
      },
    );
  }

  List<Widget> _buildPermissionAwareActions(BuildContext context, User? user) {
    final actions = <Widget>[
      IconButton(
        icon: Icon(
          Icons.refresh,
          color: Theme.of(context).appBarTheme.iconTheme?.color,
        ),
        onPressed: () => _refreshDebts(context),
        tooltip: 'Refresh',
        style: IconButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.surface.withValues(alpha: 0.1),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
      ),
      IconButton(
        icon: Icon(
          Icons.filter_list,
          color: Theme.of(context).appBarTheme.iconTheme?.color,
        ),
        onPressed: () => _showFilterOptions(context),
        tooltip: 'Filter',
        style: IconButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.surface.withValues(alpha: 0.1),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
      ),
    ];

    // Only show "Add Debt" button if user has permission to create debts
    if (PermissionUtils.canCreateDebts(user)) {
      actions.add(
        IconButton(
          icon: Icon(
            Icons.add,
            color: Theme.of(context).colorScheme.onPrimary,
          ),
          onPressed: () => _navigateToCreateDebt(context),
          tooltip: 'Add Debt',
          style: IconButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        ),
      );
    }

    actions.add(const SizedBox(width: 8));
    return actions;
  }

  void _handleStateChanges(BuildContext context, DebtState state) {
    if (state is DebtError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: Theme.of(context).colorScheme.error,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } else if (state is DebtCreated) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Debt created successfully!'),
          backgroundColor: AppThemes.successColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
      // Refresh the list and stats using the refresh function
      _refreshDebtsAfterUpdate(context);
    } else if (state is DebtUpdated) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Debt updated successfully!'),
          backgroundColor: AppThemes.successColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
      // Immediately refresh the list and stats after successful update
      _refreshDebtsAfterUpdate(context);
    } else if (state is PaymentAdded) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Payment recorded successfully!'),
          backgroundColor: AppThemes.successColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
      // IMPORTANT: Automatically refresh to prevent icon issues
      _refreshDebtsAfterUpdate(context);
    } else if (state is DebtDeleted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Debt deleted successfully!'),
          backgroundColor: AppThemes.successColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
      // IMPORTANT: Automatically refresh to prevent list state issues
      _refreshDebtsAfterUpdate(context);
    }
  }

  Widget _buildBody(BuildContext context, DebtState state) {
    if (state is DebtListLoading) {
      return _buildLoadingState();
    }
    
    if (state is DebtListError) {
      return _buildErrorState(context, state);
    }
    
    if (state is DebtListLoaded) {
      return _buildLoadedState(context, state);
    }
    
    // If it's initial state or other state, show loading
    return _buildLoadingState();
  }

  Widget _buildLoadingState() {
    return SkeletonListView(
      itemCount: 5,
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemBuilder: (index) => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: _buildDebtItemSkeleton(),
      ),
    );
  }

  Widget _buildDebtItemSkeleton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Customer name and debt ID row
          Row(
            children: [
              SkeletonLoader.text(width: 120, height: 18),
              const Spacer(),
              SkeletonLoader.text(width: 60, height: 14),
            ],
          ),
          const SizedBox(height: 12),
          // Amount and status row
          Row(
            children: [
              SkeletonLoader.text(width: 80, height: 20),
              const Spacer(),
              SkeletonLoader(
                width: 60,
                height: 20,
                borderRadius: BorderRadius.circular(10),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Due date and remaining amount
          Row(
            children: [
              SkeletonLoader.text(width: 100, height: 14),
              const Spacer(),
              SkeletonLoader.text(width: 70, height: 14),
            ],
          ),
          const SizedBox(height: 16),
          // Action buttons row
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              SkeletonLoader.circular(size: 32),
              const SizedBox(width: 8),
              SkeletonLoader.circular(size: 32),
              const SizedBox(width: 8),
              SkeletonLoader.circular(size: 32),
              const SizedBox(width: 8),
              SkeletonLoader.circular(size: 32),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, DebtListError state) {
    final isAuthError = state.message.toLowerCase().contains('auth') || 
                       state.message.toLowerCase().contains('token') ||
                       state.message.toLowerCase().contains('unauthorized');
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: (isAuthError ? AppThemes.warningColor : AppThemes.errorColor).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                isAuthError ? Icons.lock_outline : Icons.error_outline,
                size: 64,
                color: isAuthError ? AppThemes.warningColor : AppThemes.errorColor,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              isAuthError ? 'Authentication Required' : 'Failed to load debts',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isAuthError 
                ? 'Please check your authentication status and try again.'
                : state.message,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _refreshDebts(context),
              icon: const Icon(Icons.refresh),
              label: Text(isAuthError ? 'Try Again' : 'Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: isAuthError ? AppThemes.warningColor : Theme.of(context).colorScheme.error,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadedState(BuildContext context, DebtListLoaded state) {
    final debts = state.response.debts;
    
    if (debts.isEmpty) {
      return _buildEmptyState(context);
    }
    
    return RefreshIndicator(
      onRefresh: _handleRefresh,
      color: Theme.of(context).colorScheme.primary,
      backgroundColor: Theme.of(context).colorScheme.surface,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: debts.length, // Remove infinite scroll item count
        itemBuilder: (context, index) {
          final debt = debts[index];
          return DebtListItem(
            debtId: debt.debtId,
            customerName: debt.customerName,
            amount: debt.amount,
            remainingAmount: debt.remainingAmount,
            dueDate: debt.dueDate,
            status: debt.status.displayName,
            onTap: () => _navigateToDebtDetails(context, debt.debtId),
            actions: [
              // View Action with Label
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                            Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(Icons.visibility_rounded, size: 18, color: Theme.of(context).colorScheme.primary),
                    ),
                    onPressed: () => _navigateToDebtDetails(context, debt.debtId),
                    tooltip: 'View Details',
                  ),
                  Text(
                    'View',
                    style: TextStyle(
                      fontSize: 10,
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              // Edit Action with Label
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppThemes.warningColor.withValues(alpha: 0.1),
                            AppThemes.warningColor.withValues(alpha: 0.2),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: AppThemes.warningColor.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(Icons.edit_rounded, size: 18, color: AppThemes.warningColor),
                    ),
                    onPressed: () => _navigateToEditDebt(context, debt.debtId),
                    tooltip: 'Edit Debt',
                  ),
                  Text(
                    'Edit',
                    style: TextStyle(
                      fontSize: 10,
                      color: AppThemes.warningColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              // Payment Action with Label (conditional)
              if (debt.remainingAmount > 0)
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              AppThemes.successColor.withValues(alpha: 0.1),
                              AppThemes.successColor.withValues(alpha: 0.2),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(10),
                          boxShadow: [
                            BoxShadow(
                              color: AppThemes.successColor.withValues(alpha: 0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(Icons.payments_rounded, size: 18, color: AppThemes.successColor),
                      ),
                      onPressed: () => _showAddPaymentDialog(context, debt.debtId),
                      tooltip: 'Add Payment',
                    ),
                    Text(
                      'Pay',
                      style: TextStyle(
                        fontSize: 10,
                        color: AppThemes.successColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              // Delete Action with Label
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppThemes.errorColor.withValues(alpha: 0.1),
                            AppThemes.errorColor.withValues(alpha: 0.2),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: AppThemes.errorColor.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(Icons.delete_rounded, size: 18, color: AppThemes.errorColor),
                    ),
                    onPressed: () => _navigateToDeleteDebt(context, debt.debtId),
                    tooltip: 'Delete Debt',
                  ),
                  Text(
                    'Delete',
                    style: TextStyle(
                      fontSize: 10,
                      color: AppThemes.errorColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  /// Build pagination controls for debt list
  Widget _buildPaginationControls(BuildContext context, DebtListLoaded state) {
    // TODO: Fix pagination structure - temporarily disabled
    // Need to check DebtListResult model for correct property names
    
    return const SizedBox.shrink(); // Temporarily disable pagination
    
    /* 
    final currentPage = state.response.currentPage ?? 1;
    final totalPages = state.response.totalPages ?? 1;
    final hasNextPage = state.response.hasNextPage ?? false;
    final hasPreviousPage = currentPage > 1;

    if (totalPages <= 1) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Page info
          Expanded(
            child: Text(
              'Page $currentPage of $totalPages',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
          
          // Previous button
          IconButton(
            onPressed: hasPreviousPage ? () => _loadPage(currentPage - 1) : null,
            icon: Icon(
              Icons.chevron_left_rounded,
              color: hasPreviousPage 
                ? Theme.of(context).colorScheme.primary 
                : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            style: IconButton.styleFrom(
              backgroundColor: hasPreviousPage 
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                : Theme.of(context).colorScheme.surface,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
          ),
          
          const SizedBox(width: 8),
          
          // Page numbers (show current and nearby pages)
          ..._buildPageNumbers(context, currentPage, totalPages),
          
          const SizedBox(width: 8),
          
          // Next button
          IconButton(
            onPressed: hasNextPage ? () => _loadPage(currentPage + 1) : null,
            icon: Icon(
              Icons.chevron_right_rounded,
              color: hasNextPage 
                ? Theme.of(context).colorScheme.primary 
                : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            style: IconButton.styleFrom(
              backgroundColor: hasNextPage 
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                : Theme.of(context).colorScheme.surface,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
          ),
        ],
      ),
    );
    */
  }

  /// Build page number buttons for debt list
  List<Widget> _buildPageNumbers(BuildContext context, int currentPage, int totalPages) {
    List<Widget> pageButtons = [];
    
    // Show max 5 page numbers
    int startPage = (currentPage - 2).clamp(1, totalPages);
    int endPage = (currentPage + 2).clamp(1, totalPages);
    
    // Adjust range to always show 5 pages if possible
    if (endPage - startPage < 4) {
      if (startPage == 1) {
        endPage = (startPage + 4).clamp(1, totalPages);
      } else {
        startPage = (endPage - 4).clamp(1, totalPages);
      }
    }
    
    for (int page = startPage; page <= endPage; page++) {
      final isCurrentPage = page == currentPage;
      
      pageButtons.add(
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 2),
          child: InkWell(
            onTap: isCurrentPage ? null : () => _loadPage(page),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: isCurrentPage 
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.surface,
                border: Border.all(
                  color: isCurrentPage
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).dividerColor.withValues(alpha: 0.3),
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '$page',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isCurrentPage 
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurface,
                  fontWeight: isCurrentPage ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
          ),
        ),
      );
    }
    
    return pageButtons;
  }

  /// Load specific page for debt list
  void _loadPage(int page) {
    final searchQuery = _searchController.text.isNotEmpty ? _searchController.text : null;
    context.read<DebtBloc>().add(LoadDebts(page: page, search: searchQuery));
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                Icons.receipt_long_outlined,
                size: 48,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'No debts found',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Start by adding your first debt using the button above',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  void _onSearchChanged(String query) {
    context.read<DebtBloc>().add(
          LoadDebts(search: query.isNotEmpty ? query : null),
        );
  }

  /// Refresh method for RefreshIndicator (no parameters, returns Future)
  Future<void> _handleRefresh() async {
    // Clear search if active
    if (_searchController.text.isNotEmpty) {
      _searchController.clear();
    }
    
    // Trigger refresh
    context.read<DebtBloc>().add(const RefreshDebts());
    context.read<DebtBloc>().add(const LoadDebtStats());
    
    // Wait a bit for the refresh to complete
    await Future.delayed(const Duration(milliseconds: 500));
  }

  void _showFilterOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const DebtFilterSheet(),
    );
  }

  void _showAddPaymentDialog(BuildContext context, String debtId) {
    // Use ID-based approach like edit and view details to avoid list state dependency
    final bloc = context.read<DebtBloc>();
    
    // First load the debt details to get accurate data
    bloc.add(LoadDebtDetails(debtId));
    
    // Show loading dialog first
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );
    
    // Listen for debt details loaded
    final subscription = bloc.stream.listen((state) {
      if (mounted) {
        if (state is DebtDetailsLoaded) {
          // Close loading dialog
          Navigator.of(context).pop();
          
          // Show payment dialog with fresh data
          showDialog(
            context: context,
            builder: (context) => BlocProvider.value(
              value: bloc,
              child: AddPaymentDialog(
                debtId: debtId,
                remainingAmount: state.response.remainingAmount,
                customerName: state.response.customerName,
              ),
            ),
          ).then((result) {
            if (result == true && mounted) {
              // Payment was added, refresh the list
              bloc.add(const RefreshDebts());
              bloc.add(const LoadDebtStats());
            }
          });
        } else if (state is DebtDetailsError) {
          // Close loading dialog and show error
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to load debt details: ${state.message}'),
              backgroundColor: AppThemes.errorColor,
            ),
          );
        }
      }
    });
    
    // Auto-cancel subscription after 10 seconds
    Future.delayed(const Duration(seconds: 10), () {
      subscription.cancel();
    });
  }

  void _navigateToCreateDebt(BuildContext context) {
    // Check permissions before allowing debt creation
    final authState = context.read<AuthBloc>().state;
    final user = authState is AuthAuthenticated ? authState.user : null;

    if (!PermissionUtils.canCreateDebts(user)) {
      showDialog(
        context: context,
        builder: (context) => const PermissionDeniedDialog(
          featureName: 'Debt Creation',
          message: 'You need debt creation permissions to add new debts.',
        ),
      );
      return;
    }

    DebtModalHandlers.showAddDebtModal(context);
  }

  void _navigateToDebtDetails(BuildContext context, String debtId) {
    // Always use ID-based modal handler like Customer does
    // This ensures we fetch fresh data and don't rely on current list state
    DebtModalHandlers.showViewDebtByIdModal(context, debtId);
  }

  void _navigateToEditDebt(BuildContext context, String debtId) {
    // Use the same approach as customer edit - dedicated edit form by ID
    DebtModalHandlers.showEditDebtById(context, debtId);
  }

  void _navigateToDeleteDebt(BuildContext context, String debtId) {
    // Use ID-based approach to avoid list state dependency
    final bloc = context.read<DebtBloc>();
    
    // First load the debt details to get accurate data
    bloc.add(LoadDebtDetails(debtId));
    
    // Show loading dialog first
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );
    
    // Listen for debt details loaded
    final subscription = bloc.stream.listen((state) {
      if (mounted) {
        if (state is DebtDetailsLoaded) {
          // Close loading dialog
          Navigator.of(context).pop();
          
          final debt = state.response;
          
          // Show confirmation dialog with fresh data
          showDialog(
            context: context,
            builder: (dialogContext) => AlertDialog(
              title: Row(
                children: [
                  Icon(Icons.warning, color: AppThemes.errorColor),
                  const SizedBox(width: 8),
                  Text('Delete Debt', style: TextStyle(color: AppThemes.errorColor)),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Are you sure you want to delete this debt?'),
                  const SizedBox(height: 8),
                  Text('Customer: ${debt.customerName}', style: const TextStyle(fontWeight: FontWeight.bold)),
                  Text('Debt ID: ${debt.debtId}'),
                  Text('Amount: \$${debt.amount.toStringAsFixed(2)}'),
                  Text('Remaining: \$${debt.remainingAmount.toStringAsFixed(2)}'),
                  Text('Status: ${debt.status.displayName}'),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppThemes.warningColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppThemes.warningColor.withValues(alpha: 0.3)),
                    ),
                    child: Text(
                      '⚠️ This action cannot be undone. All debt data and payment history will be permanently deleted.',
                      style: TextStyle(color: AppThemes.warningColor, fontSize: 12),
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(dialogContext).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                    _attemptDeleteDebt(context, debtId, bloc);
                  },
                  style: ElevatedButton.styleFrom(backgroundColor: AppThemes.errorColor),
                  child: Text('Delete', style: TextStyle(color: Theme.of(context).colorScheme.onPrimary)),
                ),
              ],
            ),
          );
        } else if (state is DebtDetailsError) {
          // Close loading dialog and show error
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to load debt details: ${state.message}'),
              backgroundColor: AppThemes.errorColor,
            ),
          );
        }
      }
    });
    
    // Auto-cancel subscription after 10 seconds
    Future.delayed(const Duration(seconds: 10), () {
      subscription.cancel();
    });
  }

  void _refreshDebts(BuildContext context) {
    // Clear search if active
    if (_searchController.text.isNotEmpty) {
      _searchController.clear();
    }
    
    // Trigger refresh for both debts and stats
    context.read<DebtBloc>().add(const RefreshDebts());
    context.read<DebtBloc>().add(const LoadDebtStats());
    
    // Show feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Refreshing debt list...'),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _refreshDebtsAfterUpdate(BuildContext context) {
    // Force immediate refresh without search clearing for updates
    context.read<DebtBloc>().add(const RefreshDebts());
    context.read<DebtBloc>().add(const LoadDebtStats());
    
    // Don't show refresh message for automatic updates, only for manual refresh
  }

  /// Attempt debt deletion with proper error handling
  void _attemptDeleteDebt(BuildContext context, String debtId, DebtBloc bloc) {
    // Listen for the delete result
    final subscription = bloc.stream.listen((state) {
      if (state is DebtError) {
        // Handle specific error cases from backend
        if (state.errorCode == 'debt_has_payments') {
          _showDebtDeletionError(
            context,
            'Cannot Delete Debt with Payments',
            'This debt has payment records. Please refund all payments before deleting the debt record.',
            Icons.payments_rounded,
            AppThemes.warningColor,
          );
        } else if (state.errorCode == 'debt_fully_paid') {
          _showDebtDeletionError(
            context,
            'Cannot Delete Fully Paid Debt',
            'This debt is fully paid and should be kept for historical purposes. Consider archiving instead of deleting.',
            Icons.history_rounded,
            Theme.of(context).colorScheme.primary,
          );
        } else if (state.errorCode == 'not_found') {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Debt not found. It may have already been deleted.'),
              backgroundColor: AppThemes.errorColor,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppThemes.errorColor,
            ),
          );
        }
      } else if (state is DebtDeleted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Debt deleted successfully'),
            backgroundColor: AppThemes.successColor,
          ),
        );
        // Refresh the debt list and stats
        bloc.add(const RefreshDebts());
        bloc.add(const LoadDebtStats());
      }
    });

    // Trigger the delete
    bloc.add(DeleteDebt(debtId));

    // Clean up subscription after a delay
    Future.delayed(const Duration(seconds: 5), () {
      subscription.cancel();
    });
  }

  /// Show specific debt deletion error dialog
  void _showDebtDeletionError(
    BuildContext context,
    String title,
    String message,
    IconData icon,
    Color color,
  ) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Row(
          children: [
            Icon(icon, color: color),
            const SizedBox(width: 8),
            Text(title, style: TextStyle(color: color)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: color.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: color, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'This is a business rule to maintain data integrity and audit trails.',
                      style: TextStyle(color: color, fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
} 