import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/domain/models/debt.dart';
import 'package:deyncare_app/domain/models/payment.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_bloc.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_event.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_state.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_constants.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/form_mixins.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/loading_states.dart';

/// Add Payment Form Widget - Updated for new debt architecture
class AddPaymentForm extends StatefulWidget {
  final Debt debt;

  const AddPaymentForm({super.key, required this.debt});

  @override
  State<AddPaymentForm> createState() => _AddPaymentFormState();
}

class _AddPaymentFormState extends State<AddPaymentForm> 
    with ModalFormMixin<AddPaymentForm>, BlocListenerMixin<AddPaymentForm> {
  
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  
  PaymentMethod _selectedPaymentMethod = PaymentMethod.cash;
  DateTime? _selectedPaymentDate;
  DateTime? _realPaymentDate; // When customer actually paid (staff-recorded)
  bool _useRealPaymentDate = false; // Toggle for dual time tracking

  @override
  void initState() {
    super.initState();
    _selectedPaymentDate = DateTime.now();
    // Pre-fill with remaining amount for convenience
    _amountController.text = widget.debt.remainingAmount.toStringAsFixed(2);
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return buildBlocListener<DebtBloc, DebtState>(
      listener: (context, state) {
        if (state is PaymentAdded) {
          _showPaymentSuccessDialog(state);
        } else if (state is PaymentError) {
          handleError('Error: ${state.message}');
        }
      },
      child: BlocBuilder<DebtBloc, DebtState>(
        builder: (context, state) {
          // Show skeleton loader during payment processing
          if (state is PaymentAdding) {
            return ModalLoadingStates.formSkeleton(fieldCount: 4);
          }
          
          return Padding(
            padding: ModalConstants.defaultPadding,
            child: Form(
              key: formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildDebtHeader(),
                  ModalConstants.defaultSpacing,
                  _buildAmountField(),
                  ModalConstants.defaultSpacing,
                  _buildPaymentMethodField(),
                  ModalConstants.defaultSpacing,
                  _buildPaymentDateField(),
                  ModalConstants.defaultSpacing,
                  _buildNotesField(),
                  ModalConstants.largeSpacing,
                  _buildSubmitButton(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDebtHeader() {
    return CommonCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recording Payment For',
              style: ModalConstants.sectionTitleStyle(context),
            ),
            ModalConstants.defaultSpacing,
            _buildDetailRow('Debt ID:', widget.debt.debtId),
            _buildDetailRow('Customer:', widget.debt.customerName),
            _buildDetailRow('Total Amount:', '\$${widget.debt.amount.toStringAsFixed(2)}'),
            _buildDetailRow('Already Paid:', '\$${widget.debt.totalPaid.toStringAsFixed(2)}'),
            _buildDetailRow('Due Date:', _formatDate(widget.debt.dueDate)),
            ModalConstants.defaultSpacing,
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: AppThemes.successColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: AppThemes.successColor.withValues(alpha: 0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.account_balance_wallet_outlined, 
                       color: AppThemes.successColor, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'Outstanding: \$${widget.debt.remainingAmount.toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: AppThemes.successColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
                color: AppThemes.textSecondaryColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Payment Amount (\$)',
              style: ModalConstants.sectionTitleStyle(context),
            ),
            const Spacer(),
            TextButton(
              onPressed: () {
                _amountController.text = widget.debt.remainingAmount.toStringAsFixed(2);
              },
              child: Text(
                'Full Amount',
                style: TextStyle(
                  color: AppThemes.primaryColor,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
        ModalConstants.defaultSpacing,
        buildFormField(
          controller: _amountController,
          labelText: 'Amount',
          hintText: 'Enter payment amount',
          prefixIcon: const Icon(Icons.attach_money),
          helperText: 'Max: \$${widget.debt.remainingAmount.toStringAsFixed(2)}',
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter payment amount';
            }
            final amount = double.tryParse(value);
            if (amount == null || amount <= 0) {
              return 'Please enter a valid amount greater than 0';
            }
            if (amount > widget.debt.remainingAmount) {
              return 'Amount cannot exceed outstanding debt (\$${widget.debt.remainingAmount.toStringAsFixed(2)})';
            }
            return null;
          },
        ),
        
        // Payment Type Indicator
        if (_amountController.text.isNotEmpty) ...[
          const SizedBox(height: 8),
          _buildPaymentTypeIndicator(),
        ],
      ],
    );
  }

  Widget _buildPaymentMethodField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Payment Method',
          style: ModalConstants.sectionTitleStyle(context),
        ),
        ModalConstants.defaultSpacing,
        buildDropdownField<PaymentMethod>(
          value: _selectedPaymentMethod,
          items: PaymentMethod.values.map((method) {
            return DropdownMenuItem(
              value: method,
              child: Row(
                children: [
                  Icon(_getPaymentMethodIcon(method), size: 20),
                  const SizedBox(width: 12),
                  Text(method.displayName),
                ],
              ),
            );
          }).toList(),
          labelText: 'Payment Method',
          onChanged: (method) {
            if (method != null) {
              setState(() => _selectedPaymentMethod = method);
            }
          },
        ),
      ],
    );
  }

  Widget _buildPaymentDateField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Payment Timing',
              style: ModalConstants.sectionTitleStyle(context),
            ),
            const Spacer(),
            Switch(
              value: _useRealPaymentDate,
              onChanged: (value) {
                setState(() {
                  _useRealPaymentDate = value;
                  if (value) {
                    _realPaymentDate = _selectedPaymentDate;
                  } else {
                    _realPaymentDate = null;
                  }
                });
              },
            ),
            const SizedBox(width: 8),
            Text(
              'Real Time',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: _useRealPaymentDate ? AppThemes.primaryColor : AppThemes.textSecondaryColor,
              ),
            ),
          ],
        ),
        ModalConstants.defaultSpacing,
        
        // Official Payment Date (always shown)
        buildDatePickerField(
          selectedDate: _selectedPaymentDate,
          labelText: 'Official Payment Date (Server Time)',
          onDateSelected: (date) {
            setState(() => _selectedPaymentDate = date);
          },
          firstDate: DateTime.now().subtract(const Duration(days: 365)),
          lastDate: DateTime.now().add(const Duration(days: 1)),
        ),
        
        // Real Payment Date (shown when toggle is on)
        if (_useRealPaymentDate) ...[
          ModalConstants.defaultSpacing,
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppThemes.primaryColor.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppThemes.primaryColor.withValues(alpha: 0.2)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.schedule, size: 16, color: AppThemes.primaryColor),
                    const SizedBox(width: 8),
                    Text(
                      'When did customer actually pay?',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppThemes.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                buildDatePickerField(
                  selectedDate: _realPaymentDate,
                  labelText: 'Real Payment Date (Customer Paid)',
                  onDateSelected: (date) {
                    setState(() => _realPaymentDate = date);
                  },
                  firstDate: DateTime.now().subtract(const Duration(days: 365)),
                  lastDate: DateTime.now().add(const Duration(days: 1)),
                ),
              ],
            ),
          ),
        ],
        
        // Early Payment Benefits Indicator
        if (_isEarlyPayment()) ...[
          ModalConstants.defaultSpacing,
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppThemes.successColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppThemes.successColor.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.trending_up, size: 20, color: AppThemes.successColor),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '🎉 Early Payment Detected!',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppThemes.successColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '• No ML evaluation needed\n• Favorable risk assessment\n• Customer gets credit boost',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppThemes.successColor,
                          fontSize: 11,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
        
        // Help text
        ModalConstants.defaultSpacing,
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppThemes.infoColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(Icons.info_outline, size: 16, color: AppThemes.infoColor),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _useRealPaymentDate 
                    ? 'Recording both official time (for audit) and when customer actually paid (for business logic)'
                    : 'Using server time for both official record and business calculations',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppThemes.infoColor,
                    fontSize: 11,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNotesField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes (Optional)',
          style: ModalConstants.sectionTitleStyle(context),
        ),
        ModalConstants.defaultSpacing,
        buildFormField(
          controller: _notesController,
          labelText: 'Notes',
          hintText: 'Enter payment notes (optional)',
          maxLines: 3,
          validator: (value) {
            if (value != null && value.length > 500) {
              return 'Notes must be less than 500 characters';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return ElevatedButton(
      onPressed: isLoading ? null : _handleSubmit,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppThemes.successColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ModalConstants.defaultRadius),
        ),
      ),
      child: isLoading
          ? ModalLoadingStates.buttonLoading(context)
          : const Text(
              'Record Payment',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
    );
  }

  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return Icons.attach_money;
      case PaymentMethod.bankTransfer:
        return Icons.account_balance;
      case PaymentMethod.mobileMoney:
        return Icons.phone_android;
      case PaymentMethod.card:
        return Icons.credit_card;
      case PaymentMethod.other:
        return Icons.more_horiz;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  bool _isEarlyPayment() {
    final effectiveDate = _useRealPaymentDate && _realPaymentDate != null 
        ? _realPaymentDate! 
        : _selectedPaymentDate;
    
    if (effectiveDate == null) return false;
    
    // Check if payment date is before due date
    return effectiveDate.isBefore(widget.debt.dueDate);
  }

  void _showPaymentSuccessDialog(dynamic state) {
    // For now, show simple success message
    // TODO: Enhance with ML evaluation results when PaymentAdded state includes response data
    handleSuccess('Payment recorded successfully!');
    
    // Future enhancement: Show detailed success dialog with:
    // - Payment details
    // - ML evaluation results (if triggered)
    // - Early payment benefits (if applicable)
    // - Updated customer risk score
  }

  Widget _buildPaymentTypeIndicator() {
    final amount = double.tryParse(_amountController.text);
    if (amount == null || amount <= 0) return const SizedBox.shrink();

    final isFullPayment = amount >= widget.debt.remainingAmount;
    final isSignificantPartial = amount >= (widget.debt.remainingAmount * 0.5);
    final isEarly = _isEarlyPayment();

    Color indicatorColor;
    IconData indicatorIcon;
    String indicatorText;
    String mlText;

    if (isFullPayment) {
      indicatorColor = AppThemes.successColor;
      indicatorIcon = Icons.check_circle;
      indicatorText = 'Full Payment';
      mlText = isEarly ? 'No ML evaluation needed' : 'ML evaluation will be triggered';
    } else if (isSignificantPartial) {
      indicatorColor = AppThemes.warningColor;
      indicatorIcon = Icons.pie_chart;
      indicatorText = 'Significant Partial (${(amount / widget.debt.remainingAmount * 100).round()}%)';
      mlText = isEarly ? 'No ML evaluation needed' : 'ML evaluation will be triggered';
    } else {
      indicatorColor = AppThemes.errorColor;
      indicatorIcon = Icons.pie_chart_outline;
      indicatorText = 'Small Partial (${(amount / widget.debt.remainingAmount * 100).round()}%)';
      mlText = isEarly ? 'No ML evaluation needed' : 'ML evaluation will be triggered';
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: indicatorColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: indicatorColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(indicatorIcon, size: 16, color: indicatorColor),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  indicatorText,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: indicatorColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  mlText,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: indicatorColor,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleSubmit() {
    if (!validateForm()) return;
    if (_selectedPaymentDate == null) {
      handleError('Please select a payment date');
      return;
    }

    final amount = double.tryParse(_amountController.text);
    if (amount == null || amount <= 0) {
      handleError('Please enter a valid payment amount');
      return;
    }

    if (amount > widget.debt.remainingAmount) {
      handleError('Payment amount cannot exceed outstanding debt (\$${widget.debt.remainingAmount.toStringAsFixed(2)})');
      return;
    }

    // Business validation - payment date not in future
    if (_selectedPaymentDate!.isAfter(DateTime.now().add(const Duration(days: 1)))) {
      handleError('Payment date cannot be in the future');
      return;
    }

    final notes = _notesController.text.trim().isEmpty 
        ? null 
        : _notesController.text.trim();

    setLoading(true);

    context.read<DebtBloc>().add(AddPaymentToDebt(
      debtId: widget.debt.debtId,
      amount: amount,
      paymentMethod: _selectedPaymentMethod,
      notes: notes,
      paymentDate: _selectedPaymentDate,
      realPaymentDate: _useRealPaymentDate ? _realPaymentDate : null,
    ));
  }
} 