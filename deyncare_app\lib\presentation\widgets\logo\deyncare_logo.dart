import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// A responsive and reusable DeynCare logo widget
/// Supports different variants, sizes, and use cases
class DeynCareLogo extends StatelessWidget {
  final DeynCareLogoVariant variant;
  final DeynCareLogoSize size;
  final Color? backgroundColor;
  final bool showBorder;
  final double? customWidth;
  final double? customHeight;
  final EdgeInsetsGeometry? padding;
  final VoidCallback? onTap;

  const DeynCareLogo({
    super.key,
    this.variant = DeynCareLogoVariant.primary,
    this.size = DeynCareLogoSize.medium,
    this.backgroundColor,
    this.showBorder = false,
    this.customWidth,
    this.customHeight,
    this.padding,
    this.onTap,
  });

  /// Named constructor for auth screens
  const DeynCareLogo.auth({
    super.key,
    this.variant = DeynCareLogoVariant.primary,
    this.size = DeynCareLogoSize.large,
    this.backgroundColor,
    this.showBorder = true,
    this.customWidth,
    this.customHeight,
    this.padding = const EdgeInsets.all(20),
    this.onTap,
  });

  /// Named constructor for splash screen
  const DeynCareLogo.splash({
    super.key,
    this.variant = DeynCareLogoVariant.primary,
    this.size = DeynCareLogoSize.extraLarge,
    this.backgroundColor,
    this.showBorder = false,
    this.customWidth,
    this.customHeight,
    this.padding,
    this.onTap,
  });

  /// Named constructor for app bar
  const DeynCareLogo.appBar({
    super.key,
    this.variant = DeynCareLogoVariant.white,
    this.size = DeynCareLogoSize.small,
    this.backgroundColor,
    this.showBorder = false,
    this.customWidth,
    this.customHeight,
    this.padding,
    this.onTap,
  });

  /// Named constructor for compact spaces
  const DeynCareLogo.compact({
    super.key,
    this.variant = DeynCareLogoVariant.icon,
    this.size = DeynCareLogoSize.small,
    this.backgroundColor,
    this.showBorder = false,
    this.customWidth,
    this.customHeight,
    this.padding,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final logoSize = _getLogoSize();
    final logoPath = _getLogoPath();
    
    Widget logoWidget;
    
    // Build the logo based on variant
    if (variant == DeynCareLogoVariant.svg || logoPath.endsWith('.svg')) {
      logoWidget = SvgPicture.asset(
        logoPath,
        width: customWidth ?? logoSize.width,
        height: customHeight ?? logoSize.height,
        fit: BoxFit.contain,
        placeholderBuilder: (context) => _buildFallbackLogo(context, logoSize),
      );
    } else {
      logoWidget = Image.asset(
        logoPath,
        width: customWidth ?? logoSize.width,
        height: customHeight ?? logoSize.height,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return _buildFallbackLogo(context, logoSize);
        },
      );
    }

    // Add container styling if needed
    if (backgroundColor != null || showBorder || padding != null) {
      logoWidget = Container(
        padding: padding,
        decoration: BoxDecoration(
          color: backgroundColor,
          border: showBorder ? Border.all(
            color: Theme.of(context).primaryColor.withOpacity(0.2),
            width: 2,
          ) : null,
          borderRadius: showBorder ? BorderRadius.circular(16) : null,
        ),
        child: logoWidget,
      );
    }

    // Add tap functionality if needed
    if (onTap != null) {
      logoWidget = GestureDetector(
        onTap: onTap,
        child: logoWidget,
      );
    }

    return logoWidget;
  }

  /// Get the appropriate logo path based on variant
  String _getLogoPath() {
    switch (variant) {
      case DeynCareLogoVariant.primary:
      case DeynCareLogoVariant.svg:
        return 'assets/icons/deyncare_svg.svg';
      case DeynCareLogoVariant.white:
        return 'assets/images/deyncare_logo_white.png';
      case DeynCareLogoVariant.icon:
        return 'assets/icons/deyncare_icon.png';
    }
  }

  /// Get logo dimensions based on size
  _LogoSize _getLogoSize() {
    switch (size) {
      case DeynCareLogoSize.small:
        return const _LogoSize(width: 40, height: 40);
      case DeynCareLogoSize.medium:
        return const _LogoSize(width: 80, height: 80);
      case DeynCareLogoSize.large:
        return const _LogoSize(width: 120, height: 120);
      case DeynCareLogoSize.extraLarge:
        return const _LogoSize(width: 180, height: 180);
    }
  }

  /// Build fallback logo when image fails to load
  Widget _buildFallbackLogo(BuildContext context, _LogoSize logoSize) {
    return Container(
      width: logoSize.width,
      height: logoSize.height,
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(
        Icons.business_center,
        size: logoSize.width * 0.6,
        color: Theme.of(context).primaryColor,
      ),
    );
  }
}

/// Logo variant enum
enum DeynCareLogoVariant {
  primary,   // Default SVG logo
  svg,       // Explicitly use SVG
  white,     // White version for dark backgrounds
  icon,      // Icon version for compact spaces
}

/// Logo size enum
enum DeynCareLogoSize {
  small,
  medium,
  large,
  extraLarge,
}

/// Internal logo size class
class _LogoSize {
  final double width;
  final double height;

  const _LogoSize({required this.width, required this.height});
}

/// Extension for responsive logo sizing based on screen size
extension ResponsiveLogoSize on DeynCareLogoSize {
  /// Get responsive size based on screen width
  _LogoSize getResponsiveSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final baseSize = _getBaseSize();
    
    // Adjust size based on screen width
    double multiplier;
    if (screenWidth < 360) {
      multiplier = 0.8; // Small screens
    } else if (screenWidth > 600) {
      multiplier = 1.2; // Large screens/tablets
    } else {
      multiplier = 1.0; // Normal screens
    }
    
    return _LogoSize(
      width: baseSize.width * multiplier,
      height: baseSize.height * multiplier,
    );
  }
  
  _LogoSize _getBaseSize() {
    switch (this) {
      case DeynCareLogoSize.small:
        return const _LogoSize(width: 40, height: 40);
      case DeynCareLogoSize.medium:
        return const _LogoSize(width: 80, height: 80);
      case DeynCareLogoSize.large:
        return const _LogoSize(width: 120, height: 120);
      case DeynCareLogoSize.extraLarge:
        return const _LogoSize(width: 180, height: 180);
    }
  }
} 