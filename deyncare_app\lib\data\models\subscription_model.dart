class SubscriptionModel {
  final String subscriptionId;
  final String shopId;
  final String? planId;
  final PlanDetails plan;
  final PricingDetails pricing;
  final String status;
  final PaymentDetails payment;
  final DateDetails dates;
  final RenewalSettings renewalSettings;
  final CancellationDetails? cancellation;
  final List<HistoryEntry> history;
  final bool isDeleted;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SubscriptionModel({
    required this.subscriptionId,
    required this.shopId,
    this.planId,
    required this.plan,
    required this.pricing,
    required this.status,
    required this.payment,
    required this.dates,
    required this.renewalSettings,
    this.cancellation,
    required this.history,
    required this.isDeleted,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SubscriptionModel.fromJson(Map<String, dynamic> json) {
    return SubscriptionModel(
      subscriptionId: json['subscriptionId'] as String,
      shopId: json['shopId'] as String,
      planId: json['planId'] as String?,
      plan: PlanDetails.fromJson(json['plan'] as Map<String, dynamic>),
      pricing: PricingDetails.fromJson(json['pricing'] as Map<String, dynamic>),
      status: json['status'] as String,
      payment: PaymentDetails.fromJson(json['payment'] as Map<String, dynamic>),
      dates: DateDetails.fromJson(json['dates'] as Map<String, dynamic>),
      renewalSettings: RenewalSettings.fromJson(json['renewalSettings'] as Map<String, dynamic>),
      cancellation: json['cancellation'] != null 
          ? CancellationDetails.fromJson(json['cancellation'] as Map<String, dynamic>) 
          : null,
      history: (json['history'] as List<dynamic>? ?? [])
          .map((e) => HistoryEntry.fromJson(e as Map<String, dynamic>))
          .toList(),
      isDeleted: json['isDeleted'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Factory constructor for minimal cancellation response
  /// Backend only returns {subscriptionId, status} for cancellation
  factory SubscriptionModel.fromCancellationResponse(Map<String, dynamic> json) {
    final now = DateTime.now();
    return SubscriptionModel(
      subscriptionId: json['subscriptionId'] as String,
      shopId: 'unknown', // Not provided in cancellation response
      planId: null,
      plan: const PlanDetails(
        name: 'Unknown Plan',
        type: 'unknown',
      ),
      pricing: const PricingDetails(
        basePrice: 0.0,
        currency: 'USD',
        billingCycle: 'unknown',
      ),
      status: json['status'] as String,
      payment: const PaymentDetails(
        method: 'unknown',
        verified: false,
        failedPayments: 0,
      ),
      dates: DateDetails(
        startDate: now,
        endDate: now,
        trialEndsAt: null,
        lastUpdated: now,
      ),
      renewalSettings: const RenewalSettings(
        autoRenew: false,
        reminderSent: false,
        renewalAttempts: 0,
      ),
      cancellation: null, // Will be updated when full subscription is loaded
      history: [],
      isDeleted: false,
      createdAt: now,
      updatedAt: now,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'subscriptionId': subscriptionId,
      'shopId': shopId,
      'planId': planId,
      'plan': plan.toJson(),
      'pricing': pricing.toJson(),
      'status': status,
      'payment': payment.toJson(),
      'dates': dates.toJson(),
      'renewalSettings': renewalSettings.toJson(),
      'cancellation': cancellation?.toJson(),
      'history': history.map((e) => e.toJson()).toList(),
      'isDeleted': isDeleted,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Virtual properties matching backend
  String get displayStatus {
    if (isDeleted) return 'deleted';
    if (status == 'canceled') return 'canceled';
    
    final now = DateTime.now();
    final endDate = dates.endDate;
    final daysLeft = endDate.difference(now).inDays;
    
    if (status == 'trial') {
      if (daysLeft <= 0) return 'trial_expired';
      if (daysLeft <= 2) return 'trial_ending_soon';
      return 'trial';
    }
    
    if (daysLeft <= 0) return 'expired';
    if (daysLeft <= 5) return 'expiring_soon';
    if (payment.failedPayments > 0) return 'payment_issue';
    
    return status;
  }

  double get totalPrice {
    double price = pricing.basePrice;
    
    // Apply discount if active
    if (pricing.discount != null && pricing.discount!.active) {
      if (pricing.discount!.type == 'fixed') {
        price -= pricing.discount!.amount;
      } else if (pricing.discount!.type == 'percentage') {
        price -= (price * (pricing.discount!.value ?? 0) / 100);
      }
    }
    
    return price < 0 ? 0 : price;
  }

  int get durationDays {
    return dates.endDate.difference(dates.startDate).inDays;
  }

  int get daysRemaining {
    final now = DateTime.now();
    final remaining = dates.endDate.difference(now).inDays;
    return remaining > 0 ? remaining : 0;
  }

  int get percentageUsed {
    final totalDays = durationDays;
    if (totalDays == 0) return 100;
    
    final daysUsed = totalDays - daysRemaining;
    final percentage = ((daysUsed / totalDays) * 100).round();
    return percentage.clamp(0, 100);
  }

  bool get isActive {
    if (isDeleted || status == 'canceled' || status == 'expired') {
      return false;
    }
    
    final now = DateTime.now();
    return now.isBefore(dates.endDate) || now.isAtSameMomentAs(dates.endDate);
  }

  bool get isInTrial {
    if (status != 'trial' || dates.trialEndsAt == null) {
      return false;
    }
    
    final now = DateTime.now();
    return now.isBefore(dates.trialEndsAt!) || now.isAtSameMomentAs(dates.trialEndsAt!);
  }

  bool get isExpired => status == 'expired';
  bool get isCanceled => status == 'canceled';
  bool get isPastDue => status == 'past_due';
  bool get isTrialEndingSoon => displayStatus == 'trial_ending_soon';
  bool get isExpiringSoon => displayStatus == 'expiring_soon';
}

class PlanDetails {
  final String name;
  final String type; // 'trial', 'monthly', 'yearly'

  const PlanDetails({
    required this.name,
    required this.type,
  });

  factory PlanDetails.fromJson(Map<String, dynamic> json) {
    return PlanDetails(
      name: json['name'] as String,
      type: json['type'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type,
    };
  }
}

class PricingDetails {
  final double basePrice;
  final String currency;
  final String billingCycle;
  final DiscountDetails? discount;

  const PricingDetails({
    required this.basePrice,
    required this.currency,
    required this.billingCycle,
    this.discount,
  });

  factory PricingDetails.fromJson(Map<String, dynamic> json) {
    return PricingDetails(
      basePrice: (json['basePrice'] as num).toDouble(),
      currency: json['currency'] as String,
      billingCycle: json['billingCycle'] as String,
      discount: json['discount'] != null 
          ? DiscountDetails.fromJson(json['discount'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'basePrice': basePrice,
      'currency': currency,
      'billingCycle': billingCycle,
      'discount': discount?.toJson(),
    };
  }
}

class DiscountDetails {
  final bool active;
  final String? code;
  final String? discountId;
  final double amount;
  final String type; // 'fixed', 'percentage'
  final double? value;
  final DateTime? expiresAt;
  final DateTime appliedAt;

  const DiscountDetails({
    required this.active,
    this.code,
    this.discountId,
    required this.amount,
    required this.type,
    this.value,
    this.expiresAt,
    required this.appliedAt,
  });

  factory DiscountDetails.fromJson(Map<String, dynamic> json) {
    return DiscountDetails(
      active: json['active'] as bool,
      code: json['code'] as String?,
      discountId: json['discountId'] as String?,
      amount: (json['amount'] as num).toDouble(),
      type: json['type'] as String,
      value: json['value'] != null ? (json['value'] as num).toDouble() : null,
      expiresAt: json['expiresAt'] != null ? DateTime.parse(json['expiresAt'] as String) : null,
      appliedAt: DateTime.parse(json['appliedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'active': active,
      'code': code,
      'discountId': discountId,
      'amount': amount,
      'type': type,
      'value': value,
      'expiresAt': expiresAt?.toIso8601String(),
      'appliedAt': appliedAt.toIso8601String(),
    };
  }
}

class PaymentDetails {
  final String method; // 'offline', 'evc_plus', 'free'
  final bool verified;
  final DateTime? lastPaymentDate;
  final DateTime? nextPaymentDate;
  final int failedPayments;
  final PaymentInfo? paymentDetails;

  const PaymentDetails({
    required this.method,
    required this.verified,
    this.lastPaymentDate,
    this.nextPaymentDate,
    required this.failedPayments,
    this.paymentDetails,
  });

  factory PaymentDetails.fromJson(Map<String, dynamic> json) {
    return PaymentDetails(
      method: json['method'] as String,
      verified: json['verified'] as bool,
      lastPaymentDate: json['lastPaymentDate'] != null 
          ? DateTime.parse(json['lastPaymentDate'] as String)
          : null,
      nextPaymentDate: json['nextPaymentDate'] != null 
          ? DateTime.parse(json['nextPaymentDate'] as String)
          : null,
      failedPayments: json['failedPayments'] as int? ?? 0,
      paymentDetails: json['paymentDetails'] != null 
          ? PaymentInfo.fromJson(json['paymentDetails'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'method': method,
      'verified': verified,
      'lastPaymentDate': lastPaymentDate?.toIso8601String(),
      'nextPaymentDate': nextPaymentDate?.toIso8601String(),
      'failedPayments': failedPayments,
      'paymentDetails': paymentDetails?.toJson(),
    };
  }
}

class PaymentInfo {
  final String? transactionId;
  final String? receiptUrl;
  final String? payerName;
  final String? payerPhone;
  final String? payerEmail;
  final String? notes;

  const PaymentInfo({
    this.transactionId,
    this.receiptUrl,
    this.payerName,
    this.payerPhone,
    this.payerEmail,
    this.notes,
  });

  factory PaymentInfo.fromJson(Map<String, dynamic> json) {
    return PaymentInfo(
      transactionId: json['transactionId'] as String?,
      receiptUrl: json['receiptUrl'] as String?,
      payerName: json['payerName'] as String?,
      payerPhone: json['payerPhone'] as String?,
      payerEmail: json['payerEmail'] as String?,
      notes: json['notes'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'transactionId': transactionId,
      'receiptUrl': receiptUrl,
      'payerName': payerName,
      'payerPhone': payerPhone,
      'payerEmail': payerEmail,
      'notes': notes,
    };
  }
}

class DateDetails {
  final DateTime startDate;
  final DateTime endDate;
  final DateTime? trialEndsAt;
  final DateTime? canceledAt;
  final DateTime lastUpdated;

  const DateDetails({
    required this.startDate,
    required this.endDate,
    this.trialEndsAt,
    this.canceledAt,
    required this.lastUpdated,
  });

  factory DateDetails.fromJson(Map<String, dynamic> json) {
    return DateDetails(
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      trialEndsAt: json['trialEndsAt'] != null 
          ? DateTime.parse(json['trialEndsAt'] as String)
          : null,
      canceledAt: json['canceledAt'] != null 
          ? DateTime.parse(json['canceledAt'] as String)
          : null,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'trialEndsAt': trialEndsAt?.toIso8601String(),
      'canceledAt': canceledAt?.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}

class RenewalSettings {
  final bool autoRenew;
  final bool reminderSent;
  final int renewalAttempts;

  const RenewalSettings({
    required this.autoRenew,
    required this.reminderSent,
    required this.renewalAttempts,
  });

  factory RenewalSettings.fromJson(Map<String, dynamic> json) {
    return RenewalSettings(
      autoRenew: json['autoRenew'] as bool,
      reminderSent: json['reminderSent'] as bool? ?? false,
      renewalAttempts: json['renewalAttempts'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'autoRenew': autoRenew,
      'reminderSent': reminderSent,
      'renewalAttempts': renewalAttempts,
    };
  }
}

class CancellationDetails {
  final String? reason;
  final String? feedback;
  final String? byUserId;
  final bool refundIssued;

  const CancellationDetails({
    this.reason,
    this.feedback,
    this.byUserId,
    required this.refundIssued,
  });

  factory CancellationDetails.fromJson(Map<String, dynamic> json) {
    return CancellationDetails(
      reason: json['reason'] as String?,
      feedback: json['feedback'] as String?,
      byUserId: json['byUserId'] as String?,
      refundIssued: json['refundIssued'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'reason': reason,
      'feedback': feedback,
      'byUserId': byUserId,
      'refundIssued': refundIssued,
    };
  }
}

class HistoryEntry {
  final String action;
  final DateTime date;
  final Map<String, dynamic>? details;
  final String? performedBy;

  const HistoryEntry({
    required this.action,
    required this.date,
    this.details,
    this.performedBy,
  });

  factory HistoryEntry.fromJson(Map<String, dynamic> json) {
    return HistoryEntry(
      action: json['action'] as String,
      date: DateTime.parse(json['date'] as String),
      details: json['details'] as Map<String, dynamic>?,
      performedBy: json['performedBy'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'action': action,
      'date': date.toIso8601String(),
      'details': details,
      'performedBy': performedBy,
    };
  }
} 