import 'package:dartz/dartz.dart';
import 'package:deyncare_app/core/errors/failures.dart';
import 'package:deyncare_app/domain/models/plan.dart';
import 'package:deyncare_app/domain/repositories/subscription_repository.dart';

class GetAvailablePlansUseCase {
  final SubscriptionRepository repository;

  GetAvailablePlansUseCase(this.repository);

  Future<Either<Failure, List<Plan>>> call() async {
    return await repository.getAvailablePlans();
  }
} 