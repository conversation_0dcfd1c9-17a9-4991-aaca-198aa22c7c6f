# DeynCare Mobile App Push Notification Integration Guide

## 📱 Overview

This guide provides step-by-step instructions for integrating the DeynCare Flutter mobile app with our Firebase-based push notification system. The mobile app is designed for **Admin/Shop Owner roles only** - customers do not use the mobile app.

## 🎯 Target Users

- **SuperAdmin**: Full system access via mobile app
- **Admin**: Shop-specific access via mobile app  
- **Employee**: Shop-specific access via mobile app
- **Customers**: ❌ Do NOT use mobile app (notifications via SMS/other channels)

## 🔧 Prerequisites

### Backend Requirements
- ✅ Firebase project created (`deyncare-47d99`)
- ✅ Firebase Admin SDK configured
- ✅ Push notification system operational
- ✅ FCM token management APIs available

### Mobile App Requirements
- Flutter SDK installed
- Firebase Flutter plugins
- Android/iOS development environment

## 📋 Integration Steps

### Step 1: Firebase Configuration for Mobile App

#### 1.1 Download Configuration Files

**For Android:**
1. Go to [Firebase Console](https://console.firebase.google.com/project/deyncare-47d99)
2. Click on Android app or add new Android app
3. Enter package name: `com.deyncare.app` (or your chosen package)
4. Download `google-services.json`
5. Place in `android/app/` directory

**For iOS:**
1. Go to Firebase Console
2. Click on iOS app or add new iOS app  
3. Enter bundle ID: `com.deyncare.app` (or your chosen bundle)
4. Download `GoogleService-Info.plist`
5. Place in `ios/Runner/` directory

#### 1.2 Update Configuration Files

**Android (`android/app/build.gradle`):**
```gradle
dependencies {
    implementation 'com.google.firebase:firebase-messaging:23.1.0'
    implementation 'com.google.firebase:firebase-analytics:21.2.0'
}

apply plugin: 'com.google.gms.google-services'
```

**Android (`android/build.gradle`):**
```gradle
dependencies {
    classpath 'com.google.gms:google-services:4.3.15'
}
```

**iOS (`ios/Runner/Info.plist`):**
```xml
<key>FirebaseMessagingAutoInitEnabled</key>
<true/>
<key>FirebaseAnalyticsCollectionEnabled</key>
<true/>
```

### Step 2: Flutter Dependencies

#### 2.1 Add Dependencies to `pubspec.yaml`

```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # Firebase
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10
  firebase_analytics: ^10.7.4
  
  # Local notifications (for foreground notifications)
  flutter_local_notifications: ^16.3.2
  
  # HTTP requests
  http: ^1.1.0
  dio: ^5.4.0
  
  # State management
  provider: ^6.1.1
  # or bloc: ^8.1.2
  
  # Secure storage
  flutter_secure_storage: ^9.0.0
  
  # Device info
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
```

#### 2.2 Install Dependencies

```bash
flutter pub get
```

### Step 3: Firebase Service Implementation

#### 3.1 Create Firebase Service (`lib/services/firebase_service.dart`)

```dart
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';

class FirebaseService {
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications = 
      FlutterLocalNotificationsPlugin();
  
  static String? _fcmToken;
  static bool _initialized = false;

  /// Initialize Firebase and FCM
  static Future<void> initialize() async {
    if (_initialized) return;
    
    try {
      // Initialize Firebase
      await Firebase.initializeApp();
      
      // Request permissions
      await _requestPermissions();
      
      // Initialize local notifications
      await _initializeLocalNotifications();
      
      // Get FCM token
      await _getFCMToken();
      
      // Setup message handlers
      _setupMessageHandlers();
      
      _initialized = true;
      debugPrint('🔥 Firebase Service initialized successfully');
    } catch (e) {
      debugPrint('❌ Firebase initialization failed: $e');
      rethrow;
    }
  }

  /// Request notification permissions
  static Future<void> _requestPermissions() async {
    NotificationSettings settings = await _messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    debugPrint('📱 Notification permission: ${settings.authorizationStatus}');
    
    if (settings.authorizationStatus != AuthorizationStatus.authorized) {
      throw Exception('Notification permissions not granted');
    }
  }

  /// Initialize local notifications for foreground handling
  static Future<void> _initializeLocalNotifications() async {
    const AndroidInitializationSettings androidSettings = 
        AndroidInitializationSettings('@mipmap/ic_launcher');
    
    const DarwinInitializationSettings iosSettings = 
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings settings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      settings,
      onDidReceiveNotificationResponse: _onNotificationTap,
    );
  }

  /// Get FCM token
  static Future<String?> _getFCMToken() async {
    try {
      _fcmToken = await _messaging.getToken();
      debugPrint('🔑 FCM Token: $_fcmToken');
      return _fcmToken;
    } catch (e) {
      debugPrint('❌ Failed to get FCM token: $e');
      return null;
    }
  }

  /// Setup message handlers
  static void _setupMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
    
    // Handle notification taps when app is terminated
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
    
    // Handle token refresh
    _messaging.onTokenRefresh.listen(_onTokenRefresh);
  }

  /// Handle foreground messages
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    debugPrint('📨 Foreground message: ${message.notification?.title}');
    
    // Show local notification
    await _showLocalNotification(message);
  }

  /// Handle background messages
  @pragma('vm:entry-point')
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    debugPrint('📨 Background message: ${message.notification?.title}');
    // Background messages are automatically displayed by the system
  }

  /// Handle notification tap
  static void _handleNotificationTap(RemoteMessage message) {
    debugPrint('👆 Notification tapped: ${message.data}');
    
    // Navigate to specific screen based on notification data
    _navigateToScreen(message.data);
  }

  /// Handle notification tap from local notifications
  static void _onNotificationTap(NotificationResponse response) {
    debugPrint('👆 Local notification tapped: ${response.payload}');
    
    // Parse payload and navigate
    if (response.payload != null) {
      // Handle navigation based on payload
    }
  }

  /// Handle token refresh
  static void _onTokenRefresh(String newToken) {
    debugPrint('🔄 FCM Token refreshed: $newToken');
    _fcmToken = newToken;
    
    // Register new token with backend
    _registerTokenWithBackend(newToken);
  }

  /// Show local notification for foreground messages
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'deyncare_channel',
      'DeynCare Notifications',
      channelDescription: 'Important notifications from DeynCare',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'DeynCare',
      message.notification?.body ?? 'New notification',
      details,
      payload: message.data.toString(),
    );
  }

  /// Navigate to screen based on notification data
  static void _navigateToScreen(Map<String, dynamic> data) {
    // Implement navigation logic based on notification type
    final notificationType = data['type'];
    
    switch (notificationType) {
      case 'debt_created':
        // Navigate to debt details
        break;
      case 'payment_recorded':
        // Navigate to payment details
        break;
      case 'debt_reminder':
        // Navigate to debt list
        break;
      case 'custom':
        // Handle custom navigation
        break;
      default:
        // Navigate to home
        break;
    }
  }

  /// Register FCM token with backend
  static Future<void> _registerTokenWithBackend(String token) async {
    // This will be implemented in the API service
    try {
      await ApiService.registerFCMToken(token);
      debugPrint('✅ FCM token registered with backend');
    } catch (e) {
      debugPrint('❌ Failed to register FCM token: $e');
    }
  }

  /// Get current FCM token
  static String? get fcmToken => _fcmToken;

  /// Check if Firebase is initialized
  static bool get isInitialized => _initialized;
}
```

#### 3.2 Create API Service (`lib/services/api_service.dart`)

```dart
import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:flutter/foundation.dart';

class ApiService {
  static late Dio _dio;
  static const String baseUrl = 'https://your-backend-url.com/api';
  // For local development: 'http://localhost:5000/api'
  
  static String? _authToken;
  static Map<String, String> _deviceInfo = {};

  /// Initialize API service
  static Future<void> initialize() async {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(_AuthInterceptor());
    _dio.interceptors.add(_LoggingInterceptor());

    // Get device info
    await _getDeviceInfo();
  }

  /// Get device information
  static Future<void> _getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      final packageInfo = await PackageInfo.fromPlatform();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        _deviceInfo = {
          'platform': 'android',
          'deviceId': androidInfo.id,
          'osVersion': androidInfo.version.release,
          'appVersion': packageInfo.version,
          'deviceModel': '${androidInfo.manufacturer} ${androidInfo.model}',
        };
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        _deviceInfo = {
          'platform': 'ios',
          'deviceId': iosInfo.identifierForVendor ?? 'unknown',
          'osVersion': iosInfo.systemVersion,
          'appVersion': packageInfo.version,
          'deviceModel': iosInfo.model,
        };
      }
    } catch (e) {
      debugPrint('❌ Failed to get device info: $e');
    }
  }

  /// Set authentication token
  static void setAuthToken(String token) {
    _authToken = token;
  }

  /// Clear authentication token
  static void clearAuthToken() {
    _authToken = null;
  }

  /// Register FCM token with backend
  static Future<Map<String, dynamic>> registerFCMToken(String fcmToken) async {
    try {
      final response = await _dio.post('/fcm/register', data: {
        'token': fcmToken,
        'deviceInfo': _deviceInfo,
      });

      return response.data;
    } catch (e) {
      debugPrint('❌ Failed to register FCM token: $e');
      rethrow;
    }
  }

  /// Send test notification
  static Future<Map<String, dynamic>> sendTestNotification() async {
    try {
      final response = await _dio.post('/fcm/test');
      return response.data;
    } catch (e) {
      debugPrint('❌ Failed to send test notification: $e');
      rethrow;
    }
  }

  /// Login user
  static Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      final response = await _dio.post('/auth/login', data: {
        'email': email,
        'password': password,
      });

      if (response.data['success'] == true) {
        final token = response.data['data']['token'];
        setAuthToken(token);
      }

      return response.data;
    } catch (e) {
      debugPrint('❌ Login failed: $e');
      rethrow;
    }
  }

  /// Logout user
  static Future<void> logout() async {
    try {
      await _dio.post('/auth/logout');
      clearAuthToken();
    } catch (e) {
      debugPrint('❌ Logout failed: $e');
      // Continue with local logout even if API call fails
    }
  }
}

/// Auth interceptor to add token to requests
class _AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (ApiService._authToken != null) {
      options.headers['Authorization'] = 'Bearer ${ApiService._authToken}';
    }
    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == 401) {
      // Token expired, clear it
      ApiService.clearAuthToken();
      // Redirect to login screen
    }
    handler.next(err);
  }
}

/// Logging interceptor for debugging
class _LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    debugPrint('🌐 API Request: ${options.method} ${options.path}');
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    debugPrint('✅ API Response: ${response.statusCode} ${response.requestOptions.path}');
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    debugPrint('❌ API Error: ${err.response?.statusCode} ${err.requestOptions.path}');
    handler.next(err);
  }
}
```

### Step 4: App Integration

#### 4.1 Update `main.dart`

```dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'services/firebase_service.dart';
import 'services/api_service.dart';
import 'providers/auth_provider.dart';
import 'providers/notification_provider.dart';
import 'screens/splash_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize services
  await FirebaseService.initialize();
  await ApiService.initialize();
  
  runApp(const DeynCareApp());
}

class DeynCareApp extends StatelessWidget {
  const DeynCareApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => NotificationProvider()),
      ],
      child: MaterialApp(
        title: 'DeynCare Admin',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          useMaterial3: true,
        ),
        home: const SplashScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
```

#### 4.2 Create Notification Provider (`lib/providers/notification_provider.dart`)

```dart
import 'package:flutter/foundation.dart';
import '../services/firebase_service.dart';
import '../services/api_service.dart';

class NotificationProvider extends ChangeNotifier {
  bool _isRegistered = false;
  String? _fcmToken;
  List<Map<String, dynamic>> _notifications = [];

  bool get isRegistered => _isRegistered;
  String? get fcmToken => _fcmToken;
  List<Map<String, dynamic>> get notifications => _notifications;

  /// Register FCM token with backend
  Future<void> registerFCMToken() async {
    try {
      _fcmToken = FirebaseService.fcmToken;
      
      if (_fcmToken != null) {
        await ApiService.registerFCMToken(_fcmToken!);
        _isRegistered = true;
        notifyListeners();
        debugPrint('✅ FCM token registered successfully');
      }
    } catch (e) {
      debugPrint('❌ Failed to register FCM token: $e');
      _isRegistered = false;
      notifyListeners();
    }
  }

  /// Send test notification
  Future<void> sendTestNotification() async {
    try {
      await ApiService.sendTestNotification();
      debugPrint('✅ Test notification sent');
    } catch (e) {
      debugPrint('❌ Failed to send test notification: $e');
      rethrow;
    }
  }

  /// Add notification to local list
  void addNotification(Map<String, dynamic> notification) {
    _notifications.insert(0, notification);
    notifyListeners();
  }

  /// Clear all notifications
  void clearNotifications() {
    _notifications.clear();
    notifyListeners();
  }
}
```

### Step 5: Authentication Integration

#### 5.1 Create Auth Provider (`lib/providers/auth_provider.dart`)

```dart
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../services/api_service.dart';
import '../models/user_model.dart';

class AuthProvider extends ChangeNotifier {
  static const _storage = FlutterSecureStorage();
  
  User? _user;
  bool _isLoggedIn = false;
  bool _isLoading = false;

  User? get user => _user;
  bool get isLoggedIn => _isLoggedIn;
  bool get isLoading => _isLoading;

  /// Check if user is already logged in
  Future<void> checkAuthStatus() async {
    _isLoading = true;
    notifyListeners();

    try {
      final token = await _storage.read(key: 'auth_token');
      if (token != null) {
        ApiService.setAuthToken(token);
        // Verify token with backend and get user data
        await _getUserProfile();
      }
    } catch (e) {
      debugPrint('❌ Auth check failed: $e');
      await logout();
    }

    _isLoading = false;
    notifyListeners();
  }

  /// Login user
  Future<bool> login(String email, String password) async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await ApiService.login(email, password);
      
      if (response['success'] == true) {
        final userData = response['data']['user'];
        final token = response['data']['token'];
        
        _user = User.fromJson(userData);
        _isLoggedIn = true;
        
        // Store token securely
        await _storage.write(key: 'auth_token', value: token);
        
        _isLoading = false;
        notifyListeners();
        return true;
      }
    } catch (e) {
      debugPrint('❌ Login failed: $e');
    }

    _isLoading = false;
    notifyListeners();
    return false;
  }

  /// Logout user
  Future<void> logout() async {
    try {
      await ApiService.logout();
      await _storage.delete(key: 'auth_token');
      
      _user = null;
      _isLoggedIn = false;
      
      ApiService.clearAuthToken();
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Logout failed: $e');
    }
  }

  /// Get user profile
  Future<void> _getUserProfile() async {
    try {
      // Implement API call to get user profile
      // final response = await ApiService.getUserProfile();
      // _user = User.fromJson(response['data']);
      // _isLoggedIn = true;
    } catch (e) {
      debugPrint('❌ Failed to get user profile: $e');
      await logout();
    }
  }
}
```

### Step 6: Testing and Validation

#### 6.1 Create Test Screen (`lib/screens/notification_test_screen.dart`)

```dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/notification_provider.dart';
import '../services/firebase_service.dart';

class NotificationTestScreen extends StatelessWidget {
  const NotificationTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Test'),
      ),
      body: Consumer<NotificationProvider>(
        builder: (context, notificationProvider, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'FCM Status',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text('Initialized: ${FirebaseService.isInitialized}'),
                        Text('Token Registered: ${notificationProvider.isRegistered}'),
                        if (notificationProvider.fcmToken != null) ...[
                          const SizedBox(height: 8),
                          Text(
                            'FCM Token:',
                            style: Theme.of(context).textTheme.titleSmall,
                          ),
                          Text(
                            notificationProvider.fcmToken!,
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: notificationProvider.isRegistered 
                      ? null 
                      : () => notificationProvider.registerFCMToken(),
                  child: const Text('Register FCM Token'),
                ),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: notificationProvider.isRegistered 
                      ? () => notificationProvider.sendTestNotification()
                      : null,
                  child: const Text('Send Test Notification'),
                ),
                const SizedBox(height: 16),
                Text(
                  'Recent Notifications',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                Expanded(
                  child: notificationProvider.notifications.isEmpty
                      ? const Center(child: Text('No notifications yet'))
                      : ListView.builder(
                          itemCount: notificationProvider.notifications.length,
                          itemBuilder: (context, index) {
                            final notification = notificationProvider.notifications[index];
                            return Card(
                              child: ListTile(
                                title: Text(notification['title'] ?? 'No title'),
                                subtitle: Text(notification['body'] ?? 'No body'),
                                trailing: Text(
                                  notification['timestamp'] ?? '',
                                  style: Theme.of(context).textTheme.bodySmall,
                                ),
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
```

## 🔗 API Endpoints Reference

### FCM Token Management

#### Register FCM Token
```http
POST /api/fcm/register
Authorization: Bearer <token>
Content-Type: application/json

{
  "token": "fcm_token_here",
  "deviceInfo": {
    "platform": "android|ios",
    "deviceId": "device_unique_id",
    "appVersion": "1.0.0",
    "osVersion": "13.0",
    "deviceModel": "iPhone 14"
  }
}
```

#### Send Test Notification
```http
POST /api/fcm/test
Authorization: Bearer <token>
```

### Authentication

#### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

## 🚀 Deployment Checklist

### Android
- [ ] Add `google-services.json` to `android/app/`
- [ ] Update `android/app/build.gradle` with Firebase dependencies
- [ ] Update `android/build.gradle` with Google Services plugin
- [ ] Test on physical device (emulator may have issues)

### iOS
- [ ] Add `GoogleService-Info.plist` to `ios/Runner/`
- [ ] Update `ios/Runner/Info.plist` with Firebase settings
- [ ] Enable push notifications in Xcode capabilities
- [ ] Test on physical device (simulator doesn't support push notifications)

### Backend
- [ ] Firebase Admin SDK configured
- [ ] Environment variables set
- [ ] Push notification APIs deployed
- [ ] CORS configured for mobile app

## 🔍 Troubleshooting

### Common Issues

1. **FCM Token Not Generated**
   - Check Firebase configuration files
   - Verify app package/bundle ID matches Firebase project
   - Ensure permissions are granted

2. **Notifications Not Received**
   - Check device notification settings
   - Verify token registration with backend
   - Test with Firebase Console first

3. **Authentication Issues**
   - Verify API endpoints are correct
   - Check CORS configuration
   - Ensure SSL certificates are valid

### Debug Commands

```bash
# Flutter
flutter doctor
flutter clean && flutter pub get

# Firebase
firebase login
firebase projects:list

# Android
adb logcat | grep -i firebase

# iOS
# Use Xcode console for logs
```

## 📞 Support

For integration support:
- Backend API: Check `/api/health` endpoint
- Firebase Console: [https://console.firebase.google.com/project/deyncare-47d99](https://console.firebase.google.com/project/deyncare-47d99)
- Documentation: This guide and Firebase Flutter documentation

---

**Next Steps**: Once mobile app integration is complete, proceed with web dashboard development for SuperAdmin notification management. 