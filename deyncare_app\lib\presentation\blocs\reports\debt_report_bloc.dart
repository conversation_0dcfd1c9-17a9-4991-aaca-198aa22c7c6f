import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:deyncare_app/domain/usecases/reports/generate_debt_report_use_case.dart';
import 'package:flutter/foundation.dart';

// Events
abstract class DebtReportEvent extends Equatable {
  const DebtReportEvent();

  @override
  List<Object?> get props => [];
}

class GenerateDebtReportEvent extends DebtReportEvent {
  final String? month;
  final String? year;
  final String? startDate;
  final String? endDate;
  final String dateRange;
  final String shopName;
  final String? shopLogo;

  const GenerateDebtReportEvent({
    this.month,
    this.year,
    this.startDate,
    this.endDate,
    this.dateRange = 'monthly',
    required this.shopName,
    this.shopLogo,
  });

  @override
  List<Object?> get props => [month, year, startDate, endDate, dateRange, shopName, shopLogo];
}

class ResetDebtReportEvent extends DebtReportEvent {}

// States
abstract class DebtReportState extends Equatable {
  const DebtReportState();

  @override
  List<Object?> get props => [];
}

class DebtReportInitial extends DebtReportState {}

class DebtReportGenerating extends DebtReportState {}

class DebtReportGenerated extends DebtReportState {
  final String pdfPath;
  final String reportPeriod;

  const DebtReportGenerated({
    required this.pdfPath,
    required this.reportPeriod,
  });

  @override
  List<Object?> get props => [pdfPath, reportPeriod];
}

class DebtReportError extends DebtReportState {
  final String message;

  const DebtReportError(this.message);

  @override
  List<Object?> get props => [message];
}

// BLoC
class DebtReportBloc extends Bloc<DebtReportEvent, DebtReportState> {
  final GenerateDebtReportUseCase generateDebtReportUseCase;

  DebtReportBloc({
    required this.generateDebtReportUseCase,
  }) : super(DebtReportInitial()) {
    on<GenerateDebtReportEvent>(_onGenerateDebtReport);
    on<ResetDebtReportEvent>(_onResetDebtReport);
  }

  Future<void> _onGenerateDebtReport(
    GenerateDebtReportEvent event,
    Emitter<DebtReportState> emit,
  ) async {
    if (kDebugMode) {
      print('🔍 DebtReportBloc: Starting debt report generation');
      print(
          '🔍 Event: month=${event.month}, year=${event.year}, startDate=${event.startDate}, endDate=${event.endDate}, dateRange=${event.dateRange}');
    }

    emit(DebtReportGenerating());

    try {
      if (kDebugMode) {
        print('🔍 DebtReportBloc: Calling debt report use case...');
      }

      final pdfPath = await generateDebtReportUseCase.execute(
        startDate: event.startDate,
        endDate: event.endDate,
        period: event.dateRange,
        shopName: event.shopName,
        shopLogo: event.shopLogo,
      );

      if (kDebugMode) {
        print('🔍 DebtReportBloc: Use case completed, PDF path: $pdfPath');
      }

      final reportPeriod = _getReportPeriodDescription(
        month: event.month,
        year: event.year,
        startDate: event.startDate,
        endDate: event.endDate,
        dateRange: event.dateRange,
      );

      if (kDebugMode) {
        print('🔍 DebtReportBloc: Report period: $reportPeriod');
        print('✅ DebtReportBloc: Emitting success state');
      }

      emit(DebtReportGenerated(
        pdfPath: pdfPath,
        reportPeriod: reportPeriod,
      ));
    } catch (error) {
      if (kDebugMode) {
        print('❌ DebtReportBloc Error: $error');
        print('❌ Error type: ${error.runtimeType}');
        print('❌ Stack trace: ${StackTrace.current}');
      }
      emit(DebtReportError(error.toString()));
    }
  }

  void _onResetDebtReport(
    ResetDebtReportEvent event,
    Emitter<DebtReportState> emit,
  ) {
    emit(DebtReportInitial());
  }

  String _getReportPeriodDescription({
    String? month,
    String? year,
    String? startDate,
    String? endDate,
    String dateRange = 'monthly',
  }) {
    if (month != null && year != null) {
      final monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      return '${monthNames[int.parse(month) - 1]} $year';
    }

    if (startDate != null && endDate != null) {
      return '$startDate to $endDate';
    }

    switch (dateRange) {
      case 'daily':
        return 'Today';
      case 'weekly':
        return 'This Week';
      case 'monthly':
        return 'This Month';
      case 'yearly':
        return 'This Year';
      default:
        return 'All Time';
    }
  }
}
