import 'package:equatable/equatable.dart';

/// Base class for all dashboard events
abstract class DashboardEvent extends Equatable {
  const DashboardEvent();

  @override
  List<Object?> get props => [];
}

/// Event triggered when dashboard is first loaded
class DashboardInitialized extends DashboardEvent {
  const DashboardInitialized();
}

/// Event triggered to refresh dashboard data
class DashboardRefreshed extends DashboardEvent {
  const DashboardRefreshed();
}

/// Event triggered to load KPI data
class KPIDataRequested extends DashboardEvent {
  final String period; // 'today', 'week', 'month', 'year'
  
  const KPIDataRequested({this.period = 'today'});
  
  @override
  List<Object?> get props => [period];
}

/// Event triggered to load recent activity
class RecentActivityRequested extends DashboardEvent {
  final int limit;
  
  const RecentActivityRequested({this.limit = 10});
  
  @override
  List<Object?> get props => [limit];
}

/// Event triggered to load chart data
class ChartDataRequested extends DashboardEvent {
  final String chartType; // 'payments', 'customers', 'debts', 'risk'
  final String period; // 'week', 'month', 'quarter', 'year'
  
  const ChartDataRequested({
    required this.chartType,
    this.period = 'month',
  });
  
  @override
  List<Object?> get props => [chartType, period];
}

/// Event triggered when user taps on a KPI card
class KPICardTapped extends DashboardEvent {
  final String kpiType; // 'payments', 'debts', 'customers', 'risk'
  
  const KPICardTapped({required this.kpiType});
  
  @override
  List<Object?> get props => [kpiType];
}

/// Event triggered when user taps on a quick action
class QuickActionTapped extends DashboardEvent {
  final String actionType; // 'add_customer', 'record_payment', etc.
  
  const QuickActionTapped({required this.actionType});
  
  @override
  List<Object?> get props => [actionType];
}

/// Event triggered when user taps on an activity item
class ActivityItemTapped extends DashboardEvent {
  final String activityId;
  final String activityType; // 'payment', 'customer', 'debt'
  
  const ActivityItemTapped({
    required this.activityId,
    required this.activityType,
  });
  
  @override
  List<Object?> get props => [activityId, activityType];
}

/// Event triggered when bottom navigation tab is changed
class NavigationTabChanged extends DashboardEvent {
  final int tabIndex;
  
  const NavigationTabChanged({required this.tabIndex});
  
  @override
  List<Object?> get props => [tabIndex];
}

/// Event triggered to load notifications
class NotificationsRequested extends DashboardEvent {
  const NotificationsRequested();
}

/// Event triggered when notification is tapped
class NotificationTapped extends DashboardEvent {
  final String notificationId;
  
  const NotificationTapped({required this.notificationId});
  
  @override
  List<Object?> get props => [notificationId];
}

/// Event triggered to mark notification as read
class NotificationMarkedAsRead extends DashboardEvent {
  final String notificationId;
  
  const NotificationMarkedAsRead({required this.notificationId});
  
  @override
  List<Object?> get props => [notificationId];
}

/// Event triggered when user pulls to refresh
class PullToRefreshTriggered extends DashboardEvent {
  const PullToRefreshTriggered();
}

/// Event triggered when user searches in dashboard
class DashboardSearchRequested extends DashboardEvent {
  final String query;
  final String searchType; // 'customers', 'products', 'transactions'
  
  const DashboardSearchRequested({
    required this.query,
    this.searchType = 'customers',
  });
  
  @override
  List<Object?> get props => [query, searchType];
}

/// Event triggered when user applies filters
class DashboardFiltersApplied extends DashboardEvent {
  final Map<String, dynamic> filters;
  
  const DashboardFiltersApplied({required this.filters});
  
  @override
  List<Object?> get props => [filters];
}

/// Event triggered when user clears filters
class DashboardFiltersCleared extends DashboardEvent {
  const DashboardFiltersCleared();
}

/// Event triggered when user wants to retry a failed operation
class DashboardRetryRequested extends DashboardEvent {
  const DashboardRetryRequested();
}

/// Event triggered when user goes offline/online
class DashboardConnectivityChanged extends DashboardEvent {
  final bool isConnected;
  
  const DashboardConnectivityChanged({required this.isConnected});
  
  @override
  List<Object?> get props => [isConnected];
} 

/// Event to refresh only specific dashboard components
class DashboardSelectiveRefresh extends DashboardEvent {
  final List<String> components; // ['kpi', 'activity', 'charts', 'notifications']
  final bool useCache;
  final Duration? maxCacheAge;

  const DashboardSelectiveRefresh({
    required this.components,
    this.useCache = true,
    this.maxCacheAge = const Duration(minutes: 5),
  });

  @override
  List<Object?> get props => [components, useCache, maxCacheAge];
}

/// Event triggered when app comes to foreground
class DashboardAppResumed extends DashboardEvent {
  final bool forceFresh;

  const DashboardAppResumed({this.forceFresh = false});

  @override
  List<Object?> get props => [forceFresh];
}

/// Event to update cache settings
class DashboardCacheSettingsUpdated extends DashboardEvent {
  final Duration maxAge;
  final bool enableOfflineMode;

  const DashboardCacheSettingsUpdated({
    required this.maxAge,
    required this.enableOfflineMode,
  });

  @override
  List<Object?> get props => [maxAge, enableOfflineMode];
}

/// Event for background data sync
class DashboardBackgroundSync extends DashboardEvent {
  final bool silent; // Don't show loading indicators

  const DashboardBackgroundSync({this.silent = true});

  @override
  List<Object?> get props => [silent];
} 