import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';

/// Handles request retries with exponential backoff and improved error handling
class RetryHandler {
  final int maxRetries;
  final Duration initialDelay;
  final Duration maxDelay;
  
  const RetryHandler({
    this.maxRetries = 3,
    this.initialDelay = const Duration(milliseconds: 500),
    this.maxDelay = const Duration(seconds: 8),
  });
  
  /// Retry a request with exponential backoff and improved error handling
  Future<T> retryRequest<T>(Future<T> Function() requestFunc) async {
    int attempts = 0;
    dynamic lastError;
    
    while (attempts < maxRetries) {
      try {
        return await requestFunc();
      } catch (e) {
        // Do not retry for certain types of requests or errors
        if (e is ApiException) {
          // Do not retry for any client errors (4xx)
          if (e.statusCode != null && e.statusCode! >= 400 && e.statusCode! < 500) {
            if (kDebugMode) {
              print('Client error (${e.statusCode}), not retrying: ${e.message}');
            }
            rethrow;
          }
          
          // Do not retry for certain server errors that indicate permanent failures
          if (e.statusCode != null && e.statusCode! >= 500) {
            // Don't retry for "not found" endpoints or configuration errors
            if (e.message.toLowerCase().contains('cannot find') ||
                e.message.toLowerCase().contains('not found') ||
                e.code == 'not_found') {
              if (kDebugMode) {
                print('Endpoint not found (${e.statusCode}), not retrying: ${e.message}');
              }
              rethrow;
            }
            
            // Don't retry for service misconfiguration errors
            if (e.message.toLowerCase().contains('misconfigured') ||
                e.message.toLowerCase().contains('configuration')) {
              if (kDebugMode) {
                print('Configuration error (${e.statusCode}), not retrying: ${e.message}');
              }
              rethrow;
            }
          }
          
          // Handle rate limiting with special backoff strategy
          if (e.statusCode == 429 || e.code == 'rate_limit_exceeded') {
            attempts++;
            lastError = e;
            
            // If we've reached max retries for rate limiting, rethrow
            if (attempts >= maxRetries) {
              rethrow;
            }
            
            // For rate limiting, use a longer delay
            final rateLimitDelay = Duration(
              seconds: (attempts * attempts * 2) + 5, // 7, 11, 17 seconds
            );
            
            if (kDebugMode) {
              print('📈 Rate limit hit, waiting ${rateLimitDelay.inSeconds} seconds before retry $attempts/$maxRetries');
            }
            
            await Future.delayed(rateLimitDelay);
            continue;
          }
        }
        
        // Do not retry for type casting errors (these won't be fixed by retrying)
        if (e.toString().contains('type') && e.toString().contains('is not a subtype of')) {
          if (kDebugMode) {
            print('Type casting error detected, not retrying: $e');
          }
          rethrow;
        }
        
        // Do not retry for format/parsing errors
        if (e.toString().toLowerCase().contains('format') ||
            e.toString().toLowerCase().contains('parsing') ||
            e.toString().toLowerCase().contains('invalid json')) {
          if (kDebugMode) {
            print('Format/parsing error detected, not retrying: $e');
          }
          rethrow;
        }

        attempts++;
        lastError = e;
        
        // If we've reached max retries, rethrow the last error
        if (attempts >= maxRetries) {
          rethrow;
        }
        
        // Log retry attempts in debug mode
        if (kDebugMode) {
          print('❌ API Error: ${e.toString()}');
          
          if (e is ApiException) {
            print('Original error: $e');
          }
          
          bool isNetworkError = false;
          if (e.toString().toLowerCase().contains('timeout') ||
              e.toString().toLowerCase().contains('connection') ||
              e.toString().toLowerCase().contains('network') ||
              e.toString().toLowerCase().contains('socket')) {
            isNetworkError = true;
          }
          
          if (isNetworkError) {
            print('Network error: $e');
          }
          
          print('Retrying operation (attempt $attempts)');
        }
        
        // Wait before retrying with exponential backoff
        final baseDelay = initialDelay.inMilliseconds * (1 << (attempts - 1));
        // Cap the delay to avoid excessive waiting
        final cappedDelay = Duration(milliseconds: baseDelay < maxDelay.inMilliseconds ? baseDelay : maxDelay.inMilliseconds);
        await Future.delayed(cappedDelay);
      }
    }
    
    // This line should never be reached but is needed for type safety
    throw lastError;
  }
}
