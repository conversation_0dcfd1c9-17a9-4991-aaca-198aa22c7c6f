import 'package:dio/dio.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:deyncare_app/core/utils/api_toast_handler.dart';

/// Handles network and API errors with consistent user-friendly messages
class ErrorHandler {
  /// Convert DioException to a user-friendly ApiException
  static ApiException handleDioError(DioException e) {
    // Check if automatic toasts are disabled for this request
    final showAutoToast = e.requestOptions.extra['showAutoToast'] ?? true;
    
    // If we have a structured API exception, return it
    if (e.error is ApiException) {
      final apiException = e.error as ApiException;
      // Only show error toast if automatic toasts are enabled
      if (showAutoToast) {
      ApiToastHandler.handleError(apiException);
      }
      return apiException;
    }
    
    // Map network-related errors to user-friendly messages
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        final exception = ApiException(
          message: 'Connection timed out. Please check your internet connection.',
          code: 'timeout_error',
          statusCode: 0,
          data: e.response?.data,
        );
        // Only show error toast if automatic toasts are enabled
        if (showAutoToast) {
        ApiToastHandler.handleError(exception);
        }
        return exception;
        
      case DioExceptionType.connectionError:
        final exception = ApiException(
          message: 'No internet connection. Please check your network settings.',
          code: 'connection_error',
          statusCode: 0,
          data: e.response?.data,
        );
        // Only show error toast if automatic toasts are enabled
        if (showAutoToast) {
        ApiToastHandler.handleError(exception);
        }
        return exception;
        
      case DioExceptionType.badResponse:
        // Handle specific HTTP status codes
        if (e.response?.statusCode != null) {
          switch (e.response!.statusCode) {
            case 400:
              final exception = ApiException(
                message: 'The request was invalid. Please check your input and try again.',
                code: 'bad_request',
                statusCode: e.response?.statusCode,
                data: e.response?.data,
              );
              // Only show error toast if automatic toasts are enabled
              if (showAutoToast) {
              ApiToastHandler.handleError(exception);
              }
              return exception;
            case 401:
              // Default message for session expiration
              String message = 'Your session has expired. Please log in again.';
              String code = 'unauthorized';
              String? suspensionReason;
              
              if (e.response?.data is Map<String, dynamic>) {
                final data = e.response!.data as Map<String, dynamic>;
                final responseMessage = data['message'] ?? '';
                
                // Check if this is a login endpoint - preserve original error message
                if (e.requestOptions.path.contains('/auth/login')) {
                  message = responseMessage.isNotEmpty ? responseMessage : 'Invalid email or password';
                  code = data['errorCode'] ?? 'login_failed';
                }
                // Check if the response contains suspension information
                else if (responseMessage.toLowerCase().contains('suspended') || 
                    responseMessage.toLowerCase().contains('account is suspended')) {
                  // This is a suspension error
                  code = 'account_suspended';
                  message = responseMessage;
                  
                  // Extract suspension reason from the message
                  // Backend sends: "Account is suspended. Reason: [reason]"
                  final reasonMatch = RegExp(r'Reason:\s*(.+)').firstMatch(responseMessage);
                  if (reasonMatch != null) {
                    suspensionReason = reasonMatch.group(1)?.trim();
                  }
                }
              }
              
              final exception = ApiException(
                message: message,
                code: code,
                statusCode: e.response?.statusCode,
                data: e.response?.data,
                suspensionReason: suspensionReason,
              );
              
              // Only show error toast if automatic toasts are enabled and it's not a suspension
              if (showAutoToast && code != 'account_suspended') {
              ApiToastHandler.handleError(exception);
              }
              return exception;
            case 403:
              final exception = ApiException(
                message: 'You don\'t have permission to access this resource.',
                code: 'forbidden',
                statusCode: e.response?.statusCode,
                data: e.response?.data,
              );
              // Only show error toast if automatic toasts are enabled
              if (showAutoToast) {
              ApiToastHandler.handleError(exception);
              }
              return exception;
            case 404:
              final exception = ApiException(
                message: 'The requested resource was not found.',
                code: 'not_found',
                statusCode: e.response?.statusCode,
                data: e.response?.data,
              );
              // Only show error toast if automatic toasts are enabled
              if (showAutoToast) {
              ApiToastHandler.handleError(exception);
              }
              return exception;
            case 422:
              final exception = ApiException(
                message: 'Validation failed. Please check your input and try again.',
                code: 'validation_error',
                statusCode: e.response?.statusCode,
                data: e.response?.data,
              );
              // Only show error toast if automatic toasts are enabled
              if (showAutoToast) {
              ApiToastHandler.handleError(exception);
              }
              return exception;
            case 429:
              // Handle rate limiting - NEW ADDITION
              final exception = ApiException(
                message: 'Too many requests. Please wait a moment and try again.',
                code: 'rate_limit_exceeded',
                statusCode: e.response?.statusCode,
                data: e.response?.data,
              );
              // Only show error toast if automatic toasts are enabled
              if (showAutoToast) {
              ApiToastHandler.handleError(exception);
              }
              return exception;
            case 500:
            case 501:
            case 502:
            case 503:
              final exception = ApiException(
                message: 'Server error. Our team has been notified and is working on the issue.',
                code: 'server_error',
                statusCode: e.response?.statusCode,
                data: e.response?.data,
              );
              // Only show error toast if automatic toasts are enabled
              if (showAutoToast) {
              ApiToastHandler.handleError(exception);
              }
              return exception;
            default:
              // Parse error message from response if available
              String message = 'An unexpected error occurred';
              String code = 'unknown_error';
              
              if (e.response?.data is Map<String, dynamic>) {
                final data = e.response!.data as Map<String, dynamic>;
                if (data.containsKey('message')) {
                  message = data['message'];
                }
                if (data.containsKey('code') || data.containsKey('errorCode')) {
                  code = data['code'] ?? data['errorCode'] ?? code;
                }
              }
              
              final exception = ApiException(
                message: message,
                code: code,
                statusCode: e.response?.statusCode,
                data: e.response?.data,
              );
              // Only show error toast if automatic toasts are enabled
              if (showAutoToast) {
              ApiToastHandler.handleError(exception);
              }
              return exception;
          }
        }
        break;
      
      default:
        // Other Dio errors
        final exception = ApiException(
          message: e.message ?? 'An unexpected error occurred',
          code: 'unknown_error',
          statusCode: e.response?.statusCode,
          data: e.response?.data,
        );
        // Only show error toast if automatic toasts are enabled
        if (showAutoToast) {
        ApiToastHandler.handleError(exception);
        }
        return exception;
    }
    
    // Fallback error message
    final exception = ApiException(
      message: e.message ?? 'An unexpected error occurred',
      code: 'unknown_error',
      statusCode: e.response?.statusCode,
      data: e.response?.data,
    );
    // Only show error toast if automatic toasts are enabled
    if (showAutoToast) {
    ApiToastHandler.handleError(exception);
    }
    return exception;
  }
}
