import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:async';
import 'package:intl/intl.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_event.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';

/// Modal that shows when user account is suspended
class AccountSuspensionModal extends StatefulWidget {
  final String reason;
  final DateTime? suspendedAt;
  final String? suspendedBy;
  final int initialCountdown;
  final VoidCallback? onRedirect;

  const AccountSuspensionModal({
    Key? key,
    required this.reason,
    this.suspendedAt,
    this.suspendedBy,
    this.initialCountdown = 10,
    this.onRedirect,
  }) : super(key: key);

  @override
  State<AccountSuspensionModal> createState() => _AccountSuspensionModalState();
}

class _AccountSuspensionModalState extends State<AccountSuspensionModal> {
  late int _countdown;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _countdown = widget.initialCountdown;
    _startCountdown();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown > 0) {
        setState(() {
          _countdown--;
        });
        
        // Update the BLoC state with new countdown
        context.read<AuthBloc>().add(UpdateRedirectCountdown(countdown: _countdown));
      } else {
        timer.cancel();
        _handleRedirect();
      }
    });
  }

  void _handleRedirect() {
    // Complete the suspension redirect in the BLoC
    context.read<AuthBloc>().add(const CompleteSuspensionRedirect());
    
    // Close the modal
    Navigator.of(context).pop();
    
    // Call the redirect callback if provided
    widget.onRedirect?.call();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false, // Prevent dismissing by back button
      child: _buildSuspensionContent(context),
    );
  }

  Widget _buildSuspensionContent(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Warning Icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: ThemeUtils.getStatusColors(context).errorSurface,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.block,
              size: 40,
              color: ThemeUtils.getStatusColors(context).error,
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Title
          Text(
            'Account Suspended',
            style: theme.textTheme.headlineMedium?.copyWith(
              color: ThemeUtils.getStatusColors(context).error,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 16),
          
          // Reason
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: ThemeUtils.getStatusColors(context).errorSurface,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: ThemeUtils.getBorderColor(context, type: BorderType.error),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Reason:',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: ThemeUtils.getStatusColors(context).error,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  widget.reason,
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Suspension details
          if (widget.suspendedAt != null || widget.suspendedBy != null) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppThemes.dividerColor,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (widget.suspendedAt != null) ...[
                    Row(
                      children: [
                        Icon(
                          Icons.schedule,
                          size: 16,
                          color: theme.hintColor,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Suspended on:',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.hintColor,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          DateFormat('MMM dd, yyyy at HH:mm').format(widget.suspendedAt!),
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                  ],
                  if (widget.suspendedBy != null) ...[
                    Row(
                      children: [
                        Icon(
                          Icons.person,
                          size: 16,
                          color: theme.hintColor,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Suspended by:',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.hintColor,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          widget.suspendedBy!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
            const SizedBox(height: 24),
          ],
          
          // Message
          Text(
            'Your account has been suspended and you cannot access the application at this time. Please contact your administrator for assistance.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.hintColor,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 24),
          
          // Countdown and redirect info
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: ThemeUtils.getStatusColors(context).warningSurface,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: ThemeUtils.getBorderColor(context, type: BorderType.error),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.timer,
                  size: 32,
                  color: ThemeUtils.getStatusColors(context).warning,
                ),
                const SizedBox(height: 8),
                Text(
                  'Redirecting to login in',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: ThemeUtils.getStatusColors(context).warning,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '$_countdown seconds',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: ThemeUtils.getStatusColors(context).warning,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Action button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _handleRedirect,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppThemes.primaryColor,
                foregroundColor: ThemeUtils.getTextColor(context, type: TextColorType.primary),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Go to Login Now',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: ThemeUtils.getTextColor(context, type: TextColorType.primary),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Helper class to show the suspension modal
class AccountSuspensionModalHelper {
  /// Show the account suspension modal
  static void showSuspensionModal({
    required BuildContext context,
    required String reason,
    DateTime? suspendedAt,
    String? suspendedBy,
    int countdown = 10,
    VoidCallback? onRedirect,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissing by tapping outside
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: AccountSuspensionModal(
          reason: reason,
          suspendedAt: suspendedAt,
          suspendedBy: suspendedBy,
          initialCountdown: countdown,
          onRedirect: onRedirect,
        ),
      ),
    );
  }
} 