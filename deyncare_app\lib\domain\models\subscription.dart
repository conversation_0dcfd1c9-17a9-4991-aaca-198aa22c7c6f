import 'package:equatable/equatable.dart';

/// Domain model for subscription representing business logic
class Subscription extends Equatable {
  final String subscriptionId;
  final String shopId;
  final String? planId;
  final String planName;
  final String planType; // 'trial', 'monthly', 'yearly'
  final String status; // 'trial', 'active', 'past_due', 'canceled', 'expired'
  final double basePrice;
  final double totalPrice;
  final String currency;
  final String billingCycle;
  final String paymentMethod;
  final bool paymentVerified;
  final DateTime startDate;
  final DateTime endDate;
  final DateTime? trialEndsAt;
  final int daysRemaining;
  final int percentageUsed;
  final bool autoRenew;
  final bool isActive;
  final bool isInTrial;
  final bool isExpired;
  final bool isCanceled;
  final bool isPastDue;
  final String displayStatus;
  final bool isTrialEndingSoon;
  final bool isExpiringSoon;
  final bool hasDiscount;
  final double discountAmount;
  final String? discountType;
  final String? discountCode;
  final DateTime? lastPaymentDate;
  final DateTime? nextPaymentDate;
  final int failedPayments;
  final String? cancellationReason;
  final String? cancellationFeedback;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Subscription({
    required this.subscriptionId,
    required this.shopId,
    this.planId,
    required this.planName,
    required this.planType,
    required this.status,
    required this.basePrice,
    required this.totalPrice,
    required this.currency,
    required this.billingCycle,
    required this.paymentMethod,
    required this.paymentVerified,
    required this.startDate,
    required this.endDate,
    this.trialEndsAt,
    required this.daysRemaining,
    required this.percentageUsed,
    required this.autoRenew,
    required this.isActive,
    required this.isInTrial,
    required this.isExpired,
    required this.isCanceled,
    required this.isPastDue,
    required this.displayStatus,
    required this.isTrialEndingSoon,
    required this.isExpiringSoon,
    required this.hasDiscount,
    required this.discountAmount,
    this.discountType,
    this.discountCode,
    this.lastPaymentDate,
    this.nextPaymentDate,
    required this.failedPayments,
    this.cancellationReason,
    this.cancellationFeedback,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        subscriptionId,
        shopId,
        planId,
        planName,
        planType,
        status,
        basePrice,
        totalPrice,
        currency,
        billingCycle,
        paymentMethod,
        paymentVerified,
        startDate,
        endDate,
        trialEndsAt,
        daysRemaining,
        percentageUsed,
        autoRenew,
        isActive,
        isInTrial,
        isExpired,
        isCanceled,
        isPastDue,
        displayStatus,
        isTrialEndingSoon,
        isExpiringSoon,
        hasDiscount,
        discountAmount,
        discountType,
        discountCode,
        lastPaymentDate,
        nextPaymentDate,
        failedPayments,
        cancellationReason,
        cancellationFeedback,
        createdAt,
        updatedAt,
      ];

  /// Convenience methods for business logic
  String get statusDisplayText {
    switch (status) {
      case 'trial':
        return 'Trial';
      case 'active':
        return 'Active';
      case 'past_due':
        return 'Past Due';
      case 'canceled':
        return 'Canceled';
      case 'expired':
        return 'Expired';
      default:
        return status.toUpperCase();
    }
  }

  String get planDisplayName {
    switch (planType) {
      case 'trial':
        return 'Free Trial';
      case 'monthly':
        return 'Monthly Plan';
      case 'yearly':
        return 'Yearly Plan';
      default:
        return planName;
    }
  }

  bool get canUpgrade => isInTrial || planType == 'monthly';
  
  bool get canCancel => isActive && !isCanceled;
  
  bool get needsPayment => isPastDue || failedPayments > 0;

  String get daysRemainingText {
    if (daysRemaining == 0) {
      return 'Expires today';
    } else if (daysRemaining == 1) {
      return '1 day remaining';
    } else {
      return '$daysRemaining days remaining';
    }
  }

  String get progressText => '$percentageUsed% used';
} 