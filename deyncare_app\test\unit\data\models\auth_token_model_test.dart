import 'package:flutter_test/flutter_test.dart';
import 'package:deyncare_app/data/models/auth_token_model.dart';
import 'package:deyncare_app/domain/models/auth_token.dart';

void main() {
  group('AuthTokenModel', () {
    test('from<PERSON><PERSON> creates correct instance', () {
      // Create a test JSON map
      final json = {
        'accessToken': 'test_access_token',
        'refreshToken': 'test_refresh_token',
        'expiresIn': 900, // 15 minutes in seconds
      };
      
      // Create model from JSON
      final model = AuthTokenModel.fromJson(json);
      
      // Verify properties were set correctly
      expect(model.accessToken, 'test_access_token');
      expect(model.refreshToken, 'test_refresh_token');
      
      // Verify expiresAt was calculated correctly (within a second tolerance)
      final expectedExpiry = DateTime.now().add(const Duration(minutes: 15));
      final difference = expectedExpiry.difference(model.expiresAt).inSeconds.abs();
      expect(difference, lessThan(2)); // Allow 2 seconds difference due to test execution time
    });
    
    test('from<PERSON><PERSON> handles missing expiresIn by using default', () {
      // Create a test JSON map without expiresIn
      final json = {
        'accessToken': 'test_access_token',
        'refreshToken': 'test_refresh_token',
      };
      
      // Create model from JSON
      final model = AuthTokenModel.fromJson(json);
      
      // Verify properties were set correctly
      expect(model.accessToken, 'test_access_token');
      expect(model.refreshToken, 'test_refresh_token');
      
      // Verify default expiration (15 minutes) was used
      final expectedExpiry = DateTime.now().add(const Duration(minutes: 15));
      final difference = expectedExpiry.difference(model.expiresAt).inSeconds.abs();
      expect(difference, lessThan(2));
    });
    
    test('toJson returns correct map', () {
      // Create a test model
      final expiresAt = DateTime.now().add(const Duration(minutes: 15));
      final model = AuthTokenModel(
        accessToken: 'test_access_token',
        refreshToken: 'test_refresh_token',
        expiresAt: expiresAt,
      );
      
      // Convert to JSON
      final json = model.toJson();
      
      // Verify JSON has correct properties
      expect(json['accessToken'], 'test_access_token');
      expect(json['refreshToken'], 'test_refresh_token');
      expect(json['expiresAt'], expiresAt.toIso8601String());
    });
    
    test('toDomain returns correct domain entity', () {
      // Create a test model
      final expiresAt = DateTime.now().add(const Duration(minutes: 15));
      final model = AuthTokenModel(
        accessToken: 'test_access_token',
        refreshToken: 'test_refresh_token',
        expiresAt: expiresAt,
      );
      
      // Convert to domain entity
      final domain = model.toDomain();
      
      // Verify domain entity is correct type and has correct properties
      expect(domain, isA<AuthToken>());
      expect(domain.accessToken, 'test_access_token');
      expect(domain.refreshToken, 'test_refresh_token');
      expect(domain.expiresAt, expiresAt);
    });
    
    test('copyWith returns new instance with updated values', () {
      // Create a test model
      final expiresAt = DateTime.now().add(const Duration(minutes: 15));
      final model = AuthTokenModel(
        accessToken: 'original_access_token',
        refreshToken: 'original_refresh_token',
        expiresAt: expiresAt,
      );
      
      // Create a new model with updated access token
      final updatedModel = model.copyWith(
        accessToken: 'updated_access_token',
      );
      
      // Verify updated model has new access token but original refresh token
      expect(updatedModel.accessToken, 'updated_access_token');
      expect(updatedModel.refreshToken, 'original_refresh_token');
      expect(updatedModel.expiresAt, expiresAt);
      
      // Verify original model was not modified
      expect(model.accessToken, 'original_access_token');
    });
    
    test('token expiry check works correctly', () {
      // Create a token that's already expired
      final expiredModel = AuthTokenModel(
        accessToken: 'expired_token',
        refreshToken: 'refresh_token',
        expiresAt: DateTime.now().subtract(const Duration(minutes: 5)),
      );
      
      // Create a token that expires in the future
      final validModel = AuthTokenModel(
        accessToken: 'valid_token',
        refreshToken: 'refresh_token',
        expiresAt: DateTime.now().add(const Duration(minutes: 5)),
      );
      
      // Verify expiry check works - note that isExpired is a property, not a method
      expect(expiredModel.isExpired, isTrue);
      expect(validModel.isExpired, isFalse);
    });
  });
}
