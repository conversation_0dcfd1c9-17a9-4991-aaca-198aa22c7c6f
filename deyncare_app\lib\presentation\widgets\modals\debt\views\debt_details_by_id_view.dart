import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_bloc.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_event.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_state.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_constants.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/form_mixins.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/loading_states.dart';
import 'package:deyncare_app/presentation/widgets/modals/debt/views/debt_details_view.dart';

/// Debt Details By ID View Widget - Updated for new debt architecture
class DebtDetailsByIdView extends StatefulWidget {
  final String debtId;

  const DebtDetailsByIdView({super.key, required this.debtId});

  @override
  State<DebtDetailsByIdView> createState() => _DebtDetailsByIdViewState();
}

class _DebtDetailsByIdViewState extends State<DebtDetailsByIdView> 
    with BlocListenerMixin<DebtDetailsByIdView> {

  @override
  void initState() {
    super.initState();
    // Load debt details when widget initializes
    context.read<DebtBloc>().add(LoadDebtDetails(widget.debtId));
  }

  @override
  Widget build(BuildContext context) {
    return buildBlocListener<DebtBloc, DebtState>(
      listener: (context, state) {
        // Handle any additional side effects if needed
        // Error handling is done in the content builder
      },
      child: BlocBuilder<DebtBloc, DebtState>(
        builder: (context, state) {
          return Padding(
            padding: ModalConstants.defaultPadding,
            child: _buildContent(context, state),
          );
        },
      ),
    );
  }

  Widget _buildContent(BuildContext context, DebtState state) {
    // Handle loading state
    if (state is DebtDetailsLoading) {
      return _buildLoadingState();
    }

    // Handle successful data load
    if (state is DebtDetailsLoaded) {
      return DebtDetailsView(debt: state.response);
    }

    // Handle specific error state
    if (state is DebtDetailsError) {
      return _buildErrorState(context, state.message);
    }

    // Handle generic error state (fallback)
    if (state is DebtError) {
      return _buildErrorState(context, state.message);
    }

    // Default loading state for initial or other states
    return _buildLoadingState();
  }

  Widget _buildLoadingState() {
    return ModalLoadingStates.detailsSkeleton();
  }

  Widget _buildErrorState(BuildContext context, String errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppThemes.errorColor,
          ),
          ModalConstants.defaultSpacing,
          Text(
            'Error Loading Debt',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppThemes.errorColor,
            ),
          ),
          ModalConstants.defaultSpacing,
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              errorMessage,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppThemes.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          ModalConstants.largeSpacing,
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              OutlinedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ModalConstants.outlinedButtonStyle(),
                child: const Text('Close'),
              ),
              ModalConstants.defaultSpacing,
              ElevatedButton(
                onPressed: () {
                  // Retry loading debt details
                  context.read<DebtBloc>().add(LoadDebtDetails(widget.debtId));
                },
                style: ModalConstants.primaryButtonStyle(),
                child: const Text('Retry'),
              ),
            ],
          ),
        ],
      ),
    );
  }
} 