import 'package:dartz/dartz.dart';
import 'package:deyncare_app/domain/repositories/customer_repository.dart';
import 'package:deyncare_app/data/models/customer_model.dart';
import 'package:deyncare_app/core/errors/failures.dart';

/// Use case for updating customer information - BACKEND ALIGNED
class UpdateCustomerUseCase {
  final CustomerRepository _repository;

  UpdateCustomerUseCase(this._repository);

  /// Execute the use case to update customer
  Future<Either<Failure, CustomerDetailResponse>> execute({
    required String customerId,
    String? customerName,
    String? email,
    String? phone,
    String? address,
    double? creditLimit,
    String? category,
    String? notes,
  }) async {
    if (customerId.trim().isEmpty) {
      return Left(ValidationFailure(message: 'Customer ID is required'));
    }

    // Validate customer ID format (matches backend validation)
    if (!RegExp(r'^CUST\d{3}$').hasMatch(customerId.trim())) {
      return Left(ValidationFailure(message: 'Invalid customer ID format. Expected: CUST001, CUST002, etc.'));
    }

    // Validate email format if provided
    if (email != null && email.trim().isNotEmpty) {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(email.trim())) {
        return Left(ValidationFailure(message: 'Invalid email format'));
      }
    }

    // Validate phone format if provided
    if (phone != null && phone.trim().isNotEmpty) {
      final phoneRegex = RegExp(r'^\+?[\d\s\-\(\)]+$');
      if (!phoneRegex.hasMatch(phone.trim())) {
        return Left(ValidationFailure(message: 'Invalid phone number format'));
      }
    }

    // Create update request object
    final request = UpdateCustomerRequest(
      customerName: customerName?.trim(),
      email: email?.trim(),
      phone: phone?.trim(),
      address: address?.trim(),
      creditLimit: creditLimit,
      category: category,
      notes: notes?.trim(),
    );

    return await _repository.updateCustomer(customerId.trim(), request);
  }
} 