// Export all debt-related domain models and types
export 'debt.dart';
export 'payment.dart';

// Import necessary types
import 'package:equatable/equatable.dart';
import 'payment.dart';

/// Create Debt Request - matches backend validation schema
class CreateDebtRequest extends Equatable {
  final String customerId;
  final double amount;
  final String? description;
  final DateTime dueDate;

  const CreateDebtRequest({
    required this.customerId,
    required this.amount,
    this.description,
    required this.dueDate,
  });

  @override
  List<Object?> get props => [customerId, amount, description, dueDate];

  Map<String, dynamic> toJson() {
    return {
      'customerId': customerId,
      'debtAmount': amount,
      'description': description,
      'dueDate': dueDate.toIso8601String(),
    };
  }
}

/// Add Payment Request - matches backend payment validation
class AddPaymentRequest extends Equatable {
  final String debtId;
  final double amount;
  final PaymentMethod paymentMethod;
  final String? notes;
  final DateTime? paymentDate;

  const AddPaymentRequest({
    required this.debtId,
    required this.amount,
    required this.paymentMethod,
    this.notes,
    this.paymentDate,
  });

  @override
  List<Object?> get props => [debtId, amount, paymentMethod, notes, paymentDate];

  Map<String, dynamic> toJson() {
    return {
      'debtId': debtId,
      'amount': amount,
      'paymentMethod': paymentMethod.apiValue,
      'notes': notes,
      'paymentDate': paymentDate?.toIso8601String(),
    };
  }
}

/// Debt Filter Parameters
class DebtFilterParams extends Equatable {
  final String? status;
  final double? minAmount;
  final double? maxAmount;
  final String? dueDateFilter;
  final String? riskLevel;
  final String? customerId;

  const DebtFilterParams({
    this.status,
    this.minAmount,
    this.maxAmount,
    this.dueDateFilter,
    this.riskLevel,
    this.customerId,
  });

  @override
  List<Object?> get props => [
    status,
    minAmount,
    maxAmount,
    dueDateFilter,
    riskLevel,
    customerId,
  ];

  Map<String, dynamic> toQueryMap() {
    final Map<String, dynamic> params = {};
    
    if (status != null) params['status'] = status;
    if (minAmount != null) params['minAmount'] = minAmount;
    if (maxAmount != null) params['maxAmount'] = maxAmount;
    if (dueDateFilter != null) params['dueDateFilter'] = dueDateFilter;
    if (riskLevel != null) params['riskLevel'] = riskLevel;
    if (customerId != null) params['customerId'] = customerId;
    
    return params;
  }
} 