// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'debt_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DebtModel _$DebtModelFromJson(Map<String, dynamic> json) => DebtModel(
      debtId: json['debtId'] as String,
      customerId: json['customerId'] as String,
      shopId: json['shopId'] as String,
      customerName: json['CustomerName'] as String,
      customerType: json['CustomerType'] as String,
      debtAmount: (json['DebtAmount'] as num).toDouble(),
      outstandingDebt: (json['OutstandingDebt'] as num).toDouble(),
      debtCreationDate: DateTime.parse(json['DebtCreationDate'] as String),
      dueDate: DateTime.parse(json['DueDate'] as String),
      repaymentTime: (json['RepaymentTime'] as num?)?.toInt(),
      debtPaidRatio: (json['DebtPaidRatio'] as num).toDouble(),
      paymentDelay: (json['PaymentDelay'] as num).toInt(),
      isOnTime: json['IsOnTime'] as bool,
      paidAmount: (json['PaidAmount'] as num).toDouble(),
      paidDate: json['PaidDate'] == null
          ? null
          : DateTime.parse(json['PaidDate'] as String),
      riskLevel: json['RiskLevel'] as String,
      status: json['status'] as String,
      description: json['description'] as String?,
      isDeleted: json['isDeleted'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$DebtModelToJson(DebtModel instance) => <String, dynamic>{
      'debtId': instance.debtId,
      'customerId': instance.customerId,
      'shopId': instance.shopId,
      'CustomerName': instance.customerName,
      'CustomerType': instance.customerType,
      'DebtAmount': instance.debtAmount,
      'OutstandingDebt': instance.outstandingDebt,
      'DebtCreationDate': instance.debtCreationDate.toIso8601String(),
      'DueDate': instance.dueDate.toIso8601String(),
      'RepaymentTime': instance.repaymentTime,
      'DebtPaidRatio': instance.debtPaidRatio,
      'PaymentDelay': instance.paymentDelay,
      'IsOnTime': instance.isOnTime,
      'PaidAmount': instance.paidAmount,
      'PaidDate': instance.paidDate?.toIso8601String(),
      'RiskLevel': instance.riskLevel,
      'status': instance.status,
      'description': instance.description,
      'isDeleted': instance.isDeleted,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

CreateDebtRequest _$CreateDebtRequestFromJson(Map<String, dynamic> json) =>
    CreateDebtRequest(
      customerId: json['customerId'] as String,
      debtAmount: (json['debtAmount'] as num).toDouble(),
      dueDate: DateTime.parse(json['dueDate'] as String),
      description: json['description'] as String?,
      paidAmount: (json['paidAmount'] as num?)?.toDouble(),
      paidDate: json['paidDate'] == null
          ? null
          : DateTime.parse(json['paidDate'] as String),
      paymentMethod: json['paymentMethod'] as String?,
    );

Map<String, dynamic> _$CreateDebtRequestToJson(CreateDebtRequest instance) =>
    <String, dynamic>{
      'customerId': instance.customerId,
      'debtAmount': instance.debtAmount,
      'dueDate': instance.dueDate.toIso8601String(),
      'description': instance.description,
      'paidAmount': instance.paidAmount,
      'paidDate': instance.paidDate?.toIso8601String(),
      'paymentMethod': instance.paymentMethod,
    };

UpdateDebtRequest _$UpdateDebtRequestFromJson(Map<String, dynamic> json) =>
    UpdateDebtRequest(
      debtAmount: (json['debtAmount'] as num?)?.toDouble(),
      dueDate: json['dueDate'] == null
          ? null
          : DateTime.parse(json['dueDate'] as String),
      description: json['description'] as String?,
      status: json['status'] as String?,
    );

Map<String, dynamic> _$UpdateDebtRequestToJson(UpdateDebtRequest instance) =>
    <String, dynamic>{
      'debtAmount': instance.debtAmount,
      'dueDate': instance.dueDate?.toIso8601String(),
      'description': instance.description,
      'status': instance.status,
    };

AddPaymentRequest _$AddPaymentRequestFromJson(Map<String, dynamic> json) =>
    AddPaymentRequest(
      amount: (json['amount'] as num).toDouble(),
      paymentMethod: json['paymentMethod'] as String,
      notes: json['notes'] as String?,
      paymentDate: json['paymentDate'] == null
          ? null
          : DateTime.parse(json['paymentDate'] as String),
    );

Map<String, dynamic> _$AddPaymentRequestToJson(AddPaymentRequest instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'paymentMethod': instance.paymentMethod,
      'notes': instance.notes,
      'paymentDate': instance.paymentDate?.toIso8601String(),
    };
