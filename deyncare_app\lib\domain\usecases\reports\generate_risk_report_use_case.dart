import 'package:deyncare_app/data/repositories/risk_report_repository.dart';
import 'package:deyncare_app/core/services/pdf_service.dart';
import 'package:flutter/foundation.dart';

/// Use case for generating risk reports
/// Coordinates between repository and PDF service to generate risk assessment reports
class GenerateRiskReportUseCase {
  final RiskReportRepository _riskReportRepository;

  GenerateRiskReportUseCase(this._riskReportRepository);

  /// Generate a risk report PDF
  /// Returns the file path of the generated PDF
  Future<String> execute({
    String? startDate,
    String? endDate,
    String period = 'all',
    required String shopName,
    String? shopLogo,
  }) async {
    try {
      if (kDebugMode) {
        print('📊 [RISK USE CASE] Starting risk report generation...');
        print('📊 [RISK USE CASE] Period: $period');
        print('📊 [RISK USE CASE] Date range: $startDate to $endDate');
        print('📊 [RISK USE CASE] Shop: $shopName');
      }

      // Step 1: Fetch risk report data from repository
      if (kDebugMode) {
        print('📊 [RISK USE CASE] Step 1: Fetching risk data...');
      }

      final reportData = await _riskReportRepository.getRiskReportData(
        startDate: startDate,
        endDate: endDate,
        period: period,
      );

      if (kDebugMode) {
        print('📊 [RISK USE CASE] Data fetched successfully');
        print('📊 [RISK USE CASE] Risks count: ${(reportData['risks'] as List).length}');
        print('📊 [RISK USE CASE] Summary: ${reportData['summary']}');
      }

      // Step 2: Generate PDF using PDF service
      if (kDebugMode) {
        print('📊 [RISK USE CASE] Step 2: Generating PDF...');
      }

      final pdfPath = await PDFService.generateRiskReport(
        risks: reportData['risks'],
        summary: reportData['summary'],
        reportPeriod: _formatReportPeriod(period, startDate, endDate),
        shopName: shopName,
        shopLogo: shopLogo,
      );

      if (kDebugMode) {
        print('📊 [RISK USE CASE] PDF generated successfully: $pdfPath');
      }

      return pdfPath;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [RISK USE CASE] Error generating risk report: $e');
      }
      rethrow;
    }
  }

  /// Get risk report statistics
  /// Returns statistical data for risk reporting
  Future<Map<String, dynamic>> getStatistics({
    String? startDate,
    String? endDate,
    String period = 'all',
  }) async {
    try {
      if (kDebugMode) {
        print('📊 [RISK USE CASE] Fetching risk statistics...');
      }

      final stats = await _riskReportRepository.getRiskReportStats(
        startDate: startDate,
        endDate: endDate,
        period: period,
      );

      if (kDebugMode) {
        print('📊 [RISK USE CASE] Statistics fetched: $stats');
      }

      return stats;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [RISK USE CASE] Error fetching risk statistics: $e');
      }
      rethrow;
    }
  }

  /// Get risk distribution data
  /// Returns risk level distribution for analytics
  Future<Map<String, dynamic>> getRiskDistribution({
    String? startDate,
    String? endDate,
    String period = 'all',
  }) async {
    try {
      if (kDebugMode) {
        print('📊 [RISK USE CASE] Fetching risk distribution...');
      }

      final distribution = await _riskReportRepository.getRiskDistribution(
        startDate: startDate,
        endDate: endDate,
        period: period,
      );

      if (kDebugMode) {
        print('📊 [RISK USE CASE] Distribution fetched: $distribution');
      }

      return distribution;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [RISK USE CASE] Error fetching risk distribution: $e');
      }
      rethrow;
    }
  }

  /// Format the report period for display
  String _formatReportPeriod(String period, String? startDate, String? endDate) {
    switch (period.toLowerCase()) {
      case 'daily':
        return 'Daily Risk Assessment';
      case 'weekly':
        return 'Weekly Risk Assessment';
      case 'monthly':
        return 'Monthly Risk Assessment';
      case 'yearly':
        return 'Yearly Risk Assessment';
      case 'custom':
        if (startDate != null && endDate != null) {
          return 'Risk Assessment: $startDate to $endDate';
        }
        return 'Custom Risk Assessment';
      case 'all':
      default:
        return 'Complete Risk Assessment';
    }
  }

  /// Validate date range
  bool _isValidDateRange(String? startDate, String? endDate) {
    if (startDate == null || endDate == null) return true;
    
    try {
      final start = DateTime.parse(startDate);
      final end = DateTime.parse(endDate);
      return start.isBefore(end) || start.isAtSameMomentAs(end);
    } catch (e) {
      return false;
    }
  }

  /// Get formatted date range string
  String _getDateRangeString(String? startDate, String? endDate) {
    if (startDate == null || endDate == null) {
      return 'All Time';
    }
    
    try {
      final start = DateTime.parse(startDate);
      final end = DateTime.parse(endDate);
      
      final startFormatted = '${start.day}/${start.month}/${start.year}';
      final endFormatted = '${end.day}/${end.month}/${end.year}';
      
      return '$startFormatted - $endFormatted';
    } catch (e) {
      return 'Invalid Date Range';
    }
  }

  /// Calculate risk summary metrics
  Map<String, dynamic> _calculateRiskSummary(
    List<dynamic> risks,
    String period,
    String? startDate,
    String? endDate,
  ) {
    final riskList = risks.cast<Map<String, dynamic>>();
    
    final highRiskCount = riskList.where((risk) => 
      ((risk['riskScore'] as num?)?.toDouble() ?? 0.0) >= 70
    ).length;
    
    final mediumRiskCount = riskList.where((risk) {
      final score = (risk['riskScore'] as num?)?.toDouble() ?? 0.0;
      return score >= 40 && score < 70;
    }).length;
    
    final lowRiskCount = riskList.where((risk) => 
      ((risk['riskScore'] as num?)?.toDouble() ?? 0.0) < 40
    ).length;
    
    return {
      'period': period,
      'dateRange': _getDateRangeString(startDate, endDate),
      'totalCustomers': riskList.length,
      'highRiskCount': highRiskCount,
      'mediumRiskCount': mediumRiskCount,
      'lowRiskCount': lowRiskCount,
      'highRiskPercentage': riskList.isNotEmpty ? (highRiskCount / riskList.length) * 100 : 0.0,
      'mediumRiskPercentage': riskList.isNotEmpty ? (mediumRiskCount / riskList.length) * 100 : 0.0,
      'lowRiskPercentage': riskList.isNotEmpty ? (lowRiskCount / riskList.length) * 100 : 0.0,
      'averageRiskScore': riskList.isNotEmpty 
          ? riskList.fold<double>(0.0, (sum, risk) => 
              sum + ((risk['riskScore'] as num?)?.toDouble() ?? 0.0)
            ) / riskList.length
          : 0.0,
      'totalDebtAtRisk': riskList.where((risk) => 
          ((risk['riskScore'] as num?)?.toDouble() ?? 0.0) >= 40
        ).fold<double>(0.0, (sum, risk) => 
          sum + ((risk['totalDebt'] as num?)?.toDouble() ?? 0.0)
        ),
    };
  }

  /// Get risk level color for UI display
  String getRiskLevelColor(double riskScore) {
    if (riskScore >= 70) return '#FF5252'; // Red for high risk
    if (riskScore >= 40) return '#FF9800'; // Orange for medium risk
    return '#4CAF50'; // Green for low risk
  }

  /// Get risk level description
  String getRiskLevelDescription(double riskScore) {
    if (riskScore >= 70) return 'High Risk - Immediate attention required';
    if (riskScore >= 40) return 'Medium Risk - Monitor closely';
    return 'Low Risk - Good payment behavior';
  }
}
