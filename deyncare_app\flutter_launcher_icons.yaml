flutter_icons:
  # Android icons
  android: "launcher_icon"
  
  # iOS icons
  ios: true
  
  # Source image path (you'll need to create this from your SVG/PNG assets)
  image_path: "assets/icons/deyncare_icon.png"
  
  # Minimum Android SDK version
  min_sdk_android: 21
  
  # Adaptive icon settings for Android 8.0+ (API 26+)
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/icons/deyncare_icon.png"
  
  # Remove old launcher icons when generating new ones
  remove_alpha_ios: true
  
  # Generate for web (if you plan to use Flutter web)
  web:
    generate: true
    image_path: "assets/icons/deyncare_icon.png"
    background_color: "#FFFFFF"
    theme_color: "#141DEE"
  
  # Generate for Windows (if you plan to use Flutter desktop)
  windows:
    generate: true
    image_path: "assets/icons/deyncare_icon.png"
    icon_size: 48
  
  # Generate for macOS (if you plan to use Flutter desktop)
  macos:
    generate: true
    image_path: "assets/icons/deyncare_icon.png" 