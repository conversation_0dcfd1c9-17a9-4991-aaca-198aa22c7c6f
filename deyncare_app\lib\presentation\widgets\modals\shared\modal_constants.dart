import 'package:flutter/material.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';

/// Enhanced modal constants with modern design patterns
class ModalConstants {
  // Enhanced spacing system
  static const EdgeInsets defaultPadding = EdgeInsets.all(20.0);
  static const EdgeInsets largePadding = EdgeInsets.all(24.0);
  static const EdgeInsets extraLargePadding = EdgeInsets.all(32.0);
  static const EdgeInsets cardPadding = EdgeInsets.all(20.0);
  static const EdgeInsets sectionPadding = EdgeInsets.symmetric(vertical: 16.0, horizontal: 20.0);
  
  // Modern border radius system
  static const double smallRadius = 8.0;
  static const double defaultRadius = 16.0;
  static const double largeRadius = 24.0;
  static const double extraLargeRadius = 32.0;
  
  // Enhanced spacing widgets
  static const SizedBox tinySpacing = SizedBox(height: 8);
  static const SizedBox smallSpacing = SizedBox(height: 12);
  static const SizedBox defaultSpacing = SizedBox(height: 16);
  static const SizedBox largeSpacing = SizedBox(height: 24);
  static const SizedBox extraLargeSpacing = SizedBox(height: 32);
  static const SizedBox massiveSpacing = SizedBox(height: 48);
  
  // Horizontal spacing
  static const SizedBox tinyHorizontalSpacing = SizedBox(width: 8);
  static const SizedBox smallHorizontalSpacing = SizedBox(width: 12);
  static const SizedBox defaultHorizontalSpacing = SizedBox(width: 16);
  static const SizedBox largeHorizontalSpacing = SizedBox(width: 24);

  /// Modern modal title style with enhanced typography
  static TextStyle modalTitleStyle(BuildContext context, {Color? color}) {
    return Theme.of(context).textTheme.headlineMedium?.copyWith(
      color: color ?? Theme.of(context).colorScheme.onSurface,
      fontWeight: FontWeight.w700,
      letterSpacing: -0.5,
      height: 1.2,
    ) ?? const TextStyle();
  }

  /// Enhanced section title style
  static TextStyle sectionTitleStyle(BuildContext context, {Color? color}) {
    return Theme.of(context).textTheme.titleLarge?.copyWith(
      color: color ?? Theme.of(context).colorScheme.onSurface,
      fontWeight: FontWeight.w600,
      letterSpacing: -0.3,
      height: 1.3,
    ) ?? const TextStyle();
  }

  /// Modern subtitle style
  static TextStyle subtitleStyle(BuildContext context, {Color? color}) {
    return Theme.of(context).textTheme.titleMedium?.copyWith(
      color: color ?? Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
      fontWeight: FontWeight.w500,
      letterSpacing: -0.2,
      height: 1.4,
    ) ?? const TextStyle();
  }

  /// Enhanced body text style
  static TextStyle bodyTextStyle(BuildContext context, {Color? color}) {
    return Theme.of(context).textTheme.bodyLarge?.copyWith(
      color: color ?? Theme.of(context).colorScheme.onSurface,
      fontWeight: FontWeight.w400,
      height: 1.5,
    ) ?? const TextStyle();
  }

  /// Modern caption style
  static TextStyle captionStyle(BuildContext context, {Color? color}) {
    return Theme.of(context).textTheme.bodySmall?.copyWith(
      color: color ?? Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
      fontWeight: FontWeight.w400,
      height: 1.4,
    ) ?? const TextStyle();
  }

  /// Enhanced close icon button with modern styling
  static Widget closeIconButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppThemes.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppThemes.dividerColor,
          width: 1,
        ),
      ),
      child: IconButton(
        icon: Icon(
          Icons.close_rounded,
          color: AppThemes.textSecondaryColor,
          size: 20,
        ),
        onPressed: () => Navigator.of(context).pop(),
        padding: const EdgeInsets.all(8),
        constraints: const BoxConstraints(
          minWidth: 40,
          minHeight: 40,
        ),
      ),
    );
  }

  /// Modern primary action button
  static Widget primaryActionButton({
    required String text,
    required VoidCallback onPressed,
    IconData? icon,
    bool isLoading = false,
    required BuildContext context,
  }) {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        borderRadius: BorderRadius.circular(defaultRadius),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isLoading ? null : onPressed,
          borderRadius: BorderRadius.circular(defaultRadius),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isLoading) ...[
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.onPrimary),
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
                if (icon != null && !isLoading) ...[
                  Icon(icon, color: Colors.white, size: 20),
                  const SizedBox(width: 12),
                ],
                Text(
                  text,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    letterSpacing: -0.2,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Modern secondary action button
  static Widget secondaryActionButton({
    required String text,
    required VoidCallback onPressed,
    IconData? icon,
    bool isLoading = false,
    required BuildContext context,
  }) {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(defaultRadius),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary,
          width: 1.5,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isLoading ? null : onPressed,
          borderRadius: BorderRadius.circular(defaultRadius),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isLoading) ...[
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
                if (icon != null && !isLoading) ...[
                  Icon(icon, color: Theme.of(context).colorScheme.primary, size: 20),
                  const SizedBox(width: 12),
                ],
                Text(
                  text,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    letterSpacing: -0.2,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Modern card decoration
  static BoxDecoration modernCardDecoration({
    Color? backgroundColor,
    Color? borderColor,
    double? borderRadius,
    bool hasShadow = true,
  }) {
    return BoxDecoration(
      color: backgroundColor ?? AppThemes.cardColor,
      borderRadius: BorderRadius.circular(borderRadius ?? defaultRadius),
      border: Border.all(
        color: borderColor ?? AppThemes.dividerColor,
        width: 1,
      ),
      boxShadow: hasShadow ? [
        BoxShadow(
          color: AppThemes.textSecondaryColor.withValues(alpha: 0.08),
          blurRadius: 12,
          offset: const Offset(0, 4),
        ),
      ] : null,
    );
  }

  /// Enhanced status badge
  static Widget statusBadge({
    required String text,
    required Color color,
    IconData? icon,
    bool isLarge = false,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isLarge ? 16 : 12,
        vertical: isLarge ? 10 : 8,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(isLarge ? 12 : 20),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: isLarge ? 18 : 14,
              color: color,
            ),
            SizedBox(width: isLarge ? 8 : 6),
          ],
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: isLarge ? 14 : 12,
              fontWeight: FontWeight.w600,
              letterSpacing: -0.1,
            ),
          ),
        ],
      ),
    );
  }

  /// Modern divider
  static Widget modernDivider({double? height}) {
    return Container(
      height: height ?? 1,
      decoration: BoxDecoration(
        color: AppThemes.dividerColor,
        borderRadius: BorderRadius.circular(0.5),
      ),
    );
  }

  /// Enhanced input decoration
  static InputDecoration modernInputDecoration({
    required String labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    String? helperText,
    String? errorText,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      helperText: helperText,
      errorText: errorText,
      filled: true,
      fillColor: AppThemes.cardColor,
      contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(defaultRadius),
        borderSide: BorderSide(
          color: AppThemes.dividerColor,
          width: 1.5,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(defaultRadius),
        borderSide: BorderSide(
          color: AppThemes.dividerColor,
          width: 1.5,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(defaultRadius),
        borderSide: BorderSide(
          color: AppThemes.primaryColor,
          width: 2,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(defaultRadius),
        borderSide: BorderSide(
          color: AppThemes.errorColor,
          width: 1.5,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(defaultRadius),
        borderSide: BorderSide(
          color: AppThemes.errorColor,
          width: 2,
        ),
      ),
    );
  }

  /// Modern success snackbar
  static void showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.check_circle_rounded,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  letterSpacing: -0.1,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: AppThemes.successColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(defaultRadius),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  /// Modern error snackbar
  static void showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.error_rounded,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  letterSpacing: -0.1,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: AppThemes.errorColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(defaultRadius),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  /// Modern info card
  static Widget infoCard({
    required String title,
    required String subtitle,
    IconData? icon,
    Color? iconColor,
    VoidCallback? onTap,
  }) {
    return Container(
      decoration: modernCardDecoration(),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(defaultRadius),
          child: Padding(
            padding: cardPadding,
            child: Row(
              children: [
                if (icon != null) ...[
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: (iconColor ?? AppThemes.primaryColor).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      icon,
                      color: iconColor ?? AppThemes.primaryColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          letterSpacing: -0.2,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppThemes.textSecondaryColor,
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
                if (onTap != null) ...[
                  const SizedBox(width: 16),
                  Icon(
                    Icons.arrow_forward_ios_rounded,
                    size: 16,
                    color: AppThemes.textSecondaryColor,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Enhanced info icon with background
  static Widget infoIcon({
    IconData icon = Icons.info_outline,
    Color? iconColor,
    double size = 20,
    required BuildContext context,
  }) {
    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: (iconColor ?? Theme.of(context).colorScheme.primary).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Icon(
        icon,
        size: size,
        color: iconColor ?? Theme.of(context).colorScheme.primary,
      ),
    );
  }

  /// Legacy button style methods for backward compatibility
  static ButtonStyle primaryButtonStyle() {
    return ElevatedButton.styleFrom(
      backgroundColor: AppThemes.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      padding: const EdgeInsets.symmetric(vertical: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(defaultRadius),
      ),
      textStyle: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.2,
      ),
    );
  }

  /// Legacy outlined button style for backward compatibility
  static ButtonStyle outlinedButtonStyle() {
    return OutlinedButton.styleFrom(
      foregroundColor: AppThemes.textSecondaryColor,
      padding: const EdgeInsets.symmetric(vertical: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(defaultRadius),
      ),
      side: BorderSide(
        color: AppThemes.dividerColor,
        width: 1.5,
      ),
      textStyle: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.2,
      ),
    );
  }

  /// Standard input decoration for forms
  static InputDecoration inputDecoration({
    required String hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    return modernInputDecoration(
      labelText: '', // No label text for standard input
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
    );
  }
} 