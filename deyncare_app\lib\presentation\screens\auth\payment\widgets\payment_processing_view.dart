import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';

/// Enhanced payment processing view with realistic progress indicators
class PaymentProcessingView extends StatefulWidget {
  final String paymentMethod;
  final String planName;
  final double amount;
  final VoidCallback? onCancel;

  const PaymentProcessingView({
    super.key,
    required this.paymentMethod,
    required this.planName,
    required this.amount,
    this.onCancel,
  });

  @override
  State<PaymentProcessingView> createState() => _PaymentProcessingViewState();
}

class _PaymentProcessingViewState extends State<PaymentProcessingView>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _progressController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _progressAnimation;

  int _currentStep = 0;
  final List<String> _steps = [
    'Initiating payment...',
    'Connecting to payment gateway...',
    'Processing with EVC Plus...',
    'Verifying transaction...',
    'Finalizing payment...',
  ];

  @override
  void initState() {
    super.initState();
    
    // Pulse animation for the payment icon
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.9, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // Progress animation
    _progressController = AnimationController(
      duration: const Duration(seconds: 60), // 60 seconds for full progress
      vsync: this,
    );
    _progressAnimation = CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    );

    // Start animations
    _pulseController.repeat(reverse: true);
    _progressController.forward();

    // Update steps every 10-15 seconds
    _updateStepsOverTime();
  }

  void _updateStepsOverTime() {
    final stepDurations = [
      2000,  // Step 0: 2 seconds
      8000,  // Step 1: 8 seconds
      25000, // Step 2: 25 seconds (main EVC processing)
      15000, // Step 3: 15 seconds
      10000, // Step 4: 10 seconds
    ];

    int totalDelay = 0;
    for (int i = 0; i < _steps.length; i++) {
      Future.delayed(Duration(milliseconds: totalDelay), () {
        if (mounted) {
          setState(() {
            _currentStep = i;
          });
        }
      });
      totalDelay += stepDurations[i];
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppThemes.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height - 100,
            ),
            child: Column(
              children: [
                // Header
                Text(
                  'Processing Payment',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: AppThemes.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Please wait while we process your ${widget.paymentMethod} payment',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppThemes.textSecondaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 40),

                // Payment animation
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: AppThemes.primaryColor.withOpacity(0.1),
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: AppThemes.primaryColor,
                            width: 3,
                          ),
                        ),
                        child: Icon(
                          Icons.payment,
                          size: 60,
                          color: AppThemes.primaryColor,
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 40),

                // Progress bar with percentage
                Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Processing',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppThemes.textPrimaryColor,
                          ),
                        ),
                        AnimatedBuilder(
                          animation: _progressAnimation,
                          builder: (context, child) {
                            return Text(
                              '${(_progressAnimation.value * 100).toInt()}%',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: AppThemes.primaryColor,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    AnimatedBuilder(
                      animation: _progressAnimation,
                      builder: (context, child) {
                        return LinearProgressIndicator(
                          value: _progressAnimation.value,
                          backgroundColor: AppThemes.dividerColor,
                          valueColor: AlwaysStoppedAnimation<Color>(AppThemes.primaryColor),
                          minHeight: 8,
                        );
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 30),

                // Current step
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppThemes.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppThemes.primaryColor.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(AppThemes.primaryColor),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _currentStep < _steps.length ? _steps[_currentStep] : 'Completing payment...',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: AppThemes.primaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),

                // Payment details summary
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppThemes.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppThemes.primaryColor.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Payment Details',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppThemes.textPrimaryColor,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Plan:',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppThemes.textSecondaryColor,
                            ),
                          ),
                          Text(
                            widget.planName,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: AppThemes.textPrimaryColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Method:',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppThemes.textSecondaryColor,
                            ),
                          ),
                          Text(
                            widget.paymentMethod,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: AppThemes.textPrimaryColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Amount:',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppThemes.textSecondaryColor,
                            ),
                          ),
                          Text(
                            '\$${widget.amount.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppThemes.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),

                // Warning message for EVC Plus
                if (widget.paymentMethod == 'EVC Plus') ...[
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppThemes.warningColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppThemes.warningColor.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info_outline, color: AppThemes.warningColor, size: 20),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'EVC Plus payments can take 30-60 seconds. Please do not close the app or go back.',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppThemes.warningColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                ],

                // Cancel button (if provided)
                if (widget.onCancel != null)
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton(
                      onPressed: widget.onCancel,
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: AppThemes.textSecondaryColor),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Cancel Payment',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppThemes.textSecondaryColor,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
} 