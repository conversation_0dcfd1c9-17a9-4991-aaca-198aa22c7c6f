import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/domain/models/debt.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_bloc.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_event.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_state.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_constants.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/form_mixins.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/loading_states.dart';

/// Edit Debt By ID Form Widget - follows customer best practice
class EditDebtByIdForm extends StatefulWidget {
  final String debtId;

  const EditDebtByIdForm({super.key, required this.debtId});

  @override
  State<EditDebtByIdForm> createState() => _EditDebtByIdFormState();
}

class _EditDebtByIdFormState extends State<EditDebtByIdForm> 
    with ModalFormMixin<EditDebtByIdForm>, BlocListenerMixin<EditDebtByIdForm> {
  
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  DateTime? _selectedDueDate;
  bool _isLoadingData = true;
  Debt? _currentDebt;

  @override
  void initState() {
    super.initState();
    // Load debt details when widget initializes
    context.read<DebtBloc>().add(LoadDebtDetails(widget.debtId));
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _populateForm(Debt debt) {
    _currentDebt = debt;
    _selectedDueDate = debt.dueDate;
    _amountController.text = debt.amount.toString();
    _descriptionController.text = debt.description ?? '';
    setState(() => _isLoadingData = false);
  }

  @override
  Widget build(BuildContext context) {
    return buildBlocListener<DebtBloc, DebtState>(
      listener: (context, state) {
        if (state is DebtDetailsLoaded) {
          _populateForm(state.response);
        } else if (state is DebtUpdated) {
          handleSuccess('Debt updated successfully!');
        } else if (state is DebtError) {
          handleError(state.message);
        } else if (state is DebtDetailsError) {
          handleError('Failed to load debt details: ${state.message}');
        } else if (state is DebtUpdating) {
          setLoading(true);
        }
      },
      child: Padding(
        padding: ModalConstants.defaultPadding,
        child: _isLoadingData
            ? _buildLoadingState()
            : Form(
                key: formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildHeader(),
                      ModalConstants.largeSpacing,
                      _buildDebtInfoCard(),
                      ModalConstants.defaultSpacing,
                      _buildAmountField(),
                      ModalConstants.defaultSpacing,
                      _buildDueDateField(),
                      ModalConstants.defaultSpacing,
                      _buildDescriptionField(),
                      ModalConstants.largeSpacing,
                      _buildActionButtons(),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: AppThemes.primaryColor),
          ModalConstants.defaultSpacing,
          Text(
            'Loading debt details...',
            style: TextStyle(color: AppThemes.textSecondaryColor),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return CommonCard(
      child: Column(
        children: [
          Icon(
            Icons.edit_rounded,
            size: 48,
            color: AppThemes.warningColor,
          ),
          ModalConstants.defaultSpacing,
          Text(
            'Edit Debt',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppThemes.warningColor,
            ),
          ),
          ModalConstants.defaultSpacing,
          Text(
            'Update debt details below. All fields with * are required.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppThemes.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDebtInfoCard() {
    if (_currentDebt == null) return const SizedBox.shrink();
    
    return CommonCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Debt Information',
              style: ModalConstants.sectionTitleStyle(context),
            ),
            ModalConstants.defaultSpacing,
            _buildDetailRow('Debt ID:', _currentDebt!.debtId),
            _buildDetailRow('Customer:', _currentDebt!.customerName),
            _buildDetailRow('Current Amount:', '\$${_currentDebt!.amount.toStringAsFixed(2)}'),
            _buildDetailRow('Paid Amount:', '\$${_currentDebt!.totalPaid.toStringAsFixed(2)}'),
            _buildDetailRow('Remaining:', '\$${_currentDebt!.remainingAmount.toStringAsFixed(2)}'),
            _buildDetailRow('Status:', _currentDebt!.status.displayName),
            if (_currentDebt!.totalPaid > 0) ...[
              ModalConstants.defaultSpacing,
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppThemes.warningColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppThemes.warningColor.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: AppThemes.warningColor, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'This debt has payments. Changing the amount may affect payment calculations.',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppThemes.warningColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
                color: AppThemes.textSecondaryColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Debt Amount (\$) *',
          style: ModalConstants.sectionTitleStyle(context),
        ),
        ModalConstants.defaultSpacing,
        buildFormField(
          controller: _amountController,
          labelText: 'Amount',
          hintText: 'Enter debt amount',
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          prefixIcon: const Icon(Icons.attach_money),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter a debt amount';
            }
            final amount = double.tryParse(value);
            if (amount == null || amount <= 0) {
              return 'Please enter a valid amount greater than 0';
            }
            if (amount > 999999.99) {
              return 'Amount cannot exceed \$999,999.99';
            }
            // Validate that new amount is not less than already paid
            if (_currentDebt != null && amount < _currentDebt!.totalPaid) {
              return 'Amount cannot be less than already paid (\$${_currentDebt!.totalPaid.toStringAsFixed(2)})';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildDueDateField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Due Date *',
          style: ModalConstants.sectionTitleStyle(context),
        ),
        ModalConstants.defaultSpacing,
        buildDatePickerField(
          selectedDate: _selectedDueDate,
          labelText: 'due date',
          onDateSelected: (date) {
            setState(() => _selectedDueDate = date);
          },
          firstDate: DateTime.now().subtract(const Duration(days: 365)),
          lastDate: DateTime.now().add(const Duration(days: 365)),
        ),
      ],
    );
  }

  Widget _buildDescriptionField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Description (Optional)',
          style: ModalConstants.sectionTitleStyle(context),
        ),
        ModalConstants.defaultSpacing,
        buildFormField(
          controller: _descriptionController,
          labelText: 'Description',
          hintText: 'Enter description (optional)',
          maxLines: 3,
          validator: (value) {
            if (value != null && value.length > 500) {
              return 'Description must be less than 500 characters';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return buildActionButtons(
      primaryButtonText: 'Update Debt',
      onPrimaryPressed: _handleSubmit,
    );
  }

  void _handleSubmit() {
    if (!validateForm()) return;
    if (_selectedDueDate == null) {
      handleError('Please select a due date');
      return;
    }

    final amount = double.tryParse(_amountController.text);
    if (amount == null || amount <= 0) {
      handleError('Please enter a valid debt amount');
      return;
    }

    // Additional business validation
    if (_currentDebt != null && amount < _currentDebt!.totalPaid) {
      handleError('Amount cannot be less than already paid (\$${_currentDebt!.totalPaid.toStringAsFixed(2)})');
      return;
    }

    final description = _descriptionController.text.trim().isEmpty 
        ? null 
        : _descriptionController.text.trim();

    setLoading(true);

    context.read<DebtBloc>().add(UpdateDebt(
      debtId: widget.debtId,
      amount: amount,
      dueDate: _selectedDueDate!,
      description: description,
    ));
  }
} 