import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/domain/models/debt.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_bloc.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_event.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_state.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_constants.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/form_mixins.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/loading_states.dart';

/// Edit Debt Form Widget - Updated for new debt architecture
class EditDebtForm extends StatefulWidget {
  final Debt debt;

  const EditDebtForm({super.key, required this.debt});

  @override
  State<EditDebtForm> createState() => _EditDebtFormState();
}

class _EditDebtFormState extends State<EditDebtForm> 
    with ModalFormMixin<EditDebtForm>, BlocListenerMixin<EditDebtForm> {
  
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  DateTime? _selectedDueDate;

  @override
  void initState() {
    super.initState();
    _selectedDueDate = widget.debt.dueDate;
    _amountController.text = widget.debt.amount.toString();
    _descriptionController.text = widget.debt.description ?? '';
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return buildBlocListener<DebtBloc, DebtState>(
      listener: (context, state) {
        if (state is DebtUpdated) {
          setLoading(false);
          handleSuccess('Debt updated successfully!');
        } else if (state is DebtError) {
          setLoading(false);
          handleError(state.message);
        } else if (state is DebtUpdating) {
          setLoading(true);
        }
      },
      child: BlocBuilder<DebtBloc, DebtState>(
        builder: (context, state) {
          // Show skeleton loader during update
          if (state is DebtUpdating) {
            return ModalLoadingStates.formSkeleton(fieldCount: 4);
          }
          
          return Padding(
            padding: ModalConstants.defaultPadding,
            child: Form(
              key: formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildDebtInfoHeader(),
                  ModalConstants.defaultSpacing,
                  _buildAmountField(),
                  ModalConstants.defaultSpacing,
                  _buildDueDateField(),
                  ModalConstants.defaultSpacing,
                  _buildDescriptionField(),
                  ModalConstants.largeSpacing,
                  _buildSubmitButton(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDebtInfoHeader() {
    return CommonCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Editing Debt',
              style: ModalConstants.sectionTitleStyle(context),
            ),
            ModalConstants.defaultSpacing,
            _buildDetailRow('Debt ID:', widget.debt.debtId),
            _buildDetailRow('Customer:', widget.debt.customerName),
            _buildDetailRow('Current Amount:', '\$${widget.debt.amount.toStringAsFixed(2)}'),
            _buildDetailRow('Paid Amount:', '\$${widget.debt.totalPaid.toStringAsFixed(2)}'),
            _buildDetailRow('Status:', widget.debt.status.displayName),
            if (widget.debt.totalPaid > 0) ...[
              ModalConstants.defaultSpacing,
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppThemes.warningColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppThemes.warningColor.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: AppThemes.warningColor, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'This debt has payments. Changing the amount may affect payment calculations.',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppThemes.warningColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
                color: AppThemes.textSecondaryColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Debt Amount (\$)',
          style: ModalConstants.sectionTitleStyle(context),
        ),
        ModalConstants.defaultSpacing,
        buildFormField(
          controller: _amountController,
          labelText: 'Amount',
          hintText: 'Enter debt amount',
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          prefixIcon: const Icon(Icons.attach_money),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter a debt amount';
            }
            final amount = double.tryParse(value);
            if (amount == null || amount <= 0) {
              return 'Please enter a valid amount greater than 0';
            }
            if (amount > 999999.99) {
              return 'Amount cannot exceed \$999,999.99';
            }
            // Validate that new amount is not less than already paid
            if (amount < widget.debt.totalPaid) {
              return 'Amount cannot be less than already paid (\$${widget.debt.totalPaid.toStringAsFixed(2)})';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildDueDateField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Due Date',
          style: ModalConstants.sectionTitleStyle(context),
        ),
        ModalConstants.defaultSpacing,
        buildDatePickerField(
          selectedDate: _selectedDueDate,
          labelText: 'due date',
          onDateSelected: (date) {
            setState(() => _selectedDueDate = date);
          },
          firstDate: DateTime.now().subtract(const Duration(days: 365)),
          lastDate: DateTime.now().add(const Duration(days: 365)),
        ),
      ],
    );
  }

  Widget _buildDescriptionField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Description (Optional)',
          style: ModalConstants.sectionTitleStyle(context),
        ),
        ModalConstants.defaultSpacing,
        buildFormField(
          controller: _descriptionController,
          labelText: 'Description',
          hintText: 'Enter description (optional)',
          maxLines: 3,
          validator: (value) {
            if (value != null && value.length > 500) {
              return 'Description must be less than 500 characters';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return buildActionButtons(
      primaryButtonText: 'Update Debt',
      onPrimaryPressed: _handleSubmit,
    );
  }

  void _handleSubmit() {
    if (!validateForm()) return;
    if (_selectedDueDate == null) {
      handleError('Please select a due date');
      return;
    }

    final amount = double.tryParse(_amountController.text);
    if (amount == null || amount <= 0) {
      handleError('Please enter a valid debt amount');
      return;
    }

    // Additional business validation
    if (amount < widget.debt.totalPaid) {
      handleError('Amount cannot be less than already paid (\$${widget.debt.totalPaid.toStringAsFixed(2)})');
      return;
    }

    final description = _descriptionController.text.trim().isEmpty 
        ? null 
        : _descriptionController.text.trim();

    setLoading(true);

    context.read<DebtBloc>().add(UpdateDebt(
      debtId: widget.debt.debtId,
      amount: amount,
      dueDate: _selectedDueDate!,
      description: description,
    ));
  }
} 