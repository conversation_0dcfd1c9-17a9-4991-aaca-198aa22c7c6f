import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/utils/business_validation.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_bloc.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_event.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_state.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_constants.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/form_mixins.dart';
import 'package:deyncare_app/presentation/widgets/skeleton_loader.dart';

/// Enhanced Add Customer Form with Skeleton Loading
class AddCustomerForm extends StatefulWidget {
  final BuildContext parentContext;

  const AddCustomerForm({super.key, required this.parentContext});

  @override
  State<AddCustomerForm> createState() => _AddCustomerFormState();
}

class _AddCustomerFormState extends State<AddCustomerForm> 
    with ModalFormMixin<AddCustomerForm>, BlocListenerMixin<AddCustomerForm> {
  
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _creditLimitController = TextEditingController();
  final _categoryController = TextEditingController();
  final _notesController = TextEditingController();
  
  String _selectedCustomerType = 'New';
  bool _isSubmitting = false;

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _creditLimitController.dispose();
    _categoryController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: widget.parentContext.read<CustomerBloc>(),
      child: buildBlocListener<CustomerBloc, CustomerState>(
        listener: (context, state) {
          if (state is CustomerCreated) {
            setState(() => _isSubmitting = false);
            handleSuccess('Customer created successfully!');
            Navigator.of(context).pop();
          } else if (state is CustomerError) {
            setState(() => _isSubmitting = false);
            handleError(state.message);
          } else if (state is CustomerCreating) {
            setState(() => _isSubmitting = true);
          }
        },
        child: _isSubmitting ? _buildSubmittingState() : _buildForm(),
      ),
    );
  }

  /// Enhanced submitting state with skeleton loader
  Widget _buildSubmittingState() {
    return Padding(
      padding: ModalConstants.defaultPadding,
      child: Column(
        children: [
          // Header skeleton
          _buildHeaderSkeleton(),
          ModalConstants.largeSpacing,
          
          // Form fields skeleton
          ...List.generate(6, (index) => Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildFormFieldSkeleton(),
          )),
          
          ModalConstants.largeSpacing,
          
          // Submitting indicator
          Container(
            padding: const EdgeInsets.all(24),
            decoration: ModalConstants.modernCardDecoration(
                      backgroundColor: AppThemes.primaryColor.withValues(alpha: 0.05),
        borderColor: AppThemes.primaryColor.withValues(alpha: 0.2),
            ),
            child: Column(
              children: [
                SizedBox(
                  width: 40,
                  height: 40,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(AppThemes.primaryColor),
                  ),
                ),
                ModalConstants.defaultSpacing,
                Text(
                  'Creating Customer...',
                  style: ModalConstants.subtitleStyle(context, color: AppThemes.primaryColor),
                ),
                ModalConstants.smallSpacing,
                Text(
                  'Please wait while we save the customer information.',
                  style: ModalConstants.captionStyle(context),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Header skeleton
  Widget _buildHeaderSkeleton() {
    return Container(
      decoration: ModalConstants.modernCardDecoration(),
      child: Padding(
        padding: ModalConstants.cardPadding,
        child: Column(
          children: [
            SkeletonLoader.circular(size: 48),
            ModalConstants.defaultSpacing,
            SkeletonLoader.text(width: 150, height: 24),
            ModalConstants.defaultSpacing,
            SkeletonLoader.text(width: 200, height: 16),
          ],
        ),
      ),
    );
  }

  /// Form field skeleton
  Widget _buildFormFieldSkeleton() {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: AppThemes.backgroundColor,
        borderRadius: BorderRadius.circular(ModalConstants.defaultRadius),
        border: Border.all(
          color: AppThemes.dividerColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            SkeletonLoader.circular(size: 20),
            ModalConstants.defaultHorizontalSpacing,
            Expanded(
              child: SkeletonLoader.text(width: double.infinity, height: 16),
            ),
          ],
        ),
      ),
    );
  }

  /// Enhanced form with modern design
  Widget _buildForm() {
    return Padding(
      padding: ModalConstants.defaultPadding,
      child: Form(
        key: formKey,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildModernHeader(context),
              ModalConstants.largeSpacing,
              _buildCustomerNameField(),
              ModalConstants.defaultSpacing,
              _buildCustomerTypeField(),
              ModalConstants.defaultSpacing,
              _buildPhoneField(),
              ModalConstants.defaultSpacing,
              _buildEmailField(),
              ModalConstants.defaultSpacing,
              _buildAddressField(),
              ModalConstants.defaultSpacing,
              _buildCreditLimitField(),
              ModalConstants.defaultSpacing,
              _buildCategoryField(),
              ModalConstants.defaultSpacing,
              _buildNotesField(),
              ModalConstants.extraLargeSpacing,
              _buildModernActionButtons(),
              ModalConstants.largeSpacing,
            ],
          ),
        ),
      ),
    );
  }

  /// Modern header with enhanced design
  Widget _buildModernHeader(BuildContext context) {
    return Container(
      decoration: ModalConstants.modernCardDecoration(),
      child: Padding(
        padding: ModalConstants.cardPadding,
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppThemes.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(ModalConstants.defaultRadius),
              ),
              child: Icon(
                Icons.person_add_rounded,
                size: 32,
                color: AppThemes.primaryColor,
              ),
            ),
            ModalConstants.defaultSpacing,
            Text(
              'Create New Customer',
              style: ModalConstants.sectionTitleStyle(context),
            ),
            ModalConstants.smallSpacing,
            Text(
              'Fill in the customer details below. All fields with * are required.',
              style: ModalConstants.subtitleStyle(context),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Modern action buttons
  Widget _buildModernActionButtons() {
    return Container(
      decoration: ModalConstants.modernCardDecoration(),
      child: Padding(
        padding: ModalConstants.cardPadding,
        child: Column(
          children: [
            ModalConstants.primaryActionButton(
              text: 'Create Customer',
              onPressed: _submitForm,
              icon: Icons.person_add_rounded,
              isLoading: _isSubmitting,
              context: context,
            ),
            ModalConstants.defaultSpacing,
            ModalConstants.secondaryActionButton(
              text: 'Cancel',
              onPressed: () => Navigator.of(context).pop(),
              icon: Icons.close_rounded,
              context: context,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerNameField() {
    return buildFormField(
      controller: _nameController,
      labelText: 'Customer Name *',
      hintText: 'Enter full name',
      prefixIcon: const Icon(Icons.person),
      textCapitalization: TextCapitalization.words,
      validator: (value) {
        final validation = BusinessValidation.validateCustomerName(value ?? '');
        return validation.isValid ? null : validation.errorMessage;
      },
    );
  }

  Widget _buildCustomerTypeField() {
    return buildDropdownField<String>(
      value: _selectedCustomerType,
      items: const [
        DropdownMenuItem(value: 'New', child: Text('New Customer')),
        DropdownMenuItem(value: 'Returning', child: Text('Returning Customer')),
      ],
      labelText: 'Customer Type *',
      prefixIcon: const Icon(Icons.category),
      onChanged: (value) => setState(() => _selectedCustomerType = value!),
      validator: (value) {
        final validation = BusinessValidation.validateCustomerType(value ?? '');
        return validation.isValid ? null : validation.errorMessage;
      },
    );
  }

  Widget _buildPhoneField() {
    return buildFormField(
      controller: _phoneController,
      labelText: 'Phone Number *',
      hintText: 'Enter phone number',
      prefixIcon: const Icon(Icons.phone),
      keyboardType: TextInputType.phone,
      validator: (value) {
        final validation = BusinessValidation.validatePhone(value ?? '');
        return validation.isValid ? null : validation.errorMessage;
      },
    );
  }

  Widget _buildEmailField() {
    return buildFormField(
      controller: _emailController,
      labelText: 'Email (Optional)',
      hintText: 'Enter email address',
      prefixIcon: const Icon(Icons.email),
      keyboardType: TextInputType.emailAddress,
      validator: (value) {
        if (value != null && value.isNotEmpty) {
          final validation = BusinessValidation.validateEmail(value);
          return validation.isValid ? null : validation.errorMessage;
        }
        return null;
      },
    );
  }

  Widget _buildAddressField() {
    return buildFormField(
      controller: _addressController,
      labelText: 'Address (Optional)',
      hintText: 'Enter address',
      prefixIcon: const Icon(Icons.location_on),
      maxLines: 2,
      validator: (value) {
        if (value != null && value.isNotEmpty) {
          final validation = BusinessValidation.validateAddress(value);
          return validation.isValid ? null : validation.errorMessage;
        }
        return null;
      },
    );
  }

  Widget _buildCreditLimitField() {
    return buildFormField(
      controller: _creditLimitController,
      labelText: 'Credit Limit (Optional)',
      hintText: 'Enter credit limit',
      prefixIcon: const Icon(Icons.account_balance_wallet),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      validator: (value) {
        if (value != null && value.isNotEmpty) {
          final limit = double.tryParse(value);
          final validation = BusinessValidation.validateCreditLimit(limit);
          return validation.isValid ? null : validation.errorMessage;
        }
        return null;
      },
    );
  }

  Widget _buildCategoryField() {
    return buildFormField(
      controller: _categoryController,
      labelText: 'Category (Optional)',
      hintText: 'Enter customer category',
      prefixIcon: const Icon(Icons.label),
      validator: (value) {
        if (value != null && value.isNotEmpty) {
          final validation = BusinessValidation.validateCategory(value);
          return validation.isValid ? null : validation.errorMessage;
        }
        return null;
      },
    );
  }

  Widget _buildNotesField() {
    return buildFormField(
      controller: _notesController,
      labelText: 'Notes (Optional)',
      hintText: 'Enter any additional notes',
      prefixIcon: const Icon(Icons.note),
      maxLines: 3,
      validator: (value) {
        if (value != null && value.isNotEmpty) {
          final validation = BusinessValidation.validateNotes(value);
          return validation.isValid ? null : validation.errorMessage;
        }
        return null;
      },
    );
  }

  void _submitForm() {
    if (formKey.currentState!.validate() && !_isSubmitting) {
      final customerBloc = widget.parentContext.read<CustomerBloc>();
      customerBloc.add(CreateCustomer(
        customerName: _nameController.text.trim(),
        customerType: _selectedCustomerType.toLowerCase(),
        phone: _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty ? null : _emailController.text.trim(),
        address: _addressController.text.trim().isEmpty ? null : _addressController.text.trim(),
        creditLimit: _creditLimitController.text.isEmpty 
          ? null 
          : double.tryParse(_creditLimitController.text),
        category: _categoryController.text.trim().isEmpty ? null : _categoryController.text.trim(),
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      ));
    }
  }
} 