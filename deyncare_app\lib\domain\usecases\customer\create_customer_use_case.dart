import 'package:dartz/dartz.dart';
import 'package:deyncare_app/domain/repositories/customer_repository.dart';
import 'package:deyncare_app/data/models/customer_model.dart';
import 'package:deyncare_app/core/errors/failures.dart';
import 'package:deyncare_app/core/utils/business_validation.dart';

/// Use case for creating a new customer - BACKEND ALIGNED
class CreateCustomerUseCase {
  final CustomerRepository _repository;

  CreateCustomerUseCase(this._repository);

  /// Execute the use case to create a customer
  Future<Either<Failure, CustomerDetailResponse>> execute({
    required String customerName,
    required String customerType, // 'new' or 'returning'
    required String phone,
    String? email,
    String? address,
    double? creditLimit,
    String? category,
    String? notes,
  }) async {
    // Validate customer name (matches backend validation)
    final nameValidation = BusinessValidation.validateCustomerName(customerName);
    if (!nameValidation.isValid) {
      return Left(ValidationFailure(message: nameValidation.errorMessage!));
    }

    // Validate customer type (matches backend validation)
    final typeValidation = BusinessValidation.validateCustomerType(customerType);
    if (!typeValidation.isValid) {
      return Left(ValidationFailure(message: typeValidation.errorMessage!));
    }

    // Validate phone (matches backend validation)
    final phoneValidation = BusinessValidation.validatePhone(phone);
    if (!phoneValidation.isValid) {
      return Left(ValidationFailure(message: phoneValidation.errorMessage!));
    }

    // Validate email (matches backend validation)
    final emailValidation = BusinessValidation.validateEmail(email);
    if (!emailValidation.isValid) {
      return Left(ValidationFailure(message: emailValidation.errorMessage!));
    }

    // Validate address (matches backend validation)
    final addressValidation = BusinessValidation.validateAddress(address);
    if (!addressValidation.isValid) {
      return Left(ValidationFailure(message: addressValidation.errorMessage!));
    }

    // Validate credit limit (matches backend validation)
    final creditValidation = BusinessValidation.validateCreditLimit(creditLimit);
    if (!creditValidation.isValid) {
      return Left(ValidationFailure(message: creditValidation.errorMessage!));
    }

    // Validate category (matches backend validation)
    final categoryValidation = BusinessValidation.validateCategory(category);
    if (!categoryValidation.isValid) {
      return Left(ValidationFailure(message: categoryValidation.errorMessage!));
    }

    // Validate notes (matches backend validation)
    final notesValidation = BusinessValidation.validateNotes(notes);
    if (!notesValidation.isValid) {
      return Left(ValidationFailure(message: notesValidation.errorMessage!));
    }

    // Create request object
    final request = CreateCustomerRequest(
      customerName: customerName.trim(),
      customerType: customerType.toLowerCase(),
      phone: phone.trim(),
      email: email?.trim(),
      address: address?.trim(),
      creditLimit: creditLimit,
      category: category,
      notes: notes?.trim(),
    );

    return await _repository.createCustomer(request);
  }
} 