import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../data/services/firebase_service.dart';
import '../../../data/services/notification_service.dart';

// Events
abstract class NotificationEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class InitializeNotifications extends NotificationEvent {}

class RegisterFCMToken extends NotificationEvent {}

class SendTestNotification extends NotificationEvent {}

class HandleTokenRefresh extends NotificationEvent {
  final String newToken;
  HandleTokenRefresh(this.newToken);
  
  @override
  List<Object?> get props => [newToken];
}

class AddNotificationToHistory extends NotificationEvent {
  final Map<String, dynamic> notification;
  AddNotificationToHistory(this.notification);
  
  @override
  List<Object?> get props => [notification];
}

class ClearNotificationHistory extends NotificationEvent {}

// States
abstract class NotificationState extends Equatable {
  @override
  List<Object?> get props => [];
}

class NotificationInitial extends NotificationState {}

class NotificationLoading extends NotificationState {}

class NotificationInitialized extends NotificationState {
  final bool firebaseInitialized;
  final String? fcmToken;
  final bool tokenRegistered;
  
  NotificationInitialized({
    required this.firebaseInitialized,
    this.fcmToken,
    required this.tokenRegistered,
  });
  
  @override
  List<Object?> get props => [firebaseInitialized, fcmToken, tokenRegistered];
}

class NotificationTokenRegistered extends NotificationState {
  final String message;
  final Map<String, dynamic> data;
  
  NotificationTokenRegistered({
    required this.message,
    required this.data,
  });
  
  @override
  List<Object?> get props => [message, data];
}

class NotificationTestSent extends NotificationState {
  final String message;
  final Map<String, dynamic> result;
  
  NotificationTestSent({
    required this.message,
    required this.result,
  });
  
  @override
  List<Object?> get props => [message, result];
}

class NotificationHistoryUpdated extends NotificationState {
  final List<Map<String, dynamic>> notificationHistory;
  
  NotificationHistoryUpdated(this.notificationHistory);
  
  @override
  List<Object?> get props => [notificationHistory];
}

class NotificationError extends NotificationState {
  final String message;
  final String? details;
  
  NotificationError({
    required this.message,
    this.details,
  });
  
  @override
  List<Object?> get props => [message, details];
}

// BLoC - Updated to match backend reality
class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  final NotificationService _notificationService;
  final List<Map<String, dynamic>> _notificationHistory = [];

  NotificationBloc({
    required NotificationService notificationService,
  }) : _notificationService = notificationService,
       super(NotificationInitial()) {
    
    // ✅ Only register handlers for existing backend endpoints
    on<InitializeNotifications>(_onInitializeNotifications);
    on<RegisterFCMToken>(_onRegisterFCMToken);
    on<SendTestNotification>(_onSendTestNotification);
    on<HandleTokenRefresh>(_onHandleTokenRefresh);
    on<AddNotificationToHistory>(_onAddNotificationToHistory);
    on<ClearNotificationHistory>(_onClearNotificationHistory);
  }

  Future<void> _onInitializeNotifications(
    InitializeNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      emit(NotificationLoading());
      
      // Initialize Firebase service first
      await FirebaseService.initialize();
      
      // Initialize notification service (will auto-register token)
      await _notificationService.initialize();
      
      emit(NotificationInitialized(
        firebaseInitialized: FirebaseService.isInitialized,
        fcmToken: FirebaseService.fcmToken,
        tokenRegistered: NotificationService.isTokenRegistered,
      ));
    } catch (e) {
      emit(NotificationError(
        message: 'Failed to initialize notifications',
        details: e.toString(),
      ));
    }
  }

  /// ✅ Register FCM token - Backend endpoint exists
  Future<void> _onRegisterFCMToken(
    RegisterFCMToken event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      emit(NotificationLoading());
      
      final result = await _notificationService.registerFCMToken();
      
      emit(NotificationTokenRegistered(
        message: 'FCM token registered successfully',
        data: result,
      ));
    } catch (e) {
      emit(NotificationError(
        message: 'Failed to register FCM token',
        details: e.toString(),
      ));
    }
  }

  /// ✅ Send test notification - Backend endpoint exists
  Future<void> _onSendTestNotification(
    SendTestNotification event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      emit(NotificationLoading());
      
      final result = await _notificationService.sendTestNotification();
      
      emit(NotificationTestSent(
        message: 'Test notification sent successfully',
        result: result,
      ));
    } catch (e) {
      emit(NotificationError(
        message: 'Failed to send test notification',
        details: e.toString(),
      ));
    }
  }

  /// ✅ Handle token refresh - Local operation
  Future<void> _onHandleTokenRefresh(
    HandleTokenRefresh event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      await _notificationService.onTokenRefresh(event.newToken);
      
      emit(NotificationInitialized(
        firebaseInitialized: FirebaseService.isInitialized,
        fcmToken: event.newToken,
        tokenRegistered: NotificationService.isTokenRegistered,
      ));
    } catch (e) {
      emit(NotificationError(
        message: 'Failed to handle token refresh',
        details: e.toString(),
      ));
    }
  }

  /// Add notification to local history
  void _onAddNotificationToHistory(
    AddNotificationToHistory event,
    Emitter<NotificationState> emit,
  ) {
    _notificationHistory.insert(0, {
      ...event.notification,
      'receivedAt': DateTime.now().toIso8601String(),
    });
    
    // Keep only last 50 notifications
    if (_notificationHistory.length > 50) {
      _notificationHistory.removeRange(50, _notificationHistory.length);
    }
    
    emit(NotificationHistoryUpdated(List.from(_notificationHistory)));
  }

  /// Clear notification history
  void _onClearNotificationHistory(
    ClearNotificationHistory event,
    Emitter<NotificationState> emit,
  ) {
    _notificationHistory.clear();
    emit(NotificationHistoryUpdated(List.from(_notificationHistory)));
  }

  // Getters for current state data
  List<Map<String, dynamic>> get notificationHistory => 
      List.unmodifiable(_notificationHistory);
  
  bool get isFirebaseInitialized => FirebaseService.isInitialized;
  String? get fcmToken => FirebaseService.fcmToken;
  bool get isTokenRegistered => NotificationService.isTokenRegistered;
  Map<String, String> get deviceInfo => NotificationService.deviceInfo;

  /// Helper method to handle incoming notifications
  void handleIncomingNotification(Map<String, dynamic> notificationData) {
    add(AddNotificationToHistory(notificationData));
    
    // Process notification based on type
    final notificationType = notificationData['type'];
    print('📨 Received notification: $notificationType');
    
    switch (notificationType) {
      case 'debt_created':
        print('💰 Debt created notification received');
        break;
      case 'payment_recorded':
        print('💳 Payment recorded notification received');
        break;
      case 'debt_reminder':
        print('⏰ Debt reminder notification received');
        break;
      case 'test':
        print('🧪 Test notification received');
        break;
      case 'custom':
        print('📢 Custom notification received');
        break;
      default:
        print('📋 Unknown notification type: $notificationType');
    }
  }
} 