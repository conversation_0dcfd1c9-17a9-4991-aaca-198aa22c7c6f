import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_text_field.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_button.dart';
import 'package:deyncare_app/data/services/auth_service.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';

/// Form for requesting a password reset via email
class ForgotPasswordForm extends StatefulWidget {
  const ForgotPasswordForm({super.key});

  @override
  State<ForgotPasswordForm> createState() => _ForgotPasswordFormState();
}

class _ForgotPasswordFormState extends State<ForgotPasswordForm>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _authService = AuthService();
  bool _isLoading = false;
  bool _requestSent = false;
  String? _errorMessage;
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }
  
  @override
  void dispose() {
    _emailController.dispose();
    _authService.dispose();
    _animationController.dispose();
    super.dispose();
  }
  
  // Email validation
  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }
  
  // Handle password reset request using hybrid approach
  Future<void> _handleResetRequest() async {
    if (_formKey.currentState!.validate()) {
      // Clear previous error message
      setState(() {
        _errorMessage = null;
      });
      
      // Close the keyboard
      FocusScope.of(context).unfocus();
      
      setState(() {
        _isLoading = true;
      });
      
      try {
        final email = _emailController.text.trim();
        
        // Call the forgot password API with mobile indicator
        // This will send an email with a web link that contains a deep link back to the app
        await _authService.forgotPassword(email)
            .timeout(
              const Duration(seconds: 8), // Reduced from 10 to 8 seconds to match backend optimizations
              onTimeout: () => throw ApiException(
                message: 'Request timed out. Please try again.',
                code: 'timeout_error',
              ),
            );
        
        // Show success state
        if (mounted) {
          setState(() {
            _requestSent = true;
            _isLoading = false;
          });
          
          // Restart animation for success state
          _animationController.reset();
          _animationController.forward();
        }
      } catch (e) {
        // CRITICAL: ALWAYS reset loading state on ANY error
        setState(() {
          _isLoading = false;
        });
        
        if (mounted) {
          // Extract readable error message
          String errorMsg = 'Connection failed. Please check your internet and try again.';
          if (e is ApiException) {
            errorMsg = e.message;
          } else if (e.toString().contains('connection') || e.toString().contains('network')) {
            errorMsg = 'No internet connection. Please check your network settings.';
          }
          
          setState(() {
            _errorMessage = errorMsg;
          });
        }
      } finally {
        // SAFETY NET: Always ensure loading is reset
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: _requestSent ? _buildSuccessState() : _buildFormState(),
          ),
        );
      },
    );
  }
  
  /// Build the main form state
  Widget _buildFormState() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Email field with enhanced styling
          AuthTextField(
            label: 'Email Address',
            hintText: 'Enter your registered email',
            controller: _emailController,
            validator: _validateEmail,
            keyboardType: TextInputType.emailAddress,
            textInputAction: TextInputAction.done,
            prefixIcon: Icon(
              Icons.email_outlined,
              color: AppThemes.primaryColor,
            ),
            onSubmitted: (_) => _handleResetRequest(),
          ),
          const SizedBox(height: 20),
          
          // Instructions with better styling
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppThemes.infoColor.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppThemes.infoColor.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppThemes.infoColor,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'We\'ll send a secure password reset link to your email address.',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppThemes.textSecondaryColor,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Error message display with enhanced styling
          if (_errorMessage != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppThemes.errorColor.withOpacity(0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppThemes.errorColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: AppThemes.errorColor,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: TextStyle(
                        color: AppThemes.errorColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          const SizedBox(height: 32),
          
          // Enhanced submit button
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: AppThemes.primaryColor.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: AuthButton(
              label: 'Send Reset Link',
              isLoading: _isLoading,
              onPressed: _handleResetRequest,
            ),
          ),
          const SizedBox(height: 24),
          
          // Back to login button with enhanced styling
          Center(
            child: OutlinedButton.icon(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.arrow_back),
              label: const Text('Back to Login'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppThemes.primaryColor,
                side: BorderSide(color: AppThemes.primaryColor),
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// Enhanced success state UI
  Widget _buildSuccessState() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Success animation container
        Container(
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppThemes.successColor.withOpacity(0.1),
                AppThemes.primaryColor.withOpacity(0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            children: [
              // Success icon with animation
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppThemes.successColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.mark_email_read_outlined,
                  size: 60,
                  color: AppThemes.successColor,
                ),
              ),
              const SizedBox(height: 24),
              
              // Success message
              Text(
                'Reset Link Sent!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppThemes.textPrimaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              
              // Email confirmation
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: AppThemes.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _emailController.text,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppThemes.primaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 24),
        
        // Instructions with better styling
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppThemes.backgroundColor,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppThemes.primaryColor.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Text(
                'Check Your Email',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppThemes.textPrimaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                'We\'ve sent a password reset link to your email. Click the link in the email to reset your password.',
                style: TextStyle(
                  fontSize: 14,
                  color: AppThemes.textSecondaryColor,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 32),
        
        // Action buttons
        Column(
          children: [
            // Back to login button
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: AppThemes.primaryColor.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: AuthButton(
                label: 'Back to Login',
                onPressed: () => Navigator.pop(context),
              ),
            ),
            const SizedBox(height: 16),
            
            // Resend option
            Center(
              child: TextButton.icon(
                onPressed: () {
                  setState(() {
                    _requestSent = false;
                  });
                  _animationController.reset();
                  _animationController.forward();
                },
                icon: const Icon(Icons.refresh),
                label: const Text('Didn\'t receive email? Send again'),
                style: TextButton.styleFrom(
                  foregroundColor: AppThemes.primaryColor,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
