import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/data/models/employee_model.dart';
import 'package:deyncare_app/data/services/user_management_service.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/injection_container.dart' as di;

/// Add User Role Screen - Create new employee with granular permissions
class AddUserRoleScreen extends StatefulWidget {
  const AddUserRoleScreen({super.key});

  @override
  State<AddUserRoleScreen> createState() => _AddUserRoleScreenState();
}

class _AddUserRoleScreenState extends State<AddUserRoleScreen> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _userTitleController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  late final UserManagementService _userManagementService;

  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  // Permission states
  bool _checkAll = false;

  // Customer Management permissions
  bool _customerCreate = false;
  bool _customerUpdate = false;
  bool _customerView = false;
  bool _customerDelete = false;

  // Debt Management permissions
  bool _debtCreate = false;
  bool _debtUpdate = false;
  bool _debtView = false;
  bool _debtDelete = false;

  // Report Management permissions
  bool _reportGenerate = false;
  bool _reportView = false;
  bool _reportDelete = false;

  @override
  void initState() {
    super.initState();
    _userManagementService = di.sl<UserManagementService>();
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add New User Role'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        scrolledUnderElevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Basic Information Section
            _buildBasicInfoSection(),

            const SizedBox(height: 24),

            // Permissions Section
            _buildPermissionsSection(),

            const SizedBox(height: 32),

            // Action Buttons
            _buildActionButtons(),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return CommonCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Basic Information',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppThemes.primaryColor,
                ),
          ),
          const SizedBox(height: 16),

          // Full Name Field
          TextFormField(
            controller: _fullNameController,
            decoration: InputDecoration(
              labelText: 'Full Name',
              hintText: 'Enter employee full name',
              prefixIcon: const Icon(Icons.person_outline),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Full name is required';
              }
              if (value.trim().length < 3) {
                return 'Full name must be at least 3 characters';
              }
              if (value.trim().length > 100) {
                return 'Full name cannot exceed 100 characters';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // Email Field
          TextFormField(
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            decoration: InputDecoration(
              labelText: 'Email Address',
              hintText: 'Enter employee email',
              prefixIcon: const Icon(Icons.email_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Email is required';
              }
              final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
              if (!emailRegex.hasMatch(value.trim())) {
                return 'Please enter a valid email address';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // Phone Field
          TextFormField(
            controller: _phoneController,
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              labelText: 'Phone Number',
              hintText: 'Enter phone number (e.g. +252612345678)',
              prefixIcon: const Icon(Icons.phone_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Phone number is required';
              }
              // Basic phone validation for international format
              final phoneRegex = RegExp(r'^\+[1-9]\d{1,14}$');
              if (!phoneRegex.hasMatch(value.trim())) {
                return 'Please enter a valid international phone number (e.g. +252612345678)';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // User Title Field
          TextFormField(
            controller: _userTitleController,
            decoration: InputDecoration(
              labelText: 'User Title',
              hintText: 'Enter user title (e.g. Cashier, Manager)',
              prefixIcon: const Icon(Icons.work_outline),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'User title is required';
              }
              if (value.trim().length < 2) {
                return 'User title must be at least 2 characters';
              }
              if (value.trim().length > 50) {
                return 'User title cannot exceed 50 characters';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // Password Field
          TextFormField(
            controller: _passwordController,
            obscureText: _obscurePassword,
            decoration: InputDecoration(
              labelText: 'Password',
              hintText: 'Enter password',
              prefixIcon: const Icon(Icons.lock_outline),
              suffixIcon: IconButton(
                icon: Icon(
                    _obscurePassword ? Icons.visibility : Icons.visibility_off),
                onPressed: () =>
                    setState(() => _obscurePassword = !_obscurePassword),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Password is required';
              }
              if (value.length < 6) {
                return 'Password must be at least 6 characters';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // Confirm Password Field
          TextFormField(
            controller: _confirmPasswordController,
            obscureText: _obscureConfirmPassword,
            decoration: InputDecoration(
              labelText: 'Confirm Password',
              hintText: 'Re-enter password',
              prefixIcon: const Icon(Icons.lock_outline),
              suffixIcon: IconButton(
                icon: Icon(_obscureConfirmPassword
                    ? Icons.visibility
                    : Icons.visibility_off),
                onPressed: () => setState(
                    () => _obscureConfirmPassword = !_obscureConfirmPassword),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please confirm your password';
              }
              if (value != _passwordController.text) {
                return 'Passwords do not match';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionsSection() {
    return CommonCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Permissions',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppThemes.primaryColor,
                ),
          ),

          const SizedBox(height: 16),

          // Enable All Permissions Option
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: AppThemes.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                  color: AppThemes.accentColor.withValues(alpha: 0.3)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Enable All Permissions',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: AppThemes.accentColor,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      'Grant all permissions',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
                Switch(
                  value: _checkAll,
                  onChanged: (value) => _setAllPermissions(value),
                  activeColor: AppThemes.accentColor,
                ),
              ],
            ),
          ),

          const Divider(),

          // Customer Management Section
          _buildPermissionModule(
            'Customer Management',
            Icons.people_outline,
            [
              _buildPermissionCheckbox('Create', _customerCreate,
                  (value) => setState(() => _customerCreate = value)),
              _buildPermissionCheckbox('Update', _customerUpdate,
                  (value) => setState(() => _customerUpdate = value)),
              _buildPermissionCheckbox('View', _customerView,
                  (value) => setState(() => _customerView = value)),
              _buildPermissionCheckbox('Delete', _customerDelete,
                  (value) => setState(() => _customerDelete = value)),
            ],
          ),

          const SizedBox(height: 16),

          // Debt Management Section
          _buildPermissionModule(
            'Debt Management',
            Icons.receipt_long_outlined,
            [
              _buildPermissionCheckbox('Create', _debtCreate,
                  (value) => setState(() => _debtCreate = value)),
              _buildPermissionCheckbox('Update', _debtUpdate,
                  (value) => setState(() => _debtUpdate = value)),
              _buildPermissionCheckbox('View', _debtView,
                  (value) => setState(() => _debtView = value)),
              _buildPermissionCheckbox('Delete', _debtDelete,
                  (value) => setState(() => _debtDelete = value)),
            ],
          ),

          const SizedBox(height: 16),

          // Report Management Section
          _buildPermissionModule(
            'Report Management',
            Icons.analytics_outlined,
            [
              _buildPermissionCheckbox('Generate', _reportGenerate,
                  (value) => setState(() => _reportGenerate = value)),
              _buildPermissionCheckbox('View', _reportView,
                  (value) => setState(() => _reportView = value)),
              _buildPermissionCheckbox('Delete', _reportDelete,
                  (value) => setState(() => _reportDelete = value)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionModule(
      String title, IconData icon, List<Widget> permissions) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 20, color: AppThemes.secondaryColor),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: AppThemes.secondaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: permissions,
        ),
      ],
    );
  }

  Widget _buildPermissionCheckbox(
      String label, bool value, ValueChanged<bool> onChanged) {
    return Container(
      width: (MediaQuery.of(context).size.width - 80) / 2, // Two columns
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: value
            ? AppThemes.accentColor.withValues(alpha: 0.1)
            : Colors.grey.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: value
              ? AppThemes.accentColor.withValues(alpha: 0.3)
              : Colors.grey.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: value ? FontWeight.w600 : FontWeight.normal,
                color: value ? AppThemes.accentColor : Colors.grey[700],
              ),
            ),
          ),
          Switch(
            value: value,
            onChanged: (newValue) {
              onChanged(newValue);
              _updateCheckAllStatus();
            },
            activeColor: AppThemes.accentColor,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Cancel'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _createEmployee,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppThemes.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Text('Create Employee'),
          ),
        ),
      ],
    );
  }

  void _setAllPermissions(bool value) {
    setState(() {
      _checkAll = value;

      // Customer Management
      _customerCreate = value;
      _customerUpdate = value;
      _customerView = value;
      _customerDelete = value;

      // Debt Management
      _debtCreate = value;
      _debtUpdate = value;
      _debtView = value;
      _debtDelete = value;

      // Report Management
      _reportGenerate = value;
      _reportView = value;
      _reportDelete = value;
    });
  }

  void _updateCheckAllStatus() {
    final allSelected = _customerCreate &&
        _customerUpdate &&
        _customerView &&
        _customerDelete &&
        _debtCreate &&
        _debtUpdate &&
        _debtView &&
        _debtDelete &&
        _reportGenerate &&
        _reportView &&
        _reportDelete;

    setState(() => _checkAll = allSelected);
  }

  bool _hasAtLeastOnePermission() {
    return _customerCreate ||
        _customerUpdate ||
        _customerView ||
        _customerDelete ||
        _debtCreate ||
        _debtUpdate ||
        _debtView ||
        _debtDelete ||
        _reportGenerate ||
        _reportView ||
        _reportDelete;
  }

  void _createEmployee() async {
    print('🔄 Create Employee button pressed');

    if (!_formKey.currentState!.validate()) {
      print('❌ Form validation failed');
      return;
    }

    if (!_hasAtLeastOnePermission()) {
      print('❌ No permissions selected');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one permission'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    print('✅ Validation passed, creating employee...');
    setState(() => _isLoading = true);

    try {
      final request = CreateEmployeeRequestModel(
        fullName: _fullNameController.text.trim(),
        userTitle: _userTitleController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim(),
        password: _passwordController.text,
        confirmPassword: _confirmPasswordController.text,
        visibility: {
          'customerManagement': {
            'create': _customerCreate,
            'update': _customerUpdate,
            'view': _customerView,
            'delete': _customerDelete,
          },
          'debtManagement': {
            'create': _debtCreate,
            'update': _debtUpdate,
            'view': _debtView,
            'delete': _debtDelete,
          },
          'reportManagement': {
            'generate': _reportGenerate,
            'view': _reportView,
            'delete': _reportDelete,
          },
        },
      );

      print('📤 Sending request: ${request.toJson()}');
      await _userManagementService.createEmployee(request);
      print('✅ Employee created successfully');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Employee created successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      print('❌ Error creating employee: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create employee: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
