import 'package:flutter/material.dart';
import 'dart:async';
import 'package:deyncare_app/core/managers/connectivity_manager.dart';

class ConnectivityBanner extends StatefulWidget {
  final Duration onlineBannerDuration;
  final double fontSize;
  final double iconSize;
  final double borderRadius;
  final EdgeInsets margin;
  final EdgeInsets padding;

  const ConnectivityBanner({
    super.key,
    this.onlineBannerDuration = const Duration(seconds: 2),
    this.fontSize = 16,
    this.iconSize = 22,
    this.borderRadius = 16,
    this.margin = const EdgeInsets.only(top: 12, left: 16, right: 16),
    this.padding = const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
  });

  @override
  State<ConnectivityBanner> createState() => _ConnectivityBannerState();
}

class _ConnectivityBannerState extends State<ConnectivityBanner> {
  final ConnectivityManager _connectivityManager = ConnectivityManager();
  bool _showOnlineBanner = false;
  bool _showOfflineBanner = false;
  Timer? _onlineTimer;
  StreamSubscription? _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    // Delay connectivity manager initialization to allow app to load first
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        _connectivityManager.initialize().then((_) {
          if (mounted) {
            // Set initial state based on connectivity
            final initialState = _connectivityManager.currentState;
            setState(() {
              _showOfflineBanner = initialState == ConnectivityState.disconnected;
            });
            
            // Subscribe to connectivity changes
            _connectivitySubscription = _connectivityManager.stateStream.listen(_handleStateChange);
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _onlineTimer?.cancel();
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  void _handleStateChange(ConnectivityState state) {
    // Prevent unnecessary state updates
    if (!mounted) return;
    
    // Add debug logging
    debugPrint('🔄 ConnectivityBanner: State changed to $state');
    
    if (state == ConnectivityState.reconnected) {
      // Show online banner when reconnected
      setState(() {
        _showOnlineBanner = true;
        _showOfflineBanner = false; // Hide offline banner immediately
      });
      
      // Set timer to hide online banner
      _onlineTimer?.cancel();
      _onlineTimer = Timer(widget.onlineBannerDuration, () {
        if (mounted) {
          setState(() {
            _showOnlineBanner = false;
          });
        }
      });
    } else if (state == ConnectivityState.connected) {
      setState(() {
        _showOfflineBanner = false;
        // Keep the _showOnlineBanner state as is (will be managed by timer)
      });
    } else if (state == ConnectivityState.disconnected) {
      setState(() {
        _showOfflineBanner = true;
        _showOnlineBanner = false; // Cancel any online banner
      });
      // Cancel any pending online banner timer
      _onlineTimer?.cancel();
    } else if (state == ConnectivityState.unknown) {
      // Don't show banner for unknown state initially
      // This prevents false offline indicators during app startup
      debugPrint('⚠️ ConnectivityBanner: Unknown state - not showing banner');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Calculate banner visibility based on current state
    bool showBanner = _showOfflineBanner || _showOnlineBanner;
    
    // Determine message and style based on current state
    String message = _showOnlineBanner ? 'Back Online' : 'You are offline';
    Color bgColor = _showOnlineBanner ? Colors.green[600]! : Theme.of(context).colorScheme.error;
    IconData icon = _showOnlineBanner ? Icons.wifi : Icons.wifi_off;

    return AnimatedSlide(
      offset: showBanner ? Offset.zero : const Offset(0, -1),
      duration: const Duration(milliseconds: 350),
      curve: Curves.easeInOut,
      child: AnimatedOpacity(
        opacity: showBanner ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 350),
        child: Center(
          child: Container(
            margin: widget.margin,
            padding: widget.padding,
            decoration: BoxDecoration(
              color: bgColor,
              borderRadius: BorderRadius.circular(widget.borderRadius),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(icon, color: Colors.white, size: widget.iconSize),
                const SizedBox(width: 10),
                Text(
                  message,
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: widget.fontSize,
                    letterSpacing: 0.2,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}