import 'package:dio/dio.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:deyncare_app/data/services/api_client.dart';

// Mock API client that extends ApiClient for testing auth service
class MockApiClient extends ApiClient {
  // Mock response data for predictable testing
  final Map<String, dynamic> _mockUserData = {
    'id': '1',
    'name': 'Test User',
    'email': '<EMAIL>',
    'role': 'customer',
  };

  // Mock tokens for testing
  final Map<String, String> _mockTokens = {
    'access_token': '',
    'refresh_token': ''
  };

  // Mock storage for testing
  final Map<String, String> _mockStorage = {};

  // Constructor - use a mock Dio instance to avoid real network calls
  MockApiClient() : super(dio: Dio());

  @override
  Future<void> init() async {
    // Override to avoid actual initialization
    return;
  }

  // Override HTTP methods to avoid real network calls
  @override
  Future<dynamic> get(String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
    bool retry = true,
  }) async {
    if (path.contains('/auth/me')) {
      return Response(
        data: {'user': _mockUserData},
        statusCode: 200,
        requestOptions: RequestOptions(path: path),
      );
    }
    throw ApiException(message: 'Not implemented in mock', code: 'mock_error');
  }

  @override
  Future<dynamic> post(String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    bool retry = true,
  }) async {
    // Simulate auth endpoints
    if (path.contains('/auth/login')) {
      final credentials = data as Map<String, dynamic>;
      final email = credentials['email'] as String?;
      final password = credentials['password'] as String?;

      if (email == '<EMAIL>' && password == 'password') {
        _mockTokens['access_token'] = 'mock_access_token';
        _mockTokens['refresh_token'] = 'mock_refresh_token';

        return Response(
          data: {
            'access_token': 'mock_access_token',
            'refresh_token': 'mock_refresh_token',
            'user': _mockUserData
          },
          statusCode: 200,
          requestOptions: RequestOptions(path: path),
        );
      } else {
        throw ApiException(
          message: 'Invalid credentials',
          code: 'auth_error',
        );
      }
    } else if (path.contains('/auth/register')) {
      final userData = data as Map<String, dynamic>;

      _mockTokens['access_token'] = 'mock_access_token';
      _mockTokens['refresh_token'] = 'mock_refresh_token';

      return Response(
        data: {
          'access_token': 'mock_access_token',
          'refresh_token': 'mock_refresh_token',
          'user': {
            'id': '1',
            'name': userData['name'],
            'email': userData['email'],
            'role': 'customer',
          }
        },
        statusCode: 201,
        requestOptions: RequestOptions(path: path),
      );
    } else if (path.contains('/auth/logout')) {
      _mockTokens['access_token'] = '';
      _mockTokens['refresh_token'] = '';

      return Response(
        data: {'message': 'Logged out successfully'},
        statusCode: 200,
        requestOptions: RequestOptions(path: path),
      );
    } else if (path.contains('/auth/refresh-token')) {
      _mockTokens['access_token'] = 'new_mock_access_token';
      _mockTokens['refresh_token'] = 'new_mock_refresh_token';

      return Response(
        data: {
          'access_token': 'new_mock_access_token',
          'refresh_token': 'new_mock_refresh_token',
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: path),
      );
    }

    throw ApiException(message: 'Endpoint not mocked: $path', code: 'mock_error');
  }

  // Token management methods
  @override
  Future<String?> getAccessToken() async {
    return _mockTokens['access_token'];
  }

  @override
  Future<String?> getRefreshToken() async {
    return _mockTokens['refresh_token'];
  }

  @override
  Future<void> setTokens(String accessToken, String refreshToken) async {
    _mockTokens['access_token'] = accessToken;
    _mockTokens['refresh_token'] = refreshToken;
  }

  @override
  Future<void> deleteTokens() async {
    _mockTokens['access_token'] = '';
    _mockTokens['refresh_token'] = '';
  }

  // Helper method to clear tokens
  @override
  Future<void> clearTokens() async {
    await deleteTokens();
  }
  
  // Secure storage methods
  Future<String?> getFromStorage(String key) async {
    return _mockStorage[key];
  }
  
  Future<void> writeToStorage(String key, String value) async {
    _mockStorage[key] = value;
  }
  
  Future<void> deleteFromStorage(String key) async {
    _mockStorage.remove(key);
  }
}
