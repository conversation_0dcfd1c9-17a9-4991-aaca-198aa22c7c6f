import 'package:dartz/dartz.dart';
import 'package:deyncare_app/core/errors/failures.dart';
import 'package:deyncare_app/domain/models/subscription.dart';
import 'package:deyncare_app/domain/repositories/subscription_repository.dart';

class GetCurrentSubscriptionUseCase {
  final SubscriptionRepository repository;

  GetCurrentSubscriptionUseCase(this.repository);

  Future<Either<Failure, Subscription>> call() async {
    return await repository.getCurrentSubscription();
  }
} 