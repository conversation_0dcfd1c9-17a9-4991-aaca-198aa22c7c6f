import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:deyncare_app/data/models/user_model.dart';

/// Utility functions for authentication operations
class AuthUtils {
  final FlutterSecureStorage _secureStorage;
  
  // Storage keys
  static const String _userDataKey = 'user_data';
  
  /// Creates a new instance with optional dependencies
  AuthUtils({
    FlutterSecureStorage? secureStorage,
  }) : _secureStorage = secureStorage ?? const FlutterSecureStorage();
  
  /// Save user data to secure storage
  Future<void> saveUserData(UserModel user) async {
    try {
      final jsonString = jsonEncode(user.toJson());
      await _secureStorage.write(
        key: _userDataKey,
        value: jsonString,
      );
    } catch (e) {
      throw Exception('Failed to save user data: $e');
    }
  }
  
  /// Get user data from secure storage
  Future<UserModel?> getUserData() async {
    try {
      final userData = await _secureStorage.read(key: _userDataKey);
      if (userData == null) {
        return null;
      }
      
      final dynamic decodedData = jsonDecode(userData);
      final Map<String, dynamic> json;
      
      if (decodedData is Map<String, dynamic>) {
        json = decodedData;
      } else if (decodedData is Map) {
        json = Map<String, dynamic>.from(decodedData);
      } else {
        throw Exception('Invalid user data format');
      }
      
      return UserModel.fromJson(json);
    } catch (e) {
      // Invalid user data, clear it
      await _secureStorage.delete(key: _userDataKey);
      return null;
    }
  }
  
  /// Clear user data from secure storage
  Future<void> clearUserData() async {
    await _secureStorage.delete(key: _userDataKey);
  }
  
  /// Generate device name for authentication
  String getDeviceName() {
    return 'DeynCare Mobile App';
  }
}
