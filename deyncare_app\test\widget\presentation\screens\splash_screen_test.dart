import 'package:deyncare_app/domain/repositories/auth_repository.dart';
import 'package:deyncare_app/domain/usecases/auth/check_auth_status_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/change_password_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/forgot_password_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/get_available_payment_methods_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/login_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/logout_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/process_payment_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/refresh_token_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/register_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/reset_password_success_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/verify_email_use_case.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/screens/splash/splash_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

// Mock classes for all dependencies required by AuthBloc
class MockAuthRepository extends Mock implements AuthRepository {}

class MockCheckAuthStatusUseCase extends Mock implements CheckAuthStatusUseCase {}

class MockLoginUseCase extends Mock implements LoginUseCase {}

class MockLogoutUseCase extends Mock implements LogoutUseCase {}

class MockRegisterUseCase extends Mock implements RegisterUseCase {}

class MockVerifyEmailUseCase extends Mock implements VerifyEmailUseCase {}

class MockForgotPasswordUseCase extends Mock implements ForgotPasswordUseCase {}

class MockChangePasswordUseCase extends Mock implements ChangePasswordUseCase {}

class MockRefreshTokenUseCase extends Mock implements RefreshTokenUseCase {}

class MockResetPasswordSuccessUseCase extends Mock
    implements ResetPasswordSuccessUseCase {}

class MockProcessPaymentUseCase extends Mock implements ProcessPaymentUseCase {}

class MockGetAvailablePaymentMethodsUseCase extends Mock
    implements GetAvailablePaymentMethodsUseCase {}

// Helper function to create a fully mocked AuthBloc
AuthBloc createMockAuthBloc() {
  final mockAuthRepository = MockAuthRepository();
  final mockCheckAuthStatusUseCase = MockCheckAuthStatusUseCase();
  final mockLoginUseCase = MockLoginUseCase();
  final mockLogoutUseCase = MockLogoutUseCase();
  final mockRegisterUseCase = MockRegisterUseCase();
  final mockVerifyEmailUseCase = MockVerifyEmailUseCase();
  final mockForgotPasswordUseCase = MockForgotPasswordUseCase();
  final mockChangePasswordUseCase = MockChangePasswordUseCase();
  final mockRefreshTokenUseCase = MockRefreshTokenUseCase();
  final mockResetPasswordSuccessUseCase = MockResetPasswordSuccessUseCase();
  final mockProcessPaymentUseCase = MockProcessPaymentUseCase();
  final mockGetAvailablePaymentMethodsUseCase =
      MockGetAvailablePaymentMethodsUseCase();

  // Default stub for CheckAuthStatusUseCase, which is called on startup.
  // Return false to simulate a logged-out state.
  when(mockCheckAuthStatusUseCase.execute()).thenAnswer((_) async => false);

  return AuthBloc(
    authRepository: mockAuthRepository,
    checkAuthStatus: mockCheckAuthStatusUseCase,
    login: mockLoginUseCase,
    logout: mockLogoutUseCase,
    register: mockRegisterUseCase,
    verifyEmail: mockVerifyEmailUseCase,
    forgotPassword: mockForgotPasswordUseCase,
    changePassword: mockChangePasswordUseCase,
    refreshToken: mockRefreshTokenUseCase,
    resetPasswordSuccess: mockResetPasswordSuccessUseCase,
    processPayment: mockProcessPaymentUseCase,
    getAvailablePaymentMethods: mockGetAvailablePaymentMethodsUseCase,
  );
}

void main() {
  late AuthBloc authBloc;

  setUp(() {
    authBloc = createMockAuthBloc();
  });

  tearDown(() {
    authBloc.close();
  });

  group('SplashScreen', () {
    testWidgets('should display the DeynCare logo and app name',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AuthBloc>.value(
            value: authBloc,
            child: const SplashScreen(),
          ),
        ),
      );

      await tester.pump(const Duration(milliseconds: 500));

      expect(find.text('DeynCare'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should handle an authenticated state scenario',
        (WidgetTester tester) async {
      final authBlocAuthenticated = createMockAuthBloc();

      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AuthBloc>.value(
            value: authBlocAuthenticated,
            child: const SplashScreen(),
          ),
        ),
      );

      await tester.pump();

      expect(find.byType(SplashScreen), findsOneWidget);

      authBlocAuthenticated.close();
    });

    testWidgets('should handle an unauthenticated state scenario',
        (WidgetTester tester) async {
      final authBlocUnauthenticated = createMockAuthBloc();

      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AuthBloc>.value(
            value: authBlocUnauthenticated,
            child: const SplashScreen(),
          ),
        ),
      );

      await tester.pump();

      expect(find.byType(SplashScreen), findsOneWidget);

      authBlocUnauthenticated.close();
    });

    testWidgets('loads splash screen animation correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AuthBloc>.value(
            value: authBloc,
            child: const SplashScreen(),
          ),
        ),
      );

      await tester.pump();

      expect(find.byType(Image), findsAtLeastNWidgets(1));

      await tester.pump(const Duration(milliseconds: 500));

      expect(find.byType(SplashScreen), findsOneWidget);
    });
  });
}


