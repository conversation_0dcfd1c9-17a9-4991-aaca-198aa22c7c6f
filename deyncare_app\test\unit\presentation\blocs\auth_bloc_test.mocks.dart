// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in deyncare_app/test/unit/presentation/blocs/auth_bloc_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;
import 'dart:io' as _i9;

import 'package:deyncare_app/domain/models/auth_token.dart' as _i3;
import 'package:deyncare_app/domain/models/registration_progress.dart' as _i4;
import 'package:deyncare_app/domain/models/user.dart' as _i2;
import 'package:deyncare_app/domain/repositories/auth_repository.dart' as _i18;
import 'package:deyncare_app/domain/usecases/auth/change_password_use_case.dart'
    as _i13;
import 'package:deyncare_app/domain/usecases/auth/check_auth_status_use_case.dart'
    as _i5;
import 'package:deyncare_app/domain/usecases/auth/forgot_password_use_case.dart'
    as _i11;
import 'package:deyncare_app/domain/usecases/auth/get_available_payment_methods_use_case.dart'
    as _i17;
import 'package:deyncare_app/domain/usecases/auth/login_use_case.dart' as _i7;
import 'package:deyncare_app/domain/usecases/auth/logout_use_case.dart' as _i10;
import 'package:deyncare_app/domain/usecases/auth/process_payment_use_case.dart'
    as _i16;
import 'package:deyncare_app/domain/usecases/auth/refresh_token_use_case.dart'
    as _i14;
import 'package:deyncare_app/domain/usecases/auth/register_use_case.dart'
    as _i8;
import 'package:deyncare_app/domain/usecases/auth/reset_password_success_use_case.dart'
    as _i15;
import 'package:deyncare_app/domain/usecases/auth/verify_email_use_case.dart'
    as _i12;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUser_0 extends _i1.SmartFake implements _i2.User {
  _FakeUser_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAuthToken_1 extends _i1.SmartFake implements _i3.AuthToken {
  _FakeAuthToken_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRegistrationProgress_2 extends _i1.SmartFake
    implements _i4.RegistrationProgress {
  _FakeRegistrationProgress_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [CheckAuthStatusUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockCheckAuthStatusUseCase extends _i1.Mock
    implements _i5.CheckAuthStatusUseCase {
  MockCheckAuthStatusUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<bool> execute() =>
      (super.noSuchMethod(
            Invocation.method(#execute, []),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);
}

/// A class which mocks [LoginUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginUseCase extends _i1.Mock implements _i7.LoginUseCase {
  MockLoginUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<(_i2.User, _i3.AuthToken)> execute(
    String? email,
    String? password,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#execute, [email, password]),
            returnValue: _i6.Future<(_i2.User, _i3.AuthToken)>.value((
              _FakeUser_0(this, Invocation.method(#execute, [email, password])),
              _FakeAuthToken_1(
                this,
                Invocation.method(#execute, [email, password]),
              ),
            )),
          )
          as _i6.Future<(_i2.User, _i3.AuthToken)>);
}

/// A class which mocks [RegisterUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockRegisterUseCase extends _i1.Mock implements _i8.RegisterUseCase {
  MockRegisterUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<(_i2.User, _i4.RegistrationProgress)> execute({
    required String? fullName,
    required String? email,
    required String? phone,
    required String? password,
    required String? shopName,
    required String? shopAddress,
    _i9.File? shopLogo,
    String? planType = 'trial',
    String? paymentMethod = 'offline',
    bool? initialPaid = false,
    String? discountCode,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#execute, [], {
              #fullName: fullName,
              #email: email,
              #phone: phone,
              #password: password,
              #shopName: shopName,
              #shopAddress: shopAddress,
              #shopLogo: shopLogo,
              #planType: planType,
              #paymentMethod: paymentMethod,
              #initialPaid: initialPaid,
              #discountCode: discountCode,
            }),
            returnValue:
                _i6.Future<(_i2.User, _i4.RegistrationProgress)>.value((
                  _FakeUser_0(
                    this,
                    Invocation.method(#execute, [], {
                      #fullName: fullName,
                      #email: email,
                      #phone: phone,
                      #password: password,
                      #shopName: shopName,
                      #shopAddress: shopAddress,
                      #shopLogo: shopLogo,
                      #planType: planType,
                      #paymentMethod: paymentMethod,
                      #initialPaid: initialPaid,
                      #discountCode: discountCode,
                    }),
                  ),
                  _FakeRegistrationProgress_2(
                    this,
                    Invocation.method(#execute, [], {
                      #fullName: fullName,
                      #email: email,
                      #phone: phone,
                      #password: password,
                      #shopName: shopName,
                      #shopAddress: shopAddress,
                      #shopLogo: shopLogo,
                      #planType: planType,
                      #paymentMethod: paymentMethod,
                      #initialPaid: initialPaid,
                      #discountCode: discountCode,
                    }),
                  ),
                )),
          )
          as _i6.Future<(_i2.User, _i4.RegistrationProgress)>);
}

/// A class which mocks [LogoutUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLogoutUseCase extends _i1.Mock implements _i10.LogoutUseCase {
  MockLogoutUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<void> execute() =>
      (super.noSuchMethod(
            Invocation.method(#execute, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> executeLogoutAll() =>
      (super.noSuchMethod(
            Invocation.method(#executeLogoutAll, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}

/// A class which mocks [ForgotPasswordUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockForgotPasswordUseCase extends _i1.Mock
    implements _i11.ForgotPasswordUseCase {
  MockForgotPasswordUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<void> execute(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#execute, [email]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}

/// A class which mocks [VerifyEmailUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockVerifyEmailUseCase extends _i1.Mock
    implements _i12.VerifyEmailUseCase {
  MockVerifyEmailUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<(_i2.User, _i4.RegistrationProgress)> execute(
    String? email,
    String? verificationCode,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#execute, [email, verificationCode]),
            returnValue:
                _i6.Future<(_i2.User, _i4.RegistrationProgress)>.value((
                  _FakeUser_0(
                    this,
                    Invocation.method(#execute, [email, verificationCode]),
                  ),
                  _FakeRegistrationProgress_2(
                    this,
                    Invocation.method(#execute, [email, verificationCode]),
                  ),
                )),
          )
          as _i6.Future<(_i2.User, _i4.RegistrationProgress)>);

  @override
  _i6.Future<void> resendVerification(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#resendVerification, [email]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}

/// A class which mocks [ChangePasswordUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockChangePasswordUseCase extends _i1.Mock
    implements _i13.ChangePasswordUseCase {
  MockChangePasswordUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<void> execute({
    required String? currentPassword,
    required String? newPassword,
    required String? confirmPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#execute, [], {
              #currentPassword: currentPassword,
              #newPassword: newPassword,
              #confirmPassword: confirmPassword,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}

/// A class which mocks [RefreshTokenUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockRefreshTokenUseCase extends _i1.Mock
    implements _i14.RefreshTokenUseCase {
  MockRefreshTokenUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<_i3.AuthToken> execute(String? refreshToken) =>
      (super.noSuchMethod(
            Invocation.method(#execute, [refreshToken]),
            returnValue: _i6.Future<_i3.AuthToken>.value(
              _FakeAuthToken_1(
                this,
                Invocation.method(#execute, [refreshToken]),
              ),
            ),
          )
          as _i6.Future<_i3.AuthToken>);
}

/// A class which mocks [ResetPasswordSuccessUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockResetPasswordSuccessUseCase extends _i1.Mock
    implements _i15.ResetPasswordSuccessUseCase {
  MockResetPasswordSuccessUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<void> execute() =>
      (super.noSuchMethod(
            Invocation.method(#execute, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}

/// A class which mocks [ProcessPaymentUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockProcessPaymentUseCase extends _i1.Mock
    implements _i16.ProcessPaymentUseCase {
  MockProcessPaymentUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<(_i2.User, _i4.RegistrationProgress)> execute({
    required String? userId,
    required String? shopId,
    required String? planId,
    required String? paymentMethod,
    required String? phoneNumber,
    double? amount,
    String? discountCode,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#execute, [], {
              #userId: userId,
              #shopId: shopId,
              #planId: planId,
              #paymentMethod: paymentMethod,
              #phoneNumber: phoneNumber,
              #amount: amount,
              #discountCode: discountCode,
            }),
            returnValue:
                _i6.Future<(_i2.User, _i4.RegistrationProgress)>.value((
                  _FakeUser_0(
                    this,
                    Invocation.method(#execute, [], {
                      #userId: userId,
                      #shopId: shopId,
                      #planId: planId,
                      #paymentMethod: paymentMethod,
                      #phoneNumber: phoneNumber,
                      #amount: amount,
                      #discountCode: discountCode,
                    }),
                  ),
                  _FakeRegistrationProgress_2(
                    this,
                    Invocation.method(#execute, [], {
                      #userId: userId,
                      #shopId: shopId,
                      #planId: planId,
                      #paymentMethod: paymentMethod,
                      #phoneNumber: phoneNumber,
                      #amount: amount,
                      #discountCode: discountCode,
                    }),
                  ),
                )),
          )
          as _i6.Future<(_i2.User, _i4.RegistrationProgress)>);
}

/// A class which mocks [GetAvailablePaymentMethodsUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetAvailablePaymentMethodsUseCase extends _i1.Mock
    implements _i17.GetAvailablePaymentMethodsUseCase {
  MockGetAvailablePaymentMethodsUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<List<String>> execute(String? context) =>
      (super.noSuchMethod(
            Invocation.method(#execute, [context]),
            returnValue: _i6.Future<List<String>>.value(<String>[]),
          )
          as _i6.Future<List<String>>);
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i18.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<bool> isLoggedIn() =>
      (super.noSuchMethod(
            Invocation.method(#isLoggedIn, []),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<_i2.User?> getCurrentUser() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentUser, []),
            returnValue: _i6.Future<_i2.User?>.value(),
          )
          as _i6.Future<_i2.User?>);

  @override
  _i6.Future<(_i2.User, _i3.AuthToken)> login(
    String? email,
    String? password,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#login, [email, password]),
            returnValue: _i6.Future<(_i2.User, _i3.AuthToken)>.value((
              _FakeUser_0(this, Invocation.method(#login, [email, password])),
              _FakeAuthToken_1(
                this,
                Invocation.method(#login, [email, password]),
              ),
            )),
          )
          as _i6.Future<(_i2.User, _i3.AuthToken)>);

  @override
  _i6.Future<(_i2.User, _i4.RegistrationProgress)> register({
    required String? fullName,
    required String? email,
    required String? phone,
    required String? password,
    required String? shopName,
    required String? shopAddress,
    _i9.File? shopLogo,
    String? planType = 'trial',
    String? paymentMethod = 'offline',
    bool? initialPaid = false,
    String? discountCode,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#register, [], {
              #fullName: fullName,
              #email: email,
              #phone: phone,
              #password: password,
              #shopName: shopName,
              #shopAddress: shopAddress,
              #shopLogo: shopLogo,
              #planType: planType,
              #paymentMethod: paymentMethod,
              #initialPaid: initialPaid,
              #discountCode: discountCode,
            }),
            returnValue:
                _i6.Future<(_i2.User, _i4.RegistrationProgress)>.value((
                  _FakeUser_0(
                    this,
                    Invocation.method(#register, [], {
                      #fullName: fullName,
                      #email: email,
                      #phone: phone,
                      #password: password,
                      #shopName: shopName,
                      #shopAddress: shopAddress,
                      #shopLogo: shopLogo,
                      #planType: planType,
                      #paymentMethod: paymentMethod,
                      #initialPaid: initialPaid,
                      #discountCode: discountCode,
                    }),
                  ),
                  _FakeRegistrationProgress_2(
                    this,
                    Invocation.method(#register, [], {
                      #fullName: fullName,
                      #email: email,
                      #phone: phone,
                      #password: password,
                      #shopName: shopName,
                      #shopAddress: shopAddress,
                      #shopLogo: shopLogo,
                      #planType: planType,
                      #paymentMethod: paymentMethod,
                      #initialPaid: initialPaid,
                      #discountCode: discountCode,
                    }),
                  ),
                )),
          )
          as _i6.Future<(_i2.User, _i4.RegistrationProgress)>);

  @override
  _i6.Future<_i2.User> createEmployee({
    required String? fullName,
    required String? email,
    required String? phone,
    String? password,
    bool? generatePassword = true,
    List<String>? permissions,
    String? position,
    String? note,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createEmployee, [], {
              #fullName: fullName,
              #email: email,
              #phone: phone,
              #password: password,
              #generatePassword: generatePassword,
              #permissions: permissions,
              #position: position,
              #note: note,
            }),
            returnValue: _i6.Future<_i2.User>.value(
              _FakeUser_0(
                this,
                Invocation.method(#createEmployee, [], {
                  #fullName: fullName,
                  #email: email,
                  #phone: phone,
                  #password: password,
                  #generatePassword: generatePassword,
                  #permissions: permissions,
                  #position: position,
                  #note: note,
                }),
              ),
            ),
          )
          as _i6.Future<_i2.User>);

  @override
  _i6.Future<(_i2.User, _i4.RegistrationProgress)> verifyEmail(
    String? email,
    String? verificationCode,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#verifyEmail, [email, verificationCode]),
            returnValue:
                _i6.Future<(_i2.User, _i4.RegistrationProgress)>.value((
                  _FakeUser_0(
                    this,
                    Invocation.method(#verifyEmail, [email, verificationCode]),
                  ),
                  _FakeRegistrationProgress_2(
                    this,
                    Invocation.method(#verifyEmail, [email, verificationCode]),
                  ),
                )),
          )
          as _i6.Future<(_i2.User, _i4.RegistrationProgress)>);

  @override
  _i6.Future<void> resendVerification(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#resendVerification, [email]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<bool> checkEmailExists(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#checkEmailExists, [email]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<void> forgotPassword(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#forgotPassword, [email]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> resetPassword({
    required String? token,
    required String? newPassword,
    required String? confirmPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#resetPassword, [], {
              #token: token,
              #newPassword: newPassword,
              #confirmPassword: confirmPassword,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i3.AuthToken> refreshToken(String? refreshToken) =>
      (super.noSuchMethod(
            Invocation.method(#refreshToken, [refreshToken]),
            returnValue: _i6.Future<_i3.AuthToken>.value(
              _FakeAuthToken_1(
                this,
                Invocation.method(#refreshToken, [refreshToken]),
              ),
            ),
          )
          as _i6.Future<_i3.AuthToken>);

  @override
  _i6.Future<void> logout() =>
      (super.noSuchMethod(
            Invocation.method(#logout, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> logoutAll() =>
      (super.noSuchMethod(
            Invocation.method(#logoutAll, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> changePassword({
    required String? currentPassword,
    required String? newPassword,
    required String? confirmPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#changePassword, [], {
              #currentPassword: currentPassword,
              #newPassword: newPassword,
              #confirmPassword: confirmPassword,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<(_i2.User, _i4.RegistrationProgress)> processPayment({
    required String? userId,
    required String? shopId,
    required String? planId,
    required String? paymentMethod,
    required String? phoneNumber,
    double? amount,
    String? discountCode,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#processPayment, [], {
              #userId: userId,
              #shopId: shopId,
              #planId: planId,
              #paymentMethod: paymentMethod,
              #phoneNumber: phoneNumber,
              #amount: amount,
              #discountCode: discountCode,
            }),
            returnValue:
                _i6.Future<(_i2.User, _i4.RegistrationProgress)>.value((
                  _FakeUser_0(
                    this,
                    Invocation.method(#processPayment, [], {
                      #userId: userId,
                      #shopId: shopId,
                      #planId: planId,
                      #paymentMethod: paymentMethod,
                      #phoneNumber: phoneNumber,
                      #amount: amount,
                      #discountCode: discountCode,
                    }),
                  ),
                  _FakeRegistrationProgress_2(
                    this,
                    Invocation.method(#processPayment, [], {
                      #userId: userId,
                      #shopId: shopId,
                      #planId: planId,
                      #paymentMethod: paymentMethod,
                      #phoneNumber: phoneNumber,
                      #amount: amount,
                      #discountCode: discountCode,
                    }),
                  ),
                )),
          )
          as _i6.Future<(_i2.User, _i4.RegistrationProgress)>);

  @override
  _i6.Future<List<String>> getAvailablePaymentMethods(String? context) =>
      (super.noSuchMethod(
            Invocation.method(#getAvailablePaymentMethods, [context]),
            returnValue: _i6.Future<List<String>>.value(<String>[]),
          )
          as _i6.Future<List<String>>);
}
