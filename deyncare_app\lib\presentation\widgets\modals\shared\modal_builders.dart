import 'package:flutter/material.dart';
import 'package:wolt_modal_sheet/wolt_modal_sheet.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_constants.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';

/// Enhanced modal sheet builder utility functions with modern design
class ModalBuilders {
  
  /// Build a modern enhanced SliverWoltModalSheetPage
  static SliverWoltModalSheetPage buildModernPage({
    required BuildContext context,
    required String title,
    required Widget child,
    String? subtitle,
    Color? titleColor,
    List<Widget>? trailingActions,
    bool hasCloseButton = true,
    double? maxHeight,
  }) {
    return WoltModalSheetPage(
      backgroundColor: ThemeUtils.getBackgroundColor(context, type: BackgroundType.surface),
      surfaceTintColor: Colors.transparent,
      topBarTitle: _buildModernHeader(
        context: context,
        title: title,
        subtitle: subtitle,
        titleColor: titleColor,
      ),
      isTopBarLayerAlwaysVisible: true,
      trailingNavBarWidget: hasCloseButton 
        ? _buildModernTrailingActions(context, trailingActions)
        : null,
      child: Container(
        constraints: maxHeight != null 
          ? BoxConstraints(maxHeight: maxHeight)
          : null,
        child: _buildModernContent(context, child),
      ),
    );
  }

  /// Build a modern page with enhanced actions
  static SliverWoltModalSheetPage buildModernPageWithActions({
    required BuildContext context,
    required String title,
    required Widget child,
    required List<Widget> actions,
    String? subtitle,
    Color? titleColor,
    bool hasCloseButton = true,
    double? maxHeight,
  }) {
    return WoltModalSheetPage(
      backgroundColor: ThemeUtils.getBackgroundColor(context, type: BackgroundType.surface),
      surfaceTintColor: Colors.transparent,
      topBarTitle: _buildModernHeader(
        context: context,
        title: title,
        subtitle: subtitle,
        titleColor: titleColor,
      ),
      isTopBarLayerAlwaysVisible: true,
      trailingNavBarWidget: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          ...actions.map((action) => Padding(
            padding: const EdgeInsets.only(right: 8),
            child: action,
          )),
          if (hasCloseButton) ModalConstants.closeIconButton(context),
        ],
      ),
      child: Container(
        constraints: maxHeight != null 
          ? BoxConstraints(maxHeight: maxHeight)
          : null,
        child: _buildModernContent(context, child),
      ),
    );
  }

  /// Build a modern full-screen modal page
  static SliverWoltModalSheetPage buildModernFullScreenPage({
    required BuildContext context,
    required String title,
    required Widget child,
    String? subtitle,
    Color? titleColor,
    List<Widget>? trailingActions,
    bool hasCloseButton = true,
  }) {
    return WoltModalSheetPage(
      backgroundColor: ThemeUtils.getBackgroundColor(context, type: BackgroundType.surface),
      surfaceTintColor: Colors.transparent,
      forceMaxHeight: true,
      topBarTitle: _buildModernHeader(
        context: context,
        title: title,
        subtitle: subtitle,
        titleColor: titleColor,
      ),
      isTopBarLayerAlwaysVisible: true,
      trailingNavBarWidget: hasCloseButton 
        ? _buildModernTrailingActions(context, trailingActions)
        : null,
      child: Container(
        height: MediaQuery.of(context).size.height * 0.9,
        child: _buildModernContent(context, child),
      ),
    );
  }

  /// Build a modern bottom sheet style modal
  static SliverWoltModalSheetPage buildModernBottomSheet({
    required BuildContext context,
    required String title,
    required Widget child,
    String? subtitle,
    Color? titleColor,
    List<Widget>? trailingActions,
    bool hasCloseButton = true,
    bool hasHandle = true,
  }) {
    return WoltModalSheetPage(
      backgroundColor: AppThemes.cardColor,
      surfaceTintColor: Colors.transparent,
      topBarTitle: Column(
        children: [
          if (hasHandle) ...[
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: AppThemes.dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
          _buildModernHeader(
            context: context,
            title: title,
            subtitle: subtitle,
            titleColor: titleColor,
          ),
        ],
      ),
      isTopBarLayerAlwaysVisible: true,
      trailingNavBarWidget: hasCloseButton 
        ? _buildModernTrailingActions(context, trailingActions)
        : null,
      child: _buildModernContent(context, child),
    );
  }

  /// Show a modern modal dialog with enhanced animations
  static void showModernModal({
    required BuildContext context,
    required SliverWoltModalSheetPage page,
    bool barrierDismissible = true,
    WoltModalType? modalType,
  }) {
    WoltModalSheet.show<void>(
      context: context,
      pageListBuilder: (modalSheetContext) => [page],
      modalTypeBuilder: (context) => modalType ?? WoltModalType.dialog(),
      onModalDismissedWithBarrierTap: barrierDismissible 
        ? () => Navigator.of(context).pop()
        : null,
      barrierDismissible: barrierDismissible,
    );
  }

  /// Show a modern bottom sheet modal
  static void showModernBottomSheet({
    required BuildContext context,
    required SliverWoltModalSheetPage page,
    bool barrierDismissible = true,
  }) {
    WoltModalSheet.show<void>(
      context: context,
      pageListBuilder: (modalSheetContext) => [page],
      modalTypeBuilder: (context) => WoltModalType.bottomSheet(),
      onModalDismissedWithBarrierTap: barrierDismissible 
        ? () => Navigator.of(context).pop()
        : null,
      barrierDismissible: barrierDismissible,
    );
  }

  /// Show a modern side sheet modal
  static void showModernSideSheet({
    required BuildContext context,
    required SliverWoltModalSheetPage page,
    bool barrierDismissible = true,
  }) {
    WoltModalSheet.show<void>(
      context: context,
      pageListBuilder: (modalSheetContext) => [page],
      modalTypeBuilder: (context) => WoltModalType.sideSheet(),
      onModalDismissedWithBarrierTap: barrierDismissible 
        ? () => Navigator.of(context).pop()
        : null,
      barrierDismissible: barrierDismissible,
    );
  }

  /// Build modern header with enhanced typography
  static Widget _buildModernHeader({
    required BuildContext context,
    required String title,
    String? subtitle,
    Color? titleColor,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            title,
            style: ModalConstants.modalTitleStyle(context, color: titleColor),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: ModalConstants.subtitleStyle(context),
            ),
          ],
        ],
      ),
    );
  }

  /// Build modern trailing actions with enhanced styling
  static Widget _buildModernTrailingActions(BuildContext context, List<Widget>? actions) {
    if (actions == null || actions.isEmpty) {
      return ModalConstants.closeIconButton(context);
    }
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...actions.map((action) => Padding(
          padding: const EdgeInsets.only(right: 8),
          child: action,
        )),
        ModalConstants.closeIconButton(context),
      ],
    );
  }

  /// Build modern content wrapper with enhanced styling
  static Widget _buildModernContent(BuildContext context, Widget child) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppThemes.backgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(ModalConstants.largeRadius),
          topRight: Radius.circular(ModalConstants.largeRadius),
        ),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(ModalConstants.largeRadius),
          topRight: Radius.circular(ModalConstants.largeRadius),
        ),
        child: child,
      ),
    );
  }

  /// Build modern action buttons row
  static Widget buildModernActionRow({
    required List<Widget> actions,
    MainAxisAlignment alignment = MainAxisAlignment.spaceEvenly,
    EdgeInsets padding = const EdgeInsets.all(20),
  }) {
    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: AppThemes.cardColor,
        border: Border(
          top: BorderSide(
            color: AppThemes.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: alignment,
        children: actions.map((action) => Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 6),
            child: action,
          ),
        )).toList(),
      ),
    );
  }

  /// Build modern loading state
  static Widget buildModernLoadingState({
    required BuildContext context,
    String? message,
    Color? color,
  }) {
    return Container(
      padding: ModalConstants.extraLargePadding,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 40,
            height: 40,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(
                color ?? AppThemes.primaryColor,
              ),
            ),
          ),
          if (message != null) ...[
            ModalConstants.largeSpacing,
            Text(
              message,
              style: ModalConstants.subtitleStyle(context),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  /// Build modern empty state
  static Widget buildModernEmptyState({
    required BuildContext context,
    required String title,
    required String subtitle,
    IconData? icon,
    Widget? action,
  }) {
    return Container(
      padding: ModalConstants.extraLargePadding,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppThemes.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(ModalConstants.largeRadius),
              ),
              child: Icon(
                icon,
                size: 48,
                color: AppThemes.primaryColor,
              ),
            ),
            ModalConstants.largeSpacing,
          ],
          Text(
            title,
            style: ModalConstants.sectionTitleStyle(context),
            textAlign: TextAlign.center,
          ),
          ModalConstants.defaultSpacing,
          Text(
            subtitle,
            style: ModalConstants.subtitleStyle(context),
            textAlign: TextAlign.center,
          ),
          if (action != null) ...[
            ModalConstants.largeSpacing,
            action,
          ],
        ],
      ),
    );
  }
} 