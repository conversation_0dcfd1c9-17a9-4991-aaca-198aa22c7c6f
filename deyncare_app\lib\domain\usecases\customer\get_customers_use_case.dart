import 'package:dartz/dartz.dart';
import 'package:deyncare_app/domain/repositories/customer_repository.dart';
import 'package:deyncare_app/data/models/customer_model.dart';
import 'package:deyncare_app/core/errors/failures.dart';

/// Use case for retrieving customers with pagination and filtering - BACKEND ALIGNED
class GetCustomersUseCase {
  final CustomerRepository _repository;

  GetCustomersUseCase(this._repository);

  /// Execute the use case to get customers
  Future<Either<Failure, CustomerListResponse>> execute({
    int page = 1,
    int limit = 20,
    String? search,
    String? customerType,
    String? riskLevel,
    String? category,
    String? sortBy,
    bool ascending = true,
    bool? hasDebt,
    bool? hasOutstandingBalance,
  }) async {
    // Create query parameters object
    final params = CustomerQueryParams(
      page: page,
      limit: limit,
      search: search,
      customerType: customerType,
      riskLevel: riskLevel,
      category: category,
      sortBy: sortBy ?? 'createdAt',
      sortOrder: ascending ? 'asc' : 'desc',
      hasDebt: hasDebt,
      hasOutstandingBalance: hasOutstandingBalance,
    );

    return await _repository.getCustomers(params: params);
  }
} 