import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logger/logger.dart';

/// Service for caching dashboard data to improve performance and offline access
class DashboardCacheService {
  final FlutterSecureStorage _storage;
  final Logger _logger = Logger();
  
  // Cache keys
  static const String _kpiCacheKey = 'dashboard_kpi_cache';
  static const String _activityCacheKey = 'dashboard_activity_cache';
  static const String _chartsCacheKey = 'dashboard_charts_cache';
  static const String _notificationsCacheKey = 'dashboard_notifications_cache';
  
  // Cache metadata keys
  static const String _kpiTimestampKey = 'dashboard_kpi_timestamp';
  static const String _activityTimestampKey = 'dashboard_activity_timestamp';
  static const String _chartsTimestampKey = 'dashboard_charts_timestamp';
  static const String _notificationsTimestampKey = 'dashboard_notifications_timestamp';
  
  DashboardCacheService({FlutterSecureStorage? storage})
      : _storage = storage ?? const FlutterSecureStorage();

  /// Cache KPI data with timestamp
  Future<void> cacheKPIData(Map<String, dynamic> data) async {
    try {
      await _storage.write(key: _kpiCacheKey, value: jsonEncode(data));
      await _storage.write(key: _kpiTimestampKey, value: DateTime.now().millisecondsSinceEpoch.toString());
      _logger.d('DashboardCache: KPI data cached successfully');
    } catch (e) {
      _logger.e('DashboardCache: Failed to cache KPI data: $e');
    }
  }

  /// Cache activity data with timestamp
  Future<void> cacheActivityData(List<Map<String, dynamic>> data) async {
    try {
      await _storage.write(key: _activityCacheKey, value: jsonEncode(data));
      await _storage.write(key: _activityTimestampKey, value: DateTime.now().millisecondsSinceEpoch.toString());
      _logger.d('DashboardCache: Activity data cached successfully');
    } catch (e) {
      _logger.e('DashboardCache: Failed to cache activity data: $e');
    }
  }

  /// Cache charts data with timestamp
  Future<void> cacheChartsData(List<Map<String, dynamic>> data) async {
    try {
      await _storage.write(key: _chartsCacheKey, value: jsonEncode(data));
      await _storage.write(key: _chartsTimestampKey, value: DateTime.now().millisecondsSinceEpoch.toString());
      _logger.d('DashboardCache: Charts data cached successfully');
    } catch (e) {
      _logger.e('DashboardCache: Failed to cache charts data: $e');
    }
  }

  /// Cache notifications data with timestamp
  Future<void> cacheNotificationsData(List<Map<String, dynamic>> data) async {
    try {
      await _storage.write(key: _notificationsCacheKey, value: jsonEncode(data));
      await _storage.write(key: _notificationsTimestampKey, value: DateTime.now().millisecondsSinceEpoch.toString());
      _logger.d('DashboardCache: Notifications data cached successfully');
    } catch (e) {
      _logger.e('DashboardCache: Failed to cache notifications data: $e');
    }
  }

  /// Get cached KPI data if not expired
  Future<Map<String, dynamic>?> getCachedKPIData({Duration maxAge = const Duration(minutes: 5)}) async {
    return await _getCachedData(_kpiCacheKey, _kpiTimestampKey, maxAge);
  }

  /// Get cached activity data if not expired
  Future<List<Map<String, dynamic>>?> getCachedActivityData({Duration maxAge = const Duration(minutes: 5)}) async {
    final data = await _getCachedData(_activityCacheKey, _activityTimestampKey, maxAge);
    if (data != null && data is List) {
      return List<Map<String, dynamic>>.from(data);
    }
    return null;
  }

  /// Get cached charts data if not expired
  Future<List<Map<String, dynamic>>?> getCachedChartsData({Duration maxAge = const Duration(minutes: 10)}) async {
    final data = await _getCachedData(_chartsCacheKey, _chartsTimestampKey, maxAge);
    if (data != null && data is List) {
      return List<Map<String, dynamic>>.from(data);
    }
    return null;
  }

  /// Get cached notifications data if not expired
  Future<List<Map<String, dynamic>>?> getCachedNotificationsData({Duration maxAge = const Duration(minutes: 2)}) async {
    final data = await _getCachedData(_notificationsCacheKey, _notificationsTimestampKey, maxAge);
    if (data != null && data is List) {
      return List<Map<String, dynamic>>.from(data);
    }
    return null;
  }

  /// Generic method to get cached data with expiry check
  Future<dynamic> _getCachedData(String dataKey, String timestampKey, Duration maxAge) async {
    try {
      final timestampStr = await _storage.read(key: timestampKey);
      if (timestampStr == null) return null;

      final timestamp = DateTime.fromMillisecondsSinceEpoch(int.parse(timestampStr));
      final now = DateTime.now();
      
      if (now.difference(timestamp) > maxAge) {
        _logger.d('DashboardCache: Cache expired for key: $dataKey');
        return null;
      }

      final dataStr = await _storage.read(key: dataKey);
      if (dataStr == null) return null;

      final data = jsonDecode(dataStr);
      _logger.d('DashboardCache: Retrieved cached data for key: $dataKey');
      return data;
    } catch (e) {
      _logger.e('DashboardCache: Failed to get cached data for key: $dataKey - $e');
      return null;
    }
  }

  /// Check if specific data type is cached and not expired
  Future<bool> isCached(String component, {Duration maxAge = const Duration(minutes: 5)}) async {
    switch (component) {
      case 'kpi':
        return await getCachedKPIData(maxAge: maxAge) != null;
      case 'activity':
        return await getCachedActivityData(maxAge: maxAge) != null;
      case 'charts':
        return await getCachedChartsData(maxAge: maxAge) != null;
      case 'notifications':
        return await getCachedNotificationsData(maxAge: maxAge) != null;
      default:
        return false;
    }
  }

  /// Clear specific cache
  Future<void> clearCache(String component) async {
    try {
      switch (component) {
        case 'kpi':
          await _storage.delete(key: _kpiCacheKey);
          await _storage.delete(key: _kpiTimestampKey);
          break;
        case 'activity':
          await _storage.delete(key: _activityCacheKey);
          await _storage.delete(key: _activityTimestampKey);
          break;
        case 'charts':
          await _storage.delete(key: _chartsCacheKey);
          await _storage.delete(key: _chartsTimestampKey);
          break;
        case 'notifications':
          await _storage.delete(key: _notificationsCacheKey);
          await _storage.delete(key: _notificationsTimestampKey);
          break;
      }
      _logger.d('DashboardCache: Cleared cache for component: $component');
    } catch (e) {
      _logger.e('DashboardCache: Failed to clear cache for component: $component - $e');
    }
  }

  /// Clear all dashboard cache
  Future<void> clearAllCache() async {
    try {
      await Future.wait([
        _storage.delete(key: _kpiCacheKey),
        _storage.delete(key: _kpiTimestampKey),
        _storage.delete(key: _activityCacheKey),
        _storage.delete(key: _activityTimestampKey),
        _storage.delete(key: _chartsCacheKey),
        _storage.delete(key: _chartsTimestampKey),
        _storage.delete(key: _notificationsCacheKey),
        _storage.delete(key: _notificationsTimestampKey),
      ]);
      _logger.d('DashboardCache: All cache cleared successfully');
    } catch (e) {
      _logger.e('DashboardCache: Failed to clear all cache: $e');
    }
  }

  /// Get cache status for debugging
  Future<Map<String, dynamic>> getCacheStatus() async {
    final futures = [
      isCached('kpi'),
      isCached('activity'),
      isCached('charts'),
      isCached('notifications'),
    ];
    
    final results = await Future.wait(futures);
    
    return {
      'kpi': results[0],
      'activity': results[1],
      'charts': results[2],
      'notifications': results[3],
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
} 