import 'package:deyncare_app/data/models/customer_model.dart';
import 'package:deyncare_app/data/network/clients/dio_client.dart';

/// Customer Remote Data Source - handles customer API calls
class CustomerRemoteDataSource {
  final DioClient _dioClient;

  CustomerRemoteDataSource({required DioClient dioClient})
      : _dioClient = dioClient;

  /// GET /api/customers
  Future<CustomerListResponse> getAllCustomers({
    required int page,
    required int limit,
    required SearchCriteria criteria,
  }) async {
    final queryParams = <String, dynamic>{
      'page': page,
      'limit': limit,
    };

    if (criteria.search != null && criteria.search!.isNotEmpty) {
      queryParams['search'] = criteria.search;
    }
    if (criteria.riskLevel != null && criteria.riskLevel!.isNotEmpty) {
      queryParams['riskLevel'] = criteria.riskLevel;
    }
    if (criteria.customerType != null && criteria.customerType!.isNotEmpty) {
      queryParams['customerType'] = criteria.customerType;
    }

    final response = await _dioClient.get(
      '/customers',
      queryParameters: queryParams,
    );

    // DioClient returns processed response data directly, not wrapped in .data
    return CustomerListResponse.fromJson(response);
  }

  /// GET /api/customers/:customerId
  Future<Map<String, dynamic>> getCustomerById(String customerId) async {
    final response = await _dioClient.get('/customers/$customerId');
    return response as Map<String, dynamic>;
  }

  /// POST /api/customers
  Future<Map<String, dynamic>> createCustomer(
      CreateCustomerRequest request) async {
    final response = await _dioClient.post(
      '/customers',
      data: request.toJson(),
    );
    return response as Map<String, dynamic>;
  }

  /// PUT /api/customers/:customerId
  Future<Map<String, dynamic>> updateCustomer(
    String customerId,
    UpdateCustomerRequest request,
  ) async {
    final response = await _dioClient.put(
      '/customers/$customerId',
      data: request.toJson(),
    );
    return response as Map<String, dynamic>;
  }

  /// DELETE /api/customers/:customerId
  /// Supports force parameter for customers with debt history
  Future<Map<String, dynamic>> deleteCustomer(
    String customerId, {
    bool force = false,
  }) async {
    final queryParams = <String, dynamic>{};
    if (force) {
      queryParams['force'] = 'true';
    }

    final response = await _dioClient.delete(
      '/customers/$customerId',
      queryParameters: queryParams.isNotEmpty ? queryParams : null,
    );
    return response as Map<String, dynamic>;
  }
}
