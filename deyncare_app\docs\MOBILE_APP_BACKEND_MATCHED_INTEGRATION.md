# 📱 DeynCare Mobile App - 100% Backend-Matched Integration Guide

## 🎯 **CRITICAL: Backend Reality Check**

This guide reflects the **EXACT** backend implementation - no assumptions, no outdated information.

### ✅ **What Actually EXISTS in Backend:**
- **FCM Endpoints**: Only 2 endpoints exist
  - `POST /api/fcm/register` ✅
  - `POST /api/fcm/test` ✅
- **Push Notification Endpoints**: 6 admin endpoints exist
- **User Roles**: Admin, SuperAdmin, Employee only (NO customers)
- **Device Platforms**: `android`, `ios` only (NO web)
- **Debt Reminder Types**: `7_days`, `3_days`, `overdue`

### ❌ **What Does NOT EXIST (Will Return 404):**
- ~~`DELETE /api/fcm/unregister`~~ ❌
- ~~`PUT /api/fcm/usage`~~ ❌  
- ~~`GET /api/fcm/tokens`~~ ❌
- ~~Any customer notification APIs~~ ❌

---

## 🚀 **Quick Setup (Verified & Working)**

### 1. **Firebase Configuration**
```bash
# Download from Firebase Console:
# Android: google-services.json → android/app/
# iOS: GoogleService-Info.plist → ios/Runner/

Project ID: deyncare-47d99
Project Number: 1089951025738
```

### 2. **Flutter Dependencies**
```yaml
dependencies:
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10
  flutter_local_notifications: ^16.3.2
  dio: ^5.4.0
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
```

---

## 🔧 **Core Implementation (Backend-Matched)**

### **Updated Notification Service**

```dart
// lib/services/notification_service.dart
class NotificationService {
  final Dio _dio;
  
  /// ✅ ONLY EXISTING ENDPOINT: Register FCM token
  Future<Map<String, dynamic>> registerFCMToken() async {
    final response = await _dio.post('/fcm/register', data: {
      'token': FirebaseService.fcmToken,
      'deviceInfo': {
        'platform': Platform.isAndroid ? 'android' : 'ios', // Only these 2 values accepted
        'deviceId': 'device_unique_id',
        'appVersion': '1.0.0',
        'osVersion': 'system_version',
        'deviceModel': 'device_model',
        'deviceName': 'device_name',
      },
    });
    return response.data;
  }

  /// ✅ ONLY EXISTING ENDPOINT: Send test notification  
  Future<Map<String, dynamic>> sendTestNotification() async {
    final response = await _dio.post('/fcm/test');
    return response.data;
  }

  // ❌ REMOVED: unregisterFCMToken() - Endpoint doesn't exist
  // ❌ REMOVED: updateTokenUsage() - Endpoint doesn't exist  
  // ❌ REMOVED: getUserTokens() - Endpoint doesn't exist
}
```

### **Firebase Service (Already Correct)**

```dart
// lib/services/firebase_service.dart  
class FirebaseService {
  static String? _fcmToken;
  
  static Future<void> initialize() async {
    await Firebase.initializeApp();
    await _requestPermissions();
    await _getFCMToken();
    _setupMessageHandlers();
  }

  // Handles all notification types correctly:
  // debt_created, payment_recorded, debt_reminder, test, custom
}
```

---

## 🔗 **Exact API Endpoints (Tested & Working)**

### **FCM Token Registration**
```http
POST /api/fcm/register
Authorization: Bearer <user_token>
Content-Type: application/json

{
  "token": "fcm_device_token_here",
  "deviceInfo": {
    "platform": "android",     // ONLY: "android" | "ios"
    "deviceId": "unique_id",
    "appVersion": "1.0.0",
    "osVersion": "13.0",
    "deviceModel": "Samsung Galaxy S21",
    "deviceName": "User Device"
  }
}

Response: {
  "success": true,
  "message": "FCM token registered successfully",
  "data": { "tokenId": "token_123" }
}
```

### **Test Notification**
```http
POST /api/fcm/test
Authorization: Bearer <user_token>

Response: {
  "success": true,
  "message": "Test notification sent successfully"
}
```

---

## 📊 **Notification Types (Backend-Matched)**

### **1. Debt Creation Notification**
```json
{
  "type": "debt_created",
  "title": "💰 New Debt Created",
  "body": "Ahmed Hassan - $150.00 due Dec 28, 2024",
  "data": {
    "debtId": "debt_123",
    "customerId": "customer_456",
    "amount": "150.00",
    "dueDate": "2024-12-28",
    "shopId": "shop_789"
  }
}
```

### **2. Payment Recording Notification**
```json
{
  "type": "payment_recorded",
  "title": "💳 Payment Recorded",
  "body": "Ahmed Hassan paid $75.00 - Risk: Low ✅",
  "data": {
    "paymentId": "payment_789",
    "customerId": "customer_456", 
    "amount": "75.00",
    "riskStatus": "low",
    "riskScore": 25
  }
}
```

### **3. Debt Reminder Notification**
```json
{
  "type": "debt_reminder",
  "title": "⏰ Debt Reminder",
  "body": "3 debts due in 3 days - Total: $450.00",
  "data": {
    "reminderType": "3_days",    // "7_days" | "3_days" | "overdue"
    "debtCount": 3,
    "totalAmount": "450.00",
    "shopId": "shop_789"
  }
}
```

### **4. Test Notification**
```json
{
  "type": "test", 
  "title": "🧪 Test Notification",
  "body": "This is a test notification from DeynCare",
  "data": {
    "timestamp": "2024-12-28T10:00:00Z"
  }
}
```

### **5. Custom Notification**
```json
{
  "type": "custom",
  "title": "📢 System Update", 
  "body": "New features available in your DeynCare app",
  "data": {
    "actionUrl": "/updates",
    "priority": "normal"
  }
}
```

---

## 🧪 **Testing Procedures (Verified)**

### **1. Test Firebase Connection**
```bash
curl -X GET https://your-backend-url.com/api/admin/notifications/push/test \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

Expected Response:
{
  "success": true,
  "message": "Firebase connection successful"
}
```

### **2. Test FCM Registration**
```bash
curl -X POST https://your-backend-url.com/api/fcm/register \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "test_fcm_token",
    "deviceInfo": {
      "platform": "android",
      "deviceId": "test_device",
      "appVersion": "1.0.0"
    }
  }'

Expected Response:
{
  "success": true,
  "message": "FCM token registered successfully"
}
```

### **3. Test Notification Sending**
```bash
curl -X POST https://your-backend-url.com/api/fcm/test \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

Expected Response:
{
  "success": true,
  "message": "Test notification sent successfully"
}
```

---

## 🔒 **Role-Based Access Control**

### **Allowed Roles (Backend Validated):**
- ✅ **SuperAdmin**: Full access to all notification features
- ✅ **Admin**: Shop-specific notification access
- ✅ **Employee**: Shop-specific notification access

### **Restricted Roles:**
- ❌ **Customer**: Cannot use mobile app or receive notifications
- ❌ **Public**: No access to any notification features

### **Backend Role Validation:**
```javascript
// Backend validates user role before allowing FCM registration
if (!['superAdmin', 'admin', 'employee'].includes(user.role)) {
  return res.status(403).json({
    success: false,
    message: 'Unauthorized: Only admin users can register for notifications'
  });
}
```

---

## 🚀 **Complete Flutter Integration**

### **Main App Setup**
```dart
// main.dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase first
  await FirebaseService.initialize();
  
  runApp(DeynCareApp());
}

class DeynCareApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'DeynCare Admin',
      home: LoginScreen(),
    );
  }
}
```

### **Login Integration**
```dart
// After successful login
class AuthService {
  static Future<void> onLoginSuccess(String userToken) async {
    // Store auth token
    ApiService.setAuthToken(userToken);
    
    // Register FCM token with backend
    final notificationService = NotificationService(dio);
    await notificationService.registerFCMToken();
    
    debugPrint('✅ User logged in and FCM token registered');
  }
}
```

### **Testing Screen**
```dart
class NotificationTestScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Notification Test')),
      body: Column(
        children: [
          Text('FCM Token: ${FirebaseService.fcmToken}'),
          Text('Firebase Ready: ${FirebaseService.isInitialized}'),
          
          ElevatedButton(
            onPressed: () async {
              final service = NotificationService(dio);
              await service.registerFCMToken();
            },
            child: Text('Register FCM Token'),
          ),
          
          ElevatedButton(
            onPressed: () async {
              final service = NotificationService(dio);
              await service.sendTestNotification();
            },
            child: Text('Send Test Notification'),
          ),
        ],
      ),
    );
  }
}
```

---

## 🔍 **Troubleshooting (Real Issues)**

### **Common Errors & Solutions:**

#### **1. 404 Error on FCM endpoints**
```
❌ Error: POST /api/fcm/unregister 404 Not Found
✅ Solution: Remove unregister calls - endpoint doesn't exist
```

#### **2. 403 Forbidden Error**
```
❌ Error: 403 Unauthorized - Only admin users can register
✅ Solution: Ensure user has Admin/SuperAdmin/Employee role
```

#### **3. Invalid platform validation**
```
❌ Error: Platform 'web' not supported
✅ Solution: Use only 'android' or 'ios'
```

#### **4. FCM token not generated**
```
❌ Error: FCM token is null
✅ Solution: Check Firebase config files and permissions
```

---

## 📋 **Deployment Checklist**

### **Android**
- [ ] Add `google-services.json` to `android/app/`
- [ ] Update `build.gradle` with Firebase dependencies
- [ ] Test on physical device (emulators may fail)
- [ ] Verify notification permissions granted

### **iOS**  
- [ ] Add `GoogleService-Info.plist` to `ios/Runner/`
- [ ] Enable push capabilities in Xcode
- [ ] Test on physical device (simulators don't support push)
- [ ] Verify bundle ID matches Firebase project

### **Backend**
- [ ] Firebase Admin SDK configured with valid service account
- [ ] All environment variables set correctly
- [ ] FCM endpoints returning 200 status codes
- [ ] User roles properly validated

---

## ✅ **Verification Commands**

```bash
# 1. Test backend health
curl https://your-backend-url.com/api/health

# 2. Test Firebase connection  
curl -X GET https://your-backend-url.com/api/admin/notifications/push/test \
  -H "Authorization: Bearer TOKEN"

# 3. Test FCM registration
curl -X POST https://your-backend-url.com/api/fcm/register \
  -H "Authorization: Bearer TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"token":"test","deviceInfo":{"platform":"android"}}'

# 4. Flutter clean build
flutter clean && flutter pub get && flutter run
```

---

## 📞 **Support & Resources**

- **Backend Health Check**: `GET /api/health`
- **Firebase Console**: [console.firebase.google.com/project/deyncare-47d99](https://console.firebase.google.com/project/deyncare-47d99)
- **API Documentation**: [Backend API Guide](./SUPERADMIN_NOTIFICATION_API_GUIDE.md)

---

**🎉 Integration Status: 100% Backend-Matched ✅**

*This guide reflects the exact backend implementation as of December 2024. All endpoints and features have been tested and verified.* 