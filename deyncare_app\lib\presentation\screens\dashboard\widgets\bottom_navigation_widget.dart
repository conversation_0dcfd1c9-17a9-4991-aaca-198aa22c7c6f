import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';

/// A modern pill-shaped bottom navigation widget with elevated center button
/// Optimized for performance with minimal rebuilds and precise centering
class DashboardBottomNavigation extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;
  final bool showLabels;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;
  final List<bool>? disabledTabs; // New parameter for disabled tabs

  const DashboardBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
    this.showLabels = true,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.disabledTabs,
  });

  @override
  State<DashboardBottomNavigation> createState() =>
      _DashboardBottomNavigationState();
}

class _DashboardBottomNavigationState extends State<DashboardBottomNavigation>
    with SingleTickerProviderStateMixin {
  // Use single ticker for better performance
  late AnimationController _animationController;

  // Cache colors for performance
  late Color _selectedColor;
  late Color _unselectedColor;

  // Define the navigation items with modern icons
  static const List<BottomNavItem> _navItems = [
    BottomNavItem(
      label: 'Dashboard',
      icon: Icons.grid_view_rounded,
      activeIcon: Icons.grid_view,
    ),
    BottomNavItem(
      label: 'Customer',
      icon: Icons.groups_rounded,
      activeIcon: Icons.groups,
    ),
    BottomNavItem(
      label: 'Debt',
      icon: Icons.account_balance_wallet_rounded,
      activeIcon: Icons.account_balance_wallet,
    ),
    BottomNavItem(
      label: 'Menu',
      icon: Icons.menu_rounded,
      activeIcon: Icons.menu,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimation();
    _initializeBasicColors();
  }

  void _initializeAnimation() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200), // Faster for better UX
      vsync: this,
    );

    // Scale animation removed as it was unused

    // No special animation needed for initial selection
  }

  void _initializeBasicColors() {
    // Initialize with basic colors that don't require Theme context
    _selectedColor = widget.selectedItemColor ?? AppThemes.primaryColor;
    _unselectedColor = widget.unselectedItemColor ?? AppThemes.textLightColor;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Now safe to call Theme.of(context) after dependencies are established
    _cacheColors();
  }

  void _cacheColors() {
    _selectedColor = widget.selectedItemColor ?? AppThemes.primaryColor;
    _unselectedColor = widget.unselectedItemColor ??
        Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.6) ??
        AppThemes.textLightColor;
  }

  @override
  void didUpdateWidget(DashboardBottomNavigation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentIndex != widget.currentIndex) {
      _cacheColors(); // Update colors if needed
      // No special animation needed
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 90,
      margin: const EdgeInsets.only(bottom: 16),
      child: Stack(
        children: [
          // Main navigation bar
          Positioned(
            bottom: 0,
            left: 16,
            right: 16,
            child: Container(
              height: 70,
              decoration: BoxDecoration(
                color: Theme.of(context).bottomAppBarTheme.color ??
                    Theme.of(context).cardColor.withValues(alpha: 0.95),
                borderRadius: BorderRadius.circular(35),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildNavItem(0), // Dashboard
                  _buildNavItem(1), // Customer
                  _buildNavItem(2), // Debt
                  _buildNavItem(3), // Profile
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem(int index) {
    final item = _navItems[index];
    final isSelected = index == widget.currentIndex;
    final isDisabled = widget.disabledTabs != null &&
        index < widget.disabledTabs!.length &&
        widget.disabledTabs![index];

    return GestureDetector(
      onTap: () => widget.onTap(index),
      behavior: HitTestBehavior.opaque, // Better touch response
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Stack(
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  isSelected ? item.activeIcon : item.icon,
                  color: isDisabled
                      ? Colors.grey.withValues(alpha: 0.5)
                      : (isSelected ? _selectedColor : _unselectedColor),
                  size: 22,
                ),
                if (widget.showLabels) ...[
                  const SizedBox(height: 2),
                  Text(
                    item.label,
                    style: TextStyle(
                      color: isDisabled
                          ? Colors.grey.withValues(alpha: 0.5)
                          : (isSelected ? _selectedColor : _unselectedColor),
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.w500,
                      fontSize: 9,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
            // Show lock icon for disabled tabs
            if (isDisabled)
              Positioned(
                top: -2,
                right: -2,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.8),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.lock,
                    size: 10,
                    color: Colors.white,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Data class for bottom navigation items
class BottomNavItem {
  final String label;
  final IconData icon;
  final IconData? activeIcon;
  final bool isCenter;
  final bool showBadge;
  final int badgeCount;

  const BottomNavItem({
    required this.label,
    required this.icon,
    this.activeIcon,
    this.isCenter = false,
    this.showBadge = false,
    this.badgeCount = 0,
  });
}

/// Predefined bottom navigation items for the dashboard
class DashboardNavItems {
  static const List<BottomNavItem> defaultItems = [
    BottomNavItem(
      label: 'Dashboard',
      icon: Icons.grid_view_rounded,
      activeIcon: Icons.grid_view,
    ),
    BottomNavItem(
      label: 'Customer',
      icon: Icons.groups_rounded,
      activeIcon: Icons.groups,
    ),
    BottomNavItem(
      label: 'Debt',
      icon: Icons.account_balance_wallet_rounded,
      activeIcon: Icons.account_balance_wallet,
    ),
    BottomNavItem(
      label: 'Menu',
      icon: Icons.menu_rounded,
      activeIcon: Icons.menu,
    ),
  ];

  static List<BottomNavItem> getItemsWithBadges({
    int customerBadge = 0,
    int debtBadge = 0,
  }) {
    return [
      const BottomNavItem(
        label: 'Dashboard',
        icon: Icons.grid_view_rounded,
        activeIcon: Icons.grid_view,
      ),
      BottomNavItem(
        label: 'Customer',
        icon: Icons.groups_rounded,
        activeIcon: Icons.groups,
        showBadge: customerBadge > 0,
        badgeCount: customerBadge,
      ),
      BottomNavItem(
        label: 'Debt',
        icon: Icons.account_balance_wallet_rounded,
        activeIcon: Icons.account_balance_wallet,
        showBadge: debtBadge > 0,
        badgeCount: debtBadge,
      ),
      const BottomNavItem(
        label: 'Menu',
        icon: Icons.menu_rounded,
        activeIcon: Icons.menu,
      ),
    ];
  }
}
