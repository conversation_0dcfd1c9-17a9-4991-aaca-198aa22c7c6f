import 'package:bloc_test/bloc_test.dart';
import 'package:deyncare_app/domain/models/auth_token.dart';
import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/domain/repositories/auth_repository.dart';
import 'package:deyncare_app/domain/usecases/auth/change_password_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/check_auth_status_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/forgot_password_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/get_available_payment_methods_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/login_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/logout_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/process_payment_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/refresh_token_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/register_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/reset_password_success_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/verify_email_use_case.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_event.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_state.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'auth_bloc_test.mocks.dart';

// Generate mock classes
@GenerateMocks([
  CheckAuthStatusUseCase,
  LoginUseCase,
  RegisterUseCase,
  LogoutUseCase,
  ForgotPasswordUseCase,
  VerifyEmailUseCase,
  ChangePasswordUseCase,
  RefreshTokenUseCase,
  ResetPasswordSuccessUseCase,
  ProcessPaymentUseCase, // Added
  GetAvailablePaymentMethodsUseCase, // Added
  AuthRepository,
])
void main() {
  late AuthBloc authBloc;
  late MockCheckAuthStatusUseCase mockCheckAuthStatusUseCase;
  late MockLoginUseCase mockLoginUseCase;
  late MockRegisterUseCase mockRegisterUseCase;
  late MockLogoutUseCase mockLogoutUseCase;
  late MockForgotPasswordUseCase mockForgotPasswordUseCase;
  late MockVerifyEmailUseCase mockVerifyEmailUseCase;
  late MockChangePasswordUseCase mockChangePasswordUseCase;
  late MockRefreshTokenUseCase mockRefreshTokenUseCase;
  late MockResetPasswordSuccessUseCase mockResetPasswordSuccessUseCase;
  late MockProcessPaymentUseCase mockProcessPaymentUseCase;
  late MockGetAvailablePaymentMethodsUseCase mockGetAvailablePaymentMethodsUseCase;
  late MockAuthRepository mockAuthRepository;

  setUp(() {
    // Initialize mock dependencies
    mockCheckAuthStatusUseCase = MockCheckAuthStatusUseCase();
    mockLoginUseCase = MockLoginUseCase();
    mockRegisterUseCase = MockRegisterUseCase();
    mockLogoutUseCase = MockLogoutUseCase();
    mockForgotPasswordUseCase = MockForgotPasswordUseCase();
    mockVerifyEmailUseCase = MockVerifyEmailUseCase();
    mockChangePasswordUseCase = MockChangePasswordUseCase();
    mockRefreshTokenUseCase = MockRefreshTokenUseCase();
    mockResetPasswordSuccessUseCase = MockResetPasswordSuccessUseCase();
    mockProcessPaymentUseCase = MockProcessPaymentUseCase();
    mockGetAvailablePaymentMethodsUseCase = MockGetAvailablePaymentMethodsUseCase();
    mockAuthRepository = MockAuthRepository();

    // Create the AuthBloc with all mock dependencies
    authBloc = AuthBloc(
      checkAuthStatus: mockCheckAuthStatusUseCase,
      login: mockLoginUseCase,
      register: mockRegisterUseCase,
      logout: mockLogoutUseCase,
      forgotPassword: mockForgotPasswordUseCase,
      verifyEmail: mockVerifyEmailUseCase,
      changePassword: mockChangePasswordUseCase,
      refreshToken: mockRefreshTokenUseCase,
      resetPasswordSuccess: mockResetPasswordSuccessUseCase,
      processPayment: mockProcessPaymentUseCase,
      getAvailablePaymentMethods: mockGetAvailablePaymentMethodsUseCase,
      authRepository: mockAuthRepository,
    );
  });

  tearDown(() {
    authBloc.close();
  });

  test('initial state should be AuthInitial', () {
    expect(authBloc.state, isA<AuthInitial>());
  });

  group('AppStarted event', () {
    final testUser = User(
      userId: 'test123',
      fullName: 'Test User',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      registrationStatus: 'completed',
      isPaid: true,
      isShopActive: true,
    );

    blocTest<AuthBloc, AuthState>(
      'emits [AuthLoading, AuthAuthenticated] when user is logged in',
      build: () {
        when(mockCheckAuthStatusUseCase.execute()).thenAnswer((_) async => true);
        when(mockAuthRepository.getCurrentUser()).thenAnswer((_) async => testUser);
        return authBloc;
      },
      act: (bloc) => bloc.add(AppStarted()),
      expect: () => [
        isA<AuthLoading>(),
        isA<AuthAuthenticated>(),
      ],
    );

    blocTest<AuthBloc, AuthState>(
      'emits [AuthLoading, AuthUnauthenticated] when user is not logged in',
      build: () {
        when(mockCheckAuthStatusUseCase.execute()).thenAnswer((_) async => false);
        return authBloc;
      },
      act: (bloc) => bloc.add(AppStarted()),
      expect: () => [
        isA<AuthLoading>(),
        isA<AuthUnauthenticated>(),
      ],
    );
  });

  group('LoggedIn event', () {
    final testUser = User(
      userId: 'test123',
      fullName: 'Test User',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      registrationStatus: 'completed',
      isPaid: true,
      isShopActive: true,
    );

    final testToken = AuthToken(
      accessToken: 'test_access_token',
      refreshToken: 'test_refresh_token',
      expiresAt: DateTime.now().add(const Duration(hours: 1)),
    );

    blocTest<AuthBloc, AuthState>(
      'emits [AuthAuthenticated] when user logs in successfully',
      build: () => authBloc,
      act: (bloc) => bloc.add(LoggedIn(user: testUser, token: testToken)),
      expect: () => [isA<AuthAuthenticated>()],
    );
  });

  group('LoggedOut event', () {
    blocTest<AuthBloc, AuthState>(
      'emits [AuthUnauthenticated] when user logs out',
      build: () {
        when(mockLogoutUseCase.execute()).thenAnswer((_) async => {});
        return authBloc;
      },
      act: (bloc) => bloc.add(LoggedOut()),
      expect: () => [isA<AuthUnauthenticated>()],
    );
  });
}
