import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:dio/dio.dart';

import 'package:deyncare_app/data/network/token/token_manager.dart';
import 'package:deyncare_app/data/network/clients/dio_client.dart';
import 'package:deyncare_app/data/network/interceptors/auth_interceptor.dart';
import 'package:deyncare_app/data/repositories/auth_repository_impl.dart';
import 'package:deyncare_app/data/services/auth/auth_remote_source.dart';
import 'package:deyncare_app/data/services/auth/auth_utils.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_event.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_state.dart';
import 'package:deyncare_app/core/utils/auth_debug_logger.dart';

import '../mocks/mock_definitions.dart';

/// Integration test for complete authentication token flow
/// Tests the critical token persistence and loading issues
void main() {
  group('Authentication Token Flow Integration Tests', () {
    late TokenManager tokenManager;
    late DioClient dioClient;
    late AuthRepositoryImpl authRepository;
    late AuthBloc authBloc;
    late MockFlutterSecureStorage mockSecureStorage;
    late MockAuthRemoteSource mockAuthRemoteSource;
    late MockAuthUtils mockAuthUtils;
    late Dio mockDio;

    // Test JWT tokens (these are fake tokens for testing)
    const testAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.Jwt-Authentication-Token-For-Testing';
    const testRefreshToken = 'refresh_token_for_testing_purposes_only';

    setUp(() {
      // Initialize mocks
      mockSecureStorage = MockFlutterSecureStorage();
      mockAuthRemoteSource = MockAuthRemoteSource();
      mockAuthUtils = MockAuthUtils();
      mockDio = MockDio();

      // Initialize components
      tokenManager = TokenManager(secureStorage: mockSecureStorage);
      dioClient = DioClient(dio: mockDio, tokenManager: tokenManager);
      
      authRepository = AuthRepositoryImpl(
        remoteSource: mockAuthRemoteSource,
        tokenManager: tokenManager,
        authUtils: mockAuthUtils,
        dioClient: dioClient,
      );
    });

    group('Token Persistence and Loading', () {
      testWidgets('should save and load tokens correctly', (WidgetTester tester) async {
        // Arrange
        when(mockSecureStorage.write(key: anyNamed('key'), value: anyNamed('value')))
            .thenAnswer((_) async {});
        when(mockSecureStorage.read(key: 'access_token'))
            .thenAnswer((_) async => testAccessToken);
        when(mockSecureStorage.read(key: 'refresh_token'))
            .thenAnswer((_) async => testRefreshToken);

        // Act - Save tokens
        await tokenManager.saveTokens(
          accessToken: testAccessToken,
          refreshToken: testRefreshToken,
        );

        // Act - Load tokens
        final loadedAccessToken = await tokenManager.getAccessToken();
        final loadedRefreshToken = await tokenManager.getRefreshToken();

        // Assert
        expect(loadedAccessToken, equals(testAccessToken));
        expect(loadedRefreshToken, equals(testRefreshToken));
        
        // Verify storage was called
        verify(mockSecureStorage.write(key: 'access_token', value: testAccessToken)).called(1);
        verify(mockSecureStorage.write(key: 'refresh_token', value: testRefreshToken)).called(1);
      });

      testWidgets('should validate token integrity correctly', (WidgetTester tester) async {
        // Arrange
        when(mockSecureStorage.read(key: 'access_token'))
            .thenAnswer((_) async => testAccessToken);

        // Act
        final isValid = await tokenManager.validateTokenIntegrity(testAccessToken);

        // Assert - Test token should be valid (not expired in our test)
        expect(isValid, isTrue);
      });

      testWidgets('should detect expired tokens', (WidgetTester tester) async {
        // Arrange - Use an expired token
        const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.Expired-Token-For-Testing';
        
        when(mockSecureStorage.read(key: 'token_expiry'))
            .thenAnswer((_) async => '1516239022000'); // Past date

        // Act
        final isExpired = await tokenManager.isTokenExpired();

        // Assert
        expect(isExpired, isTrue);
      });
    });

    group('DioClient Token Synchronization', () {
      testWidgets('should load stored tokens on initialization', (WidgetTester tester) async {
        // Arrange
        when(mockSecureStorage.read(key: 'access_token'))
            .thenAnswer((_) async => testAccessToken);

        // Act
        await dioClient.init();

        // Assert
        expect(dioClient.hasTokensLoaded, isTrue);
      });

      testWidgets('should synchronize tokens from storage', (WidgetTester tester) async {
        // Arrange
        when(mockSecureStorage.read(key: 'access_token'))
            .thenAnswer((_) async => testAccessToken);

        // Act
        await dioClient.syncTokensFromStorage();

        // Assert
        expect(dioClient.hasTokensLoaded, isTrue);
      });

      testWidgets('should clear tokens properly', (WidgetTester tester) async {
        // Arrange - First load a token
        when(mockSecureStorage.read(key: 'access_token'))
            .thenAnswer((_) async => testAccessToken);
        await dioClient.syncTokensFromStorage();
        expect(dioClient.hasTokensLoaded, isTrue);

        // Act
        dioClient.clearAuthToken();

        // Assert
        expect(dioClient.hasTokensLoaded, isFalse);
      });
    });

    group('Authentication Flow Integration', () {
      testWidgets('should complete login flow with token synchronization', (WidgetTester tester) async {
        // Arrange
        final mockUserData = {
          'id': 'test-user-id',
          'email': '<EMAIL>',
          'fullName': 'Test User',
          'role': 'admin',
          'status': 'active',
        };

        final mockLoginResponse = {
          'user': mockUserData,
          'accessToken': testAccessToken,
          'refreshToken': testRefreshToken,
          'expiresIn': 7200,
        };

        // Mock storage operations
        when(mockSecureStorage.write(key: anyNamed('key'), value: anyNamed('value')))
            .thenAnswer((_) async {});
        when(mockSecureStorage.read(key: 'access_token'))
            .thenAnswer((_) async => testAccessToken);
        when(mockSecureStorage.read(key: 'refresh_token'))
            .thenAnswer((_) async => testRefreshToken);

        // Mock auth operations
        when(mockAuthRemoteSource.login(any, any))
            .thenAnswer((_) async => mockLoginResponse);
        when(mockAuthUtils.saveUserData(any))
            .thenAnswer((_) async {});

        // Act
        final result = await authRepository.login('<EMAIL>', 'password');

        // Assert
        expect(result.$1.email, equals('<EMAIL>'));
        expect(result.$2.accessToken, equals(testAccessToken));
        
        // Verify DioClient was updated with token
        expect(dioClient.hasTokensLoaded, isTrue);
        
        // Verify storage operations
        verify(mockSecureStorage.write(key: 'access_token', value: testAccessToken)).called(1);
        verify(mockSecureStorage.write(key: 'refresh_token', value: testRefreshToken)).called(1);
      });

      testWidgets('should handle app startup with stored tokens', (WidgetTester tester) async {
        // This test simulates the critical app restart scenario
        // where tokens should be loaded automatically
        
        // Arrange - Simulate stored tokens from previous session
        when(mockSecureStorage.read(key: 'access_token'))
            .thenAnswer((_) async => testAccessToken);
        when(mockSecureStorage.read(key: 'refresh_token'))
            .thenAnswer((_) async => testRefreshToken);
        when(mockSecureStorage.read(key: 'token_expiry'))
            .thenAnswer((_) async => '9999999999000'); // Future date

        final mockUserData = {
          'id': 'test-user-id',
          'email': '<EMAIL>',
          'fullName': 'Test User',
          'role': 'admin',
          'status': 'active',
        };

        when(mockAuthUtils.getUserData()).thenAnswer((_) async => 
            UserModel.fromJson(mockUserData));

        // Act - Initialize DioClient (simulating app startup)
        await dioClient.init();

        // Assert - DioClient should have loaded the stored tokens
        expect(dioClient.hasTokensLoaded, isTrue);
        
        // Verify token was read from storage
        verify(mockSecureStorage.read(key: 'access_token')).called(1);
      });

      testWidgets('should handle logout with complete cleanup', (WidgetTester tester) async {
        // Arrange - Start with logged in state
        when(mockSecureStorage.read(key: 'access_token'))
            .thenAnswer((_) async => testAccessToken);
        when(mockSecureStorage.delete(key: anyNamed('key')))
            .thenAnswer((_) async {});
        when(mockAuthRemoteSource.logout())
            .thenAnswer((_) async {});
        when(mockAuthUtils.clearUserData())
            .thenAnswer((_) async {});

        await dioClient.syncTokensFromStorage();
        expect(dioClient.hasTokensLoaded, isTrue);

        // Act
        await authRepository.logout();

        // Assert
        expect(dioClient.hasTokensLoaded, isFalse);
        
        // Verify cleanup operations
        verify(mockSecureStorage.delete(key: 'access_token')).called(1);
        verify(mockSecureStorage.delete(key: 'refresh_token')).called(1);
        verify(mockAuthUtils.clearUserData()).called(1);
      });
    });

    group('Token Refresh Flow', () {
      testWidgets('should refresh tokens when expired', (WidgetTester tester) async {
        // Arrange
        const newAccessToken = 'new_access_token_after_refresh';
        const newRefreshToken = 'new_refresh_token_after_refresh';

        when(mockSecureStorage.read(key: 'access_token'))
            .thenAnswer((_) async => testAccessToken);
        when(mockSecureStorage.read(key: 'refresh_token'))
            .thenAnswer((_) async => testRefreshToken);
        when(mockSecureStorage.read(key: 'token_expiry'))
            .thenAnswer((_) async => '1516239022000'); // Past date
        when(mockSecureStorage.write(key: anyNamed('key'), value: anyNamed('value')))
            .thenAnswer((_) async {});

        final refreshResponse = {
          'accessToken': newAccessToken,
          'refreshToken': newRefreshToken,
          'expiresIn': 7200,
        };

        when(mockAuthRemoteSource.refreshToken(testRefreshToken))
            .thenAnswer((_) async => refreshResponse);

        // Act
        final result = await authRepository.refreshToken(testRefreshToken);

        // Assert
        expect(result.accessToken, equals(newAccessToken));
        expect(result.refreshToken, equals(newRefreshToken));
        
        // Verify DioClient was updated
        expect(dioClient.hasTokensLoaded, isTrue);
        
        // Verify storage was updated
        verify(mockSecureStorage.write(key: 'access_token', value: newAccessToken)).called(1);
        verify(mockSecureStorage.write(key: 'refresh_token', value: newRefreshToken)).called(1);
      });
    });

    group('Debug Logging Integration', () {
      testWidgets('should generate comprehensive auth report', (WidgetTester tester) async {
        // Arrange
        when(mockSecureStorage.read(key: 'access_token'))
            .thenAnswer((_) async => testAccessToken);
        when(mockSecureStorage.read(key: 'refresh_token'))
            .thenAnswer((_) async => testRefreshToken);
        when(mockSecureStorage.read(key: 'token_expiry'))
            .thenAnswer((_) async => '9999999999000');

        final mockUserData = UserModel.fromJson({
          'id': 'test-user-id',
          'email': '<EMAIL>',
          'fullName': 'Test User',
          'role': 'admin',
          'status': 'active',
        });

        when(mockAuthUtils.getUserData()).thenAnswer((_) async => mockUserData);

        await dioClient.syncTokensFromStorage();

        // Act
        final report = await AuthDebugLogger.generateAuthReport(
          tokenManager: tokenManager,
          dioClient: dioClient,
          authUtils: mockAuthUtils,
        );

        // Assert
        expect(report, contains('AUTHENTICATION DEBUG REPORT'));
        expect(report, contains('TOKEN INFORMATION'));
        expect(report, contains('USER DATA'));
        expect(report, contains('HTTP CLIENT STATUS'));
      });
    });
  });
}

// Mock class for UserModel - simple representation for testing
class UserModel {
  final String id;
  final String email;
  final String fullName;
  final String role;
  final String status;

  UserModel({
    required this.id,
    required this.email,
    required this.fullName,
    required this.role,
    required this.status,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? '',
      email: json['email'] ?? '',
      fullName: json['fullName'] ?? '',
      role: json['role'] ?? '',
      status: json['status'] ?? '',
    );
  }
} 