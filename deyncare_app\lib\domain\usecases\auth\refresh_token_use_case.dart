import 'package:deyncare_app/domain/models/auth_token.dart';
import 'package:deyncare_app/domain/repositories/auth_repository.dart';

/// Use case for refreshing authentication tokens
///
/// This class handles the process of refreshing an expired access token
/// using a valid refresh token to maintain continuous authentication.
class RefreshTokenUseCase {
  final AuthRepository _repository;

  /// Creates a new instance with the required repository
  RefreshTokenUseCase(this._repository);

  /// Executes the token refresh with an existing refresh token
  ///
  /// Returns a Future<AuthToken> with the new tokens
  /// Throws exceptions if refresh fails (e.g., expired refresh token)
  Future<AuthToken> execute(String refreshToken) async {
    return await _repository.refreshToken(refreshToken);
  }
}
