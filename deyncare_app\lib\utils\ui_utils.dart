import 'package:flutter/material.dart';

class UiUtils {
  static void showSuccessSnackbar(BuildContext context, String message) {
    _showSnackbar(context, message, Colors.green);
  }

  static void showErrorSnackbar(BuildContext context, String message) {
    _showSnackbar(context, message, Colors.red);
  }

  static void _showSnackbar(BuildContext context, String message, Color backgroundColor) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
