import 'package:equatable/equatable.dart';
import 'package:deyncare_app/domain/models/registration_progress.dart';

class RegistrationProgressModel extends Equatable {
  final String currentStep;
  final int progress;
  final Map<String, dynamic> data;
  final String nextStep;
  final DateTime? verificationCodeExpiresAt;
  final String? selectedPlanId;
  final String? selectedPaymentMethod;

  const RegistrationProgressModel({
    required this.currentStep,
    required this.progress,
    required this.data,
    required this.nextStep,
    this.verificationCodeExpiresAt,
    this.selectedPlanId,
    this.selectedPaymentMethod,
  });

  factory RegistrationProgressModel.fromJson(Map<String, dynamic> json) {
    // Safely cast data to Map<String, dynamic>
    Map<String, dynamic> dataMap = {};
    if (json['data'] != null) {
      if (json['data'] is Map<String, dynamic>) {
        dataMap = json['data'] as Map<String, dynamic>;
      } else if (json['data'] is Map) {
        dataMap = Map<String, dynamic>.from(json['data'] as Map);
      }
      // If it's not a Map at all, we'll use empty map as fallback
    }
    
    return RegistrationProgressModel(
      currentStep: json['currentStep'] ?? '',
      progress: json['progress'] ?? 0,
      data: dataMap,
      nextStep: json['nextStep'] ?? '',
      verificationCodeExpiresAt: json['verificationCodeExpiresAt'] != null
          ? DateTime.tryParse(json['verificationCodeExpiresAt'])
          : null,
      selectedPlanId: json['selectedPlanId'],
      selectedPaymentMethod: json['selectedPaymentMethod'],
    );
  }

  @override
  List<Object?> get props => [
        currentStep,
        progress,
        data,
        nextStep,
        verificationCodeExpiresAt,
        selectedPlanId,
        selectedPaymentMethod,
      ];

  /// Converts this data model to its domain entity
  RegistrationProgress toDomain() {
    return RegistrationProgress(
      currentStep: currentStep,
      progress: progress,
      data: data,
      nextStep: nextStep,
      verificationCodeExpiresAt: verificationCodeExpiresAt,
      selectedPlanId: selectedPlanId,
      selectedPaymentMethod: selectedPaymentMethod,
    );
  }
} 