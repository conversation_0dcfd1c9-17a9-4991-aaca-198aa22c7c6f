import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:deyncare_app/presentation/screens/dashboard/widgets/unified_chart_widget.dart';
import 'package:deyncare_app/presentation/blocs/dashboard/dashboard_state.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';

void main() {
  group('UnifiedChartWidget Tests', () {
    late KPIData mockKpiData;

    setUp(() {
      mockKpiData = KPIData(
        todayPayments: 1500.0,
        paymentsGrowth: 12.5,
        outstandingDebts: 8500.0,
        overdueDebts: 3,
        newCustomers: 25,
        riskLevel: 'Medium',
        riskTrend: 'Improving',
      );
    });

    testWidgets('should render correctly in light theme',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppThemes.lightTheme,
          home: Scaffold(
            body: UnifiedChartWidget(kpiData: mockKpiData),
          ),
        ),
      );

      // Verify the widget renders
      expect(find.byType(UnifiedChartWidget), findsOneWidget);

      // Verify header text
      expect(find.text('Financial Analytics'), findsOneWidget);
      expect(find.text('Real-time payment and debt insights'), findsOneWidget);
      expect(find.text('Live Data'), findsOneWidget);

      // Verify tab buttons
      expect(find.text('Payment Trends'), findsOneWidget);
      expect(find.text('Debt Analysis'), findsOneWidget);

      // Verify no overlay issues by checking layout
      final container = tester.widget<Container>(
        find.descendant(
          of: find.byType(UnifiedChartWidget),
          matching: find.byType(Container).first,
        ),
      );
      expect(container.decoration, isA<BoxDecoration>());
    });

    testWidgets('should render correctly in dark theme',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppThemes.darkTheme,
          home: Scaffold(
            body: UnifiedChartWidget(kpiData: mockKpiData),
          ),
        ),
      );

      // Verify the widget renders in dark theme
      expect(find.byType(UnifiedChartWidget), findsOneWidget);

      // Verify header text is still visible
      expect(find.text('Financial Analytics'), findsOneWidget);
      expect(find.text('Real-time payment and debt insights'), findsOneWidget);

      // Verify tab buttons are present
      expect(find.text('Payment Trends'), findsOneWidget);
      expect(find.text('Debt Analysis'), findsOneWidget);
    });

    testWidgets('should switch between tabs correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppThemes.lightTheme,
          home: Scaffold(
            body: UnifiedChartWidget(kpiData: mockKpiData),
          ),
        ),
      );

      // Initially on first tab (Payment Trends)
      expect(find.text('Payment Trends'), findsOneWidget);

      // Tap on second tab
      await tester.tap(find.text('Debt Analysis'));
      await tester.pumpAndSettle();

      // Verify tab switch worked
      expect(find.text('Debt Analysis'), findsOneWidget);
    });

    testWidgets('should handle compact layout on small screens',
        (WidgetTester tester) async {
      // Set a small screen size
      tester.view.physicalSize = const Size(350, 600);
      tester.view.devicePixelRatio = 1.0;

      await tester.pumpWidget(
        MaterialApp(
          theme: AppThemes.lightTheme,
          home: Scaffold(
            body: UnifiedChartWidget(kpiData: mockKpiData),
          ),
        ),
      );

      // Verify compact layout shows shorter text
      expect(find.text('Trends'), findsOneWidget);
      expect(find.text('Analysis'), findsOneWidget);

      // Reset screen size
      addTearDown(() {
        tester.view.resetPhysicalSize();
        tester.view.resetDevicePixelRatio();
      });
    });

    testWidgets('should have proper spacing to prevent overlay issues',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppThemes.lightTheme,
          home: Scaffold(
            body: UnifiedChartWidget(kpiData: mockKpiData),
          ),
        ),
      );

      // Find the main column
      final column = tester.widget<Column>(
        find.descendant(
          of: find.byType(UnifiedChartWidget),
          matching: find.byType(Column).first,
        ),
      );

      // Verify proper spacing exists
      expect(column.children.length,
          equals(6)); // Header, spacing, tabs, spacing, chart, bottom spacing

      // Verify SizedBox widgets for spacing
      final sizedBoxes = column.children.whereType<SizedBox>().toList();
      expect(sizedBoxes.length, greaterThanOrEqualTo(2));
    });
  });
}
