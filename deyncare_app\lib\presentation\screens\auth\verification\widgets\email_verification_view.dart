import 'package:flutter/material.dart';
import 'package:deyncare_app/presentation/screens/auth/verification/widgets/verification_form.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_header.dart';

class EmailVerificationView extends StatelessWidget {
  final String email;
  final String userId;
  final DateTime expiresAt;
  final Function(String) onVerifyPressed;
  final VoidCallback onResendCodeRequested;
  final bool isLoading;
  final String? errorMessage;

  const EmailVerificationView({
    super.key,
    required this.email,
    required this.userId,
    required this.expiresAt,
    required this.onVerifyPressed,
    required this.onResendCodeRequested,
    required this.isLoading,
    this.errorMessage,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const AuthHeader(
            title: 'Verify Your Email',
            subtitle: 'Please enter the verification code sent to your email',
          ),
          const SizedBox(height: 20),
          
          // Error message if there is one
          if (errorMessage != null && errorMessage!.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red.shade700),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      errorMessage!,
                      style: TextStyle(color: Colors.red.shade700),
                    ),
                  ),
                ],
              ),
            ),
            
          VerificationForm(
            email: email,
            isLoading: isLoading,
            onVerifyPressed: onVerifyPressed,
            onResendVerificationCodeRequested: onResendCodeRequested,
          ),
          // You can add a countdown timer here if needed, using the expiresAt property
          // For example:
          // Text('Code expires in: ${expiresAt.difference(DateTime.now()).inMinutes} minutes'),
        ],
      ),
    );
  }
} 