import 'package:equatable/equatable.dart';
import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/domain/models/auth_token.dart';
import 'package:deyncare_app/domain/models/registration_progress.dart';

/// Base class for all authentication states
abstract class AuthState extends Equatable {
  final String? selectedPlanId;

  const AuthState({
    this.selectedPlanId,
  });
  
  @override
  List<Object?> get props => [selectedPlanId];
}

/// Initial state when app starts
class AuthInitial extends AuthState {
  const AuthInitial() : super();
}

/// State while checking authentication
class AuthLoading extends AuthState {
  const AuthLoading({super.selectedPlanId});
}

/// State when user is authenticated
class AuthAuthenticated extends AuthState {
  final User user;
  final AuthToken? token;
  
  const AuthAuthenticated({
    required this.user,
    this.token,
  }) : super();
  
  @override
  List<Object?> get props => [user, token];
}

/// State when user is not authenticated
class AuthUnauthenticated extends AuthState {}

/// State when onboarding is required
class AuthOnboardingRequired extends AuthState {}

/// State when authentication fails with error
class AuthFailure extends AuthState {
  final String message;
  
  const AuthFailure({required this.message}) : super();
  
  @override
  List<Object?> get props => [message];
}

/// State when forgot password email has been sent
class AuthForgotPasswordSent extends AuthState {}

/// State when email has been verified
// This state might become redundant with RegistrationComplete and AuthPaymentRequired
// For now, keep it but monitor its usage.
class AuthEmailVerified extends AuthState {}

/// State when password has been changed successfully
class AuthPasswordChanged extends AuthState {}

/// State when password has been reset successfully
class AuthPasswordReset extends AuthState {}

/// State when registration is successful and email verification is pending
class AuthEmailVerificationPending extends AuthState {
  final User user;
  final DateTime expiresAt;
  @override
  final String selectedPlanId;
  final String? errorMessage;

  const AuthEmailVerificationPending({
    required this.user,
    required this.expiresAt,
    required this.selectedPlanId,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [
        user,
        expiresAt,
        selectedPlanId,
        errorMessage,
      ];

  AuthEmailVerificationPending copyWith({
    User? user,
    DateTime? expiresAt,
    String? selectedPlanId,
    String? errorMessage,
  }) {
    return AuthEmailVerificationPending(
      user: user ?? this.user,
      expiresAt: expiresAt ?? this.expiresAt,
      selectedPlanId: selectedPlanId ?? this.selectedPlanId,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

/// State when email is verified but payment is required for full activation
class AuthPaymentRequired extends AuthState {
  final User user;
  @override
  final String selectedPlanId;
  final String? selectedPaymentMethod; // For pre-selection in payment form
  final List<String>? availablePaymentMethods;
  final bool isLoadingPaymentMethods;
  final String? errorMessage;

  const AuthPaymentRequired({
    required this.user,
    required this.selectedPlanId,
    this.selectedPaymentMethod,
    this.availablePaymentMethods,
    this.isLoadingPaymentMethods = false,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [
        user,
        selectedPlanId,
        selectedPaymentMethod,
        availablePaymentMethods,
        isLoadingPaymentMethods,
        errorMessage,
      ];

  AuthPaymentRequired copyWith({
    User? user,
    String? selectedPlanId,
    String? selectedPaymentMethod,
    String? errorMessage,
    List<String>? availablePaymentMethods,
    bool? isLoadingPaymentMethods,
  }) {
    return AuthPaymentRequired(
      user: user ?? this.user,
      selectedPlanId: selectedPlanId ?? this.selectedPlanId,
      selectedPaymentMethod:
          selectedPaymentMethod ?? this.selectedPaymentMethod,
      errorMessage: errorMessage ?? this.errorMessage,
      availablePaymentMethods:
          availablePaymentMethods ?? this.availablePaymentMethods,
      isLoadingPaymentMethods:
          isLoadingPaymentMethods ?? this.isLoadingPaymentMethods,
    );
  }
}

/// State when an operation succeeds with a message
class AuthSuccess extends AuthState {
  final String message;

  const AuthSuccess({required this.message}) : super();

  @override
  List<Object?> get props => [message];
}

// New states for Registration UI Upgrade

/// State when registration is in progress (e.g., multi-step form progress)
class RegistrationInProgress extends AuthState {
  final RegistrationProgress registrationProgress;

  const RegistrationInProgress({
    required this.registrationProgress,
  });

  @override
  List<Object?> get props => [registrationProgress];
}

/// State when payment processing is ongoing
class PaymentProcessing extends AuthState {
  final User user;
  final String planId;
  final String paymentMethod;
  final Map<String, dynamic> paymentDetails;

  const PaymentProcessing({
    required this.user,
    required this.planId,
    required this.paymentMethod,
    required this.paymentDetails,
  });

  @override
  List<Object?> get props => [user, planId, paymentMethod, paymentDetails];
}

/// State when user account is suspended
class AuthAccountSuspended extends AuthState {
  final String reason;
  final DateTime? suspendedAt;
  final String? suspendedBy;
  final int redirectCountdown; // Countdown in seconds before redirect

  const AuthAccountSuspended({
    required this.reason,
    this.suspendedAt,
    this.suspendedBy,
    this.redirectCountdown = 10,
  }) : super();

  @override
  List<Object?> get props => [reason, suspendedAt, suspendedBy, redirectCountdown];

  AuthAccountSuspended copyWith({
    String? reason,
    DateTime? suspendedAt,
    String? suspendedBy,
    int? redirectCountdown,
  }) {
    return AuthAccountSuspended(
      reason: reason ?? this.reason,
      suspendedAt: suspendedAt ?? this.suspendedAt,
      suspendedBy: suspendedBy ?? this.suspendedBy,
      redirectCountdown: redirectCountdown ?? this.redirectCountdown,
    );
  }
}

/// State when user role is not allowed for mobile app access
class AuthRoleRestricted extends AuthState {
  final String userRole;
  final String message;
  final int redirectCountdown; // Countdown in seconds before redirect

  const AuthRoleRestricted({
    required this.userRole,
    required this.message,
    this.redirectCountdown = 5,
  }) : super();

  @override
  List<Object?> get props => [userRole, message, redirectCountdown];

  AuthRoleRestricted copyWith({
    String? userRole,
    String? message,
    int? redirectCountdown,
  }) {
    return AuthRoleRestricted(
      userRole: userRole ?? this.userRole,
      message: message ?? this.message,
      redirectCountdown: redirectCountdown ?? this.redirectCountdown,
    );
  }
}

/// State when the entire registration process is complete
class RegistrationComplete extends AuthState {
  final User user;

  const RegistrationComplete({
    required this.user,
  }) : super();

  @override
  List<Object?> get props => [user];
}
