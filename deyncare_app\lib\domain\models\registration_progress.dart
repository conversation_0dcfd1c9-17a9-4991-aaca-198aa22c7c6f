import 'package:equatable/equatable.dart';

class RegistrationProgress extends Equatable {
  final String currentStep;
  final int progress;
  final Map<String, dynamic> data;
  final String nextStep; // Added to reflect the backend response
  final DateTime? verificationCodeExpiresAt;
  final String? selectedPlanId;
  final String? selectedPaymentMethod;

  const RegistrationProgress({
    required this.currentStep,
    required this.progress,
    required this.data,
    required this.nextStep, // Added to reflect the backend response
    this.verificationCodeExpiresAt,
    this.selectedPlanId,
    this.selectedPaymentMethod,
  });

  @override
  List<Object?> get props => [
        currentStep,
        progress,
        data,
        nextStep,
        verificationCodeExpiresAt,
        selectedPlanId,
        selectedPaymentMethod,
      ];
} 