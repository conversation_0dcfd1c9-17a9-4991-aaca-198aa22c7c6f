/// Custom exception for API errors with structured error information
class ApiException implements Exception {
  final String message;
  final String code;
  final int? statusCode;
  final dynamic data;
  final String? suspensionReason;

  ApiException({
    required this.message,
    this.code = 'unknown_error',
    this.statusCode,
    this.data,
    this.suspensionReason,
  });

  @override
  String toString() => 'ApiException: [$code] $message';
}
