import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:deyncare_app/data/services/connectivity_service.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';

/// A mixin to handle network errors consistently across view models and BLoCs
mixin NetworkErrorHandler {
  final ConnectivityService _connectivityService = ConnectivityService();
  
  /// Executes a network operation with error handling and retry capabilities
  /// Returns a [NetworkResult] with either data or error information
  Future<NetworkResult<T>> executeNetworkCall<T>({
    required Future<T> Function() networkCall,
    bool checkConnectivity = true,
    int maxRetries = 1,
    Duration retryDelay = const Duration(seconds: 1),
  }) async {
    if (checkConnectivity && !_connectivityService.isConnected()) {
      return NetworkResult<T>.error(
        NetworkError(
          type: NetworkErrorType.offline,
          message: 'No internet connection. Please check your network settings.',
        ),
      );
    }

    int attempts = 0;
    dynamic lastError;

    while (attempts <= maxRetries) {
      try {
        final result = await networkCall();
        return NetworkResult<T>.success(result);
      } catch (e) {
        lastError = e;
        
        // Don't retry if it's not a network error or it's the last attempt
        if (!_isRetryableError(e) || attempts == maxRetries) {
          break;
        }
        
        // Wait before retrying
        await Future.delayed(retryDelay);
        attempts++;
      }
    }

    // Process the final error
    return NetworkResult<T>.error(_mapToNetworkError(lastError));
  }

  /// Checks if an error is retryable (network-related)
  bool _isRetryableError(dynamic error) {
    if (error is DioException) {
      return error.type == DioExceptionType.connectionTimeout ||
             error.type == DioExceptionType.sendTimeout ||
             error.type == DioExceptionType.receiveTimeout ||
             error.type == DioExceptionType.connectionError ||
             (error.response?.statusCode != null && error.response!.statusCode! >= 500);
    }
    return false;
  }

  /// Maps any error to a consistent NetworkError format
  NetworkError _mapToNetworkError(dynamic error) {
    if (error is ApiException) {
      // Handle API exceptions
      if (error.code == 'connection_error' || error.code == 'network_error') {
        return NetworkError(
          type: NetworkErrorType.offline,
          message: error.message,
          originalError: error,
        );
      } else if (error.code == 'timeout_error') {
        return NetworkError(
          type: NetworkErrorType.timeout,
          message: error.message,
          originalError: error,
        );
      } else if (error.code == 'server_error' || 
                 (error.statusCode != null && error.statusCode! >= 500)) {
        return NetworkError(
          type: NetworkErrorType.server,
          message: error.message,
          originalError: error,
        );
      } else if (error.code == 'unauthorized' || error.statusCode == 401) {
        return NetworkError(
          type: NetworkErrorType.unauthorized,
          message: error.message,
          originalError: error,
        );
      } else {
        return NetworkError(
          type: NetworkErrorType.api,
          message: error.message,
          code: error.code,
          originalError: error,
        );
      }
    } else if (error is DioException) {
      // Handle Dio exceptions
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return NetworkError(
            type: NetworkErrorType.timeout,
            message: 'Connection timed out. Please try again.',
            originalError: error,
          );
        case DioExceptionType.connectionError:
          return NetworkError(
            type: NetworkErrorType.offline,
            message: 'No internet connection. Please check your network settings.',
            originalError: error,
          );
        case DioExceptionType.badResponse:
          if (error.response?.statusCode == 401) {
            return NetworkError(
              type: NetworkErrorType.unauthorized,
              message: 'Your session has expired. Please log in again.',
              originalError: error,
            );
          } else if (error.response?.statusCode != null && error.response!.statusCode! >= 500) {
            return NetworkError(
              type: NetworkErrorType.server,
              message: 'Server error. Our team has been notified.',
              originalError: error,
            );
          } else {
            String message = 'An unexpected error occurred';
            if (error.response?.data is Map<String, dynamic>) {
              final data = error.response!.data as Map<String, dynamic>;
              if (data.containsKey('message')) {
                message = data['message'];
              }
            }
            return NetworkError(
              type: NetworkErrorType.api,
              message: message,
              originalError: error,
            );
          }
        default:
          return NetworkError(
            type: NetworkErrorType.unknown,
            message: error.message ?? 'An unexpected error occurred',
            originalError: error,
          );
      }
    }
    
    // Handle generic errors
    return NetworkError(
      type: NetworkErrorType.unknown,
      message: error.toString(),
      originalError: error,
    );
  }

  /// Shows a Snackbar with the appropriate error message
  void showErrorSnackBar(BuildContext context, NetworkError error) {
    final snackBar = SnackBar(
      content: Text(error.message),
      backgroundColor: _getColorForErrorType(error.type),
      action: SnackBarAction(
        label: 'Dismiss',
        onPressed: () {},
        textColor: Colors.white,
      ),
      duration: const Duration(seconds: 5),
    );
    
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  /// Returns the appropriate color for each error type
  Color _getColorForErrorType(NetworkErrorType type) {
    switch (type) {
      case NetworkErrorType.offline:
      case NetworkErrorType.timeout:
        return Colors.orange[700]!;
      case NetworkErrorType.server:
        return Colors.red[700]!;
      case NetworkErrorType.unauthorized:
        return Colors.purple[700]!;
      case NetworkErrorType.api:
      case NetworkErrorType.unknown:
        return Colors.red[900]!;
    }
  }
}

/// Result wrapper for network operations
class NetworkResult<T> {
  final T? data;
  final NetworkError? error;
  final bool isSuccess;

  NetworkResult.success(this.data)
      : error = null,
        isSuccess = true;

  NetworkResult.error(this.error)
      : data = null,
        isSuccess = false;

  /// Check if this result contains an error of a specific type
  bool hasErrorOfType(NetworkErrorType type) {
    return !isSuccess && error?.type == type;
  }
}

/// Types of network errors
enum NetworkErrorType {
  offline,    // No internet connection
  timeout,    // Request timed out
  server,     // Server error (5xx)
  unauthorized, // Authentication error (401)
  api,        // API error with a specific error code
  unknown     // Any other error
}

/// Structured network error information
class NetworkError {
  final NetworkErrorType type;
  final String message;
  final String? code;
  final dynamic originalError;

  NetworkError({
    required this.type,
    required this.message,
    this.code,
    this.originalError,
  });

  @override
  String toString() => 'NetworkError[$type]: $message${code != null ? ' (Code: $code)' : ''}';
}
