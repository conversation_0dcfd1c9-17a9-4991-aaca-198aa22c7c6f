group 'name.avioli.unilinks'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.3.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    namespace 'name.avioli.unilinks'
    compileSdkVersion 33

    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 33
        versionCode 1
        versionName "1.0"
    }
    lintOptions {
        disable 'InvalidPackage'
    }
} 