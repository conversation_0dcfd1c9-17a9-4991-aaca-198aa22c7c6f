import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:deyncare_app/data/network/token/token_manager.dart';
import 'package:deyncare_app/data/network/clients/dio_client.dart';
import 'package:deyncare_app/data/services/auth/auth_utils.dart';

/// Comprehensive debug logger for authentication and token management issues
class AuthDebugLogger {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
  );
  
  static const String _logPrefix = '🔐 AUTH_DEBUG';
  
  /// Log comprehensive authentication state information
  static Future<void> logAuthState({
    required TokenManager tokenManager,
    required DioClient dioClient,
    required AuthUtils authUtils,
    String? context,
  }) async {
    if (!kDebugMode) return;
    
    try {
      _logger.i('$_logPrefix: === AUTH STATE DEBUG ${context ?? ''} ===');
      
      // Token Manager State
      final tokenInfo = await tokenManager.getTokenInfo();
      _logger.i('$_logPrefix: Token Manager State:');
      _logger.i('$_logPrefix:   - Has Access Token: ${tokenInfo['hasAccessToken']}');
      _logger.i('$_logPrefix:   - Has Refresh Token: ${tokenInfo['hasRefreshToken']}');
      _logger.i('$_logPrefix:   - Is Expired: ${tokenInfo['isExpired']}');
      _logger.i('$_logPrefix:   - Should Refresh: ${tokenInfo['shouldRefresh']}');
      _logger.i('$_logPrefix:   - Time Until Expiry: ${tokenInfo['timeUntilExpiry']} minutes');
      _logger.i('$_logPrefix:   - Token Expiry: ${tokenInfo['tokenExpiry']}');
      _logger.i('$_logPrefix:   - Refresh Token Valid: ${tokenInfo['refreshTokenValid']}');
      _logger.i('$_logPrefix:   - Access Token Valid: ${tokenInfo['accessTokenValid']}');
      
      // DioClient State
      _logger.i('$_logPrefix: DioClient State:');
      _logger.i('$_logPrefix:   - Has Tokens Loaded: ${dioClient.hasTokensLoaded}');
      
      // User Data State
      final userData = await authUtils.getUserData();
      _logger.i('$_logPrefix: User Data State:');
      _logger.i('$_logPrefix:   - Has User Data: ${userData != null}');
      if (userData != null) {
        _logger.i('$_logPrefix:   - User Email: ${userData.email}');
        _logger.i('$_logPrefix:   - User Status: ${userData.status}');
        _logger.i('$_logPrefix:   - User Role: ${userData.role}');
        _logger.i('$_logPrefix:   - Shop ID: ${userData.shopId}');
      }
      
      _logger.i('$_logPrefix: === END AUTH STATE DEBUG ===');
    } catch (e) {
      _logger.e('$_logPrefix: Error logging auth state: $e');
    }
  }
  
  /// Log token-related operation
  static void logTokenOperation(String operation, {
    String? details,
    bool success = true,
    String? error,
  }) {
    if (!kDebugMode) return;
    
    final emoji = success ? '✅' : '❌';
    _logger.i('$_logPrefix: $emoji Token Operation: $operation');
    
    if (details != null) {
      _logger.i('$_logPrefix:   Details: $details');
    }
    
    if (error != null) {
      _logger.e('$_logPrefix:   Error: $error');
    }
  }
  
  /// Log authentication flow step
  static void logAuthFlow(String step, {
    String? details,
    Map<String, dynamic>? data,
  }) {
    if (!kDebugMode) return;
    
    _logger.i('$_logPrefix: 🔄 Auth Flow: $step');
    
    if (details != null) {
      _logger.i('$_logPrefix:   Details: $details');
    }
    
    if (data != null) {
      _logger.i('$_logPrefix:   Data: ${_sanitizeLogData(data)}');
    }
  }
  
  /// Log HTTP request/response for auth-related endpoints
  static void logHttpAuth(String method, String path, {
    int? statusCode,
    bool hasAuthHeader = false,
    String? error,
    Map<String, dynamic>? responseData,
  }) {
    if (!kDebugMode) return;
    
    final emoji = statusCode != null && statusCode >= 200 && statusCode < 300 ? '🌐' : '🔴';
    _logger.i('$_logPrefix: $emoji HTTP $method $path');
    
    if (statusCode != null) {
      _logger.i('$_logPrefix:   Status: $statusCode');
    }
    
    _logger.i('$_logPrefix:   Has Auth Header: $hasAuthHeader');
    
    if (error != null) {
      _logger.e('$_logPrefix:   Error: $error');
    }
    
    if (responseData != null) {
      _logger.i('$_logPrefix:   Response: ${_sanitizeLogData(responseData)}');
    }
  }
  
  /// Log token parsing/validation issues
  static void logTokenValidation(String token, {
    bool isValid = false,
    String? reason,
    DateTime? expiry,
    Map<String, dynamic>? claims,
  }) {
    if (!kDebugMode) return;
    
    final emoji = isValid ? '✅' : '❌';
    final tokenPreview = token.length > 20 ? '${token.substring(0, 20)}...' : token;
    
    _logger.i('$_logPrefix: $emoji Token Validation: $tokenPreview');
    _logger.i('$_logPrefix:   Valid: $isValid');
    
    if (reason != null) {
      _logger.i('$_logPrefix:   Reason: $reason');
    }
    
    if (expiry != null) {
      _logger.i('$_logPrefix:   Expiry: $expiry');
      _logger.i('$_logPrefix:   Time Until Expiry: ${expiry.difference(DateTime.now()).inMinutes} minutes');
    }
    
    if (claims != null) {
      _logger.i('$_logPrefix:   Claims: ${_sanitizeLogData(claims)}');
    }
  }
  
  /// Log session lifecycle events
  static void logSessionEvent(String event, {
    String? details,
    bool success = true,
  }) {
    if (!kDebugMode) return;
    
    final emoji = success ? '🔄' : '⚠️';
    _logger.i('$_logPrefix: $emoji Session Event: $event');
    
    if (details != null) {
      _logger.i('$_logPrefix:   Details: $details');
    }
  }
  
  /// Log critical authentication errors
  static void logAuthError(String operation, dynamic error, {
    StackTrace? stackTrace,
    String? context,
  }) {
    if (!kDebugMode) return;
    
    _logger.e('$_logPrefix: 💥 Auth Error in $operation');
    
    if (context != null) {
      _logger.e('$_logPrefix:   Context: $context');
    }
    
    _logger.e('$_logPrefix:   Error: $error');
    
    if (stackTrace != null) {
      _logger.e('$_logPrefix:   Stack Trace: $stackTrace');
    }
  }
  
  /// Log app lifecycle changes affecting authentication
  static void logAppLifecycle(String state, {
    String? authAction,
    bool sessionValid = true,
  }) {
    if (!kDebugMode) return;
    
    final emoji = sessionValid ? '🔄' : '⚠️';
    _logger.i('$_logPrefix: $emoji App Lifecycle: $state');
    
    if (authAction != null) {
      _logger.i('$_logPrefix:   Auth Action: $authAction');
    }
    
    _logger.i('$_logPrefix:   Session Valid: $sessionValid');
  }
  
  /// Sanitize data for logging (remove sensitive information)
  static Map<String, dynamic> _sanitizeLogData(Map<String, dynamic> data) {
    final sanitized = <String, dynamic>{};
    
    data.forEach((key, value) {
      if (_isSensitiveKey(key)) {
        sanitized[key] = '***REDACTED***';
      } else if (value is Map<String, dynamic>) {
        sanitized[key] = _sanitizeLogData(value);
      } else if (value is String && value.length > 100) {
        // Truncate very long strings
        sanitized[key] = '${value.substring(0, 100)}...';
      } else {
        sanitized[key] = value;
      }
    });
    
    return sanitized;
  }
  
  /// Check if a key contains sensitive information
  static bool _isSensitiveKey(String key) {
    final sensitiveKeys = [
      'password',
      'token',
      'accessToken',
      'refreshToken',
      'authorization',
      'secret',
      'key',
      'credential',
    ];
    
    return sensitiveKeys.any((sensitive) => 
      key.toLowerCase().contains(sensitive.toLowerCase()));
  }
  
  /// Generate a comprehensive authentication report
  static Future<String> generateAuthReport({
    required TokenManager tokenManager,
    required DioClient dioClient,
    required AuthUtils authUtils,
  }) async {
    if (!kDebugMode) return 'Debug logging disabled in release mode';
    
    final buffer = StringBuffer();
    buffer.writeln('=== AUTHENTICATION DEBUG REPORT ===');
    buffer.writeln('Generated at: ${DateTime.now().toIso8601String()}');
    buffer.writeln();
    
    try {
      // Token information
      final tokenInfo = await tokenManager.getTokenInfo();
      buffer.writeln('TOKEN INFORMATION:');
      tokenInfo.forEach((key, value) {
        buffer.writeln('  $key: $value');
      });
      buffer.writeln();
      
      // User data
      final userData = await authUtils.getUserData();
      buffer.writeln('USER DATA:');
      if (userData != null) {
        buffer.writeln('  Email: ${userData.email}');
        buffer.writeln('  Status: ${userData.status}');
        buffer.writeln('  Role: ${userData.role}');
        buffer.writeln('  Shop ID: ${userData.shopId}');
      } else {
        buffer.writeln('  No user data found');
      }
      buffer.writeln();
      
      // DioClient status
      buffer.writeln('HTTP CLIENT STATUS:');
      buffer.writeln('  Has Tokens Loaded: ${dioClient.hasTokensLoaded}');
      buffer.writeln();
      
    } catch (e) {
      buffer.writeln('ERROR GENERATING REPORT: $e');
    }
    
    buffer.writeln('=== END REPORT ===');
    return buffer.toString();
  }
} 