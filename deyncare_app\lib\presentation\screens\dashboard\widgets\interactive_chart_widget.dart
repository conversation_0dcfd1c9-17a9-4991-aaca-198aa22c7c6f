import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';

class InteractiveChartWidget extends StatefulWidget {
  const InteractiveChartWidget({super.key});

  @override
  State<InteractiveChartWidget> createState() => _InteractiveChartWidgetState();
}

class _InteractiveChartWidgetState extends State<InteractiveChartWidget> {
  int _selectedChartIndex = 0;

  final List<ChartType> _chartTypes = [
    ChartType(
      title: 'Payment Trends',
      subtitle: 'Daily payment collections',
      icon: Icons.trending_up,
      color: AppThemes.successColor,
    ),
    ChartType(
      title: 'Payment Methods',
      subtitle: 'Payment distribution by type',
      icon: Icons.pie_chart,
      color: AppThemes.primaryColor,
    ),
    ChartType(
      title: 'Debt Aging',
      subtitle: 'Outstanding debts by period',
      icon: Icons.bar_chart,
      color: AppThemes.warningColor,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _chartTypes[_selectedChartIndex].title,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppThemes.textPrimaryColor,
                        ),
                      ),
                      Text(
                        _chartTypes[_selectedChartIndex].subtitle,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppThemes.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildChartTypeSwitcher(),
              ],
            ),
            const SizedBox(height: 16),
            _buildChart(),
          ],
        ),
      ),
    );
  }

  Widget _buildChart() {
    switch (_selectedChartIndex) {
      case 0:
        return _buildPaymentLineChart();
      case 1:
        return _buildRevenuePieChart();
      case 2:
        return _buildDebtBarChart();
      default:
        return _buildPaymentLineChart();
    }
  }

  Widget _buildChartTypeSwitcher() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildChartTypeButton(0, Icons.show_chart, 'Line'),
          const SizedBox(width: 4),
          _buildChartTypeButton(1, Icons.pie_chart, 'Pie'),
          const SizedBox(width: 4),
          _buildChartTypeButton(2, Icons.bar_chart, 'Bar'),
        ],
      ),
    );
  }

  Widget _buildChartTypeButton(int index, IconData icon, String label) {
    final isSelected = _selectedChartIndex == index;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedChartIndex = index;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppThemes.primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : AppThemes.textSecondaryColor,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : AppThemes.textSecondaryColor,
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentLineChart() {
    return SizedBox(
      height: 200,
      child: SfCartesianChart(
        backgroundColor: Colors.transparent,
        plotAreaBorderWidth: 0,
        primaryXAxis: CategoryAxis(
          axisLine: const AxisLine(width: 0),
          majorTickLines: const MajorTickLines(width: 0),
          labelStyle: const TextStyle(color: AppThemes.textSecondaryColor),
        ),
        primaryYAxis: NumericAxis(
          axisLine: const AxisLine(width: 0),
          majorTickLines: const MajorTickLines(width: 0),
          majorGridLines: const MajorGridLines(
            width: 0.5,
            color: AppThemes.dividerColor,
          ),
          labelStyle: const TextStyle(color: AppThemes.textSecondaryColor),
        ),
        title: ChartTitle(
          text: 'Payment Trends',
          textStyle: const TextStyle(
            color: AppThemes.textPrimaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        series: <LineSeries<PaymentData, String>>[
          LineSeries<PaymentData, String>(
            dataSource: _getPaymentData(),
            xValueMapper: (PaymentData payment, _) => payment.day,
            yValueMapper: (PaymentData payment, _) => payment.amount,
            name: 'Payments',
            color: AppThemes.primaryColor,
            width: 2,
            markerSettings: const MarkerSettings(
              isVisible: true,
              color: AppThemes.primaryColor,
              borderColor: Colors.white,
              borderWidth: 2,
            ),
            dataLabelSettings: const DataLabelSettings(
              isVisible: false,
            ),
          ),
        ],
        tooltipBehavior: TooltipBehavior(
          enable: true,
          shared: true,
          format: 'point.x: \$point.y',
        ),
      ),
    );
  }

  Widget _buildRevenuePieChart() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Payment Methods',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppThemes.textPrimaryColor,
                  ),
                ),
                Text(
                  'Payment distribution by type',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppThemes.textSecondaryColor,
                  ),
                ),
              ],
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppThemes.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'This Month',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppThemes.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Expanded(
          child: SfCircularChart(
            legend: Legend(
              isVisible: true,
              position: LegendPosition.bottom,
              textStyle: TextStyle(
                color: AppThemes.textSecondaryColor,
                fontSize: 10,
              ),
            ),
            series: <CircularSeries>[
              DoughnutSeries<RevenueData, String>(
                dataSource: _getRevenueData(),
                xValueMapper: (RevenueData data, _) => data.category,
                yValueMapper: (RevenueData data, _) => data.revenue,
                pointColorMapper: (RevenueData data, _) => data.color,
                dataLabelSettings: const DataLabelSettings(
                  isVisible: true,
                ),
                innerRadius: '60%',
              ),
            ],
            tooltipBehavior: TooltipBehavior(enable: true),
          ),
        ),
      ],
    );
  }

  Widget _buildDebtBarChart() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Debt Aging Analysis',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppThemes.textPrimaryColor,
                  ),
                ),
                Text(
                  'Outstanding amounts by aging periods',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppThemes.textSecondaryColor,
                  ),
                ),
              ],
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppThemes.warningColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Total: \$15,240',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppThemes.warningColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Expanded(
          child: SfCartesianChart(
            primaryXAxis: CategoryAxis(
              majorGridLines: const MajorGridLines(width: 0),
              axisLine: const AxisLine(width: 0),
              labelStyle: TextStyle(
                color: AppThemes.textSecondaryColor,
                fontSize: 10,
              ),
            ),
            primaryYAxis: NumericAxis(
              majorGridLines: MajorGridLines(
                color: Colors.grey.withOpacity(0.1),
              ),
              axisLine: const AxisLine(width: 0),
              labelStyle: TextStyle(
                color: AppThemes.textSecondaryColor,
                fontSize: 10,
              ),
            ),
            plotAreaBorderWidth: 0,
            series: <CartesianSeries>[
              ColumnSeries<DebtData, String>(
                dataSource: _getDebtData(),
                xValueMapper: (DebtData debt, _) => debt.period,
                yValueMapper: (DebtData debt, _) => debt.amount,
                pointColorMapper: (DebtData debt, _) => debt.color,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
                dataLabelSettings: const DataLabelSettings(
                  isVisible: true,
                ),
              ),
            ],
            tooltipBehavior: TooltipBehavior(enable: true),
          ),
        ),
      ],
    );
  }

  List<PaymentData> _getPaymentData() {
    return [
      PaymentData('Mon', 2500),
      PaymentData('Tue', 3200),
      PaymentData('Wed', 2800),
      PaymentData('Thu', 4100),
      PaymentData('Fri', 3800),
      PaymentData('Sat', 4500),
      PaymentData('Sun', 3900),
    ];
  }

  List<RevenueData> _getRevenueData() {
    return [
      RevenueData('Cash Payments', 45, AppThemes.successColor),
      RevenueData('Credit Cards', 30, AppThemes.primaryColor),
      RevenueData('Bank Transfer', 20, AppThemes.infoColor),
      RevenueData('Mobile Pay', 5, AppThemes.warningColor),
    ];
  }

  List<DebtData> _getDebtData() {
    return [
      DebtData('0-30 days', 5200, AppThemes.successColor),
      DebtData('31-60 days', 3800, AppThemes.warningColor),
      DebtData('61-90 days', 2400, AppThemes.errorColor),
      DebtData('90+ days', 3840, AppThemes.textSecondaryColor),
    ];
  }
}

class ChartType {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;

  ChartType({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
  });
}



class RevenueData {
  final String category;
  final double revenue;
  final Color color;

  RevenueData(this.category, this.revenue, this.color);
}

class DebtData {
  final String period;
  final double amount;
  final Color color;

  DebtData(this.period, this.amount, this.color);
}

/// Data class for payment chart
class PaymentData {
  final String day;
  final double amount;

  PaymentData(this.day, this.amount);
} 