import 'package:flutter/material.dart';
import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/core/utils/permission_utils.dart';
import 'package:deyncare_app/presentation/widgets/permission_denied_dialog.dart';

/// Service for handling navigation guards and permission-based access control
class NavigationGuardService {
  /// Navigate to customers with permission check
  static Future<void> navigateToCustomers(
      BuildContext context, User? user) async {
    if (!PermissionUtils.canAccessCustomers(user)) {
      await _showPermissionDeniedDialog(
        context,
        'Customer Management',
        'You need customer management permissions to access this feature.',
      );
      return;
    }

    // Navigate to customers
    Navigator.pushNamed(context, '/customers');
  }

  /// Navigate to debts with permission check
  static Future<void> navigateToDebts(BuildContext context, User? user) async {
    if (!PermissionUtils.canAccessDebts(user)) {
      await _showPermissionDeniedDialog(
        context,
        'Debt Management',
        'You need debt management permissions to access this feature.',
      );
      return;
    }

    // Navigate to debts
    Navigator.pushNamed(context, '/debts');
  }

  /// Navigate to reports with permission check
  static Future<void> navigateToReports(
      BuildContext context, User? user) async {
    if (!PermissionUtils.canAccessReports(user)) {
      await _showPermissionDeniedDialog(
        context,
        'Reports',
        'You need report permissions to access this feature.',
      );
      return;
    }

    // Navigate to reports
    Navigator.pushNamed(context, '/reports');
  }

  /// Navigate to user management with permission check
  static Future<void> navigateToUserManagement(
      BuildContext context, User? user) async {
    if (!PermissionUtils.canAccessUserManagement(user)) {
      await _showPermissionDeniedDialog(
        context,
        'User Management',
        'Only administrators can access user management.',
      );
      return;
    }

    // Navigate to user management
    Navigator.pushNamed(context, '/user-role-list');
  }

  /// Navigate to shop settings with permission check
  static Future<void> navigateToShopSettings(
      BuildContext context, User? user) async {
    if (!PermissionUtils.canAccessShopSettings(user)) {
      await _showPermissionDeniedDialog(
        context,
        'Shop Settings',
        'Only administrators can access shop settings.',
      );
      return;
    }

    // Navigate to shop settings
    Navigator.pushNamed(context, '/settings/shop');
  }

  /// Navigate to subscription with permission check
  static Future<void> navigateToSubscription(
      BuildContext context, User? user) async {
    if (!PermissionUtils.canAccessSubscription(user)) {
      await _showPermissionDeniedDialog(
        context,
        'Subscription Management',
        'Only administrators can access subscription management.',
      );
      return;
    }

    // Navigate to subscription
    Navigator.pushNamed(context, '/subscription');
  }

  /// Check if user can perform action and show appropriate feedback
  static Future<bool> checkPermissionWithFeedback(BuildContext context,
      User? user, bool Function(User?) permissionCheck, String featureName,
      {String? customMessage}) async {
    if (permissionCheck(user)) {
      return true;
    }

    await _showPermissionDeniedDialog(
      context,
      featureName,
      customMessage ?? 'You don\'t have permission to access this feature.',
    );
    return false;
  }

  /// Show permission denied dialog with consistent styling
  static Future<void> _showPermissionDeniedDialog(
      BuildContext context, String featureName, String message) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return PermissionDeniedDialog(
          featureName: featureName,
          message: message,
        );
      },
    );
  }

  /// Get navigation callback that checks permissions
  static VoidCallback? getPermissionAwareCallback(
    BuildContext context,
    User? user,
    bool Function(User?) permissionCheck,
    VoidCallback action,
    String featureName,
  ) {
    if (!permissionCheck(user)) {
      return () => _showPermissionDeniedDialog(
            context,
            featureName,
            'You don\'t have permission to access this feature.',
          );
    }
    return action;
  }

  /// Check if navigation should be blocked for bottom navigation
  static bool shouldBlockNavigation(User? user, int tabIndex) {
    bool isBlocked;
    switch (tabIndex) {
      case 0: // Dashboard - always accessible
        isBlocked = false;
        break;
      case 1: // Customers
        isBlocked = !PermissionUtils.canAccessCustomers(user);
        break;
      case 2: // Debts
        isBlocked = !PermissionUtils.canAccessDebts(user);
        break;
      case 3: // Menu - always accessible
        isBlocked = false;
        break;
      default:
        isBlocked = false;
        break;
    }

    return isBlocked;
  }

  /// Get blocked navigation message for bottom navigation
  static String getBlockedNavigationMessage(int tabIndex) {
    switch (tabIndex) {
      case 1:
        return 'Customer Management access required';
      case 2:
        return 'Debt Management access required';
      default:
        return 'Access denied';
    }
  }
}
