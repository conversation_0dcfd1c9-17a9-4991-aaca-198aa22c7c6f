import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import '../../../../../core/utils/api_toast_handler.dart';
import '../../../../../data/services/auth_service.dart';
import '../../../../../core/exceptions/api_exception.dart';
import '../../../../blocs/auth/auth_bloc.dart';
import '../../../../blocs/auth/auth_event.dart';
import '../../../../blocs/auth/auth_state.dart';
// TODO: Create verify_account_dialog.dart if needed
// import '../../../../widgets/verify_account_dialog.dart';
import '../../widgets/auth_text_field.dart';
import '../../../../../core/routes/app_router.dart';
import '../../../../../core/constants/app_themes.dart';
import '../../../../widgets/suspension_dialog.dart';

/// Login form component that handles user authentication with the DeynCare backend API
class LoginForm extends StatefulWidget {
  /// Optional email to prefill in the email field
  final String? prefilledEmail;

  const LoginForm({
    super.key,
    this.prefilledEmail,
  });

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> with WidgetsBindingObserver {
  // Form controllers and validators
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  // State management with ValueNotifiers for better reactivity
  final _isLoadingNotifier = ValueNotifier<bool>(false);
  final _errorMessageNotifier = ValueNotifier<String?>(null);
  final _obscurePasswordNotifier = ValueNotifier<bool>(true);

  // This is the only property we need - remove getters/setters to avoid confusion
  // and just use the _setLoading method directly

  // Auth service for API calls
  late final AuthService _authService;

  // Helper methods for state management
  void _setLoading(bool value) {
    if (mounted) {
      _isLoadingNotifier.value = value;
    }
  }

  // Properly typed VoidCallback for the button
  void _onLoginPressed() {
    // Wrap in a try-catch to ensure the loading state is always reset
    try {
      _handleLogin();
    } catch (e) {
      // This is a safety net in case any synchronous errors occur
      _setLoading(false);
      _setErrorMessage('An unexpected error occurred');
    }
  }

  void _setErrorMessage(String? message) {
    if (mounted) {
      _errorMessageNotifier.value = message;
    }
  }

  void _togglePasswordVisibility() {
    _obscurePasswordNotifier.value = !_obscurePasswordNotifier.value;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _authService = GetIt.I<AuthService>();

    // Set prefilled email if provided
    if (widget.prefilledEmail != null && widget.prefilledEmail!.isNotEmpty) {
      _emailController.text = widget.prefilledEmail!;
    }
  }

  @override
  void dispose() {
    // Dispose of controllers and observers
    _emailController.dispose();
    _passwordController.dispose();
    _isLoadingNotifier.dispose();
    _errorMessageNotifier.dispose();
    _obscurePasswordNotifier.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  // Email validation
  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  // Password validation
  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }

    if (value.length < 8) {
      return 'Password must be at least 8 characters';
    }

    return null;
  }

  // Handle login submission with proper error handling
  Future<void> _handleLogin() async {
    // Exit immediately if already in loading state to prevent double-submission
    if (_isLoadingNotifier.value) return;

    // Close keyboard and clear any previous errors
    FocusScope.of(context).unfocus();
    _setErrorMessage(null);

    // Validate form first - don't start loading if validation fails
    if (!(_formKey.currentState?.validate() ?? false)) return;

    // Set loading state at the start of the process
    _setLoading(true);

    // Add a flag to track if we need to manually reset the loading state
    // This ensures we don't accidentally leave the button in a loading state
    bool needsReset = true;

    try {
      final email = _emailController.text.trim();
      final password = _passwordController.text;

      // Show loading toast
      ApiToastHandler.cancelAll();
      ApiToastHandler.handleInfo('Logging in...');

      // Attempt login with a timeout to prevent hanging
      final (user, token) = await _authService.login(email, password).timeout(
            const Duration(seconds: 10), // 10 second timeout
            onTimeout: () => throw ApiException(
              message:
                  'Connection timeout. Please check your internet and try again.',
              code: 'timeout_error',
            ),
          );

      // Guard against widget being disposed
      if (!mounted) return;

      // Show success toast
      ApiToastHandler.cancelAll();
      ApiToastHandler.handleSuccess(message: 'Login successful!');

      // Dispatch login event to BLoC
      context.read<AuthBloc>().add(LoggedIn(user: user, token: token));
    } catch (e) {
      // CRITICAL: ALWAYS reset loading state on ANY error - NO EXCEPTIONS!
      _setLoading(false);
      needsReset = false; // Mark that we've reset it

      if (!mounted) return;

      ApiToastHandler.cancelAll();

      // Handle different error types
      if (e is ApiException) {
        final email = _emailController.text.trim();

        // Account suspended case
        if (e.code == 'account_suspended' ||
            e.message.toLowerCase().contains('suspended')) {
          ApiToastHandler.handleWarning('Account suspended');

          // Parse suspension details from error message if available
          String? suspensionReason;
          if (e.message.contains('Reason:')) {
            suspensionReason = e.message.split('Reason:')[1].trim();
          }

          // Dispatch SuspensionDetected event to auth bloc
          context.read<AuthBloc>().add(SuspensionDetected(
                reason: suspensionReason ?? e.message,
              ));
        }
        // Unverified email case
        else if (e.code == 'account_not_verified' ||
            e.message.toLowerCase().contains('verify') ||
            e.message.toLowerCase().contains('verification')) {
          _setErrorMessage(
              'Your account is not verified. Please verify your email.');
          ApiToastHandler.handleWarning('Your email is not verified');
          _showVerificationDialog(email);
        }
        // Invalid credentials case
        else if (e.code == 'invalid_credentials' ||
            e.message.toLowerCase().contains('invalid') ||
            e.message.toLowerCase().contains('incorrect')) {
          _setErrorMessage(
              'Invalid email or password. Please check your credentials.');
          ApiToastHandler.handleError(e,
              fallbackMessage: 'Invalid email or password');
        }
        // Rate limit case
        else if (e.code == 'rate_limit_exceeded' ||
            e.message.toLowerCase().contains('rate limit')) {
          _setErrorMessage('Too many login attempts. Please try again later.');
          ApiToastHandler.handleError(e, fallbackMessage: 'Too many attempts');
        }
        // Other API errors
        else {
          _setErrorMessage(e.message);
          ApiToastHandler.handleError(e,
              fallbackMessage: 'Authentication failed');
        }
      }
      // Generic errors (network, timeout, etc.)
      else {
        _setErrorMessage(
            'Connection failed. Please check your internet and try again.');
        ApiToastHandler.handleError(e, fallbackMessage: 'Connection failed');
      }
    } finally {
      // SAFETY NET: Always ensure loading is reset
      if (mounted) {
        _setLoading(false);
      }
    }
  }

  // Show dialog for unverified accounts
  void _showVerificationDialog(String email) {
    // Using showDialog with proper structure
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Email Verification Required'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Your account with email $email needs verification.'),
            const SizedBox(height: 8),
            const Text('Please check your inbox for the verification email.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              _handleResendVerification(email);
            },
            child: const Text('Resend Verification'),
          ),
        ],
      ),
    );
  }

  // Show dialog for suspended accounts
  void _showSuspensionDialog(AuthAccountSuspended state) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => SuspensionDialog(
        reason: state.reason,
        suspendedAt: state.suspendedAt,
        suspendedBy: state.suspendedBy,
        initialCountdown: state.redirectCountdown,
        onRedirectComplete: () {
          Navigator.pop(context);
          context.read<AuthBloc>().add(CompleteSuspensionRedirect());
        },
        onDismiss: () {
          Navigator.pop(context);
          context.read<AuthBloc>().add(CompleteSuspensionRedirect());
        },
      ),
    );
  }

  // Show dialog for role restriction
  void _showRoleRestrictionDialog(AuthRoleRestricted state) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => RoleRestrictionDialog(
        userRole: state.userRole,
        message: state.message,
        initialCountdown: state.redirectCountdown,
        onRedirectComplete: () {
          Navigator.pop(context);
          context.read<AuthBloc>().add(CompleteSuspensionRedirect());
        },
        onDismiss: () {
          Navigator.pop(context);
          context.read<AuthBloc>().add(CompleteSuspensionRedirect());
        },
      ),
    );
  }

  // Handle resend verification - using ValueNotifier for state consistency
  Future<void> _handleResendVerification(String email) async {
    // Use _setLoading for consistency instead of setState
    _setLoading(true);

    try {
      // Clear any previous error messages
      _setErrorMessage(null);

      // Show loading toast
      ApiToastHandler.cancelAll(); // Cancel any existing toasts
      ApiToastHandler.handleInfo('Sending verification email...');

      // Call the resend verification API
      await _authService.resendVerification(email).timeout(
            const Duration(seconds: 10), // 10 second timeout
            onTimeout: () => throw ApiException(
              message:
                  'Connection timeout. Please check your internet and try again.',
              code: 'timeout_error',
            ),
          );

      // Show success toast
      ApiToastHandler.cancelAll();
      ApiToastHandler.handleSuccess(
          message: 'Verification email sent! Please check your inbox.');

      // Navigate to verification screen if component is still mounted
      if (!mounted) return;
      Navigator.pushNamed(context, AppRouter.verificationRoute,
          arguments: {'email': email});
    } catch (e) {
      // CRITICAL: Reset loading immediately on error
      _setLoading(false);

      // Show error toast and message
      if (mounted) {
        ApiToastHandler.cancelAll();
        ApiToastHandler.handleError(e,
            fallbackMessage: 'Failed to send verification email');
        _setErrorMessage(
            'Could not send verification email. Please try again later.');
      }
    } finally {
      // SAFETY NET: Ensure loading is always reset
      if (mounted) {
        _setLoading(false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthAuthenticated) {
          // Reset loading and navigate to dashboard
          _setLoading(false);
          AppRouter.navigateToDashboard(context);
        } else if (state is AuthFailure) {
          // CRITICAL: Always reset loading on ANY auth failure
          _setLoading(false);
          _setErrorMessage(state.message);
          ApiToastHandler.handleError(Exception(state.message),
              fallbackMessage: 'Authentication failed');
        } else if (state is AuthAccountSuspended) {
          // Reset loading and show suspension dialog
          _setLoading(false);
          _showSuspensionDialog(state);
        } else if (state is AuthRoleRestricted) {
          // Reset loading and show role restriction dialog
          _setLoading(false);
          _showRoleRestrictionDialog(state);
        }
      },
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Email field with enhanced styling
              AuthTextField(
                label: 'Email Address',
                hintText: 'Enter your email address',
                controller: _emailController,
                validator: _validateEmail,
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                prefixIcon: Icon(
                  Icons.email_outlined,
                  color: AppThemes.primaryColor,
                ),
              ),

              const SizedBox(height: 16),

              // Password field with enhanced styling and visibility toggle
              ValueListenableBuilder<bool>(
                valueListenable: _obscurePasswordNotifier,
                builder: (context, obscurePassword, _) {
                  return AuthTextField(
                    label: 'Password',
                    hintText: 'Enter your password',
                    controller: _passwordController,
                    validator: _validatePassword,
                    obscureText: obscurePassword,
                    textInputAction: TextInputAction.done,
                    prefixIcon: Icon(
                      Icons.lock_outline,
                      color: AppThemes.primaryColor,
                    ),
                    suffixIcon: IconButton(
                      icon: Icon(
                        obscurePassword
                            ? Icons.visibility_outlined
                            : Icons.visibility_off_outlined,
                        color: AppThemes.textSecondaryColor,
                      ),
                      onPressed: _togglePasswordVisibility,
                    ),
                    onSubmitted: (_) => _handleLogin(),
                  );
                },
              ),

              const SizedBox(height: 12),

              // Forgot password link with enhanced styling
              Align(
                alignment: Alignment.centerRight,
                child: TextButton.icon(
                  onPressed: () => AppRouter.navigateToForgotPassword(context),
                  icon: Icon(
                    Icons.help_outline,
                    size: 16,
                    color: AppThemes.primaryColor,
                  ),
                  label: Text(
                    'Forgot Password?',
                    style: TextStyle(
                      color: AppThemes.primaryColor,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Enhanced login button with loading state
              ValueListenableBuilder<bool>(
                valueListenable: _isLoadingNotifier,
                builder: (context, isLoading, _) {
                  return Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: AppThemes.primaryColor.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: ElevatedButton(
                      onPressed: isLoading ? null : _onLoginPressed,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppThemes.primaryColor,
                        foregroundColor: Colors.white,
                        disabledBackgroundColor: AppThemes.textLightColor,
                        padding: const EdgeInsets.symmetric(vertical: 18),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child: isLoading
                          ? Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2.0,
                                    valueColor:
                                        const AlwaysStoppedAnimation<Color>(
                                            Colors.white),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                const Text(
                                  'Signing In...',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.login,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  'Sign In',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                    ),
                  );
                },
              ),

              // Enhanced error message display
              ValueListenableBuilder<String?>(
                valueListenable: _errorMessageNotifier,
                builder: (context, errorMessage, _) {
                  if (errorMessage == null) return const SizedBox(height: 16);

                  return Padding(
                    padding: const EdgeInsets.only(top: 12.0),
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppThemes.errorColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: AppThemes.errorColor.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: AppThemes.errorColor,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              errorMessage,
                              style: TextStyle(
                                color: AppThemes.errorColor,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 20),

              // Divider with text
              Row(
                children: [
                  Expanded(
                    child: Divider(
                      color: AppThemes.dividerColor,
                      thickness: 1,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      'New to DeynCare?',
                      style: TextStyle(
                        color: AppThemes.textSecondaryColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Divider(
                      color: AppThemes.dividerColor,
                      thickness: 1,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Enhanced register option
              ValueListenableBuilder<bool>(
                valueListenable: _isLoadingNotifier,
                builder: (context, isLoading, _) {
                  return Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppThemes.primaryColor.withValues(alpha: 0.3),
                        width: 1.5,
                      ),
                    ),
                    child: OutlinedButton(
                      onPressed: isLoading
                          ? null
                          : () => AppRouter.navigateToRegister(context),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppThemes.primaryColor,
                        side: BorderSide.none,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.business_center_outlined,
                            size: 20,
                            color: AppThemes.primaryColor,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Register New Business',
                            style: TextStyle(
                              color: AppThemes.primaryColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
