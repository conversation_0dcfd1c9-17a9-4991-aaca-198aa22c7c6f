import 'package:deyncare_app/domain/repositories/auth_repository.dart';

/// Use case for handling password reset success
///
/// This class handles the success callback when a user returns to the app
/// after completing password reset in the browser using the hybrid approach.
/// It doesn't perform the actual password reset but manages the app state
/// after a successful reset.
class ResetPasswordSuccessUseCase {
  final AuthRepository _repository;

  /// Creates a new instance with the required repository
  ResetPasswordSuccessUseCase(this._repository);

  /// Executes the password reset success logic
  ///
  /// This method is called when the app receives a deep link indicating
  /// successful password reset from the web interface. It ensures the
  /// local auth state is properly reset so the user can log in with
  /// their new credentials.
  ///
  /// Returns a Future<void> that completes when the local state is updated
  Future<void> execute() async {
    // Clear any existing tokens to ensure user needs to log in again
    // This is important as the password has changed
    await _repository.logout();
  }
}
