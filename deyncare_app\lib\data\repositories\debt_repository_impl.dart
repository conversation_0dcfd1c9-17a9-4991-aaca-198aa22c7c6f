import 'package:dartz/dartz.dart';
import 'package:deyncare_app/core/errors/failures.dart';
import 'package:deyncare_app/domain/repositories/debt_repository.dart';
import 'package:deyncare_app/domain/models/debt.dart';
import 'package:deyncare_app/domain/models/payment.dart';
import 'package:deyncare_app/data/network/debt/debt_remote_data_source.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:deyncare_app/core/utils/logger.dart';

/// Implementation of debt repository - matches Customer repository pattern exactly
/// Implements Either<Failure, Success> pattern for consistent error handling
class DebtRepositoryImpl implements DebtRepository {
  final DebtRemoteDataSource _remoteDataSource;

  DebtRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, Debt>> createDebt({
    required String customerId,
    required double amount,
    required DateTime dueDate,
    String? description,
    double? paidAmount,
    DateTime? paidDate,
    PaymentMethod? paymentMethod,
  }) async {
    try {
      logger.d('DebtRepository: Creating debt for customer: $customerId');
      
      final data = <String, dynamic>{
        'customerId': customerId,
        'debtAmount': amount,
        'dueDate': dueDate.toIso8601String(),
        if (description != null) 'description': description,
        if (paidAmount != null) 'paidAmount': paidAmount,
        if (paidDate != null) 'paidDate': paidDate.toIso8601String(),
        if (paymentMethod != null) 'paymentMethod': paymentMethod.value,
      };

      final debtModel = await _remoteDataSource.createDebt(data);
      logger.i('DebtRepository: Debt created successfully with ID: ${debtModel.debtId}');
      logger.i('DebtRepository: Amount: \$${debtModel.debtAmount}, Due: ${debtModel.dueDate.day}/${debtModel.dueDate.month}/${debtModel.dueDate.year}');
      
      return Right(debtModel.toDomain());
    } on ApiException catch (e) {
      logger.e('DebtRepository: API error creating debt: $e');
      
      // Handle specific authentication errors
      if (e.code == 'unauthorized' || e.code == 'token_expired') {
        return Left(AuthFailure(message: 'Your session has expired. Please log in again.', code: e.code));
      }
      
      if (e.code == 'validation_error' || e.code == 'bad_request') {
        return Left(ValidationFailure(message: e.message, code: e.code, data: e.data));
      }
      
      if (e.code == 'not_found') {
        return Left(NotFoundFailure(message: 'Customer not found.', code: e.code));
      }
      
      return Left(ServerFailure(message: 'Failed to create debt: ${e.message}', code: e.code));
    } catch (e, stackTrace) {
      logger.e('DebtRepository: Error creating debt: $e');
      logger.e('DebtRepository: Stack trace: $stackTrace');
      return Left(ServerFailure(message: 'Failed to create debt: $e'));
    }
  }

  @override
  Future<Either<Failure, DebtListResult>> getDebts({
    int page = 1,
    int limit = 20,
    DebtStatus? status,
    RiskLevel? riskLevel,
    String? customerType,
    String? search,
    String? sortBy,
    bool ascending = false,
  }) async {
    try {
      logger.d('DebtRepository: Getting debts - page: $page, limit: $limit');
      
      final queryParams = <String, dynamic>{
        'page': page.toString(),
        'limit': limit.toString(),
        if (status != null) 'status': status.value,
        if (riskLevel != null) 'riskLevel': _riskLevelToString(riskLevel),
        if (customerType != null) 'customerType': customerType,
        if (search != null) 'search': search,
        if (sortBy != null) 'sortBy': sortBy,
        'sortOrder': ascending ? 'asc' : 'desc',
      };

      final response = await _remoteDataSource.getDebts(queryParams);
      
      logger.d('DebtRepository: Raw debt list response structure: ${response.keys}');
      
      // Backend returns: { success, message, data: { debts, pagination, analytics } }
      final dataContent = response['data'] as Map<String, dynamic>? ?? {};
      final debtsArray = dataContent['debts'] as List<dynamic>? ?? [];
      final paginationData = dataContent['pagination'] as Map<String, dynamic>? ?? {};
      final analyticsData = dataContent['analytics'] as Map<String, dynamic>? ?? {};
      
      logger.d('DebtRepository: Found ${debtsArray.length} debts in response');
      
      // Parse debts from backend structure
      final debts = <Debt>[];
      for (final debtMap in debtsArray) {
        try {
          final debt = _parseDebtFromListStructure(debtMap as Map<String, dynamic>);
          debts.add(debt);
        } catch (e) {
          logger.w('DebtRepository: Failed to parse debt: $e, continuing with others');
          // Continue parsing other debts instead of failing completely
        }
      }
      
      logger.d('DebtRepository: Successfully parsed ${debts.length} debts');
      
      // Parse pagination info from backend response
      final totalCount = paginationData['totalItems'] ?? debts.length;
      final currentPage = paginationData['currentPage'] ?? page;
      final totalPages = paginationData['totalPages'] ?? 1;
      final hasNextPage = paginationData['hasNextPage'] ?? false;
      final hasPreviousPage = paginationData['hasPrevPage'] ?? false;
      
      final result = DebtListResult(
        debts: debts,
        totalCount: totalCount,
        currentPage: currentPage,
        totalPages: totalPages,
        hasNextPage: hasNextPage,
        hasPreviousPage: hasPreviousPage,
      );
      
      logger.i('DebtRepository: Successfully retrieved ${debts.length} debts');
      return Right(result);
    } on ApiException catch (e) {
      logger.e('DebtRepository: API error getting debts: $e');
      
      // Handle specific authentication errors
      if (e.code == 'unauthorized' || e.code == 'token_expired') {
        return Left(AuthFailure(message: 'Your session has expired. Please log in again.', code: e.code));
      }
      
      return Left(ServerFailure(message: 'Failed to fetch debts: ${e.message}', code: e.code));
    } catch (e, stackTrace) {
      logger.e('DebtRepository: Error getting debts: $e');
      logger.e('DebtRepository: Stack trace: $stackTrace');
      return Left(ServerFailure(message: 'Failed to fetch debts: $e'));
    }
  }

  @override
  Future<Either<Failure, Debt>> getDebtById(String debtId) async {
    try {
      logger.d('DebtRepository: Getting debt details for ID: $debtId');
      
      final debtModel = await _remoteDataSource.getDebtById(debtId);
      
      logger.i('DebtRepository: Got debt detail response');
      return Right(debtModel.toDomain());
    } on ApiException catch (e) {
      logger.e('DebtRepository: API error getting debt $debtId: $e');
      
      // Handle specific authentication errors
      if (e.code == 'unauthorized' || e.code == 'token_expired') {
        return Left(AuthFailure(message: 'Your session has expired. Please log in again.', code: e.code));
      }
      
      if (e.code == 'not_found') {
        return Left(NotFoundFailure(message: 'Debt not found.', code: e.code));
      }
      
      return Left(ServerFailure(message: 'Failed to get debt: ${e.message}', code: e.code));
    } catch (e, stackTrace) {
      logger.e('DebtRepository: Unexpected error getting debt $debtId: $e');
      logger.e('DebtRepository: Stack trace: $stackTrace');
      return Left(ServerFailure(message: 'Failed to get debt: $e'));
    }
  }

  @override
  Future<Either<Failure, Debt>> updateDebt({
    required String debtId,
    double? amount,
    DateTime? dueDate,
    String? description,
    DebtStatus? status,
  }) async {
    try {
      logger.d('DebtRepository: Updating debt: $debtId');
      
      final data = <String, dynamic>{
        if (amount != null) 'debtAmount': amount,
        if (dueDate != null) 'dueDate': dueDate.toIso8601String(),
        if (description != null) 'description': description,
        if (status != null) 'status': status.value,
      };

      final debtModel = await _remoteDataSource.updateDebt(debtId, data);
      
      logger.i('DebtRepository: Debt updated successfully');
      return Right(debtModel.toDomain());
    } on ApiException catch (e) {
      logger.e('DebtRepository: API error updating debt: $e');
      
      // Handle specific authentication errors
      if (e.code == 'unauthorized' || e.code == 'token_expired') {
        return Left(AuthFailure(message: 'Your session has expired. Please log in again.', code: e.code));
      }
      
      if (e.code == 'not_found') {
        return Left(NotFoundFailure(message: 'Debt not found.', code: e.code));
      }
      
      if (e.code == 'validation_error' || e.code == 'bad_request') {
        return Left(ValidationFailure(message: e.message, code: e.code, data: e.data));
      }
      
      return Left(ServerFailure(message: 'Failed to update debt: ${e.message}', code: e.code));
    } catch (e, stackTrace) {
      logger.e('DebtRepository: Error updating debt: $e');
      logger.e('DebtRepository: Stack trace: $stackTrace');
      return Left(ServerFailure(message: 'Failed to update debt: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteDebt(String debtId) async {
    try {
      logger.d('DebtRepository: Deleting debt: $debtId');
      
      await _remoteDataSource.deleteDebt(debtId);
      
      logger.i('DebtRepository: Debt deleted successfully');
      return const Right(true);
    } on ApiException catch (e) {
      logger.e('DebtRepository: API error deleting debt: $e');
      
      // Handle specific authentication errors
      if (e.code == 'unauthorized' || e.code == 'token_expired') {
        return Left(AuthFailure(message: 'Your session has expired. Please log in again.', code: e.code));
      }
      
      if (e.code == 'not_found') {
        return Left(NotFoundFailure(message: 'Debt not found.', code: e.code));
      }
      
      return Left(ServerFailure(message: 'Failed to delete debt: ${e.message}', code: e.code));
    } catch (e, stackTrace) {
      logger.e('DebtRepository: Error deleting debt: $e');
      logger.e('DebtRepository: Stack trace: $stackTrace');
      return Left(ServerFailure(message: 'Failed to delete debt: $e'));
    }
  }

  @override
  Future<Either<Failure, Debt>> addPaymentToDebt({
    required String debtId,
    required double amount,
    required PaymentMethod paymentMethod,
    String? notes,
    DateTime? paymentDate,
  }) async {
    try {
      logger.d('DebtRepository: Adding payment to debt: $debtId, amount: $amount');
      
      final data = <String, dynamic>{
        'amount': amount,
        'paymentMethod': paymentMethod.value,
        if (notes != null) 'notes': notes,
        if (paymentDate != null) 'paymentDate': paymentDate.toIso8601String(),
      };

      final debtModel = await _remoteDataSource.addPaymentToDebt(debtId, data);
      
      logger.i('DebtRepository: Payment added successfully');
      return Right(debtModel.toDomain());
    } on ApiException catch (e) {
      logger.e('DebtRepository: API error adding payment: $e');
      
      // Handle specific authentication errors
      if (e.code == 'unauthorized' || e.code == 'token_expired') {
        return Left(AuthFailure(message: 'Your session has expired. Please log in again.', code: e.code));
      }
      
      if (e.code == 'not_found') {
        return Left(NotFoundFailure(message: 'Debt not found.', code: e.code));
      }
      
      if (e.code == 'validation_error' || e.code == 'bad_request') {
        return Left(ValidationFailure(message: e.message, code: e.code, data: e.data));
      }
      
      return Left(ServerFailure(message: 'Failed to add payment: ${e.message}', code: e.code));
    } catch (e, stackTrace) {
      logger.e('DebtRepository: Error adding payment: $e');
      logger.e('DebtRepository: Stack trace: $stackTrace');
      return Left(ServerFailure(message: 'Failed to add payment: $e'));
    }
  }

  @override
  Future<Either<Failure, DebtAnalytics>> getDebtAnalytics({
    DateTime? startDate,
    DateTime? endDate,
    String? shopId,
  }) async {
    try {
      logger.d('DebtRepository: Getting debt analytics');
      
      final queryParams = <String, dynamic>{
        if (startDate != null) 'startDate': startDate.toIso8601String(),
        if (endDate != null) 'endDate': endDate.toIso8601String(),
        if (shopId != null) 'shopId': shopId,
      };

      final response = await _remoteDataSource.getDebtAnalytics(queryParams);
      
      logger.d('DebtRepository: Raw analytics response: $response');
      
      // Backend returns: { success, message, data: { overview, financial, riskAnalysis, ... } }
      final dataContent = response['data'] as Map<String, dynamic>? ?? {};
      
      // Extract data with exact backend structure from getDebtStats.js
      final overviewData = dataContent['overview'] as Map<String, dynamic>? ?? {};
      final financialData = dataContent['financial'] as Map<String, dynamic>? ?? {};
      final riskAnalysisData = dataContent['riskAnalysis'] as Map<String, dynamic>? ?? {};
      
      logger.d('DebtRepository: Raw response keys: ${response.keys}');
      logger.d('DebtRepository: Data content keys: ${dataContent.keys}');
      logger.d('DebtRepository: Overview data: $overviewData');
      logger.d('DebtRepository: Financial data: $financialData');
      
      // Extract values using exact backend field names from getDebtStats.js
      final totalDebts = overviewData['totalDebts'] ?? 0;
      final activeDebts = overviewData['activeDebts'] ?? 0;
      final overdueDebts = overviewData['overdueDebts'] ?? 0;
      final completedDebts = overviewData['paidDebts'] ?? 0; // Backend uses 'paidDebts'
      
      final totalAmount = (financialData['totalLent'] ?? 0).toDouble();
      final totalPaid = (financialData['totalPaid'] ?? 0).toDouble();
      final totalOutstanding = (financialData['totalOutstanding'] ?? 0).toDouble();
      final averageAmount = (financialData['averageDebtAmount'] ?? 0).toDouble();
      final collectionRate = (financialData['collectionRate'] ?? 0).toDouble();
      
      logger.d('DebtRepository: Extracted stats - total: $totalDebts, active: $activeDebts, overdue: $overdueDebts');
      
      // Extract risk distribution from riskAnalysis
      final riskDistData = <String, int>{};
      if (riskAnalysisData.containsKey('riskDistribution')) {
        final riskDist = riskAnalysisData['riskDistribution'] as Map<String, dynamic>? ?? {};
        for (final entry in riskDist.entries) {
          final distInfo = entry.value as Map<String, dynamic>? ?? {};
          riskDistData[entry.key] = distInfo['count'] ?? 0;
        }
      } else {
        // Fallback: estimate from totals if specific risk breakdown not available
        riskDistData['Low Risk'] = (totalDebts * 0.6).round();
        riskDistData['Medium Risk'] = (totalDebts * 0.3).round();
        riskDistData['High Risk'] = (totalDebts * 0.1).round();
      }
      
      // Create status distribution
      final statusDistData = <String, int>{
        'active': activeDebts,
        'completed': completedDebts,
        'overdue': overdueDebts,
      };
      
      final analytics = DebtAnalytics(
        stats: DebtStats(
          totalDebts: totalDebts,
          totalAmount: totalAmount,
          totalPaid: totalPaid,
          totalOutstanding: totalOutstanding,
          activeDebts: activeDebts,
          overdueDebts: overdueDebts,
          completedDebts: completedDebts,
          averageDebtAmount: averageAmount,
          collectionRate: collectionRate,
        ),
        riskDistribution: riskDistData.entries.map((entry) => 
          RiskDistribution(
            riskLevel: _parseRiskLevel(entry.key),
            count: entry.value,
            percentage: riskDistData.values.fold(0, (a, b) => a + b) > 0 
              ? (entry.value / riskDistData.values.fold(0, (a, b) => a + b)) * 100 
              : 0.0,
          )
        ).toList(),
        statusDistribution: statusDistData.entries.map((entry) => 
          StatusDistribution(
            status: _parseDebtStatus(entry.key),
            count: entry.value,
            percentage: statusDistData.values.fold(0, (a, b) => a + b) > 0 
              ? (entry.value / statusDistData.values.fold(0, (a, b) => a + b)) * 100 
              : 0.0,
          )
        ).toList(),
        monthlyTrends: [], // Empty for now - would need monthly data from API
        paymentMetrics: PaymentMetrics(
          averagePaymentTime: _extractDouble(dataContent, ['performance', 'averagePaymentDelay']),
          onTimePaymentRate: collectionRate,
          defaultRate: 0.0, // Would need calculation from API
          mostUsedPaymentMethod: PaymentMethod.cash, // Default - would need from API
        ),
      );
      
      logger.i('DebtRepository: Successfully retrieved debt analytics');
      return Right(analytics);
    } on ApiException catch (e) {
      logger.e('DebtRepository: API error getting debt analytics: $e');
      
      // Handle specific authentication errors
      if (e.code == 'unauthorized' || e.code == 'token_expired') {
        return Left(AuthFailure(message: 'Your session has expired. Please log in again.', code: e.code));
      }
      
      return Left(ServerFailure(message: 'Failed to fetch debt analytics: ${e.message}', code: e.code));
    } catch (e, stackTrace) {
      logger.e('DebtRepository: Error getting debt analytics: $e');
      logger.e('DebtRepository: Stack trace: $stackTrace');
      return Left(ServerFailure(message: 'Failed to fetch debt analytics: $e'));
    }
  }

  /// Helper method to extract integer value from map with multiple possible keys
  int _extractInt(Map<String, dynamic> map, List<String> possibleKeys) {
    for (final key in possibleKeys) {
      final value = map[key];
      if (value != null) {
        if (value is int) return value;
        if (value is double) return value.round();
        if (value is String) return int.tryParse(value) ?? 0;
      }
    }
    return 0;
  }

  /// Helper method to extract double value from map with multiple possible keys
  double _extractDouble(Map<String, dynamic> map, List<String> possibleKeys) {
    for (final key in possibleKeys) {
      final value = map[key];
      if (value != null) {
        if (value is double) return value;
        if (value is int) return value.toDouble();
        if (value is String) return double.tryParse(value) ?? 0.0;
      }
    }
    return 0.0;
  }

  @override
  Future<Either<Failure, List<Debt>>> getCustomerDebts(String customerId) async {
    try {
      logger.d('DebtRepository: Getting debts for customer: $customerId');
      
      final debtModels = await _remoteDataSource.getCustomerDebts(customerId);
      final debts = debtModels.map((model) => model.toDomain()).toList();
      
      logger.i('DebtRepository: Successfully retrieved ${debts.length} customer debts');
      return Right(debts);
    } on ApiException catch (e) {
      logger.e('DebtRepository: API error getting customer debts: $e');
      
      // Handle specific authentication errors
      if (e.code == 'unauthorized' || e.code == 'token_expired') {
        return Left(AuthFailure(message: 'Your session has expired. Please log in again.', code: e.code));
      }
      
      if (e.code == 'not_found') {
        return Left(NotFoundFailure(message: 'Customer not found.', code: e.code));
      }
      
      return Left(ServerFailure(message: 'Failed to fetch customer debts: ${e.message}', code: e.code));
    } catch (e, stackTrace) {
      logger.e('DebtRepository: Error getting customer debts: $e');
      logger.e('DebtRepository: Stack trace: $stackTrace');
      return Left(ServerFailure(message: 'Failed to fetch customer debts: $e'));
    }
  }

  /// Helper method to parse risk level string to enum
  RiskLevel _parseRiskLevel(String riskLevel) {
    switch (riskLevel.toLowerCase()) {
      case 'low':
      case 'low risk':
        return RiskLevel.low;
      case 'medium':
      case 'medium risk':
        return RiskLevel.medium;
      case 'high':
      case 'high risk':
        return RiskLevel.high;
      case 'critical':
      case 'critical risk':
        return RiskLevel.critical;
      default:
        return RiskLevel.low;
    }
  }

  /// Helper method to parse debt status string to enum
  DebtStatus _parseDebtStatus(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return DebtStatus.active;
      case 'completed':
      case 'paid':
        return DebtStatus.completed;
      case 'overdue':
        return DebtStatus.overdue;
      case 'defaulted':
        return DebtStatus.defaulted;
      case 'cancelled':
        return DebtStatus.cancelled;
      default:
        return DebtStatus.active;
    }
  }

  /// Helper method to convert risk level enum to string
  String _riskLevelToString(RiskLevel riskLevel) {
    switch (riskLevel) {
      case RiskLevel.low:
        return 'low';
      case RiskLevel.medium:
        return 'medium';
      case RiskLevel.high:
        return 'high';
      case RiskLevel.critical:
        return 'critical';
    }
  }

  /// Parse debt from getAllDebts response structure
  Debt _parseDebtFromListStructure(Map<String, dynamic> debtMap) {
    // Backend structure: { debtId, customer: {name, type}, debt: {amount, outstanding, paid}, timeline: {...}, risk: {...}, status, description }
    final customerData = debtMap['customer'] as Map<String, dynamic>? ?? {};
    final debtData = debtMap['debt'] as Map<String, dynamic>? ?? {};
    final timelineData = debtMap['timeline'] as Map<String, dynamic>? ?? {};
    final riskData = debtMap['risk'] as Map<String, dynamic>? ?? {};
    
    // Helper to safely convert to double
    double safeDouble(dynamic value, [double defaultValue = 0.0]) {
      if (value == null) return defaultValue;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.tryParse(value) ?? defaultValue;
      return defaultValue;
    }
    
    // Helper to safely convert to int
    int safeInt(dynamic value, [int defaultValue = 0]) {
      if (value == null) return defaultValue;
      if (value is int) return value;
      if (value is double) return value.round();
      if (value is String) return int.tryParse(value) ?? defaultValue;
      return defaultValue;
    }
    
    // Helper to safely parse DateTime
    DateTime safeDateTime(dynamic value, [DateTime? defaultValue]) {
      final fallback = defaultValue ?? DateTime.now();
      if (value == null) return fallback;
      if (value is String) {
        try {
          return DateTime.parse(value);
        } catch (e) {
          return fallback;
        }
      }
      return fallback;
    }
    
    final totalPaid = safeDouble(debtData['paid']);
    final debtAmount = safeDouble(debtData['amount']);
    
    return Debt(
      debtId: debtMap['debtId']?.toString() ?? 'UNKNOWN',
      customerId: 'UNKNOWN', // Not provided in list structure
      customerName: customerData['name']?.toString() ?? 'Unknown Customer',
      shopId: 'SHOP001', // Default shop ID
      amount: debtAmount,
      interestRate: null,
      dueDate: safeDateTime(timelineData['dueDate']),
      createdAt: safeDateTime(timelineData['createdDate']),
      updatedAt: safeDateTime(debtMap['updatedAt'], safeDateTime(timelineData['createdDate'])),
      status: _parseDebtStatus(debtMap['status']?.toString() ?? 'active'),
      description: debtMap['description']?.toString(),
      payments: [], // Payments loaded separately
      totalPaid: totalPaid,
      remainingAmount: debtAmount - totalPaid,
      isOverdue: timelineData['isOverdue'] == true,
      daysPastDue: safeInt(timelineData['daysOverdue']),
      riskAssessment: RiskAssessment(
        riskScore: safeDouble(debtData['paidRatio']) * 100,
        riskLevel: _parseRiskLevel(riskData['level']?.toString() ?? 'low'),
        source: 'ML',
        assessmentDate: safeDateTime(timelineData['createdDate']),
      ),
    );
  }

} 
