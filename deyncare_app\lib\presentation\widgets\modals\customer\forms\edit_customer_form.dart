import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/utils/business_validation.dart';
import 'package:deyncare_app/domain/models/customer.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_bloc.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_event.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_state.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_constants.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/form_mixins.dart';
import 'package:deyncare_app/presentation/widgets/skeleton_loader.dart';

/// Enhanced Edit Customer Form with Pre-populated Data
class EditCustomerForm extends StatefulWidget {
  final Customer customer;
  final BuildContext? parentContext;

  const EditCustomerForm({
    super.key, 
    required this.customer,
    this.parentContext,
  });

  @override
  State<EditCustomerForm> createState() => _EditCustomerFormState();
}

class _EditCustomerFormState extends State<EditCustomerForm> 
    with ModalFormMixin<EditCustomerForm>, BlocListenerMixin<EditCustomerForm> {
  
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _notesController = TextEditingController();
  
  String _selectedCustomerType = 'New';
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _populateFields();
  }

  /// Pre-populate form fields with existing customer data
  void _populateFields() {
    _nameController.text = widget.customer.fullName;
    _phoneController.text = widget.customer.phone;
    _emailController.text = widget.customer.email;
    _addressController.text = widget.customer.address ?? '';
    _notesController.text = widget.customer.notes ?? '';
    
    // Map customer status to customer type for editing
    _selectedCustomerType = _mapStatusToType(widget.customer.status);
  }

  /// Map customer status to customer type for form
  String _mapStatusToType(CustomerStatus status) {
    switch (status) {
      case CustomerStatus.active:
        return 'returning';
      case CustomerStatus.inactive:
        return 'returning';
      case CustomerStatus.suspended:
        return 'returning';
      default:
        return 'new';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: (widget.parentContext ?? context).read<CustomerBloc>(),
      child: buildBlocListener<CustomerBloc, CustomerState>(
        listener: (context, state) {
          if (state is CustomerUpdated) {
            setState(() => _isSubmitting = false);
            handleSuccess('Customer updated successfully!');
            Navigator.of(context).pop();
          } else if (state is CustomerError) {
            setState(() => _isSubmitting = false);
            handleError(state.message);
          } else if (state is CustomerUpdating) {
            setState(() => _isSubmitting = true);
          }
        },
        child: _isSubmitting ? _buildSubmittingState() : _buildForm(),
      ),
    );
  }

  /// Enhanced submitting state with skeleton loader
  Widget _buildSubmittingState() {
    return Padding(
      padding: ModalConstants.defaultPadding,
      child: Column(
        children: [
          // Header skeleton
          _buildHeaderSkeleton(),
          ModalConstants.largeSpacing,
          
          // Form fields skeleton
          ...List.generate(6, (index) => Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildFormFieldSkeleton(),
          )),
          
          ModalConstants.largeSpacing,
          
          // Submitting indicator
          Container(
            padding: const EdgeInsets.all(24),
            decoration: ModalConstants.modernCardDecoration(
              backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
              borderColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
            ),
            child: Column(
              children: [
                SizedBox(
                  width: 40,
                  height: 40,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
                  ),
                ),
                ModalConstants.defaultSpacing,
                Text(
                  'Updating Customer...',
                  style: ModalConstants.subtitleStyle(context, color: Theme.of(context).colorScheme.primary),
                ),
                ModalConstants.smallSpacing,
                Text(
                  'Please wait while we save the changes.',
                  style: ModalConstants.captionStyle(context),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Header skeleton
  Widget _buildHeaderSkeleton() {
    return Container(
      decoration: ModalConstants.modernCardDecoration(),
      child: Padding(
        padding: ModalConstants.cardPadding,
        child: Column(
          children: [
            SkeletonLoader.circular(size: 48),
            ModalConstants.defaultSpacing,
            SkeletonLoader.text(width: 150, height: 24),
            ModalConstants.defaultSpacing,
            SkeletonLoader.text(width: 200, height: 16),
          ],
        ),
      ),
    );
  }

  /// Form field skeleton
  Widget _buildFormFieldSkeleton() {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(ModalConstants.defaultRadius),
        border: Border.all(
          color: Theme.of(context).dividerColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            SkeletonLoader.circular(size: 20),
            ModalConstants.defaultHorizontalSpacing,
            Expanded(
              child: SkeletonLoader.text(width: double.infinity, height: 16),
            ),
          ],
        ),
      ),
    );
  }

  /// Enhanced form with modern design
  Widget _buildForm() {
    return Padding(
      padding: ModalConstants.defaultPadding,
      child: Form(
        key: formKey,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildModernHeader(context),
              ModalConstants.largeSpacing,
              _buildCustomerNameField(),
              ModalConstants.defaultSpacing,
              _buildCustomerTypeField(),
              ModalConstants.defaultSpacing,
              _buildPhoneField(),
              ModalConstants.defaultSpacing,
              _buildEmailField(),
              ModalConstants.defaultSpacing,
              _buildAddressField(),
              ModalConstants.defaultSpacing,
              _buildNotesField(),
              ModalConstants.extraLargeSpacing,
              _buildModernActionButtons(),
              ModalConstants.largeSpacing,
            ],
          ),
        ),
      ),
    );
  }

  /// Modern header with enhanced design
  Widget _buildModernHeader(BuildContext context) {
    return Container(
      decoration: ModalConstants.modernCardDecoration(),
      child: Padding(
        padding: ModalConstants.cardPadding,
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(ModalConstants.defaultRadius),
              ),
              child: Icon(
                Icons.edit_rounded,
                size: 32,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            ModalConstants.defaultSpacing,
            Text(
              'Edit Customer',
              style: ModalConstants.sectionTitleStyle(context),
            ),
            ModalConstants.smallSpacing,
            Text(
              'Update ${widget.customer.fullName}\'s information below. Changes will be saved immediately.',
              style: ModalConstants.subtitleStyle(context),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Customer Name Field
  Widget _buildCustomerNameField() {
    return buildFormField(
      controller: _nameController,
      labelText: 'Customer Name *',
      hintText: 'Enter customer full name',
      prefixIcon: const Icon(Icons.person_rounded),
      validator: (value) {
        final validation = BusinessValidation.validateCustomerName(value ?? '');
        return validation.isValid ? null : validation.errorMessage;
      },
    );
  }

  /// Customer Type Field
  Widget _buildCustomerTypeField() {
    return buildDropdownField<String>(
      value: _selectedCustomerType,
      labelText: 'Customer Type *',
      hintText: 'Select customer type',
      prefixIcon: const Icon(Icons.category_rounded),
      items: const [
        DropdownMenuItem(value: 'new', child: Text('New Customer')),
        DropdownMenuItem(value: 'returning', child: Text('Returning Customer')),
      ],
      onChanged: (value) {
        setState(() => _selectedCustomerType = value ?? 'new');
      },
      validator: (value) => value == null ? 'Please select customer type' : null,
    );
  }

  /// Phone Field
  Widget _buildPhoneField() {
    return buildFormField(
      controller: _phoneController,
      labelText: 'Phone Number *',
      hintText: 'Enter phone number',
      prefixIcon: const Icon(Icons.phone_rounded),
      keyboardType: TextInputType.phone,
      validator: (value) {
        final validation = BusinessValidation.validatePhone(value ?? '');
        return validation.isValid ? null : validation.errorMessage;
      },
    );
  }

  /// Email Field
  Widget _buildEmailField() {
    return buildFormField(
      controller: _emailController,
      labelText: 'Email Address',
      hintText: 'Enter email address (optional)',
      prefixIcon: const Icon(Icons.email_rounded),
      keyboardType: TextInputType.emailAddress,
      validator: (value) {
        if (value != null && value.isNotEmpty) {
          final validation = BusinessValidation.validateEmail(value);
          return validation.isValid ? null : validation.errorMessage;
        }
        return null;
      },
    );
  }

  /// Address Field
  Widget _buildAddressField() {
    return buildFormField(
      controller: _addressController,
      labelText: 'Address',
      hintText: 'Enter customer address (optional)',
      prefixIcon: const Icon(Icons.location_on_rounded),
      maxLines: 2,
      validator: (value) {
        if (value != null && value.isNotEmpty) {
          final validation = BusinessValidation.validateAddress(value);
          return validation.isValid ? null : validation.errorMessage;
        }
        return null;
      },
    );
  }

  /// Notes Field
  Widget _buildNotesField() {
    return buildFormField(
      controller: _notesController,
      labelText: 'Notes',
      hintText: 'Enter additional notes (optional)',
      prefixIcon: const Icon(Icons.note_rounded),
      maxLines: 3,
      validator: (value) {
        if (value != null && value.isNotEmpty) {
          final validation = BusinessValidation.validateNotes(value);
          return validation.isValid ? null : validation.errorMessage;
        }
        return null;
      },
    );
  }

  /// Modern action buttons
  Widget _buildModernActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ModalConstants.secondaryActionButton(
            text: 'Cancel',
            onPressed: () => Navigator.of(context).pop(),
            icon: Icons.close_rounded,
            context: context,
          ),
        ),
        ModalConstants.defaultHorizontalSpacing,
        Expanded(
          flex: 2,
          child: ModalConstants.primaryActionButton(
            text: 'Update Customer',
            onPressed: _handleSubmit,
            icon: Icons.save_rounded,
            context: context,
          ),
        ),
      ],
    );
  }

  /// Handle form submission
  void _handleSubmit() {
    if (!formKey.currentState!.validate()) {
      handleError('Please fix the errors above');
      return;
    }

    // Create update event
    context.read<CustomerBloc>().add(UpdateCustomer(
      customerId: widget.customer.customerId,
      customerName: _nameController.text.trim().isNotEmpty ? _nameController.text.trim() : null,
      phone: _phoneController.text.trim().isNotEmpty ? _phoneController.text.trim() : null,
      email: _emailController.text.trim().isNotEmpty ? _emailController.text.trim() : null,
      address: _addressController.text.trim().isNotEmpty ? _addressController.text.trim() : null,
      notes: _notesController.text.trim().isNotEmpty ? _notesController.text.trim() : null,
    ));
  }
} 