import 'package:equatable/equatable.dart';
import 'package:deyncare_app/data/models/customer_model.dart';

/// Customer States - aligned with backend responses
abstract class CustomerState extends Equatable {
  const CustomerState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class CustomerInitial extends CustomerState {
  const CustomerInitial();
}

/// Loading states
class CustomerLoading extends CustomerState {
  const CustomerLoading();
}

class CustomerListLoading extends CustomerState {
  const CustomerListLoading();
}

class CustomerDetailsLoading extends CustomerState {
  const CustomerDetailsLoading();
}

class CustomerCreating extends CustomerState {
  const CustomerCreating();
}

class CustomerUpdating extends CustomerState {
  const CustomerUpdating();
}

class CustomerDeleting extends CustomerState {
  const CustomerDeleting();
}

/// Success states
class CustomerListLoaded extends CustomerState {
  final CustomerListResponse response;
  final bool isRefreshing;

  const CustomerListLoaded({
    required this.response,
    this.isRefreshing = false,
  });

  @override
  List<Object> get props => [response, isRefreshing];

  CustomerListLoaded copyWith({
    CustomerListResponse? response,
    bool? isRefreshing,
  }) {
    return CustomerListLoaded(
      response: response ?? this.response,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }
}

class CustomerDetailsLoaded extends CustomerState {
  final CustomerDetailResponse response;

  const CustomerDetailsLoaded(this.response);

  @override
  List<Object> get props => [response];
}

class CustomerCreated extends CustomerState {
  final CustomerDetailResponse response;

  const CustomerCreated(this.response);

  /// Getter to access customer data easily  
  CustomerBasicInfo get customer => response.data?.customer ?? CustomerBasicInfo();

  @override
  List<Object> get props => [response];
}

class CustomerUpdated extends CustomerState {
  final CustomerDetailResponse response;

  const CustomerUpdated(this.response);

  @override
  List<Object> get props => [response];
}

class CustomerDeleted extends CustomerState {
  final String customerId;

  const CustomerDeleted(this.customerId);

  @override
  List<Object> get props => [customerId];
}

class CustomerDebtsLoaded extends CustomerState {
  final CustomerDebtsResponse response;

  const CustomerDebtsLoaded(this.response);

  @override
  List<Object> get props => [response];
}

/// Error states
class CustomerError extends CustomerState {
  final String message;
  final String? errorCode;

  const CustomerError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

class CustomerListError extends CustomerState {
  final String message;
  final String? errorCode;

  const CustomerListError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

class CustomerDetailsError extends CustomerState {
  final String message;
  final String? errorCode;

  const CustomerDetailsError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
} 