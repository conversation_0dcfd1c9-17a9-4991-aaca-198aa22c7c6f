import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/domain/usecases/payment/get_payment_history_use_case.dart';
import 'payment_event.dart';
import 'payment_state.dart';

/// PaymentBloc - Handles payment history functionality
/// Aligned with backend payment history endpoint
class PaymentBloc extends Bloc<PaymentEvent, PaymentState> {
  final GetPaymentHistoryUseCase _getPaymentHistoryUseCase;

  PaymentBloc({
    required GetPaymentHistoryUseCase getPaymentHistoryUseCase,
  })  : _getPaymentHistoryUseCase = getPaymentHistoryUseCase,
        super(const PaymentInitial()) {
    on<LoadPaymentHistory>(_onLoadPaymentHistory);
    on<RefreshPaymentHistory>(_onRefreshPaymentHistory);
    on<ClearPaymentHistory>(_onClearPaymentHistory);
  }

  /// Load payment history for a debt
  Future<void> _onLoadPaymentHistory(
      LoadPaymentHistory event, Emitter<PaymentState> emit) async {
    emit(const PaymentHistoryLoading());

    try {
      final result = await _getPaymentHistoryUseCase.execute(
        debtId: event.debtId,
      );

      emit(PaymentHistoryLoaded(payments: result));
    } catch (error) {
      emit(PaymentHistoryError(
        message: _mapErrorToMessage(error),
        errorCode: error.runtimeType.toString(),
      ));
    }
  }

  /// Refresh payment history
  Future<void> _onRefreshPaymentHistory(
      RefreshPaymentHistory event, Emitter<PaymentState> emit) async {
    if (state is PaymentHistoryLoaded) {
      emit((state as PaymentHistoryLoaded).copyWith(isRefreshing: true));
    }

    try {
      final result = await _getPaymentHistoryUseCase.execute(
        debtId: event.debtId,
      );

      emit(PaymentHistoryLoaded(
        payments: result,
        isRefreshing: false,
      ));
    } catch (error) {
      emit(PaymentHistoryError(
        message: _mapErrorToMessage(error),
        errorCode: error.runtimeType.toString(),
      ));
    }
  }

  /// Clear payment history
  void _onClearPaymentHistory(
      ClearPaymentHistory event, Emitter<PaymentState> emit) {
    emit(const PaymentInitial());
  }

  /// Map errors to user-friendly messages
  String _mapErrorToMessage(dynamic error) {
    if (error is ArgumentError) {
      return error.message;
    } else if (error is Exception) {
      return error.toString();
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }
} 