import 'package:equatable/equatable.dart';

abstract class SubscriptionEvent extends Equatable {
  const SubscriptionEvent();

  @override
  List<Object?> get props => [];
}

class LoadCurrentSubscription extends SubscriptionEvent {}

class LoadAvailablePlans extends SubscriptionEvent {}

class UpgradeSubscription extends SubscriptionEvent {
  final String planType;
  final String? message;

  const UpgradeSubscription({
    required this.planType,
    this.message,
  });

  @override
  List<Object?> get props => [planType, message];
}

class ChangePlan extends SubscriptionEvent {
  final String planId;
  final String? planType;
  final bool prorated;
  final String? paymentMethod;
  final Map<String, dynamic>? paymentDetails;

  const ChangePlan({
    required this.planId,
    this.planType,
    this.prorated = true,
    this.paymentMethod,
    this.paymentDetails,
  });

  @override
  List<Object?> get props => [planId, planType, prorated, paymentMethod, paymentDetails];
}

class CancelSubscription extends SubscriptionEvent {
  final String? reason;
  final String? feedback;
  final bool immediateEffect;

  const CancelSubscription({
    this.reason,
    this.feedback,
    this.immediateEffect = false,
  });

  @override
  List<Object?> get props => [reason, feedback, immediateEffect];
}

class UpdateAutoRenewal extends SubscriptionEvent {
  final bool autoRenew;

  const UpdateAutoRenewal({required this.autoRenew});

  @override
  List<Object?> get props => [autoRenew];
}

class RenewSubscription extends SubscriptionEvent {
  final String paymentMethod;
  final String transactionId;
  final double? amount;
  final String currency;
  final String? notes;

  const RenewSubscription({
    required this.paymentMethod,
    required this.transactionId,
    this.amount,
    this.currency = 'USD',
    this.notes,
  });

  @override
  List<Object?> get props => [paymentMethod, transactionId, amount, currency, notes];
}

class PayWithEvc extends SubscriptionEvent {
  final String subscriptionId;
  final String phone;
  final double amount;
  final String? planType;

  const PayWithEvc({
    required this.subscriptionId,
    required this.phone,
    required this.amount,
    this.planType,
  });

  @override
  List<Object?> get props => [subscriptionId, phone, amount, planType];
}

class RecordOfflinePayment extends SubscriptionEvent {
  final String subscriptionId;
  final double amount;
  final String method;
  final String? payerName;
  final String? payerPhone;
  final String? notes;
  final String? planType;
  final String? receiptPath;

  const RecordOfflinePayment({
    required this.subscriptionId,
    required this.amount,
    required this.method,
    this.payerName,
    this.payerPhone,
    this.notes,
    this.planType,
    this.receiptPath,
  });

  @override
  List<Object?> get props => [
        subscriptionId,
        amount,
        method,
        payerName,
        payerPhone,
        notes,
        planType,
        receiptPath,
      ];
}

class LoadPaymentMethods extends SubscriptionEvent {}

class RefreshSubscription extends SubscriptionEvent {}

class ResetSubscriptionState extends SubscriptionEvent {} 