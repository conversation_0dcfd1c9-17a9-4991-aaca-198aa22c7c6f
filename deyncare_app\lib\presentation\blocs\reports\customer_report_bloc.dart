import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:deyncare_app/domain/usecases/reports/generate_customer_report_use_case.dart';

// Events
abstract class CustomerReportEvent extends Equatable {
  const CustomerReportEvent();

  @override
  List<Object?> get props => [];
}

class GenerateCustomerReportEvent extends CustomerReportEvent {
  final String? month;
  final String? year;
  final String? startDate;
  final String? endDate;
  final String dateRange;

  const GenerateCustomerReportEvent({
    this.month,
    this.year,
    this.startDate,
    this.endDate,
    this.dateRange = 'monthly',
  });

  @override
  List<Object?> get props => [month, year, startDate, endDate, dateRange];
}

class ResetCustomerReportEvent extends CustomerReportEvent {}

// States
abstract class CustomerReportState extends Equatable {
  const CustomerReportState();

  @override
  List<Object?> get props => [];
}

class CustomerReportInitial extends CustomerReportState {}

class CustomerReportGenerating extends CustomerReportState {}

class CustomerReportGenerated extends CustomerReportState {
  final String pdfPath;
  final String reportPeriod;

  const CustomerReportGenerated({
    required this.pdfPath,
    required this.reportPeriod,
  });

  @override
  List<Object?> get props => [pdfPath, reportPeriod];
}

class CustomerReportError extends CustomerReportState {
  final String message;

  const CustomerReportError(this.message);

  @override
  List<Object?> get props => [message];
}

// BLoC
class CustomerReportBloc extends Bloc<CustomerReportEvent, CustomerReportState> {
  final GenerateCustomerReportUseCase generateCustomerReportUseCase;

  CustomerReportBloc({
    required this.generateCustomerReportUseCase,
  }) : super(CustomerReportInitial()) {
    on<GenerateCustomerReportEvent>(_onGenerateCustomerReport);
    on<ResetCustomerReportEvent>(_onResetCustomerReport);
  }

  Future<void> _onGenerateCustomerReport(
    GenerateCustomerReportEvent event,
    Emitter<CustomerReportState> emit,
  ) async {
    emit(CustomerReportGenerating());

    try {
      final pdfPath = await generateCustomerReportUseCase.execute(
        month: event.month,
        year: event.year,
        startDate: event.startDate,
        endDate: event.endDate,
        dateRange: event.dateRange,
      );

      final reportPeriod = _getReportPeriodDescription(
        month: event.month,
        year: event.year,
        startDate: event.startDate,
        endDate: event.endDate,
        dateRange: event.dateRange,
      );

      emit(CustomerReportGenerated(
        pdfPath: pdfPath,
        reportPeriod: reportPeriod,
      ));
    } catch (error) {
      emit(CustomerReportError(error.toString()));
    }
  }

  void _onResetCustomerReport(
    ResetCustomerReportEvent event,
    Emitter<CustomerReportState> emit,
  ) {
    emit(CustomerReportInitial());
  }

  String _getReportPeriodDescription({
    String? month,
    String? year,
    String? startDate,
    String? endDate,
    String dateRange = 'monthly',
  }) {
    if (month != null && year != null) {
      final monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      return '${monthNames[int.parse(month) - 1]} $year';
    }

    if (startDate != null && endDate != null) {
      return '$startDate to $endDate';
    }

    switch (dateRange) {
      case 'daily':
        return 'Today';
      case 'weekly':
        return 'This Week';
      case 'monthly':
        return 'This Month';
      case 'yearly':
        return 'This Year';
      default:
        return 'All Time';
    }
  }
}