import 'dart:async';
import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:deyncare_app/data/services/connectivity_service.dart';

/// Handles device connectivity status and waiting for connection
class ConnectivityHandler {
  final ConnectivityService _connectivityService;
  final Duration _timeout;
  
  ConnectivityHandler({
    ConnectivityService? connectivityService,
    Duration timeout = const Duration(seconds: 30),
  }) : 
    _connectivityService = connectivityService ?? ConnectivityService(),
    _timeout = timeout;
  
  /// Initialize connectivity service
  Future<void> initialize() async {
    await _connectivityService.initialize();
  }
  
  /// Check if device is currently connected
  bool isConnected() {
    return _connectivityService.isConnected();
  }
  
  /// Check if the given connectivity status represents a connected state
  bool _isConnectedStatus(ConnectivityStatus status) {
    return status == ConnectivityStatus.wifi || 
           status == ConnectivityStatus.mobile || 
           status == ConnectivityStatus.ethernet;
  }
  
  /// Wait for connectivity if device is offline
  Future<void> waitForConnectivityIfOffline() async {
    // Check current connectivity status
    if (!_connectivityService.isConnected()) {
      // No connection, wait for connection to be restored or timeout
      final completer = Completer<void>();
      late StreamSubscription<ConnectivityStatus> subscription;
      
      // Listen for connectivity changes
      subscription = _connectivityService.statusStream.listen((status) {
        if (_isConnectedStatus(status) && !completer.isCompleted) {
          completer.complete();
          subscription.cancel();
        }
      });
      
      // Set timeout to avoid waiting indefinitely
      return completer.future.timeout(
        _timeout,
        onTimeout: () {
          subscription.cancel();
          throw ApiException(
            message: 'Connection timeout. Please check your internet connection and try again.',
            code: 'connection_timeout',
          );
        },
      );
    }
  }
}
