import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/widgets/common_app_bar.dart';
import 'package:deyncare_app/presentation/widgets/common_button.dart';
import 'package:deyncare_app/presentation/screens/debt/widgets/debt_form.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_bloc.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_state.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_event.dart';
import 'package:deyncare_app/injection_container.dart' as di;

/// Screen for creating a new debt
class CreateDebtScreen extends StatelessWidget {
  final String? preSelectedCustomerId;
  
  const CreateDebtScreen({
    super.key,
    this.preSelectedCustomerId,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<DebtBloc>(),
      child: CreateDebtView(preSelectedCustomerId: preSelectedCustomerId),
    );
  }
}

class CreateDebtView extends StatefulWidget {
  final String? preSelectedCustomerId;
  
  const CreateDebtView({
    super.key,
    this.preSelectedCustomerId,
  });

  @override
  State<CreateDebtView> createState() => _CreateDebtViewState();
}

class _CreateDebtViewState extends State<CreateDebtView> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  String? _selectedCustomerId;
  DateTime? _selectedDueDate;
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _selectedCustomerId = widget.preSelectedCustomerId;
    _selectedDueDate = DateTime.now().add(const Duration(days: 30)); // Default 30 days
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CommonAppBar(
        title: 'Add Debt',
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => _handleBackPressed(context),
        ),
      ),
      body: BlocConsumer<DebtBloc, DebtState>(
        listener: _handleStateChanges,
        builder: (context, state) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Header
                  _buildHeader(context),
                  
                  const SizedBox(height: 24),
                  
                  // Form
                  DebtForm(
                    amountController: _amountController,
                    descriptionController: _descriptionController,
                    selectedCustomerId: _selectedCustomerId,
                    selectedDueDate: _selectedDueDate,
                    onCustomerChanged: (customerId) {
                      setState(() {
                        _selectedCustomerId = customerId;
                      });
                    },
                    onDueDateChanged: (date) {
                      setState(() {
                        _selectedDueDate = date;
                      });
                    },
                    isCustomerSelectionEnabled: widget.preSelectedCustomerId == null,
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Submit button
                  CommonButton(
                    label: 'Create Debt',
                    onPressed: _isSubmitting ? null : _submitForm,
                    isLoading: _isSubmitting,
                    loadingText: 'Creating...',
                    icon: const Icon(Icons.add),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Cancel button
                  CommonButton(
                    label: 'Cancel',
                    type: ButtonType.outlined,
                    onPressed: _isSubmitting ? null : () => _handleBackPressed(context),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Debt Information',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Fill in the details to create a new debt record',
          style: TextStyle(
            fontSize: 16,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  void _handleStateChanges(BuildContext context, DebtState state) {
    if (state is DebtCreating) {
      setState(() {
        _isSubmitting = true;
      });
    } else {
      setState(() {
        _isSubmitting = false;
      });
    }

    if (state is DebtCreated) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Debt ${state.debt.debtId} created successfully!'),
          backgroundColor: AppThemes.successColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
      Navigator.pop(context, true); // Return true to indicate success
    } else if (state is DebtError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: AppThemes.errorColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _submitForm() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedCustomerId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Please select a customer'),
          backgroundColor: AppThemes.errorColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    if (_selectedDueDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Please select a due date'),
          backgroundColor: AppThemes.errorColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    final createDebtEvent = CreateDebt(
      customerId: _selectedCustomerId!,
      debtAmount: double.parse(_amountController.text.trim()),
      dueDate: _selectedDueDate!,
      description: _descriptionController.text.trim().isNotEmpty 
          ? _descriptionController.text.trim() 
          : null,
    );

    context.read<DebtBloc>().add(createDebtEvent);
  }

  void _handleBackPressed(BuildContext context) {
    if (_hasUnsavedChanges()) {
      _showDiscardDialog(context);
    } else {
      Navigator.pop(context);
    }
  }

  bool _hasUnsavedChanges() {
    return _amountController.text.isNotEmpty ||
           _descriptionController.text.isNotEmpty ||
           _selectedCustomerId != widget.preSelectedCustomerId;
  }

  void _showDiscardDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).dialogBackgroundColor,
        title: Text(
          'Discard Changes?',
          style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
        ),
        content: Text(
          'You have unsaved changes. Are you sure you want to discard them?',
          style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(color: Theme.of(context).colorScheme.primary),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close screen
            },
            child: Text(
              'Discard',
              style: TextStyle(color: AppThemes.errorColor),
            ),
          ),
        ],
      ),
    );
  }
} 