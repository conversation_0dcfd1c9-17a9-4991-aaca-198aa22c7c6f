import 'package:deyncare_app/data/models/plan_model.dart';
import 'package:deyncare_app/data/services/plan/plan_remote_source.dart';
import 'package:deyncare_app/domain/repositories/plan_repository.dart';
import 'package:deyncare_app/core/mixins/connectivity_aware_service.dart';

/// Implementation of the PlanRepository interface
///
/// Connects the domain layer with the plan data sources
class PlanRepositoryImpl with ConnectivityAwareService implements PlanRepository {
  final PlanRemoteSource _remoteSource;

  /// Creates a new repository instance with required dependencies
  PlanRepositoryImpl({
    required PlanRemoteSource remoteSource,
  }) : _remoteSource = remoteSource;

  @override
  Future<List<PlanModel>> getPlans() async {
    return await executeWithConnectivity(
      operation: () async {
        final data = await _remoteSource.getPlans();
        return data.map((planJson) => PlanModel.fromJson(planJson)).toList();
      },
    );
  }

  @override
  Future<PlanModel> getPlanById(String planId) async {
    return await executeWithConnectivity(
      operation: () async {
        final data = await _remoteSource.getPlanById(planId);
        return PlanModel.fromJson(data);
      },
    );
  }
} 