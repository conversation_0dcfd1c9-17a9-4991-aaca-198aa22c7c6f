# 🔔 Admin Notification System Documentation

## Overview

DeynCare has a comprehensive notification system that allows **SuperAdmins** to send custom push notifications to **Admin/Shop Owner** mobile app users. The system supports targeted notifications to specific shops and broadcast messages to all admins.

## System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   SuperAdmin    │────│   Backend API    │────│  Admin Mobile   │
│   Dashboard     │    │   Notification   │    │      App        │
│                 │    │     Service      │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                               │
                               ▼
                       ┌──────────────────┐
                       │  Firebase FCM    │
                       │   Push Service   │
                       └──────────────────┘
```

## User Roles & Permissions

### 🎯 Notification Recipients (Admin Mobile App)
- **superAdmin**: Can receive all notifications
- **admin**: Shop owners, receive shop-specific and broadcast notifications  
- **employee**: Shop employees, receive shop-specific and broadcast notifications

### 📤 Notification Senders (Backend/Dashboard)
- **SuperAdmin**: Can send notifications via backend dashboard
- **System**: Automated notifications (debt created, payments, reminders)

## Custom Notification Types

### 1. 🏪 Shop-Targeted Notifications
Send notifications to admins of specific shops.

**Endpoint:** `POST /api/admin/notifications/push/shops`

**Payload:**
```json
{
  "shopIds": ["SHOP001", "SHOP002"],
  "title": "Payment Update Required",
  "message": "Please update your payment method for continued service.",
  "priority": "high",
  "actionUrl": "/payments/update",
  "actionLabel": "Update Payment",
  "scheduledAt": "2024-01-15T10:00:00Z"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Notification sent to 2/2 shops",
  "data": {
    "totalShops": 2,
    "successful": 2,
    "failed": 0,
    "results": [
      {
        "shopId": "SHOP001",
        "shopName": "ABC Store",
        "notificationId": "NOTIF_123456789",
        "success": true
      }
    ]
  }
}
```

### 2. 📢 Broadcast Notifications
Send notifications to all active admin users across all shops.

**Endpoint:** `POST /api/admin/notifications/push/broadcast`

**Payload:**
```json
{
  "title": "System Maintenance Notice",
  "message": "DeynCare will undergo scheduled maintenance on Jan 15, 2024 from 2-4 AM UTC.",
  "priority": "normal",
  "actionUrl": "/announcements/maintenance",
  "actionLabel": "View Details",
  "scheduledAt": "2024-01-14T18:00:00Z"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Broadcast notification sent successfully",
  "data": {
    "notificationId": "NOTIF_987654321"
  }
}
```

### 3. 💰 Debt Reminder Notifications
Automated notifications for debt management.

**Endpoint:** `POST /api/admin/notifications/push/debt-reminders`

**Payload:**
```json
{
  "reminderType": "7_days",
  "shopIds": ["SHOP001"]
}
```

**Reminder Types:**
- `7_days`: Debt due in 7 days
- `3_days`: Debt due in 3 days  
- `overdue`: Overdue debts

**Response:**
```json
{
  "success": true,
  "message": "7_days debt reminders processed",
  "data": {
    "reminderType": "7_days",
    "results": [
      {
        "shopId": "SHOP001",
        "success": true,
        "totalDebts": 15,
        "message": "15 debt reminders sent"
      }
    ]
  }
}
```

## Notification Routing Logic

### 🎯 Target Selection Algorithm
```javascript
if (notification.shopId && notification.shopId !== 'SYSTEM') {
  // Send to specific shop owners/admins
  result = await FirebaseService.sendToShopOwners([notification.shopId], pushData);
} else {
  // System-wide notification - broadcast to all admins
  result = await FirebaseService.sendBroadcastToAdmins(pushData);
}
```

### 📱 Device Token Management
- FCM tokens are stored per user with device info
- Invalid tokens are automatically cleaned up
- Only active users with valid tokens receive notifications
- Supports multiple devices per user

## Firebase Push Notification Payload

### Standard FCM Message Structure
```json
{
  "notification": {
    "title": "Payment Update Required",
    "body": "Please update your payment method for continued service."
  },
  "data": {
    "type": "admin_communication",
    "priority": "high",
    "actionUrl": "/payments/update",
    "actionLabel": "Update Payment",
    "notificationId": "NOTIF_123456789",
    "shopId": "SHOP001",
    "category": "admin_communication"
  },
  "android": {
    "priority": "high",
    "notification": {
      "channel_id": "admin_notifications",
      "sound": "default",
      "click_action": "FLUTTER_NOTIFICATION_CLICK"
    }
  },
  "apns": {
    "payload": {
      "aps": {
        "alert": {
          "title": "Payment Update Required",
          "body": "Please update your payment method for continued service."
        },
        "sound": "default",
        "badge": 1
      }
    }
  }
}
```

## Authentication & Authorization

### Required Headers
```bash
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

### Role Requirements
- **SuperAdmin**: Can send all types of notifications
- **Admin/Employee**: Can only receive notifications (via mobile app)

## Error Handling

### Common Error Responses

#### 400 - Validation Failed
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "title",
      "message": "Title is required"
    }
  ]
}
```

#### 403 - Unauthorized
```json
{
  "success": false,
  "message": "Only SuperAdmin can send push notifications"
}
```

#### 404 - No Recipients
```json
{
  "success": false,
  "message": "No valid shops found"
}
```

#### 500 - Firebase Error
```json
{
  "success": false,
  "message": "Failed to send notifications",
  "error": "Firebase service unavailable"
}
```

## Notification Categories

### System Categories
- `admin_communication`: Messages from SuperAdmin
- `system_broadcast`: System-wide announcements
- `debt_reminder`: Automated debt notifications
- `payment_alert`: Payment-related notifications
- `shop_management`: Shop operation notifications

### Priority Levels
- `low`: General information
- `normal`: Standard notifications (default)
- `high`: Important updates requiring attention
- `urgent`: Critical notifications requiring immediate action

## Testing & Monitoring

### Health Check Endpoint
**GET** `/api/admin/notifications/push/test`
```json
{
  "success": true,
  "message": "Firebase connection successful"
}
```

### Notification Statistics
**GET** `/api/admin/notifications/push/stats?days=30&shopId=SHOP001`
```json
{
  "success": true,
  "data": {
    "totalSent": 150,
    "totalDelivered": 142,
    "deliveryRate": 94.67,
    "byCategory": {
      "admin_communication": 45,
      "debt_reminder": 78,
      "system_broadcast": 27
    }
  }
}
```

### Target Discovery
**GET** `/api/admin/notifications/push/targets`
```json
{
  "success": true,
  "data": {
    "totalShops": 25,
    "shops": [
      {
        "shopId": "SHOP001",
        "shopName": "ABC Store",
        "adminCount": 3,
        "deviceCount": 5
      }
    ]
  }
}
```

## Best Practices

### 📝 Message Content
- Keep titles under 40 characters
- Keep message body under 120 characters
- Use clear, actionable language
- Include relevant action URLs when applicable

### ⏰ Timing
- Avoid sending notifications outside business hours
- Use `scheduledAt` for important announcements
- Batch similar notifications to avoid spam

### 🎯 Targeting
- Use shop-specific notifications for shop-related issues
- Use broadcast for system-wide announcements only
- Validate shop IDs before sending

### 🔄 Error Recovery
- System automatically retries failed notifications
- Invalid FCM tokens are cleaned up automatically
- Failed deliveries are logged for analysis 