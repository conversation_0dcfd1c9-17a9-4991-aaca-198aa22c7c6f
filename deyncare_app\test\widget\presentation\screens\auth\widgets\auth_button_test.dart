import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_button.dart';

void main() {
  group('AuthButton', () {
    testWidgets('renders correctly with default properties', (WidgetTester tester) async {
      bool buttonPressed = false;
      
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AuthButton(
              label: 'Login',
              onPressed: () {
                buttonPressed = true;
              },
            ),
          ),
        ),
      );
      
      // Verify the button renders with the correct text
      expect(find.text('Login'), findsOneWidget);
      
      // Tap the button
      await tester.tap(find.byType(AuthButton));
      await tester.pump();
      
      // Verify the callback was called
      expect(buttonPressed, true);
    });
    
    testWidgets('shows loading indicator when isLoading is true', (WidgetTester tester) async {
      // Build the widget with loading state
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AuthButton(
              label: 'Login',
              onPressed: () {},
              isLoading: true,
            ),
          ),
        ),
      );
      
      // Verify loading indicator is shown
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      // Verify text is not shown when loading
      expect(find.text('Login'), findsNothing);
      
      // Verify button is disabled when loading
      final button = tester.widget<ElevatedButton>(
        find.byType(ElevatedButton),
      );
      expect(button.onPressed, isNull);
    });
    
    testWidgets('renders with icon when provided', (WidgetTester tester) async {
      // Build the widget with an icon
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AuthButton(
              label: 'Login with Google',
              onPressed: () {},
              icon: const Icon(Icons.login),
            ),
          ),
        ),
      );
      
      // Verify icon is shown
      expect(find.byType(Icon), findsOneWidget);
      // Verify text is shown alongside the icon
      expect(find.text('Login with Google'), findsOneWidget);
      // Verify they're in a Row
      expect(find.byType(Row), findsOneWidget);
    });
    
    testWidgets('renders outlined variant correctly', (WidgetTester tester) async {
      // Build outlined button
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AuthButton(
              label: 'Register',
              onPressed: () {},
              isOutlined: true,
            ),
          ),
        ),
      );
      
      // Unfortunately we can't easily test the style properties directly in a widget test,
      // but we can verify the button renders
      expect(find.text('Register'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });
    
    testWidgets('uses custom colors when provided', (WidgetTester tester) async {
      // Build button with custom colors
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AuthButton(
              label: 'Custom',
              onPressed: () {},
              backgroundColor: Colors.amber,
              textColor: Colors.black,
            ),
          ),
        ),
      );
      
      // Verify the button renders
      expect(find.text('Custom'), findsOneWidget);
      
      // While we can't easily verify the exact colors in widget tests,
      // this at least confirms the button builds without errors
    });
    
    testWidgets('has correct dimensions when specified', (WidgetTester tester) async {
      const double testWidth = 200;
      const double testHeight = 60;
      
      // Build button with custom dimensions
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Center(
              child: AuthButton(
                label: 'Sized Button',
                onPressed: () {},
                width: testWidth,
                height: testHeight,
              ),
            ),
          ),
        ),
      );
      
      // Find the SizedBox that wraps the button
      final sizedBox = tester.widget<SizedBox>(find.byType(SizedBox).first);
      
      // Verify dimensions
      expect(sizedBox.width, testWidth);
      expect(sizedBox.height, testHeight);
    });
  });
}
