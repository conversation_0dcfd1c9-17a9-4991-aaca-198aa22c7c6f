import 'package:dio/dio.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:deyncare_app/core/mixins/connectivity_aware_service.dart';
import 'package:deyncare_app/data/models/subscription_model.dart';
import 'package:deyncare_app/data/models/plan_model.dart';

class SubscriptionService with ConnectivityAwareService {
  final Dio _dio;

  SubscriptionService(this._dio);

  /// Get current subscription for the authenticated shop
  Future<SubscriptionModel> getCurrentSubscription() async {
    return executeWithConnectivity<SubscriptionModel>(
      operation: () async {
        try {
          final response = await _dio.get(
            '/subscriptions/current',
            options: Options(
              sendTimeout: const Duration(seconds: 30),
              receiveTimeout: const Duration(seconds: 30),
            ),
          );
          
          if (response.statusCode == 200 && response.data['success'] == true) {
            return SubscriptionModel.fromJson(response.data['data']);
          } else {
            throw ApiException(
              message: response.data['message'] ?? 'Failed to fetch subscription',
              code: 'subscription_fetch_failed',
              statusCode: response.statusCode,
            );
          }
        } on DioException catch (e) {
          throw _handleDioException(e, 'Failed to fetch current subscription');
        }
      },
    );
  }

  /// Get all available plans for upgrade
  Future<List<PlanModel>> getAvailablePlans() async {
    return executeWithConnectivity<List<PlanModel>>(
      operation: () async {
        try {
          final response = await _dio.get('/plans');
          
          if (response.statusCode == 200 && response.data['success'] == true) {
            final List<dynamic> plansData = response.data['data'] ?? [];
            return plansData.map((plan) => PlanModel.fromJson(plan)).toList();
          } else {
            throw ApiException(
              message: response.data['message'] ?? 'Failed to fetch plans',
              code: 'plans_fetch_failed',
              statusCode: response.statusCode,
            );
          }
        } on DioException catch (e) {
          throw _handleDioException(e, 'Failed to fetch available plans');
        }
      },
    );
  }

  /// Request subscription upgrade (sends email to SuperAdmin)
  Future<Map<String, dynamic>> requestUpgrade({
    required String planType,
    String? message,
  }) async {
    return executeWithConnectivity<Map<String, dynamic>>(
      operation: () async {
        try {
          final requestData = {
            'planType': planType,
            if (message != null && message.trim().isNotEmpty) 'message': message.trim(),
          };

          final response = await _dio.post('/subscriptions/request-upgrade', data: requestData);
          
          if (response.statusCode == 200 && response.data['success'] == true) {
            return {
              'success': true,
              'message': response.data['message'] ?? 'Upgrade request sent successfully',
              'data': response.data['data'],
            };
          } else {
            throw ApiException(
              message: response.data['message'] ?? 'Failed to send upgrade request',
              code: 'upgrade_request_failed',
              statusCode: response.statusCode,
            );
          }
        } on DioException catch (e) {
          throw _handleDioException(e, 'Failed to send upgrade request');
        }
      },
    );
  }

  /// Change subscription plan (for existing paid subscriptions)
  Future<SubscriptionModel> changePlan({
    required String planId,
    String? planType,
    bool prorated = true,
    String? paymentMethod,
    Map<String, dynamic>? paymentDetails,
  }) async {
    return executeWithConnectivity<SubscriptionModel>(
      operation: () async {
        try {
          final requestData = {
            'planId': planId,
            if (planType != null) 'planType': planType,
            'prorated': prorated,
            if (paymentMethod != null) 'paymentMethod': paymentMethod,
            if (paymentDetails != null) 'paymentDetails': paymentDetails,
          };

          final response = await _dio.put('/subscriptions/change-plan', data: requestData);
          
          if (response.statusCode == 200 && response.data['success'] == true) {
            return SubscriptionModel.fromJson(response.data['data']);
          } else {
            throw ApiException(
              message: response.data['message'] ?? 'Failed to change plan',
              code: 'plan_change_failed',
              statusCode: response.statusCode,
            );
          }
        } on DioException catch (e) {
          throw _handleDioException(e, 'Failed to change subscription plan');
        }
      },
    );
  }

  /// Cancel subscription
  Future<SubscriptionModel> cancelSubscription({
    String? reason,
    String? feedback,
    bool immediateEffect = false,
  }) async {
    return executeWithConnectivity<SubscriptionModel>(
      operation: () async {
        try {
          final requestData = {
            if (reason != null) 'reason': reason,
            if (feedback != null) 'feedback': feedback,
            'immediateEffect': immediateEffect,
          };

          final response = await _dio.post('/subscriptions/cancel', data: requestData);
          
          if (response.statusCode == 200 && response.data['success'] == true) {
            final responseData = response.data['data'];
            
            // Handle minimal cancellation response - backend only returns {subscriptionId, status}
            if (responseData is Map<String, dynamic> && 
                responseData.containsKey('subscriptionId') && 
                responseData.containsKey('status') &&
                responseData.length <= 3) { // Allow for minimal response
              
              // Return a minimal subscription model for cancellation
              return SubscriptionModel.fromCancellationResponse(responseData);
            } else {
              // Handle full subscription response
              return SubscriptionModel.fromJson(responseData);
            }
          } else {
            throw ApiException(
              message: response.data['message'] ?? 'Failed to cancel subscription',
              code: 'subscription_cancel_failed',
              statusCode: response.statusCode,
            );
          }
        } on DioException catch (e) {
          throw _handleDioException(e, 'Failed to cancel subscription');
        }
      },
    );
  }

  /// Update auto-renewal settings
  Future<SubscriptionModel> updateAutoRenewal({required bool autoRenew}) async {
    return executeWithConnectivity<SubscriptionModel>(
      operation: () async {
        try {
          final requestData = {'autoRenew': autoRenew};

          final response = await _dio.patch('/subscriptions/auto-renewal', data: requestData);
          
          if (response.statusCode == 200 && response.data['success'] == true) {
            return SubscriptionModel.fromJson(response.data['data']);
          } else {
            throw ApiException(
              message: response.data['message'] ?? 'Failed to update auto-renewal',
              code: 'auto_renewal_failed',
              statusCode: response.statusCode,
            );
          }
        } on DioException catch (e) {
          throw _handleDioException(e, 'Failed to update auto-renewal settings');
        }
      },
    );
  }

  /// Renew subscription
  Future<SubscriptionModel> renewSubscription({
    required String paymentMethod,
    required String transactionId,
    double? amount,
    String currency = 'USD',
    String? notes,
  }) async {
    return executeWithConnectivity<SubscriptionModel>(
      operation: () async {
        try {
          final requestData = {
            'paymentMethod': paymentMethod,
            'transactionId': transactionId,
            if (amount != null) 'amount': amount,
            'currency': currency,
            if (notes != null) 'notes': notes,
          };

          final response = await _dio.post('/subscriptions/renew', data: requestData);
          
          if (response.statusCode == 200 && response.data['success'] == true) {
            return SubscriptionModel.fromJson(response.data['data']);
          } else {
            throw ApiException(
              message: response.data['message'] ?? 'Failed to renew subscription',
              code: 'subscription_renewal_failed',
              statusCode: response.statusCode,
            );
          }
        } on DioException catch (e) {
          throw _handleDioException(e, 'Failed to renew subscription');
        }
      },
    );
  }

  /// Pay with EVC Plus
  Future<Map<String, dynamic>> payWithEvc({
    required String subscriptionId,
    required String phone,
    required double amount,
    String? planType,
  }) async {
    return executeWithConnectivity<Map<String, dynamic>>(
      operation: () async {
        try {
          final requestData = {
            'subscriptionId': subscriptionId,
            'phone': phone,
            'amount': amount,
            if (planType != null) 'planType': planType,
          };

          // Use the recordPayment endpoint instead of pay-evc
          final response = await _dio.post('/subscriptions/payment', data: requestData);
          
          if (response.statusCode == 200 && response.data['success'] == true) {
            return response.data['data'] as Map<String, dynamic>;
          } else {
            throw ApiException(
              message: response.data['message'] ?? 'Failed to process EVC payment',
              code: 'evc_payment_failed',
              statusCode: response.statusCode,
            );
          }
        } on DioException catch (e) {
          throw _handleDioException(e, 'Failed to process EVC payment');
        }
      },
    );
  }

  /// Record offline payment
  Future<Map<String, dynamic>> recordOfflinePayment({
    required String subscriptionId,
    required double amount,
    required String method,
    String? payerName,
    String? payerPhone,
    String? notes,
    String? planType,
    String? receiptPath, // For file upload
  }) async {
    return executeWithConnectivity<Map<String, dynamic>>(
      operation: () async {
        try {
          FormData formData = FormData.fromMap({
            'subscriptionId': subscriptionId,
            'amount': amount,
            'method': method,
            if (payerName != null) 'payerName': payerName,
            if (payerPhone != null) 'payerPhone': payerPhone,
            if (notes != null) 'notes': notes,
            if (planType != null) 'planType': planType,
            if (receiptPath != null) 
              'receipt': await MultipartFile.fromFile(receiptPath),
          });

          // Use the recordPayment endpoint instead of offline-payment
          final response = await _dio.post('/subscriptions/payment', data: formData);
          
          if (response.statusCode == 200 && response.data['success'] == true) {
            return response.data['data'] as Map<String, dynamic>;
          } else {
            throw ApiException(
              message: response.data['message'] ?? 'Failed to record offline payment',
              code: 'offline_payment_failed',
              statusCode: response.statusCode,
            );
          }
        } on DioException catch (e) {
          throw _handleDioException(e, 'Failed to record offline payment');
        }
      },
    );
  }

  /// Get subscription history
  Future<List<HistoryEntry>> getSubscriptionHistory() async {
    return executeWithConnectivity<List<HistoryEntry>>(
      operation: () async {
        try {
          final response = await _dio.get('/subscriptions/history');
          
          if (response.statusCode == 200 && response.data['success'] == true) {
            final List<dynamic> historyData = response.data['data'] ?? [];
            return historyData.map((entry) => HistoryEntry.fromJson(entry)).toList();
          } else {
            throw ApiException(
              message: response.data['message'] ?? 'Failed to fetch subscription history',
              code: 'history_fetch_failed',
              statusCode: response.statusCode,
            );
          }
        } on DioException catch (e) {
          throw _handleDioException(e, 'Failed to fetch subscription history');
        }
      },
    );
  }

  /// Get available payment methods
  Future<List<String>> getAvailablePaymentMethods() async {
    return executeWithConnectivity<List<String>>(
      operation: () async {
        try {
          final response = await _dio.get('/settings/payment-methods');
          
          if (response.statusCode == 200 && response.data['success'] == true) {
            final List<dynamic> methods = response.data['data']['paymentMethods'] ?? [];
            return methods.cast<String>();
          } else {
            throw ApiException(
              message: response.data['message'] ?? 'Failed to fetch payment methods',
              code: 'payment_methods_failed',
              statusCode: response.statusCode,
            );
          }
        } on DioException catch (e) {
          throw _handleDioException(e, 'Failed to fetch payment methods');
        }
      },
    );
  }

  /// Handle Dio exceptions and convert to ApiException
  ApiException _handleDioException(DioException e, String defaultMessage) {
    if (e.response != null) {
      final data = e.response!.data;
      return ApiException(
        message: data is Map && data['message'] != null 
            ? data['message'] 
            : defaultMessage,
        code: data is Map && data['code'] != null 
            ? data['code'] 
            : 'api_error',
        statusCode: e.response!.statusCode,
        data: data,
      );
    } else {
      return ApiException(
        message: e.message ?? defaultMessage,
        code: 'network_error',
        statusCode: null,
      );
    }
  }
} 