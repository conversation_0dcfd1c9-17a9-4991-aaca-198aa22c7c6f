import 'package:flutter/material.dart';
import 'package:deyncare_app/domain/models/registration_progress.dart';

/// A widget to display the progress of the registration
class RegistrationProgressView extends StatelessWidget {
  final RegistrationProgress registrationProgress;

  const RegistrationProgressView({
    super.key,
    required this.registrationProgress,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CircularProgressIndicator(
          value: registrationProgress.progress / 100,
          strokeWidth: 8.0,
          backgroundColor: Colors.grey[300],
          valueColor: const AlwaysStoppedAnimation<Color>(Colors.blueAccent),
        ),
        const SizedBox(height: 24),
        Text(
          'Registration in Progress: ${registrationProgress.progress}%',
          style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Text(
          'Current Step: ${registrationProgress.currentStep}',
          style: TextStyle(fontSize: 16, color: Colors.grey[700]),
        ),
        const SizedBox(height: 16),
        // You can add more detailed UI based on `data` if needed
        if (registrationProgress.data.isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Text(
              'Details: ${registrationProgress.data['message'] ?? registrationProgress.data.toString()}',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
          ),
      ],
    );
  }
} 