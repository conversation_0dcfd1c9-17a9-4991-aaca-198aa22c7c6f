import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';

/// Utility class for theme-aware color and styling operations
/// Provides methods to get appropriate colors based on current theme mode
class ThemeUtils {
  /// Get theme-aware text color based on brightness
  static Color getTextColor(BuildContext context, {TextColorType type = TextColorType.primary}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    switch (type) {
      case TextColorType.primary:
        return isDark ? const Color(0xFFE5E5E5) : AppThemes.textPrimaryColor;
      case TextColorType.secondary:
        return isDark ? const Color(0xFF9CA3AF) : AppThemes.textSecondaryColor;
      case TextColorType.tertiary:
        return isDark ? const Color(0xFF6B7280) : AppThemes.textTertiaryColor;
      case TextColorType.disabled:
        return isDark ? const Color(0xFF4B5563) : AppThemes.textDisabledColor;
      case TextColorType.hint:
        return isDark ? const Color(0xFF6B7280) : AppThemes.textHintColor;
    }
  }

  /// Get theme-aware background colors
  static Color getBackgroundColor(BuildContext context, {BackgroundType type = BackgroundType.primary}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    switch (type) {
      case BackgroundType.primary:
        return isDark ? const Color(0xFF0F0F0F) : AppThemes.backgroundColor;
      case BackgroundType.surface:
        return isDark ? const Color(0xFF1A1A1A) : AppThemes.surfaceColor;
      case BackgroundType.card:
        return isDark ? const Color(0xFF1A1A1A) : AppThemes.cardColor;
      case BackgroundType.elevated:
        return isDark ? const Color(0xFF2A2A2A) : Colors.white;
    }
  }

  /// Get theme-aware border and divider colors
  static Color getBorderColor(BuildContext context, {BorderType type = BorderType.divider}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    switch (type) {
      case BorderType.divider:
        return isDark ? const Color(0xFF2A2A2A) : AppThemes.dividerColor;
      case BorderType.border:
        return isDark ? const Color(0xFF3A3A3A) : AppThemes.borderColor;
      case BorderType.focused:
        return AppThemes.primaryColor; // Keep primary color in both themes
      case BorderType.error:
        return AppThemes.errorColor; // Keep error color in both themes
    }
  }

  /// Get theme-aware shadow colors
  static Color getShadowColor(BuildContext context, {ShadowType type = ShadowType.light}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    if (isDark) {
      // Use lighter shadows in dark mode for better visibility
      switch (type) {
        case ShadowType.light:
          return const Color(0x10FFFFFF);
        case ShadowType.medium:
          return const Color(0x20FFFFFF);
        case ShadowType.dark:
          return const Color(0x30FFFFFF);
      }
    } else {
      switch (type) {
        case ShadowType.light:
          return AppThemes.shadowLight;
        case ShadowType.medium:
          return AppThemes.shadowMedium;
        case ShadowType.dark:
          return AppThemes.shadowDark;
      }
    }
  }

  /// Get theme-aware overlay colors (for loading states, disabled states)
  static Color getOverlayColor(BuildContext context, {double opacity = 0.1}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark 
        ? Colors.white.withOpacity(opacity)
        : Colors.black.withOpacity(opacity);
  }

  /// Get theme-aware shimmer colors for skeleton loaders
  static ShimmerColors getShimmerColors(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    if (isDark) {
      return ShimmerColors(
        baseColor: const Color(0xFF2A2A2A),
        highlightColor: const Color(0xFF3A3A3A),
      );
    } else {
      return ShimmerColors(
        baseColor: Colors.grey.shade50,
        highlightColor: Colors.grey.shade200,
      );
    }
  }

  /// Get appropriate icon color based on background
  static Color getIconColor(BuildContext context, {IconColorType type = IconColorType.primary}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    switch (type) {
      case IconColorType.primary:
        return isDark ? const Color(0xFFE5E5E5) : AppThemes.textPrimaryColor;
      case IconColorType.secondary:
        return isDark ? const Color(0xFF9CA3AF) : AppThemes.textSecondaryColor;
      case IconColorType.disabled:
        return isDark ? const Color(0xFF4B5563) : AppThemes.textDisabledColor;
      case IconColorType.accent:
        return AppThemes.primaryColor; // Keep brand colors consistent
    }
  }

  /// Get theme-aware status colors with proper opacity for dark mode
  static StatusColors getStatusColors(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return StatusColors(
      success: AppThemes.successColor,
      warning: AppThemes.warningColor,
      error: AppThemes.errorColor,
      info: AppThemes.infoColor,
      successSurface: isDark 
          ? AppThemes.successColor.withOpacity(0.15)
          : AppThemes.successSurface,
      warningSurface: isDark 
          ? AppThemes.warningColor.withOpacity(0.15)
          : AppThemes.warningSurface,
      errorSurface: isDark 
          ? AppThemes.errorColor.withOpacity(0.15)
          : AppThemes.errorSurface,
      infoSurface: isDark 
          ? AppThemes.infoColor.withOpacity(0.15)
          : AppThemes.infoSurface,
    );
  }

  /// Check if current theme is dark
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }

  /// Get elevation shadows based on theme
  static List<BoxShadow> getElevationShadow(BuildContext context, {double elevation = 2}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    if (isDark) {
      return [
        BoxShadow(
          color: Colors.white.withOpacity(0.05),
          blurRadius: elevation * 2,
          offset: Offset(0, elevation),
        ),
      ];
    } else {
      return [
        BoxShadow(
          color: Colors.black.withOpacity(0.1),
          blurRadius: elevation * 2,
          offset: Offset(0, elevation),
        ),
      ];
    }
  }

  /// Get theme-aware colors for trends (positive/negative indicators)
  static TrendColors getTrendColors(BuildContext context) {
    return TrendColors(
      positive: AppThemes.successColor,
      negative: AppThemes.errorColor,
      neutral: getTextColor(context, type: TextColorType.secondary),
    );
  }
}

/// Enum for different text color types
enum TextColorType { primary, secondary, tertiary, disabled, hint }

/// Enum for different background types
enum BackgroundType { primary, surface, card, elevated }

/// Enum for different border types
enum BorderType { divider, border, focused, error }

/// Enum for different shadow types
enum ShadowType { light, medium, dark }

/// Enum for different icon color types
enum IconColorType { primary, secondary, disabled, accent }

/// Data class for shimmer colors
class ShimmerColors {
  final Color baseColor;
  final Color highlightColor;

  const ShimmerColors({
    required this.baseColor,
    required this.highlightColor,
  });
}

/// Data class for status colors
class StatusColors {
  final Color success;
  final Color warning;
  final Color error;
  final Color info;
  final Color successSurface;
  final Color warningSurface;
  final Color errorSurface;
  final Color infoSurface;

  const StatusColors({
    required this.success,
    required this.warning,
    required this.error,
    required this.info,
    required this.successSurface,
    required this.warningSurface,
    required this.errorSurface,
    required this.infoSurface,
  });
}

/// Data class for trend colors
class TrendColors {
  final Color positive;
  final Color negative;
  final Color neutral;

  const TrendColors({
    required this.positive,
    required this.negative,
    required this.neutral,
  });
} 