import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';

/// A custom button component for authentication actions
/// with built-in loading state and styling
class AuthButton extends StatelessWidget {
  final String label;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isOutlined;
  final Color? backgroundColor;
  final Color? textColor;
  final double width;
  final double height;
  final double borderRadius;
  final Widget? icon;

  const AuthButton({
    super.key,
    required this.label,
    this.onPressed,
    this.isLoading = false,
    this.isOutlined = false,
    this.backgroundColor,
    this.textColor,
    this.width = double.infinity,
    this.height = 50,
    this.borderRadius = 8,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    // Get the correct background color based on button state
    final effectiveBackgroundColor = isLoading
        ? (backgroundColor ?? AppThemes.primaryColor).withOpacity(0.7)
        : backgroundColor ?? AppThemes.primaryColor;
    
    // Get correct text color based on state
    final effectiveTextColor = isOutlined
        ? textColor ?? AppThemes.primaryColor
        : textColor ?? Colors.white;
    
    return SizedBox(
      width: width,
      height: height,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          // Use different background colors for different states
          backgroundColor: isOutlined 
              ? Colors.transparent 
              : effectiveBackgroundColor,
          foregroundColor: effectiveTextColor,
          // Add subtle shadow for non-outlined buttons
          elevation: isOutlined ? 0 : 2,
          // Make disabled buttons visually distinct but not too faded
          disabledBackgroundColor: isOutlined
              ? Colors.transparent
              : effectiveBackgroundColor.withOpacity(0.8),
          disabledForegroundColor: effectiveTextColor.withOpacity(0.8),
          // Consistent border radius with the app's design language
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            side: isOutlined 
                ? BorderSide(
                    color: isLoading
                        ? (backgroundColor ?? AppThemes.primaryColor).withOpacity(0.6)
                        : backgroundColor ?? AppThemes.primaryColor,
                    width: 1.5,
                  )
                : BorderSide.none,
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        ),
        child: isLoading
            ? _buildLoadingIndicator()
            : _buildButtonContent(),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    // Show a loading indicator with text for better UX
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          height: 20,
          width: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2.0,
            valueColor: AlwaysStoppedAnimation<Color>(
              isOutlined ? (textColor ?? AppThemes.primaryColor) : Colors.white,
            ),
          ),
        ),
        const SizedBox(width: 10),
        Text(
          'Please wait...',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isOutlined
                ? textColor ?? AppThemes.primaryColor
                : textColor ?? Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildButtonContent() {
    if (icon != null) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon!,
          const SizedBox(width: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: isOutlined
                  ? textColor ?? AppThemes.primaryColor
                  : textColor ?? Colors.white,
            ),
          ),
        ],
      );
    }
    
    return Text(
      label,
      style: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: isOutlined
            ? textColor ?? AppThemes.primaryColor
            : textColor ?? Colors.white,
      ),
    );
  }
}
