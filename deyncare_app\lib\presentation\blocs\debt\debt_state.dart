import 'package:equatable/equatable.dart';
import 'package:deyncare_app/domain/models/debt.dart';
import 'package:deyncare_app/domain/repositories/debt_repository.dart';

/// Debt States - aligned with backend responses
abstract class DebtState extends Equatable {
  const DebtState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class DebtInitial extends DebtState {
  const DebtInitial();
}

/// Loading states
class DebtLoading extends DebtState {
  const DebtLoading();
}

class DebtListLoading extends DebtState {
  const DebtListLoading();
}

class DebtDetailsLoading extends DebtState {
  const DebtDetailsLoading();
}

class DebtCreating extends DebtState {
  const DebtCreating();
}

class DebtUpdating extends DebtState {
  const DebtUpdating();
}

class DebtDeleting extends DebtState {
  const DebtDeleting();
}

class PaymentAdding extends DebtState {
  const PaymentAdding();
}

class DebtStatsLoading extends DebtState {
  const DebtStatsLoading();
}

/// Success states
class DebtListLoaded extends DebtState {
  final DebtListResult response;
  final bool isRefreshing;

  const DebtListLoaded({
    required this.response,
    this.isRefreshing = false,
  });

  @override
  List<Object> get props => [response, isRefreshing];

  DebtListLoaded copyWith({
    DebtListResult? response,
    bool? isRefreshing,
  }) {
    return DebtListLoaded(
      response: response ?? this.response,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }
}

class DebtDetailsLoaded extends DebtState {
  final Debt response;

  const DebtDetailsLoaded(this.response);

  @override
  List<Object> get props => [response];
}

class DebtCreated extends DebtState {
  final Debt response;

  const DebtCreated(this.response);

  /// Getter to access debt data easily
  Debt get debt => response;

  @override
  List<Object> get props => [response];
}

class DebtUpdated extends DebtState {
  final Debt response;

  const DebtUpdated(this.response);

  @override
  List<Object> get props => [response];
}

class DebtDeleted extends DebtState {
  final String debtId;

  const DebtDeleted(this.debtId);

  @override
  List<Object> get props => [debtId];
}

class PaymentAdded extends DebtState {
  final Debt response;

  const PaymentAdded(this.response);

  @override
  List<Object> get props => [response];
}

class DebtStatsLoaded extends DebtState {
  final Map<String, dynamic> stats;

  const DebtStatsLoaded(this.stats);

  /// Add getters for commonly accessed stats properties
  int get totalDebts => stats['totalDebts'] ?? 0;
  int get activeDebts => stats['activeDebts'] ?? 0;
  int get overdueDebts => stats['overdueDebts'] ?? 0;

  @override
  List<Object> get props => [stats];
}

/// Error states
class DebtError extends DebtState {
  final String message;
  final String? errorCode;

  const DebtError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

class DebtListError extends DebtState {
  final String message;
  final String? errorCode;

  const DebtListError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

class DebtDetailsError extends DebtState {
  final String message;
  final String? errorCode;

  const DebtDetailsError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

class PaymentError extends DebtState {
  final String message;
  final String? errorCode;

  const PaymentError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

/// Add DebtStatsError state that was missing
class DebtStatsError extends DebtState {
  final String message;
  final String? errorCode;

  const DebtStatsError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
} 