import 'package:dartz/dartz.dart';
import 'package:deyncare_app/core/errors/failures.dart';
import 'package:deyncare_app/domain/repositories/debt_repository.dart';
import 'package:deyncare_app/domain/models/debt.dart';

/// Use case for retrieving debts with pagination, filtering, and analytics
class GetDebtsUseCase {
  final DebtRepository _repository;

  GetDebtsUseCase(this._repository);

  /// Execute the use case to get debts
  /// Matches GET /api/debts endpoint
  Future<Either<Failure, DebtListResult>> execute({
    int page = 1,
    int limit = 20,
    DebtStatus? status,
    RiskLevel? riskLevel,
    String? customerType,
    String? search,
    String? sortBy,
    bool ascending = false, // Backend defaults to desc
  }) async {
    return await _repository.getDebts(
      page: page,
      limit: limit,
      status: status,
      riskLevel: riskLevel,
      customerType: customerType,
      search: search,
      sortBy: sortBy,
      ascending: ascending,
    );
  }
} 