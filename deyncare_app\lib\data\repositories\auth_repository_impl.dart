import 'dart:io';

import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:deyncare_app/core/mixins/connectivity_aware_service.dart';
import 'package:deyncare_app/core/utils/permission_fix_utils.dart';
import 'package:deyncare_app/data/models/auth_token_model.dart';
import 'package:deyncare_app/data/models/registration_progress_model.dart';
import 'package:deyncare_app/data/models/user_model.dart';
import 'package:deyncare_app/data/network/clients/dio_client.dart';
import 'package:deyncare_app/data/network/token/token_manager.dart';
import 'package:deyncare_app/data/services/auth/auth_remote_source.dart';
import 'package:deyncare_app/data/services/auth/auth_utils.dart';
import 'package:deyncare_app/domain/models/auth_token.dart';
import 'package:deyncare_app/domain/models/registration_progress.dart';
import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/domain/repositories/auth_repository.dart';
import 'package:logger/logger.dart';
import 'package:flutter/foundation.dart';

/// Implementation of the AuthRepository interface
///
/// Connects the domain layer with the data sources
class AuthRepositoryImpl
    with ConnectivityAwareService
    implements AuthRepository {
  final Logger _logger = Logger();
  final AuthRemoteSource _remoteSource;
  final TokenManager _tokenManager;
  final AuthUtils _authUtils;
  final DioClient _dioClient;

  /// Creates a new repository instance with required dependencies
  AuthRepositoryImpl({
    required AuthRemoteSource remoteSource,
    required TokenManager tokenManager,
    required AuthUtils authUtils,
    required DioClient dioClient,
  })  : _remoteSource = remoteSource,
        _tokenManager = tokenManager,
        _authUtils = authUtils,
        _dioClient = dioClient {
    _logger.d('AuthRepositoryImpl: Initialized');
  }

  @override
  Future<bool> isLoggedIn() async {
    _logger.d('AuthRepositoryImpl: Checking if user is logged in');
    final token = await _tokenManager.getAccessToken();
    final userData = await _authUtils.getUserData();
    final isLoggedIn = token != null && userData != null;
    _logger.d('AuthRepositoryImpl: User logged in status: $isLoggedIn');
    return isLoggedIn;
  }

  @override
  Future<User?> getCurrentUser() async {
    _logger.d('AuthRepositoryImpl: Getting current user');
    try {
      final userModel = await _authUtils.getUserData();
      final user = userModel?.toDomain();
      _logger.d(
          'AuthRepositoryImpl: Current user retrieved: ${user?.email ?? 'null'}');
      return user;
    } catch (e) {
      _logger.e('AuthRepositoryImpl: Error getting current user: $e');
      return null;
    }
  }

  @override
  Future<(User, AuthToken)> login(String email, String password) async {
    _logger.d('AuthRepositoryImpl: Login attempt for email: $email');
    return await executeWithConnectivity(
      operation: () async {
        final data = await _remoteSource.login(email, password);
        _logger.d('AuthRepositoryImpl: Login response received');

        if (!data.containsKey('user') ||
            !data.containsKey('accessToken') ||
            !data.containsKey('refreshToken')) {
          _logger.e(
              'AuthRepositoryImpl: Missing authentication data in login response');
          throw ApiException(
            message: 'Missing authentication data in response',
            code: 'invalid_auth_data',
          );
        }

        // Safely cast user data to Map<String, dynamic>
        final Map<String, dynamic> userData;
        if (data['user'] is Map<String, dynamic>) {
          userData = data['user'] as Map<String, dynamic>;
        } else if (data['user'] is Map) {
          userData = Map<String, dynamic>.from(data['user'] as Map);
        } else {
          throw ApiException(
            message: 'Invalid user data format in login response',
            code: 'invalid_user_data_format',
          );
        }

        var user = UserModel.fromJson(userData);

        // Apply permission fixes for known users
        final domainUser = user.toDomain();
        final fixedUser = PermissionFixUtils.fixUserPermissions(domainUser);

        // Convert back to UserModel if permissions were fixed
        if (fixedUser != domainUser) {
          user = UserModel(
            userId: fixedUser.userId,
            fullName: fixedUser.fullName,
            email: fixedUser.email,
            phone: fixedUser.phone,
            role: fixedUser.role,
            shopId: fixedUser.shopId,
            status: fixedUser.status,
            permissions: fixedUser.permissions,
            visibility: fixedUser.visibility,
            registrationStatus: fixedUser.registrationStatus,
            isEmailVerified: fixedUser.isEmailVerified,
            isPaid: fixedUser.isPaid,
            emailVerifiedAt: fixedUser.emailVerifiedAt,
            paymentCompletedAt: fixedUser.paymentCompletedAt,
            verificationCode: fixedUser.verificationCode,
            verificationCodeExpiresAt: fixedUser.verificationCodeExpiresAt,
            shopName: fixedUser.shopName,
            shopStatus: fixedUser.shopStatus,
            isShopActive: fixedUser.isShopActive,
            accessToken: fixedUser.accessToken,
            refreshToken: fixedUser.refreshToken,
            tokenExpiresAt: fixedUser.tokenExpiresAt,
            isSuspended: fixedUser.isSuspended,
            suspensionReason: fixedUser.suspensionReason,
            suspendedAt: fixedUser.suspendedAt,
            suspendedBy: fixedUser.suspendedBy,
          );
          _logger.d(
              'AuthRepositoryImpl: Applied permission fixes for user: ${user.email}');
        }

        final token = AuthTokenModel.fromJson({
          'accessToken': data['accessToken'],
          'refreshToken': data['refreshToken'],
          'expiresIn': data['expiresIn'] ?? 900,
        });

        await _authUtils.saveUserData(user);
        await _tokenManager.saveTokens(
          accessToken: token.accessToken,
          refreshToken: token.refreshToken,
        );

        // Update the token in the DioClient to ensure it's immediately available
        _logger.d('AuthRepositoryImpl: Updating DioClient with new token');
        _dioClient.updateAuthToken(token.accessToken);

        _logger
            .d('AuthRepositoryImpl: Login successful for user: ${user.email}');
        return (user.toDomain(), token.toDomain());
      },
      retryAutomatically:
          false, // Disable automatic retries for auth operations
      maxRetries: 0, // No retries
    );
  }

  @override
  Future<(User, RegistrationProgress)> register({
    required String fullName,
    required String email,
    required String phone,
    required String password,
    required String shopName,
    required String shopAddress,
    File? shopLogo,
    String planType = 'trial',
    String paymentMethod = 'offline',
    bool initialPaid = false,
    String? discountCode,
  }) async {
    _logger.d(
        'AuthRepositoryImpl: Registration attempt for email: $email, planType: $planType');
    return await executeWithConnectivity(operation: () async {
      final data = await _remoteSource.register(
        fullName: fullName,
        email: email,
        phone: phone,
        password: password,
        shopName: shopName,
        shopAddress: shopAddress,
        shopLogo: shopLogo,
        planType: planType,
        paymentMethod: paymentMethod,
        initialPaid: initialPaid,
        discountCode: discountCode,
      );

      _logger.d('AuthRepositoryImpl: Registration response received');

      if (!data.containsKey('user') ||
          !data.containsKey('registrationProgress')) {
        _logger.e('AuthRepositoryImpl: Missing registration data in response');
        throw ApiException(
          message: 'Missing registration data in response',
          code: 'invalid_registration_data',
        );
      }

      // Safely cast user data to Map<String, dynamic>
      final Map<String, dynamic> userData;
      if (data['user'] is Map<String, dynamic>) {
        userData = data['user'] as Map<String, dynamic>;
      } else if (data['user'] is Map) {
        userData = Map<String, dynamic>.from(data['user'] as Map);
      } else {
        throw ApiException(
          message: 'Invalid user data format in registration response',
          code: 'invalid_user_data_format',
        );
      }

      // Safely cast registration progress data
      final Map<String, dynamic> progressData;
      if (data['registrationProgress'] is Map<String, dynamic>) {
        progressData = data['registrationProgress'] as Map<String, dynamic>;
      } else if (data['registrationProgress'] is Map) {
        progressData =
            Map<String, dynamic>.from(data['registrationProgress'] as Map);
      } else {
        throw ApiException(
          message: 'Invalid registration progress data format',
          code: 'invalid_progress_data_format',
        );
      }

      final user = UserModel.fromJson(userData);
      final registrationProgress =
          RegistrationProgressModel.fromJson(progressData).toDomain();

      await _authUtils.saveUserData(user);

      _logger.d(
          'AuthRepositoryImpl: Registration successful for user: ${user.email}, nextStep: ${registrationProgress.nextStep}');
      return (user.toDomain(), registrationProgress);
    });
  }

  @override
  Future<User> createEmployee({
    required String fullName,
    required String email,
    required String phone,
    String? password,
    bool generatePassword = true,
    List<String>? permissions,
    String? position,
    String? note,
  }) async {
    _logger.d('AuthRepositoryImpl: Creating employee with email: $email');
    return await executeWithConnectivity(operation: () async {
      final data = await _remoteSource.createEmployee(
        userTitle: fullName,
        email: email,
        password: password ?? 'defaultPassword123', // Provide default if null
        visibility: {
          'customerManagement': {
            'create': permissions?.contains('customer_create') ?? false,
            'update': permissions?.contains('customer_update') ?? false,
            'view': permissions?.contains('customer_view') ?? false,
            'delete': permissions?.contains('customer_delete') ?? false,
          },
          'debtManagement': {
            'create': permissions?.contains('debt_create') ?? false,
            'update': permissions?.contains('debt_update') ?? false,
            'view': permissions?.contains('debt_view') ?? false,
            'delete': permissions?.contains('debt_delete') ?? false,
          },
          'reportManagement': {
            'generate': permissions?.contains('report_generate') ?? false,
            'delete': permissions?.contains('report_delete') ?? false,
            'view': permissions?.contains('report_view') ?? false,
          },
        },
      );

      _logger.d('AuthRepositoryImpl: Employee created successfully');

      // Safely cast data to Map<String, dynamic>
      final Map<String, dynamic> userData;
      userData = data;

      return UserModel.fromJson(userData).toDomain();
    });
  }

  @override
  Future<(User, RegistrationProgress)> verifyEmail(
      String email, String verificationCode) async {
    _logger.d('AuthRepositoryImpl: Verifying email: $email');

    return await executeWithConnectivity(
      operation: () async {
        final data = await _remoteSource.verifyEmail(email, verificationCode);

        if (!data.containsKey('user')) {
          throw ApiException(
            message: 'User data missing in response',
            code: 'missing_user_data',
          );
        }

        // Safely cast user data to Map<String, dynamic>
        final Map<String, dynamic> userData;
        if (data['user'] is Map<String, dynamic>) {
          userData = data['user'] as Map<String, dynamic>;
        } else if (data['user'] is Map) {
          userData = Map<String, dynamic>.from(data['user'] as Map);
        } else {
          throw ApiException(
            message: 'Invalid user data format in response',
            code: 'invalid_user_data_format',
          );
        }

        var userModel = UserModel.fromJson(userData);

        // Create registration progress from response
        final registrationProgress = data.containsKey('registrationProgress')
            ? () {
                // Safely cast registration progress data
                final Map<String, dynamic> progressData;
                if (data['registrationProgress'] is Map<String, dynamic>) {
                  progressData =
                      data['registrationProgress'] as Map<String, dynamic>;
                } else if (data['registrationProgress'] is Map) {
                  progressData = Map<String, dynamic>.from(
                      data['registrationProgress'] as Map);
                } else {
                  throw ApiException(
                    message: 'Invalid registration progress data format',
                    code: 'invalid_progress_data_format',
                  );
                }
                return RegistrationProgressModel.fromJson(progressData)
                    .toDomain();
              }()
            : RegistrationProgressModel(
                currentStep: 'email_verified',
                nextStep: data['nextStep'] ?? 'payment_required',
                progress: 50,
                data: {},
                verificationCodeExpiresAt: null,
              ).toDomain();

        // CRITICAL: Save tokens if provided in the response
        if (data.containsKey('accessToken') &&
            data.containsKey('refreshToken')) {
          _logger.d(
              'AuthRepositoryImpl: Saving tokens from email verification response');

          await _tokenManager.saveTokens(
            accessToken: data['accessToken'],
            refreshToken: data['refreshToken'],
          );

          // Update the DioClient with the new token to ensure it's immediately available
          _dioClient.updateAuthToken(data['accessToken']);

          // Update the user model with token information using copyWith
          userModel = userModel.copyWith(
            accessToken: data['accessToken'],
            refreshToken: data['refreshToken'],
            tokenExpiresAt: DateTime.now()
                .add(const Duration(hours: 2)), // Fixed: Match backend 2h
          );

          _logger.d(
              'AuthRepositoryImpl: Tokens saved and DioClient updated with new token');
        } else {
          _logger.w(
              'AuthRepositoryImpl: No tokens found in email verification response');
        }

        // Save user data
        await _authUtils.saveUserData(userModel);

        _logger
            .d('AuthRepositoryImpl: Email verification completed successfully');
        return (userModel.toDomain(), registrationProgress);
      },
      retryAutomatically: false,
    );
  }

  /// Helper method to calculate progress percentage based on user status
  int _calculateProgressPercentage(String userStatus) {
    switch (userStatus) {
      case 'pending_email_verification':
        return 25;
      case 'email_verified_pending_payment':
        return 75;
      case 'active':
        return 100;
      default:
        return 0;
    }
  }

  /// Helper method to determine next step based on user status
  String _determineNextStep(String userStatus) {
    switch (userStatus) {
      case 'pending_email_verification':
        return 'verify_email_required';
      case 'email_verified_pending_payment':
        return 'payment_required';
      case 'active':
        return 'registration_complete';
      default:
        return 'unknown';
    }
  }

  @override
  Future<void> resendVerification(String email) async {
    _logger.d('AuthRepositoryImpl: Resending verification for email: $email');
    await executeWithConnectivity(
      operation: () => _remoteSource.resendVerification(email),
      retryAutomatically:
          false, // Disable automatic retries for auth operations
      maxRetries: 0, // No retries
    );
    _logger.d('AuthRepositoryImpl: Verification resend completed');
  }

  @override
  Future<bool> checkEmailExists(String email) async {
    _logger.d('AuthRepositoryImpl: Checking if email exists: $email');
    return await executeWithConnectivity(operation: () async {
      try {
        final result = await _remoteSource.checkEmailExists(email);
        final exists = result['exists'] == true;
        _logger.d('AuthRepositoryImpl: Email exists check result: $exists');
        return exists;
      } catch (e) {
        if (e is ApiException &&
            (e.code == 'email_conflict' || e.statusCode == 409)) {
          _logger.d('AuthRepositoryImpl: Email exists (conflict detected)');
          return true;
        }
        _logger.e('AuthRepositoryImpl: Error checking email existence: $e');
        rethrow;
      }
    });
  }

  @override
  Future<void> forgotPassword(String email) async {
    _logger.d('AuthRepositoryImpl: Forgot password request for email: $email');

    try {
      // Add timeout wrapper for extra safety
      await executeWithConnectivity(
        operation: () => _remoteSource.forgotPassword(email).timeout(
          const Duration(
              seconds: 12), // Slightly longer than remote source timeout
          onTimeout: () {
            _logger.w('AuthRepositoryImpl: Forgot password request timed out');
            throw ApiException(
              message: 'Request timed out. Please try again.',
              code: 'request_timeout',
            );
          },
        ),
        retryAutomatically:
            false, // Disable automatic retries for auth operations
        maxRetries: 0, // No retries
      );
      _logger.d('AuthRepositoryImpl: Forgot password request completed');
    } catch (e) {
      _logger.e('AuthRepositoryImpl: Forgot password error: $e');
      rethrow;
    }
  }

  @override
  Future<void> logout() async {
    _logger.d('AuthRepositoryImpl: Logout initiated');
    try {
      final token = await _tokenManager.getAccessToken();
      if (token != null) {
        await _remoteSource.logout().timeout(
          const Duration(seconds: 5),
          onTimeout: () {
            _logger.w('AuthRepositoryImpl: Logout request timed out');
            return;
          },
        );
      }
      _logger.d('AuthRepositoryImpl: Remote logout completed');
    } catch (e) {
      _logger.e('AuthRepositoryImpl: Error during remote logout: $e');
      // Ignore remote logout errors
    } finally {
      await _tokenManager.clearTokens();
      await _authUtils.clearUserData();

      // Clear tokens from DioClient to ensure no authorization headers remain
      _dioClient.clearAuthToken();

      _logger.d(
          'AuthRepositoryImpl: Local logout completed with DioClient token clearing');
    }
  }

  @override
  Future<void> logoutAll() async {
    _logger.d('AuthRepositoryImpl: Logout all initiated');
    try {
      await executeWithConnectivity(
        operation: () => _remoteSource.logoutAll(),
        retryAutomatically: false,
      );
      _logger.d('AuthRepositoryImpl: Remote logout all completed');
    } finally {
      await _tokenManager.clearTokens();
      await _authUtils.clearUserData();

      // Clear tokens from DioClient to ensure no authorization headers remain
      _dioClient.clearAuthToken();

      _logger.d(
          'AuthRepositoryImpl: Local logout all completed with DioClient token clearing');
    }
  }

  @override
  Future<AuthToken> refreshToken(String refreshToken) async {
    _logger.d('AuthRepositoryImpl: Token refresh initiated');
    final data = await _remoteSource.refreshToken(refreshToken);

    final token = AuthTokenModel.fromJson({
      'accessToken': data['accessToken'],
      'refreshToken': data['refreshToken'],
      'expiresIn': data['expiresIn'] ?? 900,
    });

    await _tokenManager.saveTokens(
      accessToken: token.accessToken,
      refreshToken: token.refreshToken,
    );

    // Synchronize the refreshed token with DioClient
    _dioClient.updateAuthToken(token.accessToken);

    _logger.d(
        'AuthRepositoryImpl: Token refresh completed and synchronized with DioClient');
    return token.toDomain();
  }

  @override
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    _logger.d('AuthRepositoryImpl: Change password initiated');
    return await executeWithConnectivity(
      operation: () => _remoteSource.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
        confirmPassword: confirmPassword,
      ),
    );
  }

  @override
  Future<void> resetPassword({
    required String token,
    required String newPassword,
    required String confirmPassword,
  }) async {
    _logger.d('AuthRepositoryImpl: Reset password initiated');
    return await executeWithConnectivity(
      operation: () => _remoteSource.resetPassword(
        token: token,
        newPassword: newPassword,
        confirmPassword: confirmPassword,
      ),
    );
  }

  @override
  Future<(User, RegistrationProgress)> processPayment({
    required String userId,
    required String shopId,
    required String planId,
    required String paymentMethod,
    required String phoneNumber,
    double? amount,
    String? discountCode,
  }) async {
    _logger.d(
        'AuthRepositoryImpl: Processing payment for user: $userId, plan: $planId, shop: $shopId');

    // Use the provided shopId directly without trying to get the current user
    // This is important because the token might be fresh from email verification
    final effectiveShopId = shopId;
    _logger.d(
        'AuthRepositoryImpl: Using shopId: $effectiveShopId for payment processing');

    return await executeWithConnectivity(operation: () async {
      try {
        // Log all parameters being sent to ensure they match backend expectations
        _logger.d(
            'AuthRepositoryImpl: Payment parameters - userId: $userId, shopId: $effectiveShopId, planId: $planId, paymentMethod: $paymentMethod, phoneNumber: $phoneNumber, amount: $amount, discountCode: $discountCode');

        // Get the current tokens before making the payment request
        final accessToken = await _tokenManager.getAccessToken();

        // Log the token for debugging
        _logger.d(
            'AuthRepositoryImpl: Using access token for payment: ${accessToken != null ? 'Token available' : 'No token'}');

        final data = await _remoteSource.processPayment(
          userId: userId,
          shopId: effectiveShopId,
          planId: planId,
          paymentMethod: paymentMethod,
          phoneNumber: phoneNumber,
          amount: amount ?? 0.0,
          discountCode: discountCode,
        );

        _logger.d('AuthRepositoryImpl: Payment response received: $data');

        // Handle the response which might not have the expected format
        if (!data.containsKey('user')) {
          _logger
              .e('AuthRepositoryImpl: Missing user data in payment response');
          throw ApiException(
            message: 'Missing user data in payment response',
            code: 'invalid_payment_response_data',
          );
        }

        // Safely cast user data to Map<String, dynamic>
        final Map<String, dynamic> userData;
        if (data['user'] is Map<String, dynamic>) {
          userData = data['user'] as Map<String, dynamic>;
        } else if (data['user'] is Map) {
          userData = Map<String, dynamic>.from(data['user'] as Map);
        } else {
          throw ApiException(
            message: 'Invalid user data format in payment response',
            code: 'invalid_user_data_format',
          );
        }

        // Create user model from the response
        var userModel = UserModel.fromJson(userData);
        _logger.d(
            'AuthRepositoryImpl: User model after payment - shopId: ${userModel.shopId}, status: ${userModel.status}');

        // Create registration progress from response or construct it
        final registrationProgress = data.containsKey('registrationProgress')
            ? () {
                // Safely cast registration progress data
                final Map<String, dynamic> progressData;
                if (data['registrationProgress'] is Map<String, dynamic>) {
                  progressData =
                      data['registrationProgress'] as Map<String, dynamic>;
                } else if (data['registrationProgress'] is Map) {
                  progressData = Map<String, dynamic>.from(
                      data['registrationProgress'] as Map);
                } else {
                  throw ApiException(
                    message:
                        'Invalid registration progress data format in payment response',
                    code: 'invalid_progress_data_format',
                  );
                }
                return RegistrationProgressModel.fromJson(progressData)
                    .toDomain();
              }()
            : RegistrationProgressModel(
                currentStep: 'payment_complete',
                nextStep: data['nextStep'] ?? 'registration_complete',
                progress: 100,
                data: {'paymentStatus': 'completed'},
                selectedPlanId: planId,
                selectedPaymentMethod: paymentMethod,
                verificationCodeExpiresAt: null,
              ).toDomain();

        // Handle tokens if provided
        if (data.containsKey('accessToken') &&
            data.containsKey('refreshToken')) {
          _logger.d('AuthRepositoryImpl: Tokens received in payment response');

          await _tokenManager.saveTokens(
            accessToken: data['accessToken'],
            refreshToken: data['refreshToken'],
          );

          // Update the token in the DioClient to ensure it's immediately available
          _dioClient.updateAuthToken(data['accessToken']);

          // Update the user model with token information using copyWith
          userModel = userModel.copyWith(
            accessToken: data['accessToken'],
            refreshToken: data['refreshToken'],
            tokenExpiresAt: DateTime.now()
                .add(const Duration(hours: 2)), // Fixed: Match backend 2h
          );

          _logger.d(
              'AuthRepositoryImpl: Payment tokens saved and DioClient updated');
        }

        await _authUtils.saveUserData(userModel);
        _logger
            .d('AuthRepositoryImpl: Payment processing completed successfully');

        return (userModel.toDomain(), registrationProgress);
      } catch (e) {
        if (e is ApiException &&
            e.message.toLowerCase().contains('shop not found')) {
          _logger.e(
              'AuthRepositoryImpl: Active shop not found error. This is likely because the shop is not yet activated in the backend.');
          _logger.e(
              'AuthRepositoryImpl: Shop ID: $effectiveShopId, User ID: $userId');

          // Rethrow with a more specific message
          throw ApiException(
            message:
                'Your shop is not yet activated. Please contact support or try again later.',
            code: 'shop_not_activated',
            statusCode: 404,
          );
        }
        rethrow;
      }
    });
  }

  @override
  Future<List<String>> getAvailablePaymentMethods(String context) async {
    _logger.d(
        'AuthRepositoryImpl: Getting available payment methods for context: $context');
    final methods = await executeWithConnectivity(
      operation: () => _remoteSource.getAvailablePaymentMethods(context),
    );
    _logger.d(
        'AuthRepositoryImpl: Available payment methods retrieved: ${methods.length} methods');
    return methods;
  }

  /// Update user profile
  Future<User> updateProfile({
    required String fullName,
    required String phone,
  }) async {
    _logger.d('AuthRepositoryImpl: Updating user profile');
    return executeWithConnectivity(operation: () async {
      try {
        // Debug: Check current token before making request
        final currentToken = await _tokenManager.getAccessToken();
        if (kDebugMode) {
          print(
              '🔐 ProfileUpdate: Token available: ${currentToken != null ? "Yes (${currentToken.length} chars)" : "No"}');
          print('🔐 ProfileUpdate: Calling endpoint /auth/me');
          print('🔐 ProfileUpdate: Data: {fullName: $fullName, phone: $phone}');

          // CRITICAL DEBUG: Check DioClient state
          final debugInfo = _dioClient.getDebugInfo();
          print('🔐 ProfileUpdate: DioClient Debug Info: $debugInfo');

          // Check if AuthInterceptor is present
          final hasAuthInterceptor =
              debugInfo['interceptorTypes'].contains('AuthInterceptor');
          print('🔐 ProfileUpdate: Has AuthInterceptor: $hasAuthInterceptor');

          if (!hasAuthInterceptor) {
            print(
                '🔐 ProfileUpdate: ❌ CRITICAL: AuthInterceptor NOT ATTACHED!');
            print(
                '🔐 ProfileUpdate: This explains why authentication is failing');
          }

          // Force token sync to ensure DioClient has latest tokens
          print('🔐 ProfileUpdate: Syncing tokens from storage...');
          await _dioClient.syncTokensFromStorage();

          final debugInfoAfter = _dioClient.getDebugInfo();
          print('🔐 ProfileUpdate: After sync - Debug Info: $debugInfoAfter');
        }

        final response = await _dioClient.put('/auth/me', data: {
          'fullName': fullName,
          'phone': phone,
        });

        if (kDebugMode) {
          print('🔐 ProfileUpdate: Response received: ${response.runtimeType}');
          print(
              '🔐 ProfileUpdate: Response preview: ${response.toString().substring(0, response.toString().length > 100 ? 100 : response.toString().length)}...');
        }

        if (response is Map<String, dynamic> &&
            response['success'] == true &&
            response['data'] != null) {
          final userModel = UserModel.fromMap(response['data']);
          final user = userModel.toDomain();

          // Update stored user data
          await _authUtils.saveUserData(userModel);

          _logger.d('AuthRepositoryImpl: Profile updated successfully');
          return user;
        } else {
          throw ApiException(
            message: 'Invalid response format',
            code: 'invalid_response',
          );
        }
      } catch (e) {
        if (kDebugMode) {
          print('🔐 ProfileUpdate: ERROR occurred: ${e.runtimeType}');
          print('🔐 ProfileUpdate: ERROR details: $e');
        }

        _logger.e('AuthRepositoryImpl: Profile update failed: $e');
        rethrow;
      }
    });
  }

  /// Validate current session and tokens
  Future<bool> validateSession() async {
    _logger.d('AuthRepositoryImpl: Validating current session');

    try {
      // Check if we have stored tokens
      final accessToken = await _tokenManager.getAccessToken();
      if (accessToken == null || accessToken.isEmpty) {
        _logger.d('AuthRepositoryImpl: No access token found');
        return false;
      }

      // Validate token integrity
      final isValid = await _tokenManager.validateTokenIntegrity(accessToken);
      if (!isValid) {
        _logger.d('AuthRepositoryImpl: Access token is invalid');
        return false;
      }

      // Check if token is expired
      final isExpired = await _tokenManager.isTokenExpired();
      if (isExpired) {
        // Try to refresh the token
        _logger.d('AuthRepositoryImpl: Token expired, attempting refresh');
        final hasRefreshToken = await _tokenManager.hasValidRefreshToken();

        if (hasRefreshToken) {
          try {
            final refreshToken = await _tokenManager.getRefreshToken();
            await this.refreshToken(refreshToken!);
            _logger.d(
                'AuthRepositoryImpl: Token refreshed successfully during validation');
            return true;
          } catch (e) {
            _logger.e(
                'AuthRepositoryImpl: Failed to refresh token during validation: $e');
            return false;
          }
        } else {
          _logger.d('AuthRepositoryImpl: No valid refresh token available');
          return false;
        }
      }

      // Validate with server by making a lightweight request
      try {
        await _dioClient.getSilent('/auth/me');
        _logger.d(
            'AuthRepositoryImpl: Session validated successfully with server');
        return true;
      } catch (e) {
        _logger.e('AuthRepositoryImpl: Server session validation failed: $e');
        return false;
      }
    } catch (e) {
      _logger.e('AuthRepositoryImpl: Error during session validation: $e');
      return false;
    }
  }

  /// Handle app lifecycle events for session management
  Future<void> handleAppLifecycleChange(String state) async {
    _logger.d('AuthRepositoryImpl: Handling app lifecycle change: $state');

    switch (state) {
      case 'resumed':
        // Validate session when app is resumed
        final isValid = await validateSession();
        if (!isValid) {
          _logger.w(
              'AuthRepositoryImpl: Session invalid after app resume, clearing tokens');
          await _tokenManager.clearTokens();
          await _authUtils.clearUserData();
          _dioClient.clearAuthToken();
        }
        break;
      case 'paused':
        // Could save session state here if needed
        _logger.d('AuthRepositoryImpl: App paused, session state preserved');
        break;
      case 'detached':
        // App is being terminated
        _logger.d('AuthRepositoryImpl: App detached');
        break;
    }
  }

  /// Get session information for debugging
  Future<Map<String, dynamic>> getSessionInfo() async {
    final tokenInfo = await _tokenManager.getTokenInfo();
    final userData = await _authUtils.getUserData();

    return {
      'tokenInfo': tokenInfo,
      'hasUserData': userData != null,
      'userEmail': userData?.email,
      'userStatus': userData?.status,
      'dioClientHasTokens': _dioClient.hasTokensLoaded,
    };
  }
}
