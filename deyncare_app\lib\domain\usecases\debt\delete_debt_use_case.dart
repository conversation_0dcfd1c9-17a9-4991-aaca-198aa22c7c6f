import 'package:dartz/dartz.dart';
import 'package:deyncare_app/domain/repositories/debt_repository.dart';
import 'package:deyncare_app/core/errors/failures.dart';

/// Use case for deleting debt - BACKEND ALIGNED
/// Matches DELETE /api/debts/:debtId endpoint
class DeleteDebtUseCase {
  final DebtRepository _repository;

  DeleteDebtUseCase(this._repository);

  /// Execute the use case to delete debt
  Future<Either<Failure, void>> execute(String debtId) async {
    if (debtId.trim().isEmpty) {
      return Left(ValidationFailure(message: 'Debt ID is required'));
    }

    // Validate debt ID format
    final debtIdPattern = RegExp(r'^DEBT\d{3}$');
    if (!debtIdPattern.hasMatch(debtId)) {
      return Left(ValidationFailure(message: 'Invalid debt ID format. Expected format: DEBT001'));
    }

    try {
      await _repository.deleteDebt(debtId.trim());
      return Right(null);
    } catch (e) {
      if (e.toString().contains('not found')) {
        return Left(NotFoundFailure(message: 'Debt not found'));
      } else if (e.toString().contains('network') || e.toString().contains('connection')) {
        return Left(NetworkFailure(message: 'Network error. Please check your connection.'));
      } else {
        return Left(ServerFailure(message: 'Failed to delete debt. Please try again.'));
      }
    }
  }
} 