import 'package:flutter/foundation.dart';

/// Utility class to manage loading states consistently across forms
/// Prevents buttons from getting stuck in loading state after errors
class LoadingStateManager {
  /// Handles async operations with guaranteed loading state reset
  /// 
  /// Usage:
  /// ```dart
  /// await LoadingStateManager.execute(
  ///   context: context,
  ///   operation: () async => await someApiCall(),
  ///   onLoadingChanged: (loading) => setState(() => _isLoading = loading),
  ///   onSuccess: (result) => handleSuccess(result),
  ///   onError: (error) => showError(error),
  /// );
  /// ```
  static Future<T?> execute<T>({
    required Future<T> Function() operation,
    required Function(bool isLoading) onLoadingChanged,
    Function(T result)? onSuccess,
    Function(dynamic error)? onError,
    String? defaultErrorMessage,
  }) async {
    try {
      // Start loading
      onLoadingChanged(true);
      
      // Execute operation
      final result = await operation();
      
      // Success - reset loading first, then handle success
      onLoadingChanged(false);
      onSuccess?.call(result);
      
      return result;
    } catch (error) {
      // CRITICAL: Always reset loading state on ANY error
      onLoadingChanged(false);
      
      // Handle error
      onError?.call(error);
      
      return null;
    } finally {
      // SAFETY NET: Ensure loading is always reset
      onLoadingChanged(false);
    }
  }
  
  /// Creates a safe setState wrapper that prevents setState after dispose
  static Function(VoidCallback) createSafeSetState(bool mounted) {
    return (VoidCallback fn) {
      if (mounted) {
        fn();
      }
    };
  }
} 