/// Risk Report Data Model - matches the risk report API response structure
/// This model is specifically designed for the /reports/risks/data endpoint
/// which returns customer risk assessment data for reporting
class RiskReportData {
  final String customerId;
  final String customerName;
  final String customerType;
  final String riskLevel;
  final double totalDebt;
  final double paidAmount;
  final int paymentDelay;
  final double outstandingDebt;
  final int totalTransactions;
  final int latePayments;
  final double averagePaymentTime;
  final DateTime lastPaymentDate;
  final String riskCategory;
  final double riskScore;

  const RiskReportData({
    required this.customerId,
    required this.customerName,
    required this.customerType,
    required this.riskLevel,
    required this.totalDebt,
    required this.paidAmount,
    required this.paymentDelay,
    required this.outstandingDebt,
    required this.totalTransactions,
    required this.latePayments,
    required this.averagePaymentTime,
    required this.lastPaymentDate,
    required this.riskCategory,
    required this.riskScore,
  });

  /// Factory constructor with null safety for API response parsing
  /// Provides sensible defaults for any null values from the API
  factory RiskReportData.fromJson(Map<String, dynamic> json) {
    final totalDebt = (json['totalDebt'] as num?)?.toDouble() ?? 0.0;
    final paidAmount = (json['paidAmount'] as num?)?.toDouble() ?? 0.0;
    final outstandingDebt =
        (json['outstandingDebt'] as num?)?.toDouble() ?? 0.0;
    final latePayments = (json['latePayments'] as num?)?.toInt() ?? 0;
    final totalTransactions = (json['totalTransactions'] as num?)?.toInt() ?? 0;

    // Calculate risk score based on payment behavior
    double riskScore = 0.0;
    if (totalTransactions > 0) {
      final latePaymentRatio = latePayments / totalTransactions;
      final debtRatio = outstandingDebt / (totalDebt > 0 ? totalDebt : 1);
      riskScore = (latePaymentRatio * 0.6 + debtRatio * 0.4) * 100;
    }

    // Determine risk category based on score
    String riskCategory;
    if (riskScore >= 70) {
      riskCategory = 'High Risk';
    } else if (riskScore >= 40) {
      riskCategory = 'Medium Risk';
    } else {
      riskCategory = 'Low Risk';
    }

    return RiskReportData(
      customerId: json['customerId'] as String? ?? '',
      customerName: json['customerName'] as String? ??
          json['CustomerName'] as String? ??
          '',
      customerType: json['customerType'] as String? ??
          json['CustomerType'] as String? ??
          'Unknown',
      riskLevel: json['riskLevel'] as String? ?? riskCategory,
      totalDebt: totalDebt,
      paidAmount: paidAmount,
      paymentDelay: (json['paymentDelay'] as num?)?.toInt() ?? 0,
      outstandingDebt: outstandingDebt,
      totalTransactions: totalTransactions,
      latePayments: latePayments,
      averagePaymentTime:
          (json['averagePaymentTime'] as num?)?.toDouble() ?? 0.0,
      lastPaymentDate: json['lastPaymentDate'] != null
          ? DateTime.parse(json['lastPaymentDate'] as String)
          : DateTime.now()
              .subtract(const Duration(days: 365)), // Default to 1 year ago
      riskCategory: json['riskCategory'] as String? ?? riskCategory,
      riskScore: (json['riskScore'] as num?)?.toDouble() ?? riskScore,
    );
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'customerId': customerId,
      'customerName': customerName,
      'customerType': customerType,
      'riskLevel': riskLevel,
      'totalDebt': totalDebt,
      'paidAmount': paidAmount,
      'paymentDelay': paymentDelay,
      'outstandingDebt': outstandingDebt,
      'totalTransactions': totalTransactions,
      'latePayments': latePayments,
      'averagePaymentTime': averagePaymentTime,
      'lastPaymentDate': lastPaymentDate.toIso8601String(),
      'riskCategory': riskCategory,
      'riskScore': riskScore,
    };
  }

  /// Create a copy with modified fields
  RiskReportData copyWith({
    String? customerId,
    String? customerName,
    String? customerType,
    String? riskLevel,
    double? totalDebt,
    double? paidAmount,
    int? paymentDelay,
    double? outstandingDebt,
    int? totalTransactions,
    int? latePayments,
    double? averagePaymentTime,
    DateTime? lastPaymentDate,
    String? riskCategory,
    double? riskScore,
  }) {
    return RiskReportData(
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerType: customerType ?? this.customerType,
      riskLevel: riskLevel ?? this.riskLevel,
      totalDebt: totalDebt ?? this.totalDebt,
      paidAmount: paidAmount ?? this.paidAmount,
      paymentDelay: paymentDelay ?? this.paymentDelay,
      outstandingDebt: outstandingDebt ?? this.outstandingDebt,
      totalTransactions: totalTransactions ?? this.totalTransactions,
      latePayments: latePayments ?? this.latePayments,
      averagePaymentTime: averagePaymentTime ?? this.averagePaymentTime,
      lastPaymentDate: lastPaymentDate ?? this.lastPaymentDate,
      riskCategory: riskCategory ?? this.riskCategory,
      riskScore: riskScore ?? this.riskScore,
    );
  }

  /// Check if customer is high risk
  bool get isHighRisk => riskScore >= 70;

  /// Check if customer is medium risk
  bool get isMediumRisk => riskScore >= 40 && riskScore < 70;

  /// Check if customer is low risk
  bool get isLowRisk => riskScore < 40;

  /// Get payment reliability percentage
  double get paymentReliability => totalTransactions > 0
      ? ((totalTransactions - latePayments) / totalTransactions) * 100
      : 100.0;

  /// Get formatted last payment date
  String get formattedLastPaymentDate =>
      '${lastPaymentDate.day}/${lastPaymentDate.month}/${lastPaymentDate.year}';

  /// Get risk level color indicator
  String get riskLevelColor {
    if (isHighRisk) return 'red';
    if (isMediumRisk) return 'orange';
    return 'green';
  }

  @override
  String toString() {
    return 'RiskReportData(customerId: $customerId, customerName: $customerName, riskLevel: $riskLevel, riskScore: ${riskScore.toStringAsFixed(1)}, paymentDelay: $paymentDelay days)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RiskReportData &&
        other.customerId == customerId &&
        other.customerName == customerName &&
        other.customerType == customerType &&
        other.riskLevel == riskLevel &&
        other.totalDebt == totalDebt &&
        other.paidAmount == paidAmount &&
        other.paymentDelay == paymentDelay &&
        other.outstandingDebt == outstandingDebt &&
        other.totalTransactions == totalTransactions &&
        other.latePayments == latePayments &&
        other.averagePaymentTime == averagePaymentTime &&
        other.lastPaymentDate == lastPaymentDate &&
        other.riskCategory == riskCategory &&
        other.riskScore == riskScore;
  }

  @override
  int get hashCode {
    return customerId.hashCode ^
        customerName.hashCode ^
        customerType.hashCode ^
        riskLevel.hashCode ^
        totalDebt.hashCode ^
        paidAmount.hashCode ^
        paymentDelay.hashCode ^
        outstandingDebt.hashCode ^
        totalTransactions.hashCode ^
        latePayments.hashCode ^
        averagePaymentTime.hashCode ^
        lastPaymentDate.hashCode ^
        riskCategory.hashCode ^
        riskScore.hashCode;
  }
}
