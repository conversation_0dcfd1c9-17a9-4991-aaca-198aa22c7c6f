import 'dart:async';
import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/constants/app_strings.dart';

/// Dialog widget for displaying account suspension information with countdown timer
class SuspensionDialog extends StatefulWidget {
  final String reason;
  final DateTime? suspendedAt;
  final String? suspendedBy;
  final int initialCountdown;
  final VoidCallback onRedirectComplete;
  final VoidCallback? onDismiss;

  const SuspensionDialog({
    Key? key,
    required this.reason,
    this.suspendedAt,
    this.suspendedBy,
    this.initialCountdown = 10,
    required this.onRedirectComplete,
    this.onDismiss,
  }) : super(key: key);

  @override
  State<SuspensionDialog> createState() => _SuspensionDialogState();
}

class _SuspensionDialogState extends State<SuspensionDialog>
    with SingleTickerProviderStateMixin {
  late int _countdown;
  Timer? _timer;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _countdown = widget.initialCountdown;
    
    // Initialize animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    // Start animations
    _animationController.forward();
    
    // Start countdown timer
    _startCountdown();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          if (_countdown > 1) {
            _countdown--;
          } else {
            _timer?.cancel();
            widget.onRedirectComplete();
          }
        });
      }
    });
  }

  void _handleDismiss() {
    _timer?.cancel();
    if (widget.onDismiss != null) {
      widget.onDismiss!();
    } else {
      widget.onRedirectComplete();
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        _handleDismiss();
        return false;
      },
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Opacity(
            opacity: _fadeAnimation.value,
            child: Dialog(
              backgroundColor: Colors.transparent,
              elevation: 0,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 400),
                  decoration: BoxDecoration(
                    color: AppThemes.surfaceColor,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: AppThemes.shadowDark,
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header with icon
                      Container(
                        padding: const EdgeInsets.fromLTRB(24, 24, 24, 16),
                        child: Column(
                          children: [
                            // Warning icon
                            Container(
                              width: 64,
                              height: 64,
                              decoration: BoxDecoration(
                                color: AppThemes.errorSurface,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.block_rounded,
                                size: 32,
                                color: AppThemes.errorColor,
                              ),
                            ),
                            const SizedBox(height: 16),
                            
                            // Title
                            Text(
                              'Account Suspended',
                              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                color: AppThemes.errorColor,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                      
                      // Content
                      Padding(
                        padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Reason
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: AppThemes.errorSurface,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: AppThemes.errorColor.withOpacity(0.2),
                                  width: 1,
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Reason:',
                                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                      color: AppThemes.errorColor,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    widget.reason,
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: Theme.of(context).colorScheme.onSurface,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            
                            // Additional info if available
                            if (widget.suspendedAt != null) ...[
                              const SizedBox(height: 12),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Icon(
                                    Icons.schedule_rounded,
                                    size: 16,
                                    color: AppThemes.textSecondaryColor,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      'Suspended on ${_formatDateTime(widget.suspendedAt!)}',
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: AppThemes.textSecondaryColor,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                            
                            const SizedBox(height: 20),
                            
                            // Redirect message with countdown
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: AppThemes.primarySurface,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Column(
                                children: [
                                  Text(
                                    'You will be redirected to the login screen in',
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: AppThemes.textSecondaryColor,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 8),
                                  
                                  // Countdown circle
                                  Container(
                                    width: 48,
                                    height: 48,
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).colorScheme.primary,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Center(
                                      child: Text(
                                        '$_countdown',
                                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                          color: Theme.of(context).colorScheme.onPrimary,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                  
                                  const SizedBox(height: 8),
                                  Text(
                                    _countdown == 1 ? 'second' : 'seconds',
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            
                            const SizedBox(height: 20),
                            
                            // Action button
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: _handleDismiss,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Theme.of(context).colorScheme.primary,
                                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: Text(
                                  'Back to Login',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Theme.of(context).colorScheme.onPrimary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Dialog widget for displaying role restriction information
class RoleRestrictionDialog extends StatefulWidget {
  final String userRole;
  final String message;
  final int initialCountdown;
  final VoidCallback onRedirectComplete;
  final VoidCallback? onDismiss;

  const RoleRestrictionDialog({
    Key? key,
    required this.userRole,
    required this.message,
    this.initialCountdown = 5,
    required this.onRedirectComplete,
    this.onDismiss,
  }) : super(key: key);

  @override
  State<RoleRestrictionDialog> createState() => _RoleRestrictionDialogState();
}

class _RoleRestrictionDialogState extends State<RoleRestrictionDialog>
    with SingleTickerProviderStateMixin {
  late int _countdown;
  Timer? _timer;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _countdown = widget.initialCountdown;
    
    // Initialize animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    // Start animations
    _animationController.forward();
    
    // Start countdown timer
    _startCountdown();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          if (_countdown > 1) {
            _countdown--;
          } else {
            _timer?.cancel();
            widget.onRedirectComplete();
          }
        });
      }
    });
  }

  void _handleDismiss() {
    _timer?.cancel();
    if (widget.onDismiss != null) {
      widget.onDismiss!();
    } else {
      widget.onRedirectComplete();
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        _handleDismiss();
        return false;
      },
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Opacity(
            opacity: _fadeAnimation.value,
            child: Dialog(
              backgroundColor: Colors.transparent,
              elevation: 0,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 380),
                  decoration: BoxDecoration(
                    color: AppThemes.surfaceColor,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: AppThemes.shadowDark,
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header with icon
                      Container(
                        padding: const EdgeInsets.fromLTRB(24, 24, 24, 16),
                        child: Column(
                          children: [
                            // Warning icon
                            Container(
                              width: 64,
                              height: 64,
                              decoration: BoxDecoration(
                                color: AppThemes.warningSurface,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.security_rounded,
                                size: 32,
                                color: AppThemes.warningColor,
                              ),
                            ),
                            const SizedBox(height: 16),
                            
                            // Title
                            Text(
                              'Access Restricted',
                              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                color: AppThemes.warningColor,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                      
                      // Content
                      Padding(
                        padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
                        child: Column(
                          children: [
                            // Message
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: AppThemes.warningSurface,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: AppThemes.warningColor.withOpacity(0.2),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                widget.message,
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Theme.of(context).colorScheme.onSurface,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            
                            const SizedBox(height: 16),
                            
                            // Role info
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.person_outline_rounded,
                                  size: 16,
                                  color: AppThemes.textSecondaryColor,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Current Role: ${widget.userRole}',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppThemes.textSecondaryColor,
                                  ),
                                ),
                              ],
                            ),
                            
                            const SizedBox(height: 20),
                            
                            // Countdown
                            Text(
                              'Redirecting in $_countdown ${_countdown == 1 ? 'second' : 'seconds'}...',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: AppThemes.textSecondaryColor,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            
                            const SizedBox(height: 20),
                            
                            // Action button
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: _handleDismiss,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppThemes.primaryColor,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: Text(
                                  'Back to Login',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
} 