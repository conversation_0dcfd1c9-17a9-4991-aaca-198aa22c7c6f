import 'package:deyncare_app/domain/repositories/payment_repository.dart';
import 'package:deyncare_app/domain/models/payment.dart';

/// Use case for retrieving payment history for a specific debt - BACKEND ALIGNED
/// Matches GET /api/debts/:debtId/payments endpoint
class GetPaymentHistoryUseCase {
  final PaymentRepository _repository;

  GetPaymentHistoryUseCase(this._repository);

  /// Execute the use case to get payment history for debt
  Future<List<Payment>> execute({
    required String debtId,
  }) async {
    if (debtId.trim().isEmpty) {
      throw ArgumentError('Debt ID is required');
    }

    // Validate debt ID format (matches backend validation)
    final debtIdPattern = RegExp(r'^DEBT\d{3}$');
    if (!debtIdPattern.hasMatch(debtId)) {
      throw ArgumentError('Invalid debt ID format. Expected format: DEBT001');
    }

    return await _repository.getPaymentHistory(debtId.trim());
  }
} 