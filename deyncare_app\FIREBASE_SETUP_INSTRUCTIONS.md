# 🔥 Firebase Setup Instructions for DeynCare

## ✅ Backend Already Configured

Your backend is already using the Firebase project `deyncare-47d99` with the service account:
- **Project ID**: `deyncare-47d99`
- **Project Number**: `*************`
- **Service Account**: `<EMAIL>`

## 📱 Flutter App Setup Required

You need to download the **real** configuration files from Firebase Console for your mobile apps:

### 1. Go to Firebase Console
Visit: [https://console.firebase.google.com/project/deyncare-47d99](https://console.firebase.google.com/project/deyncare-47d99)

### 2. Add/Configure Android App

1. Click on **"Add app"** or select the Android app icon
2. **Package name**: `com.deyncare.app`
3. **App nickname**: `DeynCare Admin Android`
4. **Debug signing certificate SHA-1**: (Optional, but recommended for testing)
5. Download `google-services.json`
6. **Replace** the template file at: `deyncare_app/android/app/google-services.json`

### 3. Add/Configure iOS App

1. Click on **"Add app"** or select the iOS app icon  
2. **Bundle ID**: `com.deyncare.app`
3. **App nickname**: `DeynCare Admin iOS`
4. Download `GoogleService-Info.plist`
5. **Replace** the template file at: `deyncare_app/ios/Runner/GoogleService-Info.plist`

## 🚀 Next Steps

After downloading the real configuration files:

```bash
# 1. Install dependencies
cd deyncare_app
flutter pub get

# 2. Run the app
flutter run

# 3. Test notifications
# - Open app
# - Navigate to notification test screen
# - Initialize notifications
# - Register FCM token
# - Send test notification
```

## 📋 Verification Checklist

- [ ] Downloaded real `google-services.json` from Firebase Console
- [ ] Downloaded real `GoogleService-Info.plist` from Firebase Console
- [ ] Replaced template files with real configuration
- [ ] Package name matches: `com.deyncare.app`
- [ ] Bundle ID matches: `com.deyncare.app`
- [ ] App builds successfully
- [ ] FCM token generated
- [ ] Test notification works

## 🔧 Test Integration

Your backend already has these APIs ready:
- `POST /api/fcm/register` - Register FCM token
- `POST /api/fcm/test` - Send test notification
- `DELETE /api/fcm/unregister` - Unregister token

The Flutter app will automatically:
- Initialize Firebase on app start
- Request notification permissions
- Generate FCM token
- Register with your backend
- Handle incoming notifications

## 🎯 Expected Notification Types

Your app will receive these from the backend:

1. **💰 Debt Created**: When admin creates new debt
2. **💳 Payment Recorded**: When payment is recorded with risk status
3. **⏰ Debt Reminders**: Automated reminders (7 days, 3 days, overdue)
4. **🧪 Test Notifications**: For testing the system

## 🆘 If You Need Help

1. Check the notification test screen for detailed status
2. Use the setup guide: `docs/FIREBASE_PUSH_NOTIFICATIONS_SETUP.md`
3. Test with Postman first if registration fails
4. Check Firebase Console for token registrations

---

**✨ Your Firebase push notification system is ready to connect with the existing backend!** 