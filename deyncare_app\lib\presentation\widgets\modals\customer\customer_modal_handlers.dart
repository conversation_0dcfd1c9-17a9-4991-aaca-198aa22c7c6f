import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wolt_modal_sheet/wolt_modal_sheet.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/domain/models/customer.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_bloc.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_event.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_state.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_builders.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_constants.dart';
import 'package:deyncare_app/presentation/widgets/modals/customer/forms/add_customer_form.dart';
import 'package:deyncare_app/presentation/widgets/modals/customer/forms/edit_customer_form.dart';
import 'package:deyncare_app/presentation/widgets/modals/customer/forms/edit_customer_by_id_form.dart';
import 'package:deyncare_app/presentation/widgets/modals/customer/views/customer_details_view.dart';
import 'package:deyncare_app/presentation/widgets/modals/customer/views/customer_details_by_id_view.dart';
import 'package:deyncare_app/presentation/widgets/skeleton_loader.dart';
import 'package:deyncare_app/injection_container.dart' as di;

/// Enhanced Customer Modal Handlers with Skeleton Loading
class CustomerModalHandlers {
  
  /// Show enhanced add customer modal with skeleton loading
  static Future<void> showAddCustomerModal(BuildContext context) async {
    ModalBuilders.showModernBottomSheet(
      context: context,
      page: ModalBuilders.buildModernBottomSheet(
        context: context,
        title: 'Add New Customer',
        subtitle: 'Create a new customer profile',
        child: AddCustomerForm(parentContext: context),
      ),
    );
  }

  /// Show enhanced view customer modal with skeleton loading
  static Future<void> showViewCustomerModal(BuildContext context, Customer customer) async {
    ModalBuilders.showModernBottomSheet(
      context: context,
      page: ModalBuilders.buildModernPageWithActions(
        context: context,
        title: customer.fullName,
        subtitle: 'Customer Details',
        child: CustomerDetailsView(customer: customer),
        actions: [
          IconButton(
            onPressed: () {
              Navigator.of(context).pop();
              showEditCustomerModal(context, customer);
            },
            icon: Icon(Icons.edit_rounded, color: AppThemes.primaryColor),
            tooltip: 'Edit Customer',
          ),
          IconButton(
            onPressed: () => _showCustomerQuickActions(context, customer),
            icon: Icon(Icons.more_horiz_rounded, color: AppThemes.primaryColor),
            tooltip: 'Quick Actions',
          ),
        ],
      ),
    );
  }

  /// Show enhanced view customer by ID modal with skeleton loading - FIXED with BlocProvider
  static Future<void> showViewCustomerByIdModal(BuildContext context, String customerId) async {
    ModalBuilders.showModernBottomSheet(
      context: context,
      page: ModalBuilders.buildModernBottomSheet(
        context: context,
        title: 'Customer Details',
        subtitle: 'Loading customer information...',
        child: BlocProvider<CustomerBloc>(
          create: (context) => di.sl<CustomerBloc>(),
          child: CustomerDetailsByIdView(customerId: customerId),
        ),
      ),
    );
  }

  /// Show enhanced edit customer modal with skeleton loading
  static Future<void> showEditCustomerModal(BuildContext context, Customer customer) async {
    ModalBuilders.showModernBottomSheet(
      context: context,
      page: ModalBuilders.buildModernBottomSheet(
        context: context,
        title: 'Edit Customer',
        subtitle: 'Update ${customer.fullName}\'s information',
        child: EditCustomerForm(customer: customer, parentContext: context),
      ),
    );
  }

  /// Show enhanced edit customer by ID modal with skeleton loading - FIXED with BlocProvider
  static Future<void> showEditCustomerByIdModal(BuildContext context, String customerId) async {
    ModalBuilders.showModernBottomSheet(
      context: context,
      page: ModalBuilders.buildModernBottomSheet(
        context: context,
        title: 'Edit Customer',
        subtitle: 'Loading customer for editing...',
        child: BlocProvider<CustomerBloc>(
          create: (context) => di.sl<CustomerBloc>(),
          child: EditCustomerByIdForm(customerId: customerId),
        ),
      ),
    );
  }

  /// Show enhanced delete customer confirmation modal
  static Future<void> showDeleteCustomerModal(BuildContext context, Customer customer) async {
    ModalBuilders.showModernModal(
      context: context,
      page: ModalBuilders.buildModernPageWithActions(
        context: context,
        title: 'Delete Customer',
        subtitle: 'Are you sure you want to delete ${customer.fullName}?',
        child: _buildDeleteCustomerContent(context, customer),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel', style: TextStyle(color: AppThemes.textSecondaryColor)),
          ),
          ElevatedButton(
            onPressed: () => _deleteCustomer(context, customer),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppThemes.errorColor,
              foregroundColor: Colors.white,
            ),
            child: Text('Delete'),
          ),
        ],
      ),
      modalType: WoltModalType.dialog(),
    );
  }

  /// Show customer quick actions bottom sheet
  static Future<void> _showCustomerQuickActions(BuildContext context, Customer customer) async {
    ModalBuilders.showModernBottomSheet(
      context: context,
      page: ModalBuilders.buildModernBottomSheet(
        context: context,
        title: 'Quick Actions',
        subtitle: 'Actions for ${customer.fullName}',
        child: _buildQuickActionsContent(context, customer),
      ),
    );
  }

  /// Build delete customer content
  static Widget _buildDeleteCustomerContent(BuildContext context, Customer customer) {
    return Padding(
      padding: ModalConstants.defaultPadding,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppThemes.errorColor.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(ModalConstants.defaultRadius),
              border: Border.all(
                color: AppThemes.errorColor.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.warning_rounded,
                  size: 48,
                  color: AppThemes.errorColor,
                ),
                ModalConstants.defaultSpacing,
                Text(
                  'This action cannot be undone',
                  style: ModalConstants.sectionTitleStyle(context, color: AppThemes.errorColor),
                ),
                ModalConstants.defaultSpacing,
                Text(
                  'Deleting ${customer.fullName} will permanently remove all customer data, including debt history and payment records.',
                  style: ModalConstants.subtitleStyle(context),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build quick actions content
  static Widget _buildQuickActionsContent(BuildContext context, Customer customer) {
    return Padding(
      padding: ModalConstants.defaultPadding,
      child: Column(
        children: [
          _buildQuickActionItem(
            context: context,
            icon: Icons.edit_rounded,
            title: 'Edit Customer',
            subtitle: 'Update customer information',
            color: AppThemes.primaryColor,
            onTap: () {
              Navigator.of(context).pop();
              showEditCustomerModal(context, customer);
            },
          ),
          ModalConstants.defaultSpacing,
          _buildQuickActionItem(
            context: context,
            icon: Icons.add_rounded,
            title: 'Add Debt',
            subtitle: 'Create new debt for customer',
            color: AppThemes.successColor,
            onTap: () {
              Navigator.of(context).pop();
              // TODO: Implement debt creation
            },
          ),
          ModalConstants.defaultSpacing,
          _buildQuickActionItem(
            context: context,
            icon: Icons.history_rounded,
            title: 'View History',
            subtitle: 'See customer transaction history',
            color: AppThemes.infoColor,
            onTap: () {
              Navigator.of(context).pop();
              // TODO: Implement history view
            },
          ),
          ModalConstants.defaultSpacing,
          _buildQuickActionItem(
            context: context,
            icon: Icons.delete_rounded,
            title: 'Delete Customer',
            subtitle: 'Permanently remove customer',
            color: AppThemes.errorColor,
            onTap: () {
              Navigator.of(context).pop();
              showDeleteCustomerModal(context, customer);
            },
          ),
        ],
      ),
    );
  }

  /// Build quick action item
  static Widget _buildQuickActionItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(ModalConstants.defaultRadius),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(ModalConstants.defaultRadius),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                ModalConstants.defaultHorizontalSpacing,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: color,
                        ),
                      ),
                      ModalConstants.tinySpacing,
                      Text(
                        subtitle,
                        style: ModalConstants.captionStyle(context),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: color.withValues(alpha: 0.5),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Delete customer implementation with proper error handling
  static void _deleteCustomer(BuildContext context, Customer customer) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(color: AppThemes.primaryColor),
              ModalConstants.defaultSpacing,
              Text('Deleting ${customer.fullName}...'),
            ],
          ),
        ),
      );

      // Simulate API call (replace with actual implementation)
      await Future.delayed(const Duration(seconds: 2));
      
      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }
      
      // Close delete confirmation dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }
      
      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                ModalConstants.defaultHorizontalSpacing,
                Text('${customer.fullName} deleted successfully'),
              ],
            ),
            backgroundColor: AppThemes.successColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
      
      // TODO: Add actual delete customer bloc event
      // context.read<CustomerBloc>().add(DeleteCustomer(customer.customerId));
      
    } catch (e) {
      // Close loading dialog if still open
      if (context.mounted) {
        Navigator.of(context).pop();
      }
      
      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                ModalConstants.defaultHorizontalSpacing,
                Expanded(
                  child: Text('Failed to delete customer: ${e.toString()}'),
                ),
              ],
            ),
            backgroundColor: AppThemes.errorColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }
} 