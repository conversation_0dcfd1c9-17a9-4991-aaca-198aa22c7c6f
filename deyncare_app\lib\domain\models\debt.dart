import 'package:equatable/equatable.dart';
import 'payment.dart';

/// Domain entity for Debt
class Debt extends Equatable {
  final String debtId;
  final String customerId;
  final String customerName;
  final String shopId;
  final double amount;
  final double? interestRate;
  final DateTime dueDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DebtStatus status;
  final String? description;
  final List<Payment> payments;
  final double totalPaid;
  final double remainingAmount;
  final bool isOverdue;
  final int daysPastDue;
  final RiskAssessment riskAssessment;
  final Map<String, dynamic>? metadata;

  const Debt({
    required this.debtId,
    required this.customerId,
    required this.customerName,
    required this.shopId,
    required this.amount,
    this.interestRate,
    required this.dueDate,
    required this.createdAt,
    required this.updatedAt,
    required this.status,
    this.description,
    required this.payments,
    required this.totalPaid,
    required this.remainingAmount,
    required this.isOverdue,
    required this.daysPastDue,
    required this.riskAssessment,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        debtId,
        customerId,
        customerName,
        shopId,
        amount,
        interestRate,
        dueDate,
        createdAt,
        updatedAt,
        status,
        description,
        payments,
        totalPaid,
        remainingAmount,
        isOverdue,
        daysPastDue,
        riskAssessment,
        metadata,
      ];

  /// Calculate payment progress percentage
  double get paymentProgress {
    if (amount <= 0) return 0.0;
    return (totalPaid / amount) * 100;
  }

  /// Check if debt is fully paid
  bool get isFullyPaid => remainingAmount <= 0;

  /// Get days until due date (negative if overdue)
  int get daysUntilDue {
    final now = DateTime.now();
    return dueDate.difference(now).inDays;
  }

  Debt copyWith({
    String? debtId,
    String? customerId,
    String? customerName,
    String? shopId,
    double? amount,
    double? interestRate,
    DateTime? dueDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    DebtStatus? status,
    String? description,
    List<Payment>? payments,
    double? totalPaid,
    double? remainingAmount,
    bool? isOverdue,
    int? daysPastDue,
    RiskAssessment? riskAssessment,
    Map<String, dynamic>? metadata,
  }) {
    return Debt(
      debtId: debtId ?? this.debtId,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      shopId: shopId ?? this.shopId,
      amount: amount ?? this.amount,
      interestRate: interestRate ?? this.interestRate,
      dueDate: dueDate ?? this.dueDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      status: status ?? this.status,
      description: description ?? this.description,
      payments: payments ?? this.payments,
      totalPaid: totalPaid ?? this.totalPaid,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      isOverdue: isOverdue ?? this.isOverdue,
      daysPastDue: daysPastDue ?? this.daysPastDue,
      riskAssessment: riskAssessment ?? this.riskAssessment,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Debt status enumeration
enum DebtStatus {
  active,
  completed,
  overdue,
  defaulted,
  cancelled;

  String get displayName {
    switch (this) {
      case DebtStatus.active:
        return 'Active';
      case DebtStatus.completed:
        return 'Completed';
      case DebtStatus.overdue:
        return 'Overdue';
      case DebtStatus.defaulted:
        return 'Defaulted';
      case DebtStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get value {
    switch (this) {
      case DebtStatus.active:
        return 'active';
      case DebtStatus.completed:
        return 'completed';
      case DebtStatus.overdue:
        return 'overdue';
      case DebtStatus.defaulted:
        return 'defaulted';
      case DebtStatus.cancelled:
        return 'cancelled';
    }
  }

  bool get isActive => this == DebtStatus.active || this == DebtStatus.overdue;
  bool get needsAttention => this == DebtStatus.overdue || this == DebtStatus.defaulted;
}

/// Risk assessment for debt
class RiskAssessment extends Equatable {
  final double riskScore;
  final RiskLevel riskLevel;
  final String source;
  final DateTime assessmentDate;
  final Map<String, dynamic>? factors;

  const RiskAssessment({
    required this.riskScore,
    required this.riskLevel,
    required this.source,
    required this.assessmentDate,
    this.factors,
  });

  @override
  List<Object?> get props => [
        riskScore,
        riskLevel,
        source,
        assessmentDate,
        factors,
      ];
}

/// Risk level enumeration (shared with Customer)
enum RiskLevel {
  low,
  medium,
  high,
  critical;

  String get displayName {
    switch (this) {
      case RiskLevel.low:
        return 'Low Risk';
      case RiskLevel.medium:
        return 'Medium Risk';
      case RiskLevel.high:
        return 'High Risk';
      case RiskLevel.critical:
        return 'Critical Risk';
    }
  }

  String get value {
    switch (this) {
      case RiskLevel.low:
        return 'low';
      case RiskLevel.medium:
        return 'medium';
      case RiskLevel.high:
        return 'high';
      case RiskLevel.critical:
        return 'critical';
    }
  }

  bool get isHighRisk => this == RiskLevel.high || this == RiskLevel.critical;
} 