import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_text_field.dart';
import 'package:deyncare_app/data/services/auth_service.dart';
import 'package:deyncare_app/injection_container.dart' as di;

/// Step 1 of registration: Personal Information
class RegistrationStep1 extends StatefulWidget {
  final TextEditingController fullNameController;
  final TextEditingController emailController;
  final TextEditingController phoneController;
  final TextEditingController passwordController;
  final TextEditingController confirmPasswordController;

  const RegistrationStep1({
    super.key,
    required this.fullNameController,
    required this.emailController,
    required this.phoneController,
    required this.passwordController,
    required this.confirmPasswordController,
  });

  @override
  State<RegistrationStep1> createState() => RegistrationStep1State();
}

class RegistrationStep1State extends State<RegistrationStep1> {
  final _formKey = GlobalKey<FormState>();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  String _phoneNumber = '';
  bool _isCheckingEmail = false;
  String? _emailCheckResult;
  Timer? _emailDebounceTimer;

  // Method to get the complete phone number
  String get completePhoneNumber => _phoneNumber.isNotEmpty ? _phoneNumber : widget.phoneController.text;

  // Public validation method for form validation
  bool validate() {
    return _formKey.currentState?.validate() ?? false;
  }

  // Validation for full name
  String? _validateFullName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Full name is required';
    }
    if (value.length < 3) {
      return 'Full name must be at least 3 characters';
    }
    return null;
  }

  // Check email availability with backend
  Future<void> _checkEmailAvailability(String email) async {
    if (email.isEmpty) return;
    
    setState(() {
      _isCheckingEmail = true;
      _emailCheckResult = null;
    });

    try {
      final authService = di.sl<AuthService>();
      final emailExists = await authService.checkEmailExists(email);
      
      setState(() {
        _emailCheckResult = emailExists ? 'Email already registered' : null;
      });
    } catch (e) {
      setState(() {
        _emailCheckResult = 'Unable to verify email. Please try again.';
      });
    } finally {
      setState(() {
        _isCheckingEmail = false;
      });
    }
  }

  // Validation for email
  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }

    // More robust email validation regex
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(value.trim())) {
      return 'Please enter a valid email address (e.g., <EMAIL>)';
    }

    // Additional validation for common issues
    if (value.trim().endsWith('.c') || value.trim().endsWith('.co')) {
      return 'Please complete the email domain (e.g., .com, .org)';
    }

    // Return backend email check result if available
    if (_emailCheckResult != null) {
      return _emailCheckResult;
    }

    return null;
  }

  // Validation for password
  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }

    if (value.length < 8) {
      return 'Password must be at least 8 characters';
    }

    return null;
  }

  // Validation for confirm password
  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }

    if (value != widget.passwordController.text) {
      return 'Passwords do not match';
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Personal Information',
            style: TextStyle(
              fontSize: 26,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Let\'s start by creating your personal account.',
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 32),
          Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                AuthTextField(
                  label: 'Full Name',
                  hintText: 'Enter your full name',
                  controller: widget.fullNameController,
                  validator: _validateFullName,
                  textInputAction: TextInputAction.next,
                  keyboardType: TextInputType.name,
                ),
                const SizedBox(height: 20),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AuthTextField(
                      label: 'Email',
                      hintText: 'Enter your email address',
                      controller: widget.emailController,
                      validator: _validateEmail,
                      textInputAction: TextInputAction.next,
                      keyboardType: TextInputType.emailAddress,
                      prefixIcon: const Icon(Icons.email_outlined),
                      suffixIcon: _isCheckingEmail 
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : (_emailCheckResult == null 
                            ? const Icon(Icons.check_circle, color: Colors.green)
                            : const Icon(Icons.error, color: Colors.red)),
                      onChanged: (value) {
                        // Remove spaces and convert to lowercase for email
                        final cleanEmail = value.replaceAll(' ', '').toLowerCase().trim();
                        if (cleanEmail != value) {
                          // Update the controller with cleaned email
                          widget.emailController.value = widget.emailController.value.copyWith(
                            text: cleanEmail,
                            selection: TextSelection.collapsed(offset: cleanEmail.length),
                          );
                        }
                        
                        // Debounce email checking
                        if (_emailDebounceTimer?.isActive ?? false) _emailDebounceTimer!.cancel();
                        _emailDebounceTimer = Timer(const Duration(milliseconds: 800), () {
                          if (cleanEmail.isNotEmpty && cleanEmail.contains('@')) {
                            _checkEmailAvailability(cleanEmail);
                          }
                        });
                      },
                    ),
                    if (_isCheckingEmail)
                      const Padding(
                        padding: EdgeInsets.only(top: 4, left: 12),
                        child: Text(
                          'Checking email availability...',
                          style: TextStyle(fontSize: 12, color: Colors.blue),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 20),
                // International Phone Field
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Phone Number',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    IntlPhoneField(
                      controller: widget.phoneController,
                      decoration: InputDecoration(
                        hintText: 'Enter your phone number',
                        hintStyle: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                          fontSize: 16,
                        ),
                        filled: true,
                        fillColor: Theme.of(context).colorScheme.surface,
                        contentPadding: const EdgeInsets.symmetric(
                          vertical: 16.0,
                          horizontal: 12.0,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                          borderSide: BorderSide(color: Theme.of(context).dividerColor),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                          borderSide: BorderSide(color: Theme.of(context).dividerColor),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                          borderSide: BorderSide(color: Theme.of(context).colorScheme.primary, width: 2),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                          borderSide: BorderSide(color: Theme.of(context).colorScheme.error),
                        ),
                        focusedErrorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                          borderSide: BorderSide(color: Theme.of(context).colorScheme.error, width: 2),
                        ),
                      ),
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontSize: 16,
                      ),
                      dropdownTextStyle: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontSize: 14,
                      ),
                      searchText: 'Search country',
                      dropdownIcon: Icon(
                        Icons.arrow_drop_down,
                        color: Theme.of(context).colorScheme.onSurface,
                        size: 18,
                      ),
                      flagsButtonPadding: const EdgeInsets.symmetric(horizontal: 4),
                      flagsButtonMargin: const EdgeInsets.only(right: 4),
                      showCountryFlag: true,
                      showDropdownIcon: true,
                      initialCountryCode: 'SO', // Somalia as default
                      onChanged: (phone) {
                        // Store the complete number for later use
                        _phoneNumber = phone.completeNumber;
                        // Don't manually update the controller - let IntlPhoneField handle it
                      },
                      onSaved: (phone) {
                        // Save the complete number when form is saved
                        if (phone != null) {
                          _phoneNumber = phone.completeNumber;
                          widget.phoneController.text = phone.completeNumber;
                        }
                      },
                      validator: (phone) {
                        if (phone == null || phone.number.isEmpty) {
                          return 'Phone number is required';
                        }
                        
                        // Custom validation for Somalia numbers (should be 9 digits)
                        if (phone.countryCode == 'SO') {
                          if (phone.number.length != 9) {
                            return 'Somalia phone number must be 9 digits (e.g., 686824269)';
                          }
                          // Check if it starts with valid prefixes for Somalia
                          final number = phone.number;
                          if (!number.startsWith('6') && !number.startsWith('9')) {
                            return 'Somalia phone number should start with 6 or 9';
                          }
                        } else {
                          // For other countries, minimum 6 digits
                          if (phone.number.length < 6) {
                            return 'Please enter a valid phone number';
                          }
                        }
                        return null;
                      },
                      textInputAction: TextInputAction.next,
                      keyboardType: TextInputType.phone,
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                      disableLengthCheck: true, // Disable built-in length validation
                      invalidNumberMessage: 'Invalid phone number format',
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                AuthTextField(
                  label: 'Password',
                  hintText: 'Create a secure password',
                  controller: widget.passwordController,
                  validator: _validatePassword,
                  obscureText: _obscurePassword,
                  textInputAction: TextInputAction.next,
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscurePassword
                          ? Icons.visibility_outlined
                          : Icons.visibility_off_outlined,
                      color: AppThemes.textSecondaryColor,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Password must be at least 8 characters',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppThemes.textSecondaryColor,
                    fontStyle: FontStyle.italic,
                  ),
                ),
                const SizedBox(height: 20),
                AuthTextField(
                  label: 'Confirm Password',
                  hintText: 'Confirm your password',
                  controller: widget.confirmPasswordController,
                  validator: _validateConfirmPassword,
                  obscureText: _obscureConfirmPassword,
                  textInputAction: TextInputAction.done,
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscureConfirmPassword
                          ? Icons.visibility_outlined
                          : Icons.visibility_off_outlined,
                      color: AppThemes.textSecondaryColor,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscureConfirmPassword = !_obscureConfirmPassword;
                      });
                    },
                  ),
                  onSubmitted: (_) {},
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
