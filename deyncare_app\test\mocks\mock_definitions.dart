import 'package:mockito/annotations.dart';
import 'package:deyncare_app/domain/repositories/auth_repository.dart';
import 'package:deyncare_app/domain/usecases/auth/check_auth_status_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/login_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/register_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/logout_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/forgot_password_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/verify_email_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/change_password_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/refresh_token_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/reset_password_success_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/process_payment_use_case.dart';

@GenerateMocks([
  AuthRepository,
  CheckAuthStatusUseCase,
  LoginUseCase,
  RegisterUseCase,
  LogoutUseCase,
  ForgotPasswordUseCase,
  VerifyEmailUseCase,
  ChangePasswordUseCase,
  RefreshTokenUseCase,
  ResetPasswordSuccessUseCase,
  ProcessPaymentUseCase,
])
void main() {}
