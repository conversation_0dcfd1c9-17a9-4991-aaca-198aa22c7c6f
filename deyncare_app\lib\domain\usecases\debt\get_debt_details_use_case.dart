import 'package:dartz/dartz.dart';
import 'package:deyncare_app/domain/repositories/debt_repository.dart';
import 'package:deyncare_app/domain/models/debt.dart';
import 'package:deyncare_app/core/errors/failures.dart';

/// Use case for retrieving debt details by ID - BACKEND ALIGNED
/// Matches GET /api/debts/:debtId endpoint
class GetDebtDetailsUseCase {
  final DebtRepository _repository;

  GetDebtDetailsUseCase(this._repository);

  /// Execute the use case to get debt details
  Future<Either<Failure, Debt>> execute(String debtId) async {
    if (debtId.trim().isEmpty) {
      return Left(ValidationFailure(message: 'Debt ID is required'));
    }

    // Validate debt ID format (matches backend validation)
    final debtIdPattern = RegExp(r'^DEBT\d{3}$');
    if (!debtIdPattern.hasMatch(debtId)) {
      return Left(ValidationFailure(message: 'Invalid debt ID format. Expected format: DEBT001'));
    }

    return await _repository.getDebtById(debtId.trim());
  }
} 