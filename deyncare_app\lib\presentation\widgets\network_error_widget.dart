import 'package:flutter/material.dart';
import 'package:deyncare_app/core/managers/connectivity_manager.dart';

/// A reusable widget to display network error states with retry functionality
class NetworkErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback onRetry;
  final IconData icon;
  
  const NetworkErrorWidget({
    super.key,
    this.message = 'Network connection error',
    required this.onRetry,
    this.icon = Icons.signal_wifi_off,
  });
  
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 70,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// A specialized widget for offline state
class OfflineWidget extends StatelessWidget {
  final VoidCallback onRetry;
  final bool autoRetryWhenOnline;
  
  const OfflineWidget({
    super.key,
    required this.onRetry,
    this.autoRetryWhenOnline = true,
  });
  
  @override
  Widget build(BuildContext context) {
    final connectivityManager = ConnectivityManager();
    
    // Initialize if not already initialized
    connectivityManager.initialize();
    
    // If auto-retry is enabled, schedule the retry when back online
    if (autoRetryWhenOnline) {
      connectivityManager.stateStream.listen((state) {
        if (state == ConnectivityState.reconnected) {
          onRetry();
        }
      });
    }
    
    return FutureBuilder<String>(
      future: connectivityManager.getNetworkDetails(),
      builder: (context, snapshot) {
        String networkInfo = snapshot.data ?? 'Checking network...';
        
        // Get the offline duration if available
        final offlineDuration = connectivityManager.getOfflineDuration();
        String timeInfo = '';
        
        if (offlineDuration != null) {
          final minutes = offlineDuration.inMinutes;
          if (minutes > 0) {
            timeInfo = '\nOffline for $minutes minute${minutes == 1 ? '' : 's'}';
          }
        }
        
        return AnimatedSlide(
          offset: Offset.zero, // Always show when in offline widget
          duration: const Duration(milliseconds: 350),
          curve: Curves.easeInOut,
          child: AnimatedOpacity(
            opacity: 1.0,
            duration: const Duration(milliseconds: 350),
            child: NetworkErrorWidget(
              message: 'You appear to be offline.\n$networkInfo$timeInfo',
              onRetry: onRetry,
              icon: Icons.wifi_off,
            ),
          ),
        );
      },
    );
  }
}

/// A specialized widget for timeout errors
class TimeoutErrorWidget extends StatelessWidget {
  final VoidCallback onRetry;
  
  const TimeoutErrorWidget({
    super.key,
    required this.onRetry,
  });
  
  @override
  Widget build(BuildContext context) {
    return NetworkErrorWidget(
      message: 'Connection timed out.\nPlease try again.',
      onRetry: onRetry,
      icon: Icons.timer_off,
    );
  }
}

/// A specialized widget for server errors
class ServerErrorWidget extends StatelessWidget {
  final VoidCallback onRetry;
  
  const ServerErrorWidget({
    super.key,
    required this.onRetry,
  });
  
  @override
  Widget build(BuildContext context) {
    return NetworkErrorWidget(
      message: 'Server error occurred.\nOur team has been notified.',
      onRetry: onRetry,
      icon: Icons.error_outline,
    );
  }
}
