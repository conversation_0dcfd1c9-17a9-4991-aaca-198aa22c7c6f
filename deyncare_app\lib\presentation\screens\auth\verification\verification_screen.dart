import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/routes/app_router.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_state.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_event.dart';
import 'package:deyncare_app/presentation/screens/auth/verification/widgets/email_verification_view.dart';
import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'package:logger/logger.dart';
import 'package:deyncare_app/core/utils/toast_util.dart';

/// Screen for email verification with verification code
class VerificationScreen extends StatefulWidget {
  final User user;
  final DateTime? expiresAt;
  final String? selectedPlanId;

  const VerificationScreen({
    super.key,
    required this.user,
    this.expiresAt,
    this.selectedPlanId,
  });

  @override
  State<VerificationScreen> createState() => _VerificationScreenState();
}

class _VerificationScreenState extends State<VerificationScreen> {
  final Logger _logger = Logger();
  late String _selectedPlanId;
  bool _isVerifying = false; // Track verification in progress

  @override
  void initState() {
    super.initState();
    // Use provided selectedPlanId or set a default
    _selectedPlanId = widget.selectedPlanId ?? '';
    _logger.d('VerificationScreen: Initialized for user: ${widget.user.email}, selectedPlanId: $_selectedPlanId');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Verify Email'),
        backgroundColor: AppThemes.primaryColor,
        foregroundColor: ThemeUtils.getTextColor(context, type: TextColorType.primary),
        centerTitle: true,
      ),
      body: BlocConsumer<AuthBloc, AuthState>(
        listener: (context, state) {
          _logger.d('VerificationScreen: State changed to: ${state.runtimeType}');
          
          // Update loading state based on AuthLoading
          if (state is AuthLoading) {
            setState(() {
              _isVerifying = true;
            });
          } else {
            // Reset loading state when not in AuthLoading
            if (_isVerifying) {
              setState(() {
                _isVerifying = false;
              });
            }
          }
          
          if (state is AuthAuthenticated) {
            _logger.d('VerificationScreen: Navigating to dashboard (AuthAuthenticated)');
            AppRouter.navigateToDashboard(context, replace: true);
          } else if (state is RegistrationComplete) {
            _logger.d('VerificationScreen: Navigating to login (RegistrationComplete)');
            // If registration is complete, navigate to login screen
            ToastUtil.showSuccess('Registration completed successfully! Please login to continue.');
            AppRouter.navigateToLogin(context);
          } else if (state is AuthPaymentRequired) {
            _logger.d('VerificationScreen: Processing AuthPaymentRequired state - selectedPlanId: ${state.selectedPlanId}');
            // Ensure we have all required data before navigating
            if (state.selectedPlanId.isEmpty) {
              _logger.e('VerificationScreen: Missing plan selection data for payment navigation');
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('Error: Missing plan selection data. Please try again.'),
                  backgroundColor: ThemeUtils.getStatusColors(context).error,
                ),
              );
              return;
            }
            
            _logger.d('VerificationScreen: Navigating to payment screen');
            AppRouter.navigateToPayment(
              context,
              user: state.user,
              selectedPlanId: state.selectedPlanId,
              replace: true,
            );
          } else if (state is AuthFailure) {
            _logger.e('VerificationScreen: AuthFailure received: ${state.message}');
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: ThemeUtils.getStatusColors(context).error,
              ),
            );
          }
        },
        builder: (context, state) {
          _logger.d('VerificationScreen: Building UI for state: ${state.runtimeType}');
          
          // For AuthLoading, show the verification view with loading indicator
          // instead of a full-screen loading indicator
          if (state is AuthLoading) {
            _logger.d('VerificationScreen: Handling AuthLoading state');
            
            // If we have previous state data, show the form with loading indicator
            if (state.selectedPlanId != null) {
              // Try to find the last error message if we're coming from an error state
              String? errorMessage;
              if (context.read<AuthBloc>().state is AuthEmailVerificationPending) {
                errorMessage = (context.read<AuthBloc>().state as AuthEmailVerificationPending).errorMessage;
              }
              
              return EmailVerificationView(
                email: widget.user.email,
                userId: widget.user.userId,
                expiresAt: widget.expiresAt ?? DateTime.now().add(const Duration(minutes: 10)),
                isLoading: true,
                errorMessage: errorMessage,
                onVerifyPressed: (_) {
                  // Disabled during loading
                },
                onResendCodeRequested: () {
                  // Disabled during loading
                },
              );
            }
            
            // Fallback to standard loading indicator if no previous state data
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          
          if (state is AuthEmailVerificationPending) {
            _logger.d('VerificationScreen: Showing EmailVerificationView for pending state');
            return EmailVerificationView(
              email: state.user.email,
              userId: state.user.userId,
              expiresAt: state.expiresAt,
              isLoading: _isVerifying,
              errorMessage: state.errorMessage,
              onVerifyPressed: (verificationCode) {
                _logger.d('VerificationScreen: Verify button pressed with code length: ${verificationCode.length}');
                context.read<AuthBloc>().add(
                      VerifyEmailRequested(
                        email: state.user.email,
                        verificationCode: verificationCode,
                        selectedPlanId: _selectedPlanId,
                      ),
                    );
              },
              onResendCodeRequested: () {
                _logger.d('VerificationScreen: Resend code requested');
                context.read<AuthBloc>().add(
                      ResendVerificationCodeRequested(
                        email: state.user.email,
                      ),
                    );
              },
            );
          }
          
          if (state is AuthFailure) {
            _logger.d('VerificationScreen: Showing EmailVerificationView for failure state');
            return EmailVerificationView(
              email: widget.user.email,
              userId: widget.user.userId,
              expiresAt: widget.expiresAt ?? DateTime.now().add(const Duration(minutes: 10)),
              isLoading: false,
              errorMessage: state.message,
              onVerifyPressed: (verificationCode) {
                _logger.d('VerificationScreen: Retry verify button pressed');
                context.read<AuthBloc>().add(
                      VerifyEmailRequested(
                        email: widget.user.email,
                        verificationCode: verificationCode,
                        selectedPlanId: _selectedPlanId,
                      ),
                    );
              },
              onResendCodeRequested: () {
                _logger.d('VerificationScreen: Resend code requested from failure state');
                context.read<AuthBloc>().add(
                      ResendVerificationCodeRequested(
                        email: widget.user.email,
                      ),
                    );
              },
            );
          }
          
          _logger.d('VerificationScreen: Showing default view for state: ${state.runtimeType}');
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Something went wrong'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    context.read<AuthBloc>().add(
                          ResendVerificationCodeRequested(
                            email: widget.user.email,
                          ),
                        );
                  },
                  child: const Text('Try Again'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
