import 'package:deyncare_app/domain/repositories/auth_repository.dart';
import 'package:deyncare_app/domain/usecases/auth/change_password_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/check_auth_status_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/forgot_password_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/get_available_payment_methods_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/login_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/logout_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/process_payment_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/refresh_token_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/register_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/reset_password_success_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/verify_email_use_case.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/presentation/screens/auth/register/register_screen.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:mocktail/mocktail.dart';

// Mocks for dependencies
class MockAuthRepository extends Mock implements AuthRepository {}

class MockCheckAuthStatusUseCase extends Mock implements CheckAuthStatusUseCase {}

class MockLoginUseCase extends Mock implements LoginUseCase {}

class MockRegisterUseCase extends Mock implements RegisterUseCase {}

class MockLogoutUseCase extends Mock implements LogoutUseCase {}

class MockForgotPasswordUseCase extends Mock implements ForgotPasswordUseCase {}

class MockVerifyEmailUseCase extends Mock implements VerifyEmailUseCase {}

class MockChangePasswordUseCase extends Mock implements ChangePasswordUseCase {}

class MockRefreshTokenUseCase extends Mock implements RefreshTokenUseCase {}

class MockResetPasswordSuccessUseCase extends Mock implements ResetPasswordSuccessUseCase {}

class MockProcessPaymentUseCase extends Mock implements ProcessPaymentUseCase {}

class MockGetAvailablePaymentMethodsUseCase extends Mock
    implements GetAvailablePaymentMethodsUseCase {}

void main() {
  late AuthBloc authBloc;
  late MockAuthRepository mockAuthRepository;
  late MockCheckAuthStatusUseCase mockCheckAuthStatusUseCase;
  late MockLoginUseCase mockLoginUseCase;
  late MockRegisterUseCase mockRegisterUseCase;
  late MockLogoutUseCase mockLogoutUseCase;
  late MockForgotPasswordUseCase mockForgotPasswordUseCase;
  late MockVerifyEmailUseCase mockVerifyEmailUseCase;
  late MockChangePasswordUseCase mockChangePasswordUseCase;
  late MockRefreshTokenUseCase mockRefreshTokenUseCase;
  late MockResetPasswordSuccessUseCase mockResetPasswordSuccessUseCase;
  late MockProcessPaymentUseCase mockProcessPaymentUseCase;
  late MockGetAvailablePaymentMethodsUseCase mockGetAvailablePaymentMethodsUseCase;

  setUp(() {
    mockAuthRepository = MockAuthRepository();
    mockCheckAuthStatusUseCase = MockCheckAuthStatusUseCase();
    mockLoginUseCase = MockLoginUseCase();
    mockRegisterUseCase = MockRegisterUseCase();
    mockLogoutUseCase = MockLogoutUseCase();
    mockForgotPasswordUseCase = MockForgotPasswordUseCase();
    mockVerifyEmailUseCase = MockVerifyEmailUseCase();
    mockChangePasswordUseCase = MockChangePasswordUseCase();
    mockRefreshTokenUseCase = MockRefreshTokenUseCase();
    mockResetPasswordSuccessUseCase = MockResetPasswordSuccessUseCase();
    mockProcessPaymentUseCase = MockProcessPaymentUseCase();
    mockGetAvailablePaymentMethodsUseCase = MockGetAvailablePaymentMethodsUseCase();

    authBloc = AuthBloc(
      authRepository: mockAuthRepository,
      checkAuthStatus: mockCheckAuthStatusUseCase,
      login: mockLoginUseCase,
      register: mockRegisterUseCase,
      logout: mockLogoutUseCase,
      forgotPassword: mockForgotPasswordUseCase,
      verifyEmail: mockVerifyEmailUseCase,
      changePassword: mockChangePasswordUseCase,
      refreshToken: mockRefreshTokenUseCase,
      resetPasswordSuccess: mockResetPasswordSuccessUseCase,
      processPayment: mockProcessPaymentUseCase,
      getAvailablePaymentMethods: mockGetAvailablePaymentMethodsUseCase,
    );
  });

  tearDown(() {
    authBloc.close();
  });

  Widget createTestWidget(Widget child) {
    return MaterialApp(
      home: BlocProvider<AuthBloc>(
        create: (context) => authBloc,
        child: child,
      ),
    );
  }

  group('RegisterScreen', () {
    testWidgets('renders correctly with initial state', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const RegisterScreen()));

      expect(find.byType(RegisterScreen), findsOneWidget);
      expect(find.text('Create Account'), findsOneWidget);
      expect(find.byType(TextFormField), findsWidgets);
    });

    testWidgets('shows validation errors for empty fields in step 1', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const RegisterScreen()));

      final nextButton = find.widgetWithText(ElevatedButton, 'Next');
      await tester.tap(nextButton);
      await tester.pump();

      // These validation messages are handled by the form, not the BLoC state
      // so we can test them without mocking BLoC interactions.
      expect(find.text('Full Name is required'), findsOneWidget);
      expect(find.text('Email is required'), findsOneWidget);
      expect(find.text('Phone number is required'), findsOneWidget);
    });

    testWidgets('can enter personal information in step 1', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const RegisterScreen()));

      final nameField = find.byKey(const Key('register_name_field'));
      final emailField = find.byKey(const Key('register_email_field'));
      final phoneField = find.byKey(const Key('register_phone_field'));

      await tester.enterText(nameField, 'John Doe');
      await tester.enterText(emailField, '<EMAIL>');
      await tester.enterText(phoneField, '+**********');
      await tester.pump();

      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.text('+**********'), findsOneWidget);
    });

    testWidgets('shows login link option', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const RegisterScreen()));

      expect(find.textContaining('Already have an account?'), findsOneWidget);
      expect(find.widgetWithText(TextButton, 'Login'), findsOneWidget);
    });

    testWidgets('tapping login link does not throw errors', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const RegisterScreen()));

      final loginLink = find.widgetWithText(TextButton, 'Login');
      expect(loginLink, findsOneWidget);

      await tester.tap(loginLink);
      await tester.pump();
      // We are not testing navigation, just that the tap is handled.
    });
  });
}
