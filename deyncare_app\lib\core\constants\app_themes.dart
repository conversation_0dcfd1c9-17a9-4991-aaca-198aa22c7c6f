import 'package:flutter/material.dart';

/// Enhanced App theme configuration with modern design patterns
class AppThemes {
  // Primary brand colors (keeping existing)
  static const Color primaryColor = Color(0xFF2E5BFF);
  static const Color secondaryColor = Color(0xFFFF6B2E);
  static const Color accentColor = Color(0xFF2ECC71);
  
  // Enhanced primary color variations
  static const Color primaryLight = Color(0xFF5A7AFF);
  static const Color primaryDark = Color(0xFF1E3FCC);
  static const Color primarySurface = Color(0xFFF0F3FF);
  
  // Enhanced secondary color variations
  static const Color secondaryLight = Color(0xFFFF8A5A);
  static const Color secondaryDark = Color(0xFFE55A1E);
  static const Color secondarySurface = Color(0xFFFFF3F0);
  
  // Enhanced accent color variations
  static const Color accentLight = Color(0xFF5AD685);
  static const Color accentDark = Color(0xFF25B85C);
  static const Color accentSurface = Color(0xFFF0FFF4);
  
  // Enhanced neutral colors with better contrast
  static const Color backgroundColor = Color(0xFFF8F9FA);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color cardColor = Color(0xFFFFFFFF);
  static const Color dividerColor = Color(0xFFE9ECEF);
  static const Color borderColor = Color(0xFFDEE2E6);
  static const Color textFieldBackgroundColor = Color(0xFFFFFFFF);
  
  // Enhanced text colors with better hierarchy
  static const Color textPrimaryColor = Color(0xFF212529);
  static const Color textSecondaryColor = Color(0xFF6C757D);
  static const Color textTertiaryColor = Color(0xFF9CA3AF);
  static const Color textLightColor = Color(0xFFADB5BD);
  static const Color textHintColor = Color(0xFFCED4DA);
  static const Color textDisabledColor = Color(0xFFE9ECEF);
  
  // Enhanced status colors
  static const Color successColor = Color(0xFF28A745);
  static const Color successLight = Color(0xFF4CBB69);
  static const Color successDark = Color(0xFF1E7E34);
  static const Color successSurface = Color(0xFFF0FFF4);
  
  static const Color warningColor = Color(0xFFFFC107);
  static const Color warningLight = Color(0xFFFFCD39);
  static const Color warningDark = Color(0xFFE0A800);
  static const Color warningSurface = Color(0xFFFFFBF0);
  
  static const Color errorColor = Color(0xFFDC3545);
  static const Color errorLight = Color(0xFFE15969);
  static const Color errorDark = Color(0xFFC82333);
  static const Color errorSurface = Color(0xFFFFF5F5);
  
  static const Color infoColor = Color(0xFF17A2B8);
  static const Color infoLight = Color(0xFF45B5C6);
  static const Color infoDark = Color(0xFF138496);
  static const Color infoSurface = Color(0xFFF0FDFF);
  
  // Modern shadow colors
  static const Color shadowLight = Color(0x08000000);
  static const Color shadowMedium = Color(0x12000000);
  static const Color shadowDark = Color(0x20000000);
  
  // Enhanced overlay colors
  static const Color overlayLight = Color(0x40000000);
  static const Color overlayMedium = Color(0x60000000);
  static const Color overlayDark = Color(0x80000000);

  // Enhanced light theme
  static final ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    primaryColor: primaryColor,
    scaffoldBackgroundColor: backgroundColor,
    colorScheme: ColorScheme.light(
      primary: primaryColor,
      primaryContainer: primarySurface,
      secondary: secondaryColor,
      secondaryContainer: secondarySurface,
      tertiary: accentColor,
      tertiaryContainer: accentSurface,
      surface: surfaceColor,
      surfaceVariant: backgroundColor,
      background: backgroundColor,
      error: errorColor,
      errorContainer: errorSurface,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onTertiary: Colors.white,
      onSurface: textPrimaryColor,
      onBackground: textPrimaryColor,
      onError: Colors.white,
      outline: borderColor,
      outlineVariant: dividerColor,
      shadow: shadowMedium,
      surfaceTint: primaryColor,
    ),
    
    // Enhanced AppBar theme
    appBarTheme: AppBarTheme(
      backgroundColor: surfaceColor,
      foregroundColor: textPrimaryColor,
      elevation: 0,
      scrolledUnderElevation: 1,
      shadowColor: shadowLight,
      surfaceTintColor: Colors.transparent,
      titleTextStyle: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.3,
        color: textPrimaryColor,
      ),
    ),
    
    // Enhanced Card theme
    cardTheme: CardTheme(
      color: cardColor,
      elevation: 2,
      shadowColor: shadowLight,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      margin: const EdgeInsets.all(8),
    ),
    
    // Enhanced Button themes
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        shadowColor: Colors.transparent,
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 24.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          letterSpacing: -0.2,
        ),
      ),
    ),
    
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: primaryColor,
        side: BorderSide(color: primaryColor, width: 1.5),
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 24.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          letterSpacing: -0.2,
        ),
      ),
    ),
    
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryColor,
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 24.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          letterSpacing: -0.2,
        ),
      ),
    ),
    
    // Enhanced Input decoration theme
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: textFieldBackgroundColor,
      contentPadding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
      
      // Enhanced borders
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.0),
        borderSide: BorderSide(color: borderColor, width: 1.5),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.0),
        borderSide: BorderSide(color: borderColor, width: 1.5),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.0),
        borderSide: BorderSide(color: primaryColor, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.0),
        borderSide: BorderSide(color: errorColor, width: 1.5),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.0),
        borderSide: BorderSide(color: errorColor, width: 2),
      ),
      
      // Enhanced text styles
      labelStyle: TextStyle(
        color: textSecondaryColor,
        fontSize: 16,
        fontWeight: FontWeight.w500,
      ),
      hintStyle: TextStyle(
        color: textHintColor,
        fontSize: 16,
        fontWeight: FontWeight.w400,
      ),
      helperStyle: TextStyle(
        color: textTertiaryColor,
        fontSize: 14,
        fontWeight: FontWeight.w400,
      ),
      errorStyle: TextStyle(
        color: errorColor,
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),
    ),
    
    // Enhanced Text theme
    textTheme: TextTheme(
      displayLarge: TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.w700,
        letterSpacing: -0.8,
        color: textPrimaryColor,
        height: 1.2,
      ),
      displayMedium: TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.w700,
        letterSpacing: -0.6,
        color: textPrimaryColor,
        height: 1.2,
      ),
      displaySmall: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.w700,
        letterSpacing: -0.5,
        color: textPrimaryColor,
        height: 1.3,
      ),
      headlineLarge: TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.4,
        color: textPrimaryColor,
        height: 1.3,
      ),
      headlineMedium: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.3,
        color: textPrimaryColor,
        height: 1.3,
      ),
      headlineSmall: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.2,
        color: textPrimaryColor,
        height: 1.4,
      ),
      titleLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.2,
        color: textPrimaryColor,
        height: 1.4,
      ),
      titleMedium: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.1,
        color: textPrimaryColor,
        height: 1.4,
      ),
      titleSmall: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: textPrimaryColor,
        height: 1.4,
      ),
      bodyLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: textPrimaryColor,
        height: 1.5,
      ),
      bodyMedium: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: textPrimaryColor,
        height: 1.5,
      ),
      bodySmall: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: textSecondaryColor,
        height: 1.4,
      ),
      labelLarge: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: 0,
        color: textPrimaryColor,
        height: 1.4,
      ),
      labelMedium: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        letterSpacing: 0,
        color: textPrimaryColor,
        height: 1.4,
      ),
      labelSmall: TextStyle(
        fontSize: 10,
        fontWeight: FontWeight.w500,
        letterSpacing: 0,
        color: textSecondaryColor,
        height: 1.4,
      ),
    ),
    
    // Enhanced Divider theme
    dividerTheme: DividerThemeData(
      color: dividerColor,
      thickness: 1,
      space: 1,
    ),
    
    // Enhanced Chip theme
    chipTheme: ChipThemeData(
      backgroundColor: primarySurface,
      selectedColor: primaryColor,
      disabledColor: textDisabledColor,
      labelStyle: TextStyle(
        color: primaryColor,
        fontSize: 12,
        fontWeight: FontWeight.w500,
      ),
      secondaryLabelStyle: TextStyle(
        color: Colors.white,
        fontSize: 12,
        fontWeight: FontWeight.w500,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
    ),
  );

  // Enhanced dark theme with better contrast and readability
  static final ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    primaryColor: primaryColor,
    scaffoldBackgroundColor: const Color(0xFF121212), // Material 3 dark background
    colorScheme: ColorScheme.dark(
      primary: primaryColor,
      primaryContainer: const Color(0xFF1E3A7A), // Better contrast for primary container
      secondary: secondaryColor,
      secondaryContainer: const Color(0xFF5A2F1A), // Better contrast for secondary container
      tertiary: accentColor,
      tertiaryContainer: const Color(0xFF1E4A2A), // Better contrast for tertiary container
      surface: const Color(0xFF1E1E1E), // Slightly lighter surface for better layering
      surfaceVariant: const Color(0xFF181818), // Distinct surface variant
      background: const Color(0xFF121212), // Material 3 dark background
      error: errorColor,
      errorContainer: const Color(0xFF5A1A1A), // Better contrast for error container
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onTertiary: Colors.white,
      onSurface: const Color(0xFFE8E8E8), // Better text contrast
      onBackground: const Color(0xFFE8E8E8), // Better text contrast
      onError: Colors.white,
      outline: const Color(0xFF404040), // Better outline visibility
      outlineVariant: const Color(0xFF303030), // Subtle outline variant
      shadow: const Color(0x50000000), // Slightly more visible shadows
      surfaceTint: primaryColor,
    ),
    
    // Dark AppBar theme with better contrast
    appBarTheme: AppBarTheme(
      backgroundColor: const Color(0xFF1E1E1E), // Match improved surface color
      foregroundColor: const Color(0xFFE8E8E8), // Better text contrast
      elevation: 0,
      scrolledUnderElevation: 1,
      shadowColor: const Color(0x50000000), // More visible shadow
      surfaceTintColor: Colors.transparent,
      titleTextStyle: const TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.3,
        color: Color(0xFFE8E8E8), // Better text contrast
      ),
    ),
    
    // Dark Card theme with better visibility
    cardTheme: CardTheme(
      color: const Color(0xFF1E1E1E), // Match improved surface color
      elevation: 2,
      shadowColor: const Color(0x50000000), // More visible shadow
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      margin: const EdgeInsets.all(8),
    ),
    
    // Dark input decoration theme with better contrast
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: const Color(0xFF2A2A2A), // Keep existing fill color
      contentPadding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
      
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.0),
        borderSide: const BorderSide(color: Color(0xFF404040), width: 1.5), // Better border visibility
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.0),
        borderSide: const BorderSide(color: Color(0xFF404040), width: 1.5), // Better border visibility
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.0),
        borderSide: BorderSide(color: primaryColor, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.0),
        borderSide: BorderSide(color: errorColor, width: 1.5),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.0),
        borderSide: BorderSide(color: errorColor, width: 2),
      ),
      
      labelStyle: const TextStyle(
        color: Color(0xFFA0A0A0), // Better label visibility
        fontSize: 16,
        fontWeight: FontWeight.w500,
      ),
      hintStyle: const TextStyle(
        color: Color(0xFF707070), // Better hint visibility
        fontSize: 16,
        fontWeight: FontWeight.w400,
      ),
      helperStyle: const TextStyle(
        color: Color(0xFF808080), // Better helper text visibility
        fontSize: 14,
        fontWeight: FontWeight.w400,
      ),
      errorStyle: TextStyle(
        color: errorColor,
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),
    ),
    
    // Dark text theme with better contrast
    textTheme: const TextTheme(
      displayLarge: TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.w700,
        letterSpacing: -0.8,
        color: Color(0xFFE8E8E8), // Better contrast
        height: 1.2,
      ),
      displayMedium: TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.w700,
        letterSpacing: -0.6,
        color: Color(0xFFE8E8E8), // Better contrast
        height: 1.2,
      ),
      displaySmall: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.w700,
        letterSpacing: -0.5,
        color: Color(0xFFE8E8E8), // Better contrast
        height: 1.3,
      ),
      headlineLarge: TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.4,
        color: Color(0xFFE8E8E8), // Better contrast
        height: 1.3,
      ),
      headlineMedium: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.3,
        color: Color(0xFFE8E8E8), // Better contrast
        height: 1.3,
      ),
      headlineSmall: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.2,
        color: Color(0xFFE8E8E8), // Better contrast
        height: 1.4,
      ),
      titleLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.2,
        color: Color(0xFFE8E8E8), // Better contrast
        height: 1.4,
      ),
      titleMedium: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.1,
        color: Color(0xFFE8E8E8), // Better contrast
        height: 1.4,
      ),
      titleSmall: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: Color(0xFFE8E8E8), // Better contrast
        height: 1.4,
      ),
      bodyLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: Color(0xFFE8E8E8), // Better contrast
        height: 1.5,
      ),
      bodyMedium: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: Color(0xFFE8E8E8), // Better contrast
        height: 1.5,
      ),
      bodySmall: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: Color(0xFFA0A0A0), // Better secondary text contrast
        height: 1.4,
      ),
      labelLarge: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: 0,
        color: Color(0xFFE8E8E8), // Better contrast
        height: 1.4,
      ),
      labelMedium: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        letterSpacing: 0,
        color: Color(0xFFE8E8E8), // Better contrast
        height: 1.4,
      ),
      labelSmall: TextStyle(
        fontSize: 10,
        fontWeight: FontWeight.w500,
        letterSpacing: 0,
        color: Color(0xFFA0A0A0), // Better secondary text contrast
        height: 1.4,
      ),
    ),
    
    // Dark divider theme with better visibility
    dividerTheme: const DividerThemeData(
      color: Color(0xFF303030), // Better divider visibility
      thickness: 1,
      space: 1,
    ),
  );

  /// Utility methods for color variations
  static Color lighten(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  static Color darken(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness - amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }
}
