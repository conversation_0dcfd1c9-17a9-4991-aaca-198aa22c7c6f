import 'package:deyncare_app/domain/repositories/payment_repository.dart';
import 'package:deyncare_app/domain/models/payment.dart';
import 'package:deyncare_app/data/network/payment/payment_remote_data_source.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:deyncare_app/data/models/payment_model.dart';

/// Implementation of payment repository
class PaymentRepositoryImpl implements PaymentRepository {
  final PaymentRemoteDataSource _remoteDataSource;

  PaymentRepositoryImpl(this._remoteDataSource);

  @override
  Future<Payment> createPayment({
    required double amount,
    required PaymentMethod paymentMethod,
    required String debtId,
    String? description,
  }) async {
    try {
      final data = <String, dynamic>{
        'amount': amount,
        'paymentMethod': paymentMethod.value,
        'debtId': debtId,
        'context': 'debt',
        if (description != null) 'description': description,
      };

      final paymentModel = await _remoteDataSource.createPayment(data);
      return paymentModel.toDomain();
    } on ApiException {
      rethrow;
    } catch (e) {
      throw ApiException(
        message: 'Unexpected error occurred while creating payment',
        statusCode: null,
      );
    }
  }

  @override
  Future<List<Payment>> getDebtPayments(String debtId) async {
    try {
      // Use getPayments with debtId filter instead of specific getDebtPayments method
      final queryParams = {'debtId': debtId, 'context': 'debt'};
      final response = await _remoteDataSource.getPayments(queryParams);
      final List<dynamic> paymentsJson = response['payments'] ?? [];
      return paymentsJson.map((json) => PaymentModel.fromJson(json).toDomain()).toList();
    } on ApiException {
      rethrow;
    } catch (e) {
      throw ApiException(
        message: 'Unexpected error occurred while fetching debt payments',
        statusCode: null,
      );
    }
  }

  @override
  Future<Payment> getPaymentById(String paymentId) async {
    try {
      final paymentModel = await _remoteDataSource.getPaymentById(paymentId);
      return paymentModel.toDomain();
    } on ApiException {
      rethrow;
    } catch (e) {
      throw ApiException(
        message: 'Unexpected error occurred while fetching payment',
        statusCode: null,
      );
    }
  }

  @override
  Future<List<Payment>> getPaymentHistory(String debtId) async {
    try {
      // Use the same approach as getDebtPayments since they might be the same data
      final queryParams = {'debtId': debtId, 'context': 'debt'};
      final response = await _remoteDataSource.getPayments(queryParams);
      final List<dynamic> paymentsJson = response['payments'] ?? [];
      return paymentsJson.map((json) => PaymentModel.fromJson(json).toDomain()).toList();
    } on ApiException {
      rethrow;
    } catch (e) {
      throw ApiException(
        message: 'Unexpected error occurred while fetching payment history',
        statusCode: null,
      );
    }
  }
}
