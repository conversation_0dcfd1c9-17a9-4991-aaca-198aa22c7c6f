import 'package:dartz/dartz.dart';
import 'package:deyncare_app/core/errors/failures.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:deyncare_app/domain/repositories/subscription_repository.dart';
import 'package:deyncare_app/data/models/subscription_model.dart';
import 'package:deyncare_app/data/models/plan_model.dart';
import 'package:deyncare_app/data/services/subscription_service.dart';
import 'package:deyncare_app/domain/models/subscription.dart';
import 'package:deyncare_app/domain/models/plan.dart';

class SubscriptionRepositoryImpl implements SubscriptionRepository {
  final SubscriptionService _subscriptionService;

  SubscriptionRepositoryImpl(this._subscriptionService);

  @override
  Future<Either<Failure, Subscription>> getCurrentSubscription() async {
    try {
      final subscriptionModel = await _subscriptionService.getCurrentSubscription();
      final subscription = _mapSubscriptionModelToDomain(subscriptionModel);
      return Right(subscription);
    } on ApiException catch (e) {
      return Left(_mapApiExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Plan>>> getAvailablePlans() async {
    try {
      final planModels = await _subscriptionService.getAvailablePlans();
      final plans = planModels.map(_mapPlanModelToDomain).toList();
      return Right(plans);
    } on ApiException catch (e) {
      return Left(_mapApiExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> requestUpgrade({
    required String planType,
    String? message,
  }) async {
    try {
      final result = await _subscriptionService.requestUpgrade(
        planType: planType,
        message: message,
      );
      return Right(result);
    } on ApiException catch (e) {
      return Left(_mapApiExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Subscription>> changePlan({
    required String planId,
    String? planType,
    bool prorated = true,
    String? paymentMethod,
    Map<String, dynamic>? paymentDetails,
  }) async {
    try {
      final subscriptionModel = await _subscriptionService.changePlan(
        planId: planId,
        planType: planType,
        prorated: prorated,
        paymentMethod: paymentMethod,
        paymentDetails: paymentDetails,
      );
      final subscription = _mapSubscriptionModelToDomain(subscriptionModel);
      return Right(subscription);
    } on ApiException catch (e) {
      return Left(_mapApiExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Subscription>> cancelSubscription({
    String? reason,
    String? feedback,
    bool immediateEffect = false,
  }) async {
    try {
      final subscriptionModel = await _subscriptionService.cancelSubscription(
        reason: reason,
        feedback: feedback,
        immediateEffect: immediateEffect,
      );
      final subscription = _mapSubscriptionModelToDomain(subscriptionModel);
      return Right(subscription);
    } on ApiException catch (e) {
      return Left(_mapApiExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Subscription>> updateAutoRenewal({
    required bool autoRenew,
  }) async {
    try {
      final subscriptionModel = await _subscriptionService.updateAutoRenewal(
        autoRenew: autoRenew,
      );
      final subscription = _mapSubscriptionModelToDomain(subscriptionModel);
      return Right(subscription);
    } on ApiException catch (e) {
      return Left(_mapApiExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Subscription>> renewSubscription({
    required String paymentMethod,
    required String transactionId,
    double? amount,
    String currency = 'USD',
    String? notes,
  }) async {
    try {
      final subscriptionModel = await _subscriptionService.renewSubscription(
        paymentMethod: paymentMethod,
        transactionId: transactionId,
        amount: amount,
        currency: currency,
        notes: notes,
      );
      final subscription = _mapSubscriptionModelToDomain(subscriptionModel);
      return Right(subscription);
    } on ApiException catch (e) {
      return Left(_mapApiExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> payWithEvc({
    required String subscriptionId,
    required String phone,
    required double amount,
    String? planType,
  }) async {
    try {
      final result = await _subscriptionService.payWithEvc(
        subscriptionId: subscriptionId,
        phone: phone,
        amount: amount,
        planType: planType,
      );
      return Right(result);
    } on ApiException catch (e) {
      return Left(_mapApiExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> recordOfflinePayment({
    required String subscriptionId,
    required double amount,
    required String method,
    String? payerName,
    String? payerPhone,
    String? notes,
    String? planType,
    String? receiptPath,
  }) async {
    try {
      final result = await _subscriptionService.recordOfflinePayment(
        subscriptionId: subscriptionId,
        amount: amount,
        method: method,
        payerName: payerName,
        payerPhone: payerPhone,
        notes: notes,
        planType: planType,
        receiptPath: receiptPath,
      );
      return Right(result);
    } on ApiException catch (e) {
      return Left(_mapApiExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getAvailablePaymentMethods() async {
    try {
      final methods = await _subscriptionService.getAvailablePaymentMethods();
      return Right(methods);
    } on ApiException catch (e) {
      return Left(_mapApiExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  // Private mapping methods
  Subscription _mapSubscriptionModelToDomain(SubscriptionModel model) {
    return Subscription(
      subscriptionId: model.subscriptionId,
      shopId: model.shopId,
      planId: model.planId,
      planName: model.plan.name,
      planType: model.plan.type,
      status: model.status,
      basePrice: model.pricing.basePrice,
      totalPrice: model.totalPrice,
      currency: model.pricing.currency,
      billingCycle: model.pricing.billingCycle,
      paymentMethod: model.payment.method,
      paymentVerified: model.payment.verified,
      startDate: model.dates.startDate,
      endDate: model.dates.endDate,
      trialEndsAt: model.dates.trialEndsAt,
      daysRemaining: model.daysRemaining,
      percentageUsed: model.percentageUsed,
      autoRenew: model.renewalSettings.autoRenew,
      isActive: model.isActive,
      isInTrial: model.isInTrial,
      isExpired: model.isExpired,
      isCanceled: model.isCanceled,
      isPastDue: model.isPastDue,
      displayStatus: model.displayStatus,
      isTrialEndingSoon: model.isTrialEndingSoon,
      isExpiringSoon: model.isExpiringSoon,
      hasDiscount: model.pricing.discount?.active ?? false,
      discountAmount: model.pricing.discount?.amount ?? 0,
      discountType: model.pricing.discount?.type,
      discountCode: model.pricing.discount?.code,
      lastPaymentDate: model.payment.lastPaymentDate,
      nextPaymentDate: model.payment.nextPaymentDate,
      failedPayments: model.payment.failedPayments,
      cancellationReason: model.cancellation?.reason,
      cancellationFeedback: model.cancellation?.feedback,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  Plan _mapPlanModelToDomain(PlanModel model) {
    return Plan(
      id: model.id,
      name: model.name,
      type: model.type,
      description: model.description ?? '',
      price: model.price,
      currency: model.currency,
      billingCycle: model.billingCycle,
      trialDays: model.trialDays,
      features: model.features,
      limits: model.limits,
      isActive: model.isActive,
      isPopular: model.isPopular,
      displayOrder: model.displayOrder,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  Failure _mapApiExceptionToFailure(ApiException e) {
    switch (e.statusCode) {
      case 400:
        return ValidationFailure(
          message: e.message,
          code: e.code,
          data: e.data,
        );
      case 401:
        return AuthFailure(
          message: e.message,
          code: e.code,
          data: e.data,
        );
      case 403:
        return PermissionFailure(
          message: e.message,
          code: e.code,
          data: e.data,
        );
      case 404:
        return NotFoundFailure(
          message: e.message,
          code: e.code,
          data: e.data,
        );
      case 409:
        return ConflictFailure(
          message: e.message,
          code: e.code,
          data: e.data,
        );
      case 500:
      case 502:
      case 503:
      case 504:
        return ServerFailure(
          message: e.message,
          code: e.code,
          data: e.data,
        );
      default:
        if (e.statusCode == null) {
          return NetworkFailure(
            message: e.message,
            code: e.code,
            data: e.data,
          );
        }
        return UnknownFailure(
          message: e.message,
          code: e.code,
          data: e.data,
        );
    }
  }
} 