import 'package:deyncare_app/data/network/clients/dio_client.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:logger/logger.dart';

/// Remote data source for customer operations
/// 
/// Handles all API interactions with the backend customer endpoints
class CustomerRemoteSource {
  final Logger _logger = Logger();
  final DioClient _dioClient;

  /// Creates a new instance with the required client
  CustomerRemoteSource({
    required DioClient dioClient,
  }) : _dioClient = dioClient;

  /// Get customers with pagination and filters
  Future<Map<String, dynamic>> getCustomers({
    int page = 1,
    int limit = 20,
    String? search,
    String? status,
    String? riskLevel,
    String? sortBy,
    bool ascending = true,
  }) async {
    _logger.d('CustomerRemoteSource: Getting customers - page: $page, limit: $limit');
    
    final queryParameters = <String, dynamic>{
      'page': page,
      'limit': limit,
      'sortBy': sortBy ?? 'createdAt',
      'sortOrder': ascending ? 'asc' : 'desc',
    };

    if (search != null && search.isNotEmpty) {
      queryParameters['search'] = search;
    }
    if (status != null && status.isNotEmpty) {
      queryParameters['status'] = status;
    }
    if (riskLevel != null && riskLevel.isNotEmpty) {
      queryParameters['riskLevel'] = riskLevel;
    }

    final response = await _dioClient.get(
      '/customers',
      queryParameters: queryParameters,
      showSuccessToast: false,
    );

    _validateResponse(response);
    return response['data'];
  }

  /// Get customer by ID
  Future<Map<String, dynamic>> getCustomerById(String customerId) async {
    _logger.d('CustomerRemoteSource: Getting customer by ID: $customerId');
    
    final response = await _dioClient.get(
      '/customers/$customerId',
      showSuccessToast: false,
    );

    _validateResponse(response);
    return response['data'];
  }

  /// Create new customer - BACKEND ALIGNED 100%
  Future<Map<String, dynamic>> createCustomer({
    required String customerName,
    required String customerType, // 'new' or 'returning'
    required String phone,
    String? email,
    String? address,
    double? creditLimit,
    String? category, // 'regular', 'vip', 'premium'
    String? notes,
  }) async {
    _logger.d('CustomerRemoteSource: Creating customer: $customerName');
    
    final data = <String, dynamic>{
      'CustomerName': customerName, // Backend expects 'CustomerName'
      'CustomerType': customerType, // Backend validation expects 'new' or 'returning'
      'phone': phone,
    };

    // Optional fields - only add if provided
    if (email != null && email.isNotEmpty) data['email'] = email;
    if (address != null && address.isNotEmpty) data['address'] = address;
    if (creditLimit != null) data['creditLimit'] = creditLimit;
    if (category != null) data['category'] = category;
    if (notes != null && notes.isNotEmpty) data['notes'] = notes;

    final response = await _dioClient.post(
      '/customers',
      data: data,
      showSuccessToast: true,
    );

    _validateResponse(response);
    return response['data'];
  }

  /// Update customer
  Future<Map<String, dynamic>> updateCustomer({
    required String customerId,
    String? customerName,  // Changed from fullName to customerName
    String? email,
    String? phone,
    String? address,
    String? notes,
  }) async {
    _logger.d('CustomerRemoteSource: Updating customer: $customerId');
    
    final data = <String, dynamic>{};

    if (customerName != null) data['CustomerName'] = customerName;  // Use backend field name
    if (email != null) data['email'] = email;
    if (phone != null) data['phone'] = phone;
    if (address != null) data['address'] = address;
    if (notes != null) data['notes'] = notes;

    final response = await _dioClient.put(
      '/customers/$customerId',
      data: data,
      showSuccessToast: true,
    );

    _validateResponse(response);
    return response as Map<String, dynamic>;
  }

  /// Delete customer
  Future<void> deleteCustomer(String customerId) async {
    _logger.d('CustomerRemoteSource: Deleting customer: $customerId');
    
    final response = await _dioClient.delete(
      '/customers/$customerId',
      showSuccessToast: true,
    );

    _validateResponse(response);
  }

  /// Get customer debts
  Future<List<dynamic>> getCustomerDebts({
    required String customerId,
    String? status,
    bool includeCompleted = false,
  }) async {
    _logger.d('CustomerRemoteSource: Getting debts for customer: $customerId');
    
    final queryParameters = <String, dynamic>{
      'includeCompleted': includeCompleted,
    };

    if (status != null && status.isNotEmpty) {
      queryParameters['status'] = status;
    }

    final response = await _dioClient.get(
      '/customers/$customerId/debts',
      queryParameters: queryParameters,
      showSuccessToast: false,
    );

    _validateResponse(response);
    return response['data']['debts'] ?? [];
  }

  /// Get customer statistics
  Future<Map<String, dynamic>> getCustomerStats() async {
    _logger.d('CustomerRemoteSource: Getting customer statistics');
    
    final response = await _dioClient.get(
      '/customers/stats',
      showSuccessToast: false,
    );

    _validateResponse(response);
    return response['data'];
  }

  /// Search customers
  Future<List<dynamic>> searchCustomers(String query) async {
    _logger.d('CustomerRemoteSource: Searching customers with query: $query');
    
    final response = await _dioClient.get(
      '/customers/search',
      queryParameters: {'q': query, 'limit': 10},
      showSuccessToast: false,
    );

    _validateResponse(response);
    return response['data']['customers'] ?? [];
  }

  /// Validate API response
  void _validateResponse(dynamic response) {
    if (response == null) {
      throw ApiException(
        message: 'No response received from server',
        code: 'no_response',
      );
    }
    
    if (response['success'] == false) {
      final message = response['message'] ?? 'Unknown error';
      final code = response['errorCode'] ?? 'unknown_error';
      throw ApiException(message: message, code: code);
    }
  }
} 