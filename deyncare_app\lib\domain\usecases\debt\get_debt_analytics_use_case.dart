import 'package:dartz/dartz.dart';
import 'package:deyncare_app/core/errors/failures.dart';
import 'package:deyncare_app/domain/repositories/debt_repository.dart';

/// Use case for retrieving debt analytics - BACKEND ALIGNED
/// Matches GET /api/debts/stats endpoint
class GetDebtAnalyticsUseCase {
  final DebtRepository _repository;

  GetDebtAnalyticsUseCase(this._repository);

  /// Execute the use case to get debt analytics
  /// Uses backend analytics endpoint for comprehensive statistics
  Future<Either<Failure, DebtAnalytics>> execute({
    DateTime? startDate,
    DateTime? endDate,
    String? shopId,
  }) async {
    return await _repository.getDebtAnalytics(
      startDate: startDate,
      endDate: endDate,
      shopId: shopId,
    );
  }
} 