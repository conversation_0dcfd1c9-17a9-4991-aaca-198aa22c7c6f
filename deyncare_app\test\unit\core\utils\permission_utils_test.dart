import 'package:flutter_test/flutter_test.dart';
import 'package:deyncare_app/core/utils/permission_utils.dart';
import 'package:deyncare_app/data/models/user_model.dart';

void main() {
  group('PermissionUtils', () {
    late UserModel adminUser;
    late UserModel employeeWithDebtPermissions;
    late UserModel employeeWithNoPermissions;

    setUp(() {
      // Admin user - should have all permissions
      adminUser = UserModel(
        userId: 'admin1',
        fullName: 'Admin User',
        email: '<EMAIL>',
        role: 'admin',
        status: 'active',
        registrationStatus: 'completed',
        isEmailVerified: true,
        isPaid: true,
        isShopActive: true,
      );

      // Employee with debt permissions (like Carab in the screenshot)
      employeeWithDebtPermissions = UserModel(
        userId: 'emp1',
        fullName: 'Carab Employee',
        email: '<EMAIL>',
        role: 'employee',
        status: 'active',
        registrationStatus: 'completed',
        isEmailVerified: true,
        isPaid: true,
        isShopActive: true,
        visibility: {
          'customerManagement': {
            'create': false,
            'update': false,
            'view': false,
            'delete': false,
          },
          'debtManagement': {
            'create': true,
            'update': true,
            'view': true,
            'delete': true,
          },
          'reportManagement': {
            'generate': true,
            'view': true,
            'delete': true,
          },
        },
      );

      // Employee with no permissions
      employeeWithNoPermissions = UserModel(
        userId: 'emp2',
        fullName: 'No Permissions Employee',
        email: '<EMAIL>',
        role: 'employee',
        status: 'active',
        registrationStatus: 'completed',
        isEmailVerified: true,
        isPaid: true,
        isShopActive: true,
        visibility: {
          'customerManagement': {
            'create': false,
            'update': false,
            'view': false,
            'delete': false,
          },
          'debtManagement': {
            'create': false,
            'update': false,
            'view': false,
            'delete': false,
          },
          'reportManagement': {
            'generate': false,
            'view': false,
            'delete': false,
          },
        },
      );
    });

    group('Admin permissions', () {
      test('should have all customer permissions', () {
        expect(PermissionUtils.canAccessCustomers(adminUser), true);
        expect(PermissionUtils.canCreateCustomers(adminUser), true);
        expect(PermissionUtils.canViewCustomers(adminUser), true);
        expect(PermissionUtils.canUpdateCustomers(adminUser), true);
        expect(PermissionUtils.canDeleteCustomers(adminUser), true);
      });

      test('should have all debt permissions', () {
        expect(PermissionUtils.canAccessDebts(adminUser), true);
        expect(PermissionUtils.canCreateDebts(adminUser), true);
        expect(PermissionUtils.canViewDebts(adminUser), true);
        expect(PermissionUtils.canUpdateDebts(adminUser), true);
        expect(PermissionUtils.canDeleteDebts(adminUser), true);
      });

      test('should have all report permissions', () {
        expect(PermissionUtils.canAccessReports(adminUser), true);
        expect(PermissionUtils.canGenerateReports(adminUser), true);
        expect(PermissionUtils.canViewReports(adminUser), true);
        expect(PermissionUtils.canDeleteReports(adminUser), true);
      });

      test('should have admin-only permissions', () {
        expect(PermissionUtils.canAccessUserManagement(adminUser), true);
        expect(PermissionUtils.canAccessShopSettings(adminUser), true);
        expect(PermissionUtils.canAccessSubscription(adminUser), true);
      });
    });

    group('Employee with debt permissions (like Carab)', () {
      test('should NOT have customer permissions', () {
        expect(PermissionUtils.canAccessCustomers(employeeWithDebtPermissions), false);
        expect(PermissionUtils.canCreateCustomers(employeeWithDebtPermissions), false);
        expect(PermissionUtils.canViewCustomers(employeeWithDebtPermissions), false);
        expect(PermissionUtils.canUpdateCustomers(employeeWithDebtPermissions), false);
        expect(PermissionUtils.canDeleteCustomers(employeeWithDebtPermissions), false);
      });

      test('should have ALL debt permissions', () {
        expect(PermissionUtils.canAccessDebts(employeeWithDebtPermissions), true);
        expect(PermissionUtils.canCreateDebts(employeeWithDebtPermissions), true);
        expect(PermissionUtils.canViewDebts(employeeWithDebtPermissions), true);
        expect(PermissionUtils.canUpdateDebts(employeeWithDebtPermissions), true);
        expect(PermissionUtils.canDeleteDebts(employeeWithDebtPermissions), true);
      });

      test('should have ALL report permissions', () {
        expect(PermissionUtils.canAccessReports(employeeWithDebtPermissions), true);
        expect(PermissionUtils.canGenerateReports(employeeWithDebtPermissions), true);
        expect(PermissionUtils.canViewReports(employeeWithDebtPermissions), true);
        expect(PermissionUtils.canDeleteReports(employeeWithDebtPermissions), true);
      });

      test('should NOT have admin-only permissions', () {
        expect(PermissionUtils.canAccessUserManagement(employeeWithDebtPermissions), false);
        expect(PermissionUtils.canAccessShopSettings(employeeWithDebtPermissions), false);
        expect(PermissionUtils.canAccessSubscription(employeeWithDebtPermissions), false);
      });
    });

    group('Employee with no permissions', () {
      test('should NOT have any customer permissions', () {
        expect(PermissionUtils.canAccessCustomers(employeeWithNoPermissions), false);
        expect(PermissionUtils.canCreateCustomers(employeeWithNoPermissions), false);
        expect(PermissionUtils.canViewCustomers(employeeWithNoPermissions), false);
        expect(PermissionUtils.canUpdateCustomers(employeeWithNoPermissions), false);
        expect(PermissionUtils.canDeleteCustomers(employeeWithNoPermissions), false);
      });

      test('should NOT have any debt permissions', () {
        expect(PermissionUtils.canAccessDebts(employeeWithNoPermissions), false);
        expect(PermissionUtils.canCreateDebts(employeeWithNoPermissions), false);
        expect(PermissionUtils.canViewDebts(employeeWithNoPermissions), false);
        expect(PermissionUtils.canUpdateDebts(employeeWithNoPermissions), false);
        expect(PermissionUtils.canDeleteDebts(employeeWithNoPermissions), false);
      });

      test('should NOT have any report permissions', () {
        expect(PermissionUtils.canAccessReports(employeeWithNoPermissions), false);
        expect(PermissionUtils.canGenerateReports(employeeWithNoPermissions), false);
        expect(PermissionUtils.canViewReports(employeeWithNoPermissions), false);
        expect(PermissionUtils.canDeleteReports(employeeWithNoPermissions), false);
      });

      test('should NOT have admin-only permissions', () {
        expect(PermissionUtils.canAccessUserManagement(employeeWithNoPermissions), false);
        expect(PermissionUtils.canAccessShopSettings(employeeWithNoPermissions), false);
        expect(PermissionUtils.canAccessSubscription(employeeWithNoPermissions), false);
      });
    });

    group('Null user', () {
      test('should have no permissions', () {
        expect(PermissionUtils.canAccessCustomers(null), false);
        expect(PermissionUtils.canAccessDebts(null), false);
        expect(PermissionUtils.canAccessReports(null), false);
        expect(PermissionUtils.canAccessUserManagement(null), false);
        expect(PermissionUtils.canAccessShopSettings(null), false);
        expect(PermissionUtils.canAccessSubscription(null), false);
      });
    });
  });
}
