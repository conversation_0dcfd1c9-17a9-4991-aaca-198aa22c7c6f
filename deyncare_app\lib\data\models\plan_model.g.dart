// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'plan_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PlanModel _$PlanModelFromJson(Map<String, dynamic> json) => PlanModel(
      id: json['planId'] as String,
      name: json['name'] as String,
      displayName: json['displayName'] as String,
      type: json['type'] as String,
      description: json['description'] as String?,
      pricing:
          PlanPricingModel.fromJson(json['pricing'] as Map<String, dynamic>),
      trialDays: (json['trialDays'] as num).toInt(),
      features: json['features'] as Map<String, dynamic>,
      limits: json['limits'] as Map<String, dynamic>,
      isActive: json['isActive'] as bool,
      isPopular: json['isPopular'] as bool,
      displayOrder: (json['displayOrder'] as num).toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$PlanModelToJson(PlanModel instance) => <String, dynamic>{
      'planId': instance.id,
      'name': instance.name,
      'displayName': instance.displayName,
      'type': instance.type,
      'description': instance.description,
      'pricing': instance.pricing,
      'trialDays': instance.trialDays,
      'features': instance.features,
      'limits': instance.limits,
      'isActive': instance.isActive,
      'isPopular': instance.isPopular,
      'displayOrder': instance.displayOrder,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

PlanPricingModel _$PlanPricingModelFromJson(Map<String, dynamic> json) =>
    PlanPricingModel(
      basePrice: (json['basePrice'] as num).toDouble(),
      currency: json['currency'] as String,
      billingCycle: json['billingCycle'] as String,
    );

Map<String, dynamic> _$PlanPricingModelToJson(PlanPricingModel instance) =>
    <String, dynamic>{
      'basePrice': instance.basePrice,
      'currency': instance.currency,
      'billingCycle': instance.billingCycle,
    };
