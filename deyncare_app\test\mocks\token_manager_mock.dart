import 'package:deyncare_app/data/network/token/token_manager.dart';

/// Mock implementation of TokenManager for testing
class MockTokenManager extends TokenManager {
  // Mock tokens for testing
  final Map<String, String> _mockTokens = {
    'access_token': '',
    'refresh_token': ''
  };

  @override
  Future<String?> getAccessToken() async {
    return _mockTokens['access_token'];
  }

  @override
  Future<String?> getRefreshToken() async {
    return _mockTokens['refresh_token'];
  }

  @override
  Future<void> saveTokens({required String accessToken, required String refreshToken}) async {
    _mockTokens['access_token'] = accessToken;
    _mockTokens['refresh_token'] = refreshToken;
  }

  @override
  Future<void> clearTokens() async {
    _mockTokens['access_token'] = '';
    _mockTokens['refresh_token'] = '';
  }
}
