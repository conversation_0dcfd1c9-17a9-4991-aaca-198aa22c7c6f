import 'package:deyncare_app/data/models/debt_model.dart';

/// Remote data source interface for debt API calls
abstract class DebtRemoteDataSource {
  /// Create a new debt
  Future<DebtModel> createDebt(Map<String, dynamic> debtData);

  /// Get debts with pagination and filters
  Future<Map<String, dynamic>> getDebts(Map<String, dynamic> queryParams);

  /// Get debt by ID
  Future<DebtModel> getDebtById(String debtId);

  /// Update existing debt
  Future<DebtModel> updateDebt(String debtId, Map<String, dynamic> updateData);

  /// Delete debt
  Future<void> deleteDebt(String debtId);

  /// Add payment to debt
  Future<DebtModel> addPaymentToDebt(String debtId, Map<String, dynamic> paymentData);

  /// Get debt analytics
  Future<Map<String, dynamic>> getDebtAnalytics(Map<String, dynamic> queryParams);

  /// Get customer debts
  Future<List<DebtModel>> getCustomerDebts(String customerId);
} 