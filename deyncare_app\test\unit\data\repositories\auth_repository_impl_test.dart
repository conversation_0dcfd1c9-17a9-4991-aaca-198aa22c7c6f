import 'package:deyncare_app/data/models/user_model.dart';
import 'package:deyncare_app/data/repositories/auth_repository_impl.dart';
import 'package:deyncare_app/data/services/auth/auth_remote_source.dart';
import 'package:deyncare_app/data/services/auth/auth_utils.dart';
import 'package:deyncare_app/data/network/token/token_manager.dart';
import 'package:deyncare_app/domain/repositories/auth_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'auth_repository_impl_test.mocks.dart';

@GenerateMocks([AuthRemoteSource, TokenManager, AuthUtils, UserModel])
void main() {
  late AuthRepositoryImpl authRepository;
  late MockAuthRemoteSource mockAuthRemoteSource;
  late MockTokenManager mockTokenManager;
  late MockAuthUtils mockAuthUtils;

  setUp(() {
    mockAuthRemoteSource = MockAuthRemoteSource();
    mockTokenManager = MockTokenManager();
    mockAuthUtils = MockAuthUtils();

    authRepository = AuthRepositoryImpl(
      remoteSource: mockAuthRemoteSource,
      tokenManager: mockTokenManager,
      authUtils: mockAuthUtils,
    );
  });

  group('AuthRepositoryImpl', () {
    test('implements AuthRepository interface', () {
      expect(authRepository, isA<AuthRepository>());
    });

    group('isLoggedIn', () {
      test('returns false when no token is available', () async {
        // Arrange
        when(mockTokenManager.getAccessToken()).thenAnswer((_) async => null);
        when(mockAuthUtils.getUserData()).thenAnswer((_) async => MockUserModel());

        // Act
        final result = await authRepository.isLoggedIn();

        // Assert
        expect(result, false);
        verify(mockTokenManager.getAccessToken());
        verifyNever(mockAuthUtils.getUserData());
      });

      test('returns false when no user data is available', () async {
        // Arrange
        when(mockTokenManager.getAccessToken()).thenAnswer((_) async => 'token');
        when(mockAuthUtils.getUserData()).thenAnswer((_) async => null);

        // Act
        final result = await authRepository.isLoggedIn();

        // Assert
        expect(result, false);
        verify(mockTokenManager.getAccessToken());
        verify(mockAuthUtils.getUserData());
      });

      test('returns true when a token and user data are available', () async {
        // Arrange
        when(mockTokenManager.getAccessToken()).thenAnswer((_) async => 'token');
        when(mockAuthUtils.getUserData()).thenAnswer((_) async => MockUserModel());

        // Act
        final result = await authRepository.isLoggedIn();

        // Assert
        expect(result, true);
        verify(mockTokenManager.getAccessToken());
        verify(mockAuthUtils.getUserData());
      });
    });

    test('getCurrentUser returns null when no user is cached', () async {
      // Arrange
      when(mockAuthUtils.getUserData()).thenAnswer((_) async => null);
      // Act
      final user = await authRepository.getCurrentUser();

      // Assert
      expect(user, isNull);
      verify(mockAuthUtils.getUserData());
      verifyNoMoreInteractions(mockAuthUtils);
    });

    group('logout', () {
      test('clears data and calls remote logout when a token exists', () async {
        // Arrange
        when(mockTokenManager.getAccessToken()).thenAnswer((_) async => 'fake_token');
        when(mockAuthRemoteSource.logout()).thenAnswer((_) async => {});
        when(mockTokenManager.clearTokens()).thenAnswer((_) async => Future.value());
        when(mockAuthUtils.clearUserData()).thenAnswer((_) async => Future.value());

        // Act
        await authRepository.logout();
        
        // Verify
        verify(mockAuthRemoteSource.logout());
        verify(mockTokenManager.clearTokens());
        verify(mockAuthUtils.clearUserData());
      });

      test('clears data and does not call remote logout when no token exists', () async {
        // Arrange
        when(mockTokenManager.getAccessToken()).thenAnswer((_) async => null);
        when(mockTokenManager.clearTokens()).thenAnswer((_) async => Future.value());
        when(mockAuthUtils.clearUserData()).thenAnswer((_) async => Future.value());

        // Act
        await authRepository.logout();
        
        // Verify
        verifyNever(mockAuthRemoteSource.logout());
        verify(mockTokenManager.clearTokens());
        verify(mockAuthUtils.clearUserData());
      });
    });

    test('logoutAll completes without error', () async {
      // Arrange
      when(mockAuthRemoteSource.logoutAll()).thenAnswer((_) async => Future.value());
      when(mockTokenManager.clearTokens()).thenAnswer((_) async => Future.value());
      when(mockAuthUtils.clearUserData()).thenAnswer((_) async => Future.value());

      // Act
      await authRepository.logoutAll();

      // Verify
      verify(mockAuthRemoteSource.logoutAll());
      verify(mockTokenManager.clearTokens());
      verify(mockAuthUtils.clearUserData());
    });
  });
}
