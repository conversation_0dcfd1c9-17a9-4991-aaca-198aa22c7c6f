import 'package:dartz/dartz.dart';
import 'package:deyncare_app/core/errors/failures.dart';
import 'package:deyncare_app/core/utils/business_validation.dart';
import 'package:deyncare_app/domain/repositories/debt_repository.dart';
import 'package:deyncare_app/domain/models/debt.dart';

/// Use case to validate payment against outstanding debt amount
/// Ensures payment business rules are enforced before processing payment
class ValidatePaymentUseCase {
  final DebtRepository _repository;

  ValidatePaymentUseCase(this._repository);

  /// Validates payment amount against outstanding debt amount
  /// Returns detailed validation result with business context
  Future<Either<Failure, PaymentValidationResult>> execute({
    required String debtId,
    required double paymentAmount,
  }) async {
      // Validate payment amount (basic validation)
      final paymentValidation = BusinessValidation.validatePaymentAmount(paymentAmount);
      if (!paymentValidation.isValid) {
        return Left(ValidationFailure(message: paymentValidation.errorMessage!));
      }

    // First get the current debt to check outstanding amount
    final debtResult = await _repository.getDebtById(debtId);
    
    return debtResult.fold(
      (failure) => Left(failure),
      (debt) {
      // Validate payment against outstanding debt (critical business rule)
      final outstandingValidation = BusinessValidation.validatePaymentAgainstOutstanding(
        paymentAmount, 
        debt.remainingAmount
      );
      if (!outstandingValidation.isValid) {
        return Left(ValidationFailure(message: outstandingValidation.errorMessage!));
      }

      // Calculate payment result
      final newRemainingAmount = debt.remainingAmount - paymentAmount;
      final willBeFullyPaid = newRemainingAmount <= 0.01; // Allow for small rounding errors
      final newStatus = willBeFullyPaid ? DebtStatus.completed : DebtStatus.active;

      return Right(PaymentValidationResult(
        isValid: true,
        currentOutstanding: debt.remainingAmount,
        paymentAmount: paymentAmount,
        newRemainingAmount: newRemainingAmount,
        willBeFullyPaid: willBeFullyPaid,
        newDebtStatus: newStatus,
        validationMessage: willBeFullyPaid 
          ? 'This payment will fully settle the debt'
          : 'Partial payment of \$${paymentAmount.toStringAsFixed(2)}. Remaining: \$${newRemainingAmount.toStringAsFixed(2)}',
      ));
      },
    );
  }
}

/// Result of payment validation with business context
class PaymentValidationResult {
  final bool isValid;
  final double currentOutstanding;
  final double paymentAmount;
  final double newRemainingAmount;
  final bool willBeFullyPaid;
  final DebtStatus newDebtStatus;
  final String validationMessage;

  const PaymentValidationResult({
    required this.isValid,
    required this.currentOutstanding,
    required this.paymentAmount,
    required this.newRemainingAmount,
    required this.willBeFullyPaid,
    required this.newDebtStatus,
    required this.validationMessage,
  });
} 