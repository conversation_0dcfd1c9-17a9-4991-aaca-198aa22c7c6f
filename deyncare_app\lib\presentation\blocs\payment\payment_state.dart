import 'package:equatable/equatable.dart';
import 'package:deyncare_app/domain/models/payment.dart';

/// Payment States - focused on payment history
abstract class PaymentState extends Equatable {
  const PaymentState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class PaymentInitial extends PaymentState {
  const PaymentInitial();
}

/// Loading state
class PaymentHistoryLoading extends PaymentState {
  const PaymentHistoryLoading();
}

/// Success state
class PaymentHistoryLoaded extends PaymentState {
  final List<Payment> payments;
  final bool isRefreshing;

  const PaymentHistoryLoaded({
    required this.payments,
    this.isRefreshing = false,
  });

  @override
  List<Object> get props => [payments, isRefreshing];

  PaymentHistoryLoaded copyWith({
    List<Payment>? payments,
    bool? isRefreshing,
  }) {
    return PaymentHistoryLoaded(
      payments: payments ?? this.payments,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }
}

/// Error states
class PaymentHistoryError extends PaymentState {
  final String message;
  final String? errorCode;

  const PaymentHistoryError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

/// General payment error state
class PaymentError extends PaymentState {
  final String message;
  final String? errorCode;

  const PaymentError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
} 