# Permission System Testing Guide

This guide provides comprehensive testing scenarios to verify that the permission system works correctly across different user roles and UI components.

## Test Users Setup

### 1. Admin User
- **Role**: `admin`
- **Expected Behavior**: Full access to all features
- **Permissions**: All CRUD operations on customers, debts, and reports

### 2. Employee with Customer Permissions Only
- **Role**: `employee`
- **Visibility Configuration**:
  ```json
  {
    "customerManagement": {
      "create": true,
      "update": true,
      "view": true,
      "delete": true
    },
    "debtManagement": {
      "create": false,
      "update": false,
      "view": false,
      "delete": false
    },
    "reportManagement": {
      "generate": false,
      "view": false,
      "delete": false
    }
  }
  ```

### 3. Employee with Debt Permissions Only
- **Role**: `employee`
- **Visibility Configuration**:
  ```json
  {
    "customerManagement": {
      "create": false,
      "update": false,
      "view": false,
      "delete": false
    },
    "debtManagement": {
      "create": true,
      "update": true,
      "view": true,
      "delete": true
    },
    "reportManagement": {
      "generate": false,
      "view": false,
      "delete": false
    }
  }
  ```

### 4. Employee with No Permissions
- **Role**: `employee`
- **Visibility Configuration**: All permissions set to `false`

## Testing Scenarios

### Dashboard Screen Tests

#### Test 1: Speed Dial FAB Visibility
**Objective**: Verify that FAB buttons are shown/hidden based on user permissions

**Steps**:
1. Login with each test user
2. Navigate to Dashboard
3. Check Speed Dial FAB buttons

**Expected Results**:
- **Admin**: All 3 buttons visible (Add Customer, Collect Payment, View Reports)
- **Customer-only Employee**: Only "Add Customer" button visible
- **Debt-only Employee**: Only "Collect Payment" button visible
- **No-permission Employee**: No FAB buttons visible (FAB should be hidden)

#### Test 2: Bottom Navigation Tabs
**Objective**: Verify that navigation tabs are disabled based on permissions

**Steps**:
1. Login with each test user
2. Check bottom navigation bar

**Expected Results**:
- **Admin**: All tabs enabled
- **Customer-only Employee**: Dashboard and Menu enabled, Customers enabled, Debts disabled
- **Debt-only Employee**: Dashboard and Menu enabled, Customers disabled, Debts enabled
- **No-permission Employee**: Only Dashboard and Menu enabled

### Debt Management Screen Tests

#### Test 3: Debt List Screen Access
**Objective**: Verify debt screen shows appropriate UI based on permissions

**Steps**:
1. Login with each test user
2. Navigate to Debt tab (if accessible)
3. Check for "Add Debt" button in app bar

**Expected Results**:
- **Admin**: "Add Debt" button visible
- **Customer-only Employee**: Tab should be disabled/inaccessible
- **Debt-only Employee**: "Add Debt" button visible
- **No-permission Employee**: Tab should be disabled/inaccessible

#### Test 4: Add Debt Action
**Objective**: Verify permission checks when attempting to add debt

**Steps**:
1. Login with customer-only employee
2. Try to access debt creation through any means
3. Verify permission denied dialog appears

**Expected Results**:
- Permission denied dialog should appear
- User should not be able to create debts

### Customer Details Modal Tests

#### Test 5: Customer Action Buttons
**Objective**: Verify action buttons in customer details are permission-aware

**Steps**:
1. Login with each test user
2. Open any customer details modal
3. Check available action buttons

**Expected Results**:
- **Admin**: "Edit Customer" and "Add Debt" buttons visible
- **Customer-only Employee**: Only "Edit Customer" button visible
- **Debt-only Employee**: Only "Add Debt" button visible (when implemented)
- **No-permission Employee**: No action buttons or permission message

### Report Screen Tests

#### Test 6: Report Generation Access
**Objective**: Verify report generation is restricted based on permissions

**Steps**:
1. Login with each test user
2. Try to access report generation
3. Verify appropriate behavior

**Expected Results**:
- **Admin**: Full access to report generation
- **Employees without report permissions**: Access denied or feature hidden

### Error Handling Tests

#### Test 7: Permission Denied Messages
**Objective**: Verify user-friendly error messages for permission violations

**Steps**:
1. Login with restricted user
2. Attempt unauthorized actions
3. Verify error messages are clear and helpful

**Expected Results**:
- Clear error messages explaining permission requirements
- Suggestions to contact administrator
- No app crashes or technical error messages

#### Test 8: Graceful Degradation
**Objective**: Verify app remains functional with limited permissions

**Steps**:
1. Login with no-permission employee
2. Navigate through available features
3. Verify app doesn't crash or show broken UI

**Expected Results**:
- App remains stable
- Available features work correctly
- Restricted features are cleanly hidden or disabled

## Report Generation and PDF Tests

#### Test 9: Report View/Share Button Crash Fix
**Objective**: Verify the crash issue with report buttons is resolved

**Steps**:
1. Login as admin or user with report permissions
2. Generate any type of report (Customer, Debt, or Risk)
3. Click "View Report" button
4. Click "Share Report" button
5. Test on both Android and iOS if possible

**Expected Results**:
- No app crashes when clicking View or Share buttons
- Loading indicators appear during operations
- Appropriate error messages if PDF viewer not available
- Fallback to sharing if direct opening fails
- Success messages when operations complete

#### Test 10: PDF Error Handling
**Objective**: Verify robust error handling for PDF operations

**Steps**:
1. Generate a report
2. Test scenarios:
   - No PDF viewer app installed
   - Corrupted PDF file
   - Insufficient storage space
   - Network issues during sharing

**Expected Results**:
- User-friendly error messages
- Graceful fallbacks (e.g., sharing when opening fails)
- No app crashes under any error condition

## Automated Testing

Run the integration tests:
```bash
flutter test test/integration/permission_system_integration_test.dart
```

Run unit tests for permission utilities:
```bash
flutter test test/unit/core/utils/permission_utils_test.dart
```

## Performance Testing

#### Test 11: Permission Check Performance
**Objective**: Verify permission checks don't impact app performance

**Steps**:
1. Login with complex permission structure
2. Navigate rapidly between screens
3. Monitor app responsiveness

**Expected Results**:
- No noticeable lag from permission checks
- Smooth navigation between screens
- UI updates happen immediately

## Security Testing

#### Test 12: Backend Permission Validation
**Objective**: Verify backend enforces permissions even if frontend is bypassed

**Steps**:
1. Use API testing tools to attempt unauthorized operations
2. Verify backend rejects requests appropriately

**Expected Results**:
- Backend validates permissions independently
- Appropriate HTTP error codes returned
- No data leakage or unauthorized operations

## Regression Testing

After implementing fixes, verify:
1. All existing functionality still works
2. No new permission-related bugs introduced
3. Performance hasn't degraded
4. UI remains consistent across different permission levels

## Sign-off Criteria

✅ All FAB buttons respect user permissions
✅ Navigation tabs are properly disabled
✅ Action buttons in modals are permission-aware
✅ Report View/Share buttons don't crash the app
✅ Error messages are user-friendly
✅ App remains stable with any permission configuration
✅ Backend permission validation works independently
✅ Automated tests pass
✅ Manual testing scenarios completed successfully

## Notes

- Test on both Android and iOS devices
- Test with different screen sizes and orientations
- Verify accessibility features work with permission-restricted UI
- Document any edge cases or unexpected behaviors
- Update this guide as new features are added
