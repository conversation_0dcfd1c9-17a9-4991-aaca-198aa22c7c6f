import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/domain/usecases/debt/create_debt_use_case.dart';
import 'package:deyncare_app/domain/usecases/debt/get_debts_use_case.dart';
import 'package:deyncare_app/domain/usecases/debt/get_debt_details_use_case.dart';
import 'package:deyncare_app/domain/usecases/debt/update_debt_use_case.dart';
import 'package:deyncare_app/domain/usecases/debt/delete_debt_use_case.dart';
import 'package:deyncare_app/domain/usecases/debt/add_payment_to_debt_use_case.dart';
import 'package:deyncare_app/domain/usecases/debt/get_debt_analytics_use_case.dart';
import 'debt_event.dart';
import 'debt_state.dart';

/// DebtBloc - Handles all debt-related business logic
/// Aligned 100% with backend API endpoints and validation
class DebtBloc extends Bloc<DebtEvent, DebtState> {
  final CreateDebtUseCase _createDebtUseCase;
  final GetDebtsUseCase _getDebtsUseCase;
  final GetDebtDetailsUseCase _getDebtDetailsUseCase;
  final UpdateDebtUseCase _updateDebtUseCase;
  final DeleteDebtUseCase _deleteDebtUseCase;
  final AddPaymentToDebtUseCase _addPaymentToDebtUseCase;
  final GetDebtAnalyticsUseCase _getDebtAnalyticsUseCase;

  DebtBloc({
    required CreateDebtUseCase createDebtUseCase,
    required GetDebtsUseCase getDebtsUseCase,
    required GetDebtDetailsUseCase getDebtDetailsUseCase,
    required UpdateDebtUseCase updateDebtUseCase,
    required DeleteDebtUseCase deleteDebtUseCase,
    required AddPaymentToDebtUseCase addPaymentToDebtUseCase,
    required GetDebtAnalyticsUseCase getDebtAnalyticsUseCase,
  })  : _createDebtUseCase = createDebtUseCase,
        _getDebtsUseCase = getDebtsUseCase,
        _getDebtDetailsUseCase = getDebtDetailsUseCase,
        _updateDebtUseCase = updateDebtUseCase,
        _deleteDebtUseCase = deleteDebtUseCase,
        _addPaymentToDebtUseCase = addPaymentToDebtUseCase,
        _getDebtAnalyticsUseCase = getDebtAnalyticsUseCase,
        super(const DebtInitial()) {
    on<LoadDebts>(_onLoadDebts);
    on<CreateDebt>(_onCreateDebt);
    on<LoadDebtDetails>(_onLoadDebtDetails);
    on<UpdateDebt>(_onUpdateDebt);
    on<DeleteDebt>(_onDeleteDebt);
    on<AddPaymentToDebt>(_onAddPaymentToDebt);
    on<LoadDebtStats>(_onLoadDebtStats);
    on<RefreshDebts>(_onRefreshDebts);
    on<ClearDebtDetails>(_onClearDebtDetails);
  }

  /// Load debts with pagination and filters
  Future<void> _onLoadDebts(LoadDebts event, Emitter<DebtState> emit) async {
    if (event.page == 1) {
      emit(const DebtListLoading());
    }

      final result = await _getDebtsUseCase.execute(
        page: event.page,
        limit: event.limit,
        status: event.status,
        riskLevel: event.riskLevel,
        customerType: event.customerType,
        search: event.search,
        sortBy: event.sortBy,
        ascending: event.ascending,
      );

    result.fold(
      (failure) {
      emit(DebtListError(
          message: failure.message,
          errorCode: failure.code,
      ));
      },
      (debtListResult) {
        emit(DebtListLoaded(response: debtListResult));
      },
    );
  }

  /// Create new debt
  Future<void> _onCreateDebt(CreateDebt event, Emitter<DebtState> emit) async {
    emit(const DebtCreating());

      final result = await _createDebtUseCase.execute(
        customerId: event.customerId,
        debtAmount: event.debtAmount,
        dueDate: event.dueDate,
        description: event.description,
        paidAmount: event.paidAmount,
        paidDate: event.paidDate,
        paymentMethod: event.paymentMethod,
      );

    result.fold(
      (failure) {
      emit(DebtError(
          message: failure.message,
          errorCode: failure.code,
      ));
      },
      (debt) {
        emit(DebtCreated(debt));
      },
    );
  }

  /// Load debt details
  Future<void> _onLoadDebtDetails(
      LoadDebtDetails event, Emitter<DebtState> emit) async {
    emit(const DebtDetailsLoading());

      final result = await _getDebtDetailsUseCase.execute(event.debtId);

    result.fold(
      (failure) {
      emit(DebtDetailsError(
          message: failure.message,
          errorCode: failure.code,
      ));
      },
      (debt) {
        emit(DebtDetailsLoaded(debt));
      },
    );
  }

  /// Update debt (amount, dueDate and description allowed)
  Future<void> _onUpdateDebt(UpdateDebt event, Emitter<DebtState> emit) async {
    emit(const DebtUpdating());

      final result = await _updateDebtUseCase.execute(
        debtId: event.debtId,
        amount: event.amount,
        dueDate: event.dueDate,
        description: event.description,
      );

    result.fold(
      (failure) {
      emit(DebtError(
          message: failure.message,
          errorCode: failure.code,
      ));
      },
      (debt) {
        emit(DebtUpdated(debt));
      },
    );
  }

  /// Delete debt
  Future<void> _onDeleteDebt(DeleteDebt event, Emitter<DebtState> emit) async {
    emit(const DebtDeleting());

    final result = await _deleteDebtUseCase.execute(event.debtId);

    result.fold(
      (failure) {
      emit(DebtError(
          message: failure.message,
          errorCode: failure.code,
      ));
      },
      (_) {
        emit(DebtDeleted(event.debtId));
      },
    );
  }

  /// Add payment to debt
  Future<void> _onAddPaymentToDebt(
      AddPaymentToDebt event, Emitter<DebtState> emit) async {
    emit(const PaymentAdding());

      final result = await _addPaymentToDebtUseCase.execute(
        debtId: event.debtId,
        amount: event.amount,
        paymentMethod: event.paymentMethod,
        notes: event.notes,
        paymentDate: event.paymentDate,
      );

    result.fold(
      (failure) {
      emit(PaymentError(
          message: failure.message,
          errorCode: failure.code,
      ));
      },
      (debt) {
        emit(PaymentAdded(debt));
      },
    );
  }

  /// Load debt analytics/statistics
  Future<void> _onLoadDebtStats(
      LoadDebtStats event, Emitter<DebtState> emit) async {
    emit(const DebtStatsLoading());

    final result = await _getDebtAnalyticsUseCase.execute();

    result.fold(
      (failure) {
      emit(DebtStatsError(
          message: failure.message,
          errorCode: failure.code,
        ));
      },
      (analytics) {
        // Convert DebtAnalytics to Map format expected by state
        final statsMap = {
          'totalDebts': analytics.stats.totalDebts,
          'totalAmount': analytics.stats.totalAmount,
          'activeDebts': analytics.stats.activeDebts,
          'overdueDebts': analytics.stats.overdueDebts,
          'completedDebts': analytics.stats.completedDebts,
          'totalPaid': analytics.stats.totalPaid,
          'totalOutstanding': analytics.stats.totalOutstanding,
          'averageDebtAmount': analytics.stats.averageDebtAmount,
          'collectionRate': analytics.stats.collectionRate,
        };
        emit(DebtStatsLoaded(statsMap));
      },
    );
  }

  /// Refresh debts list
  Future<void> _onRefreshDebts(
      RefreshDebts event, Emitter<DebtState> emit) async {
    // Refresh by loading first page
    add(const LoadDebts(page: 1));
  }

  /// Clear debt details
  void _onClearDebtDetails(
      ClearDebtDetails event, Emitter<DebtState> emit) {
    emit(const DebtInitial());
  }

  /// Map errors to user-friendly messages
  String _mapErrorToMessage(dynamic error) {
    if (error is ArgumentError) {
      return error.message;
    } else if (error is Exception) {
      return error.toString();
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }
} 