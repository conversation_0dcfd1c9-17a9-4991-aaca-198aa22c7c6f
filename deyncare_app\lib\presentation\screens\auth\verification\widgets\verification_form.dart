import 'dart:async';
import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_button.dart';
import 'package:deyncare_app/presentation/screens/auth/verification/widgets/verification_code_input.dart';


/// Form component for email verification
class VerificationForm extends StatefulWidget {
  final String email;
  final bool isLoading;
  final Function(String) onVerifyPressed;
  final VoidCallback onResendVerificationCodeRequested;
  
  const VerificationForm({
    super.key,
    required this.email,
    required this.isLoading,
    required this.onVerifyPressed,
    required this.onResendVerificationCodeRequested,
  });

  @override
  State<VerificationForm> createState() => _VerificationFormState();
}

class _VerificationFormState extends State<VerificationForm> {
  final _verificationController = TextEditingController();
  
  // Timer for resend countdown
  Timer? _resendTimer;
  int _resendSeconds = 0;
  
  @override
  void initState() {
    super.initState();
    _startResendTimer();
  }
  
  @override
  void dispose() {
    _verificationController.dispose();
    _resendTimer?.cancel();
    super.dispose();
  }
  
  // Start the countdown timer for resend functionality
  void _startResendTimer() {
    // Initial countdown time (60 seconds)
    _resendSeconds = 60;
    
    // Update UI
    setState(() {});
    
    // Cancel any existing timer before starting a new one
    _resendTimer?.cancel();
    
    // Create and start timer
    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_resendSeconds > 0) {
        setState(() {
          _resendSeconds--;
        });
      } else {
        timer.cancel();
      }
    });
  }
  
  // Handle verification code submission
  void _handleVerify() {
    final code = _verificationController.text.trim();
    
    if (code.length < 6) {
      // This validation can also be moved to the BLoC/use case layer
      // For now, we'll keep a simple client-side check
      return;
    }
    
    widget.onVerifyPressed(code);
  }
  
  // Handle resend verification code
  void _handleResend() {
    if (_resendSeconds > 0 || widget.isLoading) {
      return; // Still in cooldown period or another operation is in progress
    }
    
    widget.onResendVerificationCodeRequested();
    _verificationController.clear(); // Clear input after resend
    _startResendTimer(); // Restart the timer
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Email info
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppThemes.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppThemes.primaryColor.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.email_outlined,
                color: AppThemes.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                widget.email,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: AppThemes.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),
        
        // Verification code input
        VerificationCodeInput(
          controller: _verificationController,
          length: 6,
          onCompleted: (code) => _handleVerify(),
          onChanged: (code) {
            // Optional: Add any validation or UI updates as the code is being entered
            setState(() {});
          },
        ),
        const SizedBox(height: 32),
        
        // Verify button
        AuthButton(
          label: widget.isLoading ? 'Verifying...' : 'Verify',
          onPressed: widget.isLoading ? null : _handleVerify,
          isLoading: widget.isLoading,
          icon: const Icon(Icons.check_circle_outline),
        ),
        const SizedBox(height: 24),
        
        // Resend code button
        TextButton(
          onPressed: (_resendSeconds == 0 && !widget.isLoading) ? _handleResend : null,
          child: Text(
            _resendSeconds == 0
                ? 'Resend Code'
                : 'Resend Code in ${_resendSeconds}s',
            style: TextStyle(
              color: (_resendSeconds == 0 && !widget.isLoading) ? AppThemes.primaryColor : Colors.grey,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
}
