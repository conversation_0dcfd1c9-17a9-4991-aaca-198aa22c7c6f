import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/widgets/common_app_bar.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/common_button.dart';
import 'package:deyncare_app/presentation/blocs/payment/payment_bloc.dart';
import 'package:deyncare_app/presentation/blocs/payment/payment_state.dart';
import 'package:deyncare_app/presentation/blocs/payment/payment_event.dart';
import 'package:deyncare_app/injection_container.dart' as di;

/// Screen displaying payment history for a specific debt
class PaymentHistoryScreen extends StatelessWidget {
  final String debtId;
  final String customerName;

  const PaymentHistoryScreen({
    super.key,
    required this.debtId,
    required this.customerName,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<PaymentBloc>()
        ..add(LoadPaymentHistory(debtId)),
      child: PaymentHistoryView(
        debtId: debtId,
        customerName: customerName,
      ),
    );
  }
}

class PaymentHistoryView extends StatefulWidget {
  final String debtId;
  final String customerName;

  const PaymentHistoryView({
    super.key,
    required this.debtId,
    required this.customerName,
  });

  @override
  State<PaymentHistoryView> createState() => _PaymentHistoryViewState();
}

class _PaymentHistoryViewState extends State<PaymentHistoryView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppThemes.backgroundColor,
      appBar: CommonAppBar(
        title: 'Payment History',
      ),
      body: Column(
        children: [
          // Header with debt info
          _buildHeader(),
          
          // Payment list
          Expanded(
            child: BlocConsumer<PaymentBloc, PaymentState>(
              listener: _handleStateChanges,
              builder: _buildBody,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return CommonCard(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.receipt_long,
                color: AppThemes.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.customerName,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppThemes.textPrimaryColor,
                      ),
                    ),
                    Text(
                      'Debt ID: ${widget.debtId}',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppThemes.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _handleStateChanges(BuildContext context, PaymentState state) {
    if (state is PaymentError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: AppThemes.errorColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  Widget _buildBody(BuildContext context, PaymentState state) {
    if (state is PaymentHistoryLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    
    if (state is PaymentHistoryError) {
      return _buildErrorState(context, state);
    }
    
    if (state is PaymentHistoryLoaded) {
      return _buildLoadedState(context, state);
    }
    
    return const SizedBox.shrink();
  }

  Widget _buildErrorState(BuildContext context, PaymentHistoryError state) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppThemes.errorColor,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load payment history',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppThemes.textPrimaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              state.message,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: AppThemes.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 24),
            CommonButton(
              label: 'Retry',
              onPressed: () {
                context.read<PaymentBloc>().add(
                      LoadPaymentHistory(widget.debtId),
                    );
              },
              size: ButtonSize.medium,
              width: 120,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadedState(BuildContext context, PaymentHistoryLoaded state) {
    final payments = state.payments;
    
    if (payments.isEmpty) {
      return _buildEmptyState(context);
    }
    
    return RefreshIndicator(
      onRefresh: () async {
        context.read<PaymentBloc>().add(
              RefreshPaymentHistory(widget.debtId),
            );
      },
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: payments.length,
        itemBuilder: (context, index) {
          final payment = payments[index];
          return PaymentListItem(
            payment: payment,
            isLast: index == payments.length - 1,
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.payment_outlined,
              size: 64,
              color: AppThemes.textSecondaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              'No payments yet',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppThemes.textPrimaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'No payments have been recorded for this debt',
              style: TextStyle(
                fontSize: 14,
                color: AppThemes.textSecondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Individual payment list item widget
class PaymentListItem extends StatelessWidget {
  final dynamic payment; // Payment model
  final bool isLast;

  const PaymentListItem({
    super.key,
    required this.payment,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    return CommonCard(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Column(
        children: [
          Row(
            children: [
              // Payment method icon
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _getPaymentMethodColor(payment.paymentMethod).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getPaymentMethodIcon(),
                  color: _getPaymentMethodColor(payment.paymentMethod),
                  size: 20,
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Payment details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          '\$${payment.amount?.toStringAsFixed(2) ?? '0.00'}',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: AppThemes.textPrimaryColor,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          _formatDate(payment.paymentDate ?? DateTime.now()),
                          style: TextStyle(
                            fontSize: 14,
                            color: AppThemes.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          _getPaymentMethodName(),
                          style: TextStyle(
                            fontSize: 14,
                            color: AppThemes.textSecondaryColor,
                          ),
                        ),
                        if (payment.notes != null && payment.notes!.isNotEmpty) ...[
                          const SizedBox(width: 8),
                          Icon(
                            Icons.note,
                            size: 14,
                            color: AppThemes.textSecondaryColor,
                          ),
                        ],
                      ],
                    ),
                    if (payment.notes != null && payment.notes!.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        payment.notes!,
                        style: TextStyle(
                          fontSize: 12,
                          color: AppThemes.textSecondaryColor,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
          
          // Timeline connector
          if (!isLast) ...[
            const SizedBox(height: 12),
            Container(
              margin: const EdgeInsets.only(left: 20),
              height: 20,
              width: 2,
              color: AppThemes.textSecondaryColor.withOpacity(0.2),
            ),
          ],
        ],
      ),
    );
  }

  IconData _getPaymentMethodIcon() {
    switch (payment.paymentMethod?.toString().toLowerCase()) {
      case 'cash':
        return Icons.money;
      case 'banktransfer':
        return Icons.account_balance;
      case 'mobilemoney':
        return Icons.phone_android;
      case 'card':
        return Icons.credit_card;
      default:
        return Icons.payment;
    }
  }

  Color _getPaymentMethodColor(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return AppThemes.successColor;
      case 'bank_transfer':
        return AppThemes.primaryColor;
      case 'mobile_money':
        return AppThemes.warningColor;
      case 'card':
        return AppThemes.secondaryColor;
      default:
        return AppThemes.textSecondaryColor;
    }
  }

  String _getPaymentMethodName() {
    switch (payment.paymentMethod?.toString().toLowerCase()) {
      case 'cash':
        return 'Cash Payment';
      case 'banktransfer':
        return 'Bank Transfer';
      case 'mobilemoney':
        return 'Mobile Money';
      case 'card':
        return 'Card Payment';
      default:
        return 'Other Payment';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
} 