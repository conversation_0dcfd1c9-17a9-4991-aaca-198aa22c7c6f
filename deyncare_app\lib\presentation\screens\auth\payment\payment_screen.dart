import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'package:deyncare_app/core/routes/app_router.dart';
import 'package:deyncare_app/core/utils/toast_util.dart';
import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/domain/models/auth_token.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_event.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_state.dart';
import 'package:deyncare_app/presentation/screens/auth/payment/widgets/payment_form.dart';
import 'package:deyncare_app/presentation/screens/auth/payment/widgets/payment_processing_view.dart';
import 'package:deyncare_app/presentation/screens/auth/payment_success_screen.dart';
import 'package:deyncare_app/injection_container.dart'; // Import GetIt
import 'package:deyncare_app/domain/usecases/plan/get_plan_details_use_case.dart'; // Import the use case
import 'package:deyncare_app/data/models/plan_model.dart'; // Import PlanModel

/// Payment screen for completing subscription payments
class PaymentScreen extends StatefulWidget {
  final User user;
  final AuthToken? token;
  final String selectedPlanId;

  const PaymentScreen({
    super.key,
    required this.user,
    this.token,
    required this.selectedPlanId,
  });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  bool _isFetchingPlan = true;
  PlanModel? _planDetails;

  @override
  void initState() {
    super.initState();
    _fetchPlanDetails();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _fetchPlanDetails() async {
    setState(() {
      _isFetchingPlan = true;
    });
    try {
      final getPlanDetails = sl<GetPlanDetailsUseCase>();
      final plan = await getPlanDetails.execute(widget.selectedPlanId);
      setState(() {
        _planDetails = plan;
        _isFetchingPlan = false;
      });
    } catch (e) {
      ToastUtil.showError('Failed to load plan details: ${e.toString()}');
      setState(() {
        _isFetchingPlan = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppThemes.backgroundColor,
      appBar: AppBar(
        title: const Text('Complete Payment'),
        backgroundColor: AppThemes.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: SafeArea(
        child: BlocConsumer<AuthBloc, AuthState>(
          listener: (context, state) {
            if (state is AuthAuthenticated) {
              // Payment successful, navigate to success screen
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => PaymentSuccessScreen(user: state.user),
                ),
              );
            } else if (state is AuthFailure) {
              ToastUtil.showError(state.message);
            } else if (state is RegistrationComplete) {
              // Registration completed successfully
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => PaymentSuccessScreen(user: state.user),
                ),
              );
            }
          },
          builder: (context, state) {
            if (state is PaymentProcessing) {
              return PaymentProcessingView(
                paymentMethod: state.paymentMethod,
                planName: _planDetails?.displayName ?? _getPlanDisplayName(state.planId),
                amount: _planDetails?.pricing.basePrice ?? _getPlanAmount(state.planId),
                onCancel: () {
                  context.read<AuthBloc>().add(const CancelPaymentRequested());
                },
              );
            } else if (_isFetchingPlan) {
              return Container(
                padding: const EdgeInsets.all(40),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(AppThemes.primaryColor),
                    ),
                    const SizedBox(height: 24),
                    Text(
                      'Loading plan details...',
                      style: TextStyle(
                        fontSize: 16,
                        color: AppThemes.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              );
            } else if (_planDetails == null) {
              return Container(
                padding: const EdgeInsets.all(40),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red.shade300,
                    ),
                    const SizedBox(height: 24),
                    Text(
                      'Failed to load plan details',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppThemes.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Please check your connection and try again.',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppThemes.textSecondaryColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 32),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _fetchPlanDetails,
                        icon: const Icon(Icons.refresh),
                        label: const Text('Retry'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppThemes.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            } else {
              // Pass error message to payment form if available
              String? errorMessage;
              if (state is AuthPaymentRequired && state.errorMessage != null) {
                errorMessage = state.errorMessage;
              }
              
              return PaymentForm(
                planName: _planDetails!.displayName,
                planAmount: _planDetails!.pricing.basePrice,
                planType: _planDetails!.type,
                initialPhoneNumber: widget.user.phone,
                isProcessingPayment: state is AuthLoading,
                errorMessage: errorMessage,
              );
            }
          },
        ),
      ),
    );
  }

  /// Get display name for plan
  String _getPlanDisplayName(String planId) {
    switch (planId.toLowerCase()) {
      case 'trial':
        return 'Trial Plan';
      case 'basic':
        return 'Basic Plan';
      case 'premium':
        return 'Premium Plan';
      case 'enterprise':
        return 'Enterprise Plan';
      default:
        return 'Selected Plan';
    }
  }

  /// Get plan amount based on plan ID
  /// These are fallback values that should match the backend dynamic plan pricing
  /// These values should only be used if the plan details fail to load from the backend
  double _getPlanAmount(String planId) {
    switch (planId.toLowerCase()) {
      case 'trial':
        return 0.0;
      case 'monthly':
        return 0.01;  // Matches backend createDefaultPlans monthly pricing
      case 'yearly':
        return 90.0;  // Matches backend createDefaultPlans yearly pricing
      // Legacy fallbacks (should be removed once all plans use type-based IDs)
      case 'basic':
        return 0.01;  // Map to monthly plan
      case 'premium':
        return 90.0;  // Map to yearly plan
      case 'enterprise':
        return 99.99; // Keep as is for now
      default:
        return 0.0;
    }
  }
} 