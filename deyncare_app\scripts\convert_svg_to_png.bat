@echo off
REM Convert Deyncare SVG to high-quality PNG app icon
REM Usage: scripts\convert_svg_to_png.bat

echo 🎨 Converting Deyncare SVG to high-quality PNG app icon...

REM Check if input SVG exists
set SVG_FILE=assets\icons\deyncare_svg.svg
set OUTPUT_FILE=assets\icons\deyncare_app_icon_1024.png

if not exist "%SVG_FILE%" (
    echo ❌ Error: %SVG_FILE% not found!
    echo    Please ensure the SVG file exists in the assets\icons\ directory.
    pause
    exit /b 1
)

REM Method 1: Try using Inkscape (if available)
where inkscape >nul 2>nul
if %errorlevel% == 0 (
    echo ✅ Using Inkscape to convert SVG to PNG...
    inkscape --export-type=png ^
             --export-filename="%OUTPUT_FILE%" ^
             --export-width=1024 ^
             --export-height=1024 ^
             --export-background=white ^
             --export-background-opacity=1 ^
             "%SVG_FILE%"
    
    if %errorlevel% == 0 (
        echo ✅ Successfully created %OUTPUT_FILE% using Inkscape!
        echo 📐 Size: 1024x1024 pixels
        echo 🎯 Next step: Run 'flutter pub run flutter_launcher_icons:main'
        pause
        exit /b 0
    ) else (
        echo ❌ Inkscape conversion failed, trying alternative methods...
    )
)

REM Method 2: Try using ImageMagick (if available)
where magick >nul 2>nul
if %errorlevel% == 0 (
    echo ✅ Using ImageMagick to convert SVG to PNG...
    magick convert -background white ^
                   -density 300 ^
                   -resize 1024x1024 ^
                   "%SVG_FILE%" ^
                   "%OUTPUT_FILE%"
    
    if %errorlevel% == 0 (
        echo ✅ Successfully created %OUTPUT_FILE% using ImageMagick!
        echo 📐 Size: 1024x1024 pixels
        echo 🎯 Next step: Run 'flutter pub run flutter_launcher_icons:main'
        pause
        exit /b 0
    ) else (
        echo ❌ ImageMagick conversion failed...
    )
)

REM Method 3: Instructions for manual conversion
echo.
echo ❌ No suitable conversion tools found on this system.
echo.
echo 📋 Please install one of the following tools:
echo.
echo 🔧 Option 1: Install Inkscape
echo    • Download from https://inkscape.org/
echo    • Add to PATH or run from installation directory
echo.
echo 🔧 Option 2: Install ImageMagick
echo    • Download from https://imagemagick.org/
echo    • Choose the Windows installer
echo.
echo 🔧 Option 3: Use online converter
echo    • Go to https://convertio.co/svg-png/
echo    • Upload %SVG_FILE%
echo    • Set size to 1024x1024 pixels
echo    • Download and save as %OUTPUT_FILE%
echo.
echo 🎯 After creating the PNG file, run:
echo    flutter pub run flutter_launcher_icons:main
echo.
pause
exit /b 1 