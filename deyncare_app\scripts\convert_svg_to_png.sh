#!/bin/bash

# Convert Deyncare SVG to high-quality PNG app icon
# Usage: ./scripts/convert_svg_to_png.sh

echo "🎨 Converting Deyncare SVG to high-quality PNG app icon..."

# Check if input SVG exists
SVG_FILE="assets/icons/deyncare_svg.svg"
OUTPUT_FILE="assets/icons/deyncare_app_icon_1024.png"

if [ ! -f "$SVG_FILE" ]; then
    echo "❌ Error: $SVG_FILE not found!"
    echo "   Please ensure the SVG file exists in the assets/icons/ directory."
    exit 1
fi

# Method 1: Try using Inkscape (if available)
if command -v inkscape &> /dev/null; then
    echo "✅ Using Inkscape to convert SVG to PNG..."
    inkscape --export-type=png \
             --export-filename="$OUTPUT_FILE" \
             --export-width=1024 \
             --export-height=1024 \
             --export-background=white \
             --export-background-opacity=1 \
             "$SVG_FILE"
    
    if [ $? -eq 0 ]; then
        echo "✅ Successfully created $OUTPUT_FILE using Inkscape!"
        echo "📐 Size: 1024x1024 pixels"
        echo "🎯 Next step: Run 'flutter pub run flutter_launcher_icons:main'"
        exit 0
    else
        echo "❌ Inkscape conversion failed, trying alternative methods..."
    fi
fi

# Method 2: Try using ImageMagick (if available)
if command -v convert &> /dev/null; then
    echo "✅ Using ImageMagick to convert SVG to PNG..."
    convert -background white \
            -density 300 \
            -resize 1024x1024 \
            "$SVG_FILE" \
            "$OUTPUT_FILE"
    
    if [ $? -eq 0 ]; then
        echo "✅ Successfully created $OUTPUT_FILE using ImageMagick!"
        echo "📐 Size: 1024x1024 pixels"
        echo "🎯 Next step: Run 'flutter pub run flutter_launcher_icons:main'"
        exit 0
    else
        echo "❌ ImageMagick conversion failed..."
    fi
fi

# Method 3: Instructions for manual conversion
echo ""
echo "❌ No suitable conversion tools found on this system."
echo ""
echo "📋 Please install one of the following tools:"
echo ""
echo "🔧 Option 1: Install Inkscape"
echo "   • Windows: Download from https://inkscape.org/"
echo "   • Mac: brew install inkscape"
echo "   • Linux: sudo apt install inkscape"
echo ""
echo "🔧 Option 2: Install ImageMagick"
echo "   • Windows: Download from https://imagemagick.org/"
echo "   • Mac: brew install imagemagick"
echo "   • Linux: sudo apt install imagemagick"
echo ""
echo "🔧 Option 3: Use online converter"
echo "   • Go to https://convertio.co/svg-png/"
echo "   • Upload $SVG_FILE"
echo "   • Set size to 1024x1024 pixels"
echo "   • Download and save as $OUTPUT_FILE"
echo ""
echo "🎯 After creating the PNG file, run:"
echo "   flutter pub run flutter_launcher_icons:main"

exit 1 