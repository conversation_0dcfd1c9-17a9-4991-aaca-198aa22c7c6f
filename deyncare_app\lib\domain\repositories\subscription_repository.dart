import 'package:dartz/dartz.dart';
import 'package:deyncare_app/core/errors/failures.dart';
import 'package:deyncare_app/domain/models/subscription.dart';
import 'package:deyncare_app/domain/models/plan.dart';

/// Repository interface for subscription operations
abstract class SubscriptionRepository {
  /// Get current subscription for the authenticated shop
  Future<Either<Failure, Subscription>> getCurrentSubscription();

  /// Get all available plans for upgrade
  Future<Either<Failure, List<Plan>>> getAvailablePlans();

  /// Request subscription upgrade (sends email to SuperAdmin)
  Future<Either<Failure, Map<String, dynamic>>> requestUpgrade({
    required String planType,
    String? message,
  });

  /// Change subscription plan (for existing paid subscriptions)
  Future<Either<Failure, Subscription>> changePlan({
    required String planId,
    String? planType,
    bool prorated = true,
    String? paymentMethod,
    Map<String, dynamic>? paymentDetails,
  });

  /// Cancel subscription
  Future<Either<Failure, Subscription>> cancelSubscription({
    String? reason,
    String? feedback,
    bool immediateEffect = false,
  });

  /// Update auto-renewal settings
  Future<Either<Failure, Subscription>> updateAutoRenewal({
    required bool autoRenew,
  });

  /// Renew subscription
  Future<Either<Failure, Subscription>> renewSubscription({
    required String paymentMethod,
    required String transactionId,
    double? amount,
    String currency = 'USD',
    String? notes,
  });

  /// Pay with EVC Plus
  Future<Either<Failure, Map<String, dynamic>>> payWithEvc({
    required String subscriptionId,
    required String phone,
    required double amount,
    String? planType,
  });

  /// Record offline payment
  Future<Either<Failure, Map<String, dynamic>>> recordOfflinePayment({
    required String subscriptionId,
    required double amount,
    required String method,
    String? payerName,
    String? payerPhone,
    String? notes,
    String? planType,
    String? receiptPath,
  });

  /// Get available payment methods
  Future<Either<Failure, List<String>>> getAvailablePaymentMethods();
} 