import 'dart:async';
import 'package:flutter/material.dart';
import 'package:deyncare_app/core/managers/connectivity_manager.dart';
import 'package:get_it/get_it.dart';

/// A mixin that adds connectivity awareness to any service class
/// 
/// This mixin provides helper methods for executing operations that should be
/// connectivity-aware, with automatic retries when network is restored.
mixin ConnectivityAwareService {
  // Get the connectivity manager from service locator
  ConnectivityManager get _connectivityManager => 
      GetIt.instance<ConnectivityManager>();
  
  /// Execute an operation with connectivity awareness
  /// 
  /// If the device is offline, this will:
  /// 1. Show appropriate error message
  /// 2. Wait for connectivity to be restored
  /// 3. Automatically retry the operation
  /// 
  /// [operation] The async operation to execute
  /// [onNetworkError] Optional callback when network error occurs
  /// [retryAutomatically] Whether to retry automatically when connection is restored
  /// [maxRetries] Maximum number of retries (null for unlimited)
  Future<T> executeWithConnectivity<T>({
    required Future<T> Function() operation,
    Function(Exception)? onNetworkError,
    bool retryAutomatically = true,
    int? maxRetries,
  }) async {
    // Initialize the connectivity manager if needed
    await _connectivityManager.initialize();
    
    int retryCount = 0;
    
    try {
      // Check if device is currently online
      if (!_connectivityManager.isConnected()) {
        // If not online and we should retry, schedule retry
        if (retryAutomatically) {
          debugPrint('Network offline, scheduling retry when back online');
          final result = await _connectivityManager.scheduleRetryWhenOnline(operation);
          return result;
        } else {
          // Throw exception if we shouldn't retry
          throw Exception('Network offline');
        }
      }
      
      // If we're online, execute the operation directly
      return await operation();
    } catch (e) {
      // Handle network errors
      debugPrint('Network error: $e');
      
      if (onNetworkError != null) {
        onNetworkError(e as Exception);
      }
      
      // Check if we should retry
      if (retryAutomatically && (maxRetries == null || retryCount < maxRetries)) {
        retryCount++;
        debugPrint('Retrying operation (attempt $retryCount)');
        return await _connectivityManager.scheduleRetryWhenOnline(operation);
      }
      
      // Re-throw if we can't handle it
      rethrow;
    }
  }
  
  /// Queue an operation to be executed when device is back online
  /// 
  /// This is useful for operations that should be performed later when
  /// connectivity is restored.
  Future<T> queueForOnline<T>(Future<T> Function() operation) {
    return _connectivityManager.scheduleRetryWhenOnline(operation);
  }
  
  /// Check if device is currently connected
  bool isConnected() {
    return _connectivityManager.isConnected();
  }
  
  /// Get the current network state
  ConnectivityState get networkState => _connectivityManager.currentState;
  
  /// Stream of network state changes
  Stream<ConnectivityState> get networkStateStream => 
      _connectivityManager.stateStream;
}
