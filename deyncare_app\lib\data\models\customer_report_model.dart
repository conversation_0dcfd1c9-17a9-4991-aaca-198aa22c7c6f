/// Customer Report Data Model - matches the report API response structure
/// This model is specifically designed for the /reports/customers/data endpoint
/// which returns a simplified customer structure optimized for reporting
class CustomerReportData {
  final String customerId;
  final String customerName;
  final String customerType;
  final String phone;
  final String email;
  final String address;
  final String riskLevel;
  final DateTime createdAt;
  final double totalDebt;
  final double outstandingDebt;
  final double paidAmount;
  final String paymentStatus;
  final String registrationDate;
  final int daysSinceRegistration;

  const CustomerReportData({
    required this.customerId,
    required this.customerName,
    required this.customerType,
    required this.phone,
    required this.email,
    required this.address,
    required this.riskLevel,
    required this.createdAt,
    required this.totalDebt,
    required this.outstandingDebt,
    required this.paidAmount,
    required this.paymentStatus,
    required this.registrationDate,
    required this.daysSinceRegistration,
  });

  /// Factory constructor with null safety for API response parsing
  /// Provides sensible defaults for any null values from the API
  factory CustomerReportData.fromJson(Map<String, dynamic> json) {
    return CustomerReportData(
      customerId: json['customerId'] as String? ?? '',
      customerName: json['CustomerName'] as String? ?? '',
      customerType: json['CustomerType'] as String? ?? '',
      phone: json['phone'] as String? ?? '',
      email: json['email'] as String? ?? 'N/A',
      address: json['address'] as String? ?? 'N/A',
      riskLevel: json['riskLevel'] as String? ?? 'Not Assessed',
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt'] as String)
          : DateTime.now(),
      totalDebt: (json['totalDebt'] as num?)?.toDouble() ?? 0.0,
      outstandingDebt: (json['outstandingDebt'] as num?)?.toDouble() ?? 0.0,
      paidAmount: (json['paidAmount'] as num?)?.toDouble() ?? 0.0,
      paymentStatus: json['paymentStatus'] as String? ?? 'Unknown',
      registrationDate: json['registrationDate'] as String? ?? '',
      daysSinceRegistration: (json['daysSinceRegistration'] as num?)?.toInt() ?? 0,
    );
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'customerId': customerId,
      'CustomerName': customerName,
      'CustomerType': customerType,
      'phone': phone,
      'email': email,
      'address': address,
      'riskLevel': riskLevel,
      'createdAt': createdAt.toIso8601String(),
      'totalDebt': totalDebt,
      'outstandingDebt': outstandingDebt,
      'paidAmount': paidAmount,
      'paymentStatus': paymentStatus,
      'registrationDate': registrationDate,
      'daysSinceRegistration': daysSinceRegistration,
    };
  }

  /// Create a copy with modified fields
  CustomerReportData copyWith({
    String? customerId,
    String? customerName,
    String? customerType,
    String? phone,
    String? email,
    String? address,
    String? riskLevel,
    DateTime? createdAt,
    double? totalDebt,
    double? outstandingDebt,
    double? paidAmount,
    String? paymentStatus,
    String? registrationDate,
    int? daysSinceRegistration,
  }) {
    return CustomerReportData(
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerType: customerType ?? this.customerType,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      riskLevel: riskLevel ?? this.riskLevel,
      createdAt: createdAt ?? this.createdAt,
      totalDebt: totalDebt ?? this.totalDebt,
      outstandingDebt: outstandingDebt ?? this.outstandingDebt,
      paidAmount: paidAmount ?? this.paidAmount,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      registrationDate: registrationDate ?? this.registrationDate,
      daysSinceRegistration: daysSinceRegistration ?? this.daysSinceRegistration,
    );
  }

  @override
  String toString() {
    return 'CustomerReportData(customerId: $customerId, customerName: $customerName, customerType: $customerType, phone: $phone, email: $email, riskLevel: $riskLevel, totalDebt: $totalDebt, outstandingDebt: $outstandingDebt, paymentStatus: $paymentStatus)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CustomerReportData &&
        other.customerId == customerId &&
        other.customerName == customerName &&
        other.customerType == customerType &&
        other.phone == phone &&
        other.email == email &&
        other.address == address &&
        other.riskLevel == riskLevel &&
        other.createdAt == createdAt &&
        other.totalDebt == totalDebt &&
        other.outstandingDebt == outstandingDebt &&
        other.paidAmount == paidAmount &&
        other.paymentStatus == paymentStatus &&
        other.registrationDate == registrationDate &&
        other.daysSinceRegistration == daysSinceRegistration;
  }

  @override
  int get hashCode {
    return customerId.hashCode ^
        customerName.hashCode ^
        customerType.hashCode ^
        phone.hashCode ^
        email.hashCode ^
        address.hashCode ^
        riskLevel.hashCode ^
        createdAt.hashCode ^
        totalDebt.hashCode ^
        outstandingDebt.hashCode ^
        paidAmount.hashCode ^
        paymentStatus.hashCode ^
        registrationDate.hashCode ^
        daysSinceRegistration.hashCode;
  }
}
