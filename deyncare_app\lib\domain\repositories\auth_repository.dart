import 'dart:io';

import 'package:deyncare_app/domain/models/auth_token.dart';
import 'package:deyncare_app/domain/models/registration_progress.dart';
import 'package:deyncare_app/domain/models/user.dart';

/// Repository interface for authentication operations.
///
/// This abstract class defines the contract for authentication-related
/// functionalities, ensuring a consistent API for the data layer to implement.
abstract class AuthRepository {
  /// Checks if a user is currently logged in.
  ///
  /// Returns `true` if a valid session exists, otherwise `false`.
  Future<bool> isLoggedIn();

  /// Retrieves the currently authenticated user.
  ///
  /// Returns the [User] object if logged in, otherwise `null`.
  Future<User?> getCurrentUser();

  /// Logs in a user with their email and password.
  ///
  /// On success, returns a tuple containing the [User] and [AuthToken].
  Future<(User, AuthToken)> login(String email, String password);

  /// Registers a new shop owner account.
  ///
  /// This method handles the full registration flow, including user and shop creation.
  /// On success, returns a tuple with the new [User] and their [RegistrationProgress].
  Future<(User, RegistrationProgress)> register({
    required String fullName,
    required String email,
    required String phone,
    required String password,
    required String shopName,
    required String shopAddress,
    File? shopLogo,
    String planType = 'trial',
    String paymentMethod = 'offline',
    bool initialPaid = false,
    String? discountCode,
  });

  /// Creates a new employee account for the current admin's shop.
  ///
  /// Requires admin privileges.
  Future<User> createEmployee({
    required String fullName,
    required String email,
    required String phone,
    String? password,
    bool generatePassword = true,
    List<String>? permissions,
    String? position,
    String? note,
  });

  /// Verifies a user's email with a verification code.
  ///
  /// On success, returns the updated [User] and [RegistrationProgress].
  Future<(User, RegistrationProgress)> verifyEmail(
      String email, String verificationCode);

  /// Resends the email verification code.
  Future<void> resendVerification(String email);

  /// Checks if an email address is already registered in the system.
  Future<bool> checkEmailExists(String email);

  /// Initiates the password reset process for a given email.
  Future<void> forgotPassword(String email);

  /// Resets the user's password using a reset token.
  Future<void> resetPassword({
    required String token,
    required String newPassword,
    required String confirmPassword,
  });

  /// Refreshes the authentication token using a refresh token.
  Future<AuthToken> refreshToken(String refreshToken);

  /// Logs out the current user from the current device.
  Future<void> logout();

  /// Logs out the user from all devices.
  Future<void> logoutAll();

  /// Changes the password for the currently logged-in user.
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  });

  /// Processes a payment for a subscription plan.
  ///
  /// On success, returns the updated [User] and [RegistrationProgress].
  Future<(User, RegistrationProgress)> processPayment({
    required String userId,
    required String shopId,
    required String planId,
    required String paymentMethod,
    required String phoneNumber,
    double? amount,
    String? discountCode,
  });

  /// Fetches the list of available payment methods from the backend.
  ///
  /// The [context] parameter (e.g., 'subscription', 'pos') determines which
  /// set of payment methods to retrieve.
  Future<List<String>> getAvailablePaymentMethods(String context);
}

