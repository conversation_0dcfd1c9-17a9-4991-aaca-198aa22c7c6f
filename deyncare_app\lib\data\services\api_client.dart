import 'dart:io';
import 'package:dio/dio.dart';

import 'package:deyncare_app/core/config/env_config.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:deyncare_app/data/network/clients/dio_client.dart';
import 'package:deyncare_app/data/network/handlers/connectivity_handler.dart';
import 'package:deyncare_app/data/network/handlers/error_handler.dart';
import 'package:deyncare_app/data/network/handlers/response_handler.dart';
import 'package:deyncare_app/data/network/handlers/retry_handler.dart';
import 'package:deyncare_app/data/network/token/token_manager.dart';

/// API client for handling HTTP requests to the DeynCare API
/// with error handling, token management, and retry capabilities
/// 
/// This class maintains backward compatibility while using the new
/// modular network components under the hood
class ApiClient {
  // Internal Dio client that handles the actual HTTP requests
  late final DioClient _dioClient;
  
  // Token manager for authentication
  late final TokenManager _tokenManager;
  
  // Connectivity handler for network status
  late final ConnectivityHandler _connectivityHandler;
  
  // Retry handler for network failures
  late final RetryHandler _retryHandler;
  
  // Base API URL is now dynamically obtained from EnvConfig
  static String get baseUrl => EnvConfig.baseApiUrl;
  
  // Device identifier (for multi-device support)
  final String _deviceId = 'deyncare_mobile_${DateTime.now().millisecondsSinceEpoch}';
  
  // Error handling retry options
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  static const int initialBackoffMs = 500;
  
  ApiClient({Dio? dio, TokenManager? tokenManager}) {
    // Initialize token manager
    _tokenManager = tokenManager ?? TokenManager();
    
    // Initialize connectivity handler
    _connectivityHandler = ConnectivityHandler();
    
    // Initialize retry handler
    _retryHandler = RetryHandler(
      maxRetries: maxRetries,
      initialDelay: Duration(milliseconds: initialBackoffMs),
    );
    
    // Initialize DioClient
    _dioClient = DioClient(
      dio: dio,
      tokenManager: _tokenManager,
      connectivityHandler: _connectivityHandler,
      retryHandler: _retryHandler,
      deviceId: _deviceId,
    );
    
    // Initialize right away
    init();
  }
  
  /// Initialize the API client with interceptors and connectivity
  Future<void> init() async {
    await _dioClient.init();
  }
  
  /// Get the current access token from secure storage
  Future<String?> getAccessToken() async {
    return await _tokenManager.getAccessToken();
  }
  
  /// Get the current refresh token from secure storage
  Future<String?> getRefreshToken() async {
    return await _tokenManager.getRefreshToken();
  }
  
  /// Save tokens to secure storage
  Future<void> saveTokens({required String accessToken, required String refreshToken}) async {
    await _tokenManager.saveTokens(accessToken: accessToken, refreshToken: refreshToken);
  }
  
  /// Clear all tokens from secure storage
  Future<void> deleteTokens() async {
    await _tokenManager.deleteTokens();
  }
  
  /// Alias for deleteTokens for backward compatibility
  Future<void> clearTokens() async {
    return _tokenManager.clearTokens();
  }
  
  /// GET request with error handling and retry mechanism
  Future<dynamic> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
    bool retry = true,
  }) async {
    return await _dioClient.get(
      path,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onReceiveProgress: onReceiveProgress,
      retry: retry,
    );
  }
  
  /// POST request with error handling and retry mechanism
  Future<dynamic> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    bool retry = true,
  }) async {
    return await _dioClient.post(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
      retry: retry,
    );
  }

  /// PUT request with error handling and retry mechanism
  Future<dynamic> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    bool retry = true,
  }) async {
    return await _dioClient.put(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
      retry: retry,
    );
  }

  /// DELETE request with error handling and retry mechanism
  Future<dynamic> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    bool retry = true,
  }) async {
    return await _dioClient.delete(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      retry: retry,
    );
  }
  
  /// Upload file to the server
  Future<dynamic> uploadFile(
    String path, {
    required File file,
    required String fieldName,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    bool retry = true,
  }) async {
    return await _dioClient.uploadFile(
      path,
      file: file,
      fieldName: fieldName,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      retry: retry,
    );
  }
  
  /// Wait for connectivity if device is offline
  /// This is kept for backward compatibility
  Future<void> _waitForConnectivityIfOffline() async {
    await _connectivityHandler.waitForConnectivityIfOffline();
  }
  
  /// Process response according to DeynCare backend format
  /// This is kept for backward compatibility
  dynamic _processResponse(Response response) {
    return ResponseHandler.processResponse(response);
  }
  
  /// Handle common Dio errors with user-friendly messages
  /// This is kept for backward compatibility
  ApiException _handleDioError(DioException e) {
    return ErrorHandler.handleDioError(e);
  }
  
  /// Retry a request with exponential backoff
  /// This is kept for backward compatibility
  Future<T> _retryRequest<T>(Future<T> Function() requestFunc) async {
    return _retryHandler.retryRequest(requestFunc);
  }
}