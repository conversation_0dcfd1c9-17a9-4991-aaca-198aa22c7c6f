import 'package:dartz/dartz.dart';
import 'package:deyncare_app/core/errors/failures.dart';
import 'package:deyncare_app/domain/repositories/debt_repository.dart';
import 'package:deyncare_app/domain/models/debt.dart';
import 'package:deyncare_app/domain/models/payment.dart';
import 'package:deyncare_app/core/utils/business_validation.dart';

/// Use case for adding payment to existing debt
/// Matches POST /api/debts/:debtId/payments endpoint
class AddPaymentToDebtUseCase {
  final DebtRepository _repository;

  AddPaymentToDebtUseCase(this._repository);

  /// Execute the use case to add payment to debt
  Future<Either<Failure, Debt>> execute({
    required String debtId,
    required double amount,
    required PaymentMethod paymentMethod,
    String? notes,
    DateTime? paymentDate,
  }) async {
    // Validate debt ID (matches backend validation)
    final debtIdValidation = BusinessValidation.validateDebtId(debtId);
    if (!debtIdValidation.isValid) {
      return Left(ValidationFailure(message: debtIdValidation.errorMessage!));
    }

    // Validate payment amount (matches backend validation)
    final paymentAmountValidation = BusinessValidation.validatePaymentAmount(amount);
    if (!paymentAmountValidation.isValid) {
      return Left(ValidationFailure(message: paymentAmountValidation.errorMessage!));
    }

    // Validate payment date (matches backend validation)
    final paymentDateValidation = BusinessValidation.validatePaymentDate(paymentDate);
    if (!paymentDateValidation.isValid) {
      return Left(ValidationFailure(message: paymentDateValidation.errorMessage!));
    }

    // Validate payment notes (matches backend validation)
    final notesValidation = BusinessValidation.validatePaymentNotes(notes);
    if (!notesValidation.isValid) {
      return Left(ValidationFailure(message: notesValidation.errorMessage!));
    }

    // Use current date if no payment date provided
    final effectivePaymentDate = paymentDate ?? DateTime.now();

    return await _repository.addPaymentToDebt(
      debtId: debtId.trim(),
      amount: amount,
      paymentMethod: paymentMethod,
      notes: notes?.trim(),
      paymentDate: effectivePaymentDate,
    );
  }
} 