import 'package:dartz/dartz.dart';
import 'package:deyncare_app/core/errors/failures.dart';
import 'package:deyncare_app/domain/repositories/subscription_repository.dart';

class RequestUpgradeParams {
  final String planType;
  final String? message;

  const RequestUpgradeParams({
    required this.planType,
    this.message,
  });
}

class RequestUpgradeUseCase {
  final SubscriptionRepository repository;

  RequestUpgradeUseCase(this.repository);

  Future<Either<Failure, Map<String, dynamic>>> call(RequestUpgradeParams params) async {
    return await repository.requestUpgrade(
      planType: params.planType,
      message: params.message,
    );
  }
} 