import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_strings.dart';
import 'package:deyncare_app/core/routes/app_router.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_state.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_event.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/widgets/modals/auth/account_suspension_modal.dart';

/// The splash screen shown when the app starts
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _fadeController;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoRotationAnimation;
  late Animation<double> _textSlideAnimation;
  late Animation<double> _textFadeAnimation;
  late Animation<double> _backgroundFadeAnimation;

  // Remove hardcoded colors - use theme colors instead
  // These methods will get colors from the current theme
  Color get primaryColor => Theme.of(context).primaryColor;
  Color get backgroundColor => Theme.of(context).scaffoldBackgroundColor;
  Color get textPrimaryColor => Theme.of(context).textTheme.bodyLarge?.color ?? Theme.of(context).colorScheme.onBackground;
  Color get textSecondaryColor => Theme.of(context).textTheme.bodyMedium?.color ?? Theme.of(context).colorScheme.onBackground.withOpacity(0.7);

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimationSequence();
  }

  void _setupAnimations() {
    // Logo animations (scale + subtle rotation)
    _logoController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    // Text animations (slide + fade)
    _textController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Overall fade animation
    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    // Logo scale with elastic effect
    _logoScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    // Subtle logo rotation
    _logoRotationAnimation = Tween<double>(
      begin: -0.1,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.easeOutBack,
    ));

    // Text slide up animation
    _textSlideAnimation = Tween<double>(
      begin: 50.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeOutCubic,
    ));

    // Text fade in animation
    _textFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeIn,
    ));

    // Background fade animation
    _backgroundFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));
  }

  void _startAnimationSequence() async {
    // Start background fade immediately
    _fadeController.forward();
    
    // Wait a bit, then start logo animation
    await Future.delayed(const Duration(milliseconds: 300));
    if (mounted) {
      _logoController.forward();
    }
    
    // Start text animation after logo starts
    await Future.delayed(const Duration(milliseconds: 600));
    if (mounted) {
      _textController.forward();
    }

    // Navigate after animations complete + additional delay for professional timing (3 seconds total)
    await Future.delayed(const Duration(milliseconds: 2500));
    if (mounted) {
      _navigateToNextScreen();
    }
  }

  void _navigateToNextScreen() {
    // Navigation will be handled by BlocListener in the build method
    // This method is kept for timing the animations
  }

  void _showSuspensionModal(BuildContext context, AuthAccountSuspended state) {
    AccountSuspensionModalHelper.showSuspensionModal(
      context: context,
      reason: state.reason,
      suspendedAt: state.suspendedAt,
      suspendedBy: state.suspendedBy,
      countdown: state.redirectCountdown,
      onRedirect: () {
        Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
      },
    );
  }

  void _showRoleRestrictionModal(BuildContext context, AuthRoleRestricted state) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Access Restricted'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(state.message),
            const SizedBox(height: 16),
            Text(
              'Redirecting to login in ${state.redirectCountdown} seconds',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
            },
            child: const Text('Go to Login'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    final logoSize = isTablet ? 120.0 : 100.0;

    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        // Handle auth state changes after animations complete
        if (state is AuthAuthenticated) {
          // User is authenticated, navigate to dashboard
          AppRouter.navigateToDashboard(context);
        } else if (state is AuthUnauthenticated) {
          // User not authenticated, navigate to login
          AppRouter.navigateToLogin(context);
        } else if (state is AuthOnboardingRequired) {
          // Onboarding required, navigate to onboarding
          AppRouter.navigateToOnboarding(context);
        } else if (state is AuthAccountSuspended) {
          // User account is suspended, show suspension modal
          _showSuspensionModal(context, state);
        } else if (state is AuthRoleRestricted) {
          // User role not allowed, show role restriction modal
          _showRoleRestrictionModal(context, state);
        } else if (state is AuthEmailVerificationPending) {
          // Email verification needed
          Navigator.of(context).pushNamedAndRemoveUntil('/verification', (route) => false);
        } else if (state is AuthPaymentRequired) {
          // Payment required
          Navigator.of(context).pushNamedAndRemoveUntil('/payment', (route) => false);
        } else if (state is AuthFailure) {
          // Auth failure, navigate to login with error
          AppRouter.navigateToLogin(context);
        }
      },
      child: Scaffold(
        body: AnimatedBuilder(
        animation: _backgroundFadeAnimation,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  backgroundColor,
                  Theme.of(context).colorScheme.surface,
                  primaryColor.withOpacity(0.05 * _backgroundFadeAnimation.value),
                ],
              ),
            ),
            child: SafeArea(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Animated Logo with SVG
                    AnimatedBuilder(
                      animation: Listenable.merge([_logoScaleAnimation, _logoRotationAnimation]),
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _logoScaleAnimation.value,
                          child: Transform.rotate(
                            angle: _logoRotationAnimation.value,
                            child: Container(
                              width: logoSize + 40,
                              height: logoSize + 40,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.white,
                                boxShadow: [
                                  BoxShadow(
                                    color: primaryColor.withOpacity(0.15),
                                    blurRadius: 25,
                                    offset: const Offset(0, 10),
                                    spreadRadius: 2,
                                  ),
                                ],
                              ),
                              child: Center(
                                child: SvgPicture.asset(
                                  'assets/icons/deyncare_svg.svg',
                                  width: logoSize,
                                  height: logoSize,
                                  fit: BoxFit.contain,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),

                    SizedBox(height: isTablet ? 50 : 40),

                    // Animated App Name "Deyncare"
                    AnimatedBuilder(
                      animation: Listenable.merge([_textSlideAnimation, _textFadeAnimation]),
                      builder: (context, child) {
                        return Transform.translate(
                          offset: Offset(0, _textSlideAnimation.value),
                          child: Opacity(
                            opacity: _textFadeAnimation.value,
                            child: Column(
                              children: [
                                // Main app name with letter spacing animation
                                _buildAnimatedText(
                                  'Deyncare',
                                  TextStyle(
                                    fontSize: isTablet ? 42 : 36,
                                    fontWeight: FontWeight.bold,
                                    color: textPrimaryColor,
                                    letterSpacing: 2.0,
                                  ),
                                ),
                                
                                SizedBox(height: isTablet ? 16 : 12),
                                
                                // Subtitle with fade animation
                                _buildAnimatedText(
                                  'DEBT & POS MANAGEMENT',
                                  TextStyle(
                                    fontSize: isTablet ? 16 : 14,
                                    fontWeight: FontWeight.w600,
                                    color: primaryColor,
                                    letterSpacing: 1.5,
                                  ),
                                  delay: 200,
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),

                    SizedBox(height: isTablet ? 80 : 60),

                    // Loading indicator
                    AnimatedBuilder(
                      animation: _textFadeAnimation,
                      builder: (context, child) {
                        return Opacity(
                          opacity: _textFadeAnimation.value,
                          child: Column(
                            children: [
                              SizedBox(
                                width: isTablet ? 35 : 30,
                                height: isTablet ? 35 : 30,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2.5,
                                  valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                                  backgroundColor: primaryColor.withOpacity(0.2),
                                ),
                              ),
                              
                              SizedBox(height: isTablet ? 24 : 20),
                              
                              Text(
                                'Loading...',
                                style: TextStyle(
                                  fontSize: isTablet ? 16 : 14,
                                  color: textSecondaryColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),

                    SizedBox(height: isTablet ? 60 : 50),

                    // Footer
                    AnimatedBuilder(
                      animation: _textFadeAnimation,
                      builder: (context, child) {
                        return Opacity(
                          opacity: _textFadeAnimation.value * 0.8,
                          child: Column(
                            children: [
                              Text(
                                'v1.0.0',
                                style: TextStyle(
                                  fontSize: isTablet ? 14 : 12,
                                  color: textSecondaryColor.withOpacity(0.7),
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              
                              const SizedBox(height: 8),
                              
                              Text(
                                'Powered by DeynCare Team',
                                style: TextStyle(
                                  fontSize: isTablet ? 14 : 12,
                                  color: primaryColor.withOpacity(0.8),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    ),
  );
}

  Widget _buildAnimatedText(String text, TextStyle style, {int delay = 0}) {
    return AnimatedBuilder(
      animation: _textController,
      builder: (context, child) {
        // Calculate delayed animation progress
        double progress = (_textController.value - (delay / 1000)).clamp(0.0, 1.0);
        
        return Opacity(
          opacity: progress,
          child: Transform.scale(
            scale: 0.8 + (0.2 * progress),
            child: Text(
              text,
              style: style,
              textAlign: TextAlign.center,
            ),
          ),
        );
      },
    );
  }
}
