import 'package:dio/dio.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:deyncare_app/data/services/connectivity_service.dart';

/// Interceptor to handle common error cases like network connectivity issues
class ErrorInterceptor extends Interceptor {
  final ConnectivityService _connectivityService;
  
  ErrorInterceptor({ConnectivityService? connectivityService})
    : _connectivityService = connectivityService ?? ConnectivityService();
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Check if device is offline
    if (err.type == DioExceptionType.connectionTimeout ||
        err.type == DioExceptionType.sendTimeout ||
        err.type == DioExceptionType.receiveTimeout ||
        err.type == DioExceptionType.connectionError) {
      // Return a custom offline error
      return handler.reject(
        DioException(
          requestOptions: err.requestOptions,
          error: ApiException(
            message: 'You appear to be offline. Please check your internet connection.',
            code: 'network_error',
            statusCode: 0,
            data: {'offlineRequest': true, 'originalError': err.toString()},
          ),
          type: err.type,
        ),
      );
    }
    
    // Handle server errors with friendly messages
    if (err.response?.statusCode != null && err.response!.statusCode! >= 500) {
      return handler.reject(
        DioException(
          requestOptions: err.requestOptions,
          error: ApiException(
            message: 'We\'re experiencing server issues. Please try again later.',
            code: 'server_error',
            statusCode: err.response?.statusCode,
            data: err.response?.data,
          ),
          response: err.response,
        ),
      );
    }
    
    // Pass the error to the next handler
    return handler.next(err);
  }
}
