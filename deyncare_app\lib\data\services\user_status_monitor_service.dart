import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import 'package:deyncare_app/domain/repositories/auth_repository.dart';
import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/data/network/token/token_manager.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';

/// Service for monitoring user status and detecting real-time changes
class UserStatusMonitorService {
  final AuthRepository _authRepository;
  final TokenManager _tokenManager;
  final Dio _dio;
  final Logger _logger = Logger();

  Timer? _periodicTimer;
  User? _currentUser;
  bool _isMonitoring = false;

  // Callbacks for status changes
  Function(User user)? onSuspended;
  Function(User user)? onRoleRestricted;
  Function()? onTokenExpired;

  UserStatusMonitorService({
    required AuthRepository authRepository,
    required TokenManager tokenManager,
    required Dio dio,
  })  : _authRepository = authRepository,
        _tokenManager = tokenManager,
        _dio = dio;

  /// Start monitoring user status
  Future<void> startMonitoring({
    Duration interval = const Duration(minutes: 5), // Check every 5 minutes
    Function(User user)? onSuspended,
    Function(User user)? onRoleRestricted,
    Function()? onTokenExpired,
  }) async {
    if (_isMonitoring) {
      _logger.w('UserStatusMonitorService: Already monitoring');
      return;
    }

    this.onSuspended = onSuspended;
    this.onRoleRestricted = onRoleRestricted;
    this.onTokenExpired = onTokenExpired;

    try {
      // Get current user
      _currentUser = await _authRepository.getCurrentUser();
      if (_currentUser == null) {
        _logger.w('UserStatusMonitorService: No current user found');
        return;
      }

      _isMonitoring = true;
      _logger.i('UserStatusMonitorService: Started monitoring for user ${_currentUser!.userId}');

      // Start periodic checking
      _periodicTimer = Timer.periodic(interval, (timer) {
        _checkUserStatus();
      });
    } catch (e) {
      _logger.e('UserStatusMonitorService: Failed to start monitoring: $e');
    }
  }

  /// Stop monitoring user status
  void stopMonitoring() {
    _periodicTimer?.cancel();
    _periodicTimer = null;
    _isMonitoring = false;
    _currentUser = null;
    _logger.i('UserStatusMonitorService: Stopped monitoring');
  }

  /// Manually check user status (can be called by UI events)
  Future<void> checkUserStatusNow() async {
    if (!_isMonitoring) {
      _logger.w('UserStatusMonitorService: Not monitoring, cannot check status');
      return;
    }
    await _checkUserStatus();
  }

  /// Internal method to check user status
  Future<void> _checkUserStatus() async {
    try {
      if (_currentUser == null) {
        _logger.w('UserStatusMonitorService: No current user to check');
        return;
      }

      _logger.d('UserStatusMonitorService: Checking status for user ${_currentUser!.userId}');

      // Get access token
      final accessToken = await _tokenManager.getAccessToken();
      if (accessToken == null) {
        _logger.w('UserStatusMonitorService: No access token available');
        onTokenExpired?.call();
        return;
      }

      // Make API call to get current user status
      final response = await _dio.get(
        '/api/users/profile', // Assuming this endpoint returns current user data
        options: Options(
          headers: {
            'Authorization': 'Bearer $accessToken',
          },
        ),
      );

      if (response.statusCode == 200) {
        final userData = response.data;
        final updatedUser = _parseUserFromResponse(userData);

        if (updatedUser != null) {
          await _compareAndHandleStatusChanges(updatedUser);
        }
      } else {
        _logger.w('UserStatusMonitorService: Unexpected status code: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        _logger.w('UserStatusMonitorService: Token expired or unauthorized');
        onTokenExpired?.call();
      } else {
        _logger.e('UserStatusMonitorService: Network error checking status: ${e.message}');
      }
    } catch (e) {
      _logger.e('UserStatusMonitorService: Error checking user status: $e');
    }
  }

  /// Parse user data from API response
  User? _parseUserFromResponse(Map<String, dynamic> userData) {
    try {
      // Extract the user data (assuming it's nested under 'user' key)
      final userMap = userData['user'] ?? userData;
      
      return User(
        userId: userMap['userId'] ?? '',
        fullName: userMap['fullName'] ?? '',
        email: userMap['email'] ?? '',
        phone: userMap['phone'],
        role: userMap['role'] ?? '',
        shopId: userMap['shopId'],
        status: userMap['status'] ?? 'active',
        permissions: userMap['permissions']?.cast<String>(),
        registrationStatus: userMap['registrationStatus'] ?? 'completed',
        isEmailVerified: userMap['isEmailVerified'] ?? false,
        isPaid: userMap['isPaid'] ?? false,
        emailVerifiedAt: userMap['emailVerifiedAt'] != null
            ? DateTime.parse(userMap['emailVerifiedAt'])
            : null,
        paymentCompletedAt: userMap['paymentCompletedAt'] != null
            ? DateTime.parse(userMap['paymentCompletedAt'])
            : null,
        verificationCode: userMap['verificationCode'],
        verificationCodeExpiresAt: userMap['verificationCodeExpiresAt'] != null
            ? DateTime.parse(userMap['verificationCodeExpiresAt'])
            : null,
        shopName: userMap['shopName'],
        shopStatus: userMap['shopStatus'],
        isShopActive: userMap['isShopActive'] ?? false,
        // Suspension fields
        isSuspended: userMap['isSuspended'] ?? false,
        suspensionReason: userMap['suspensionReason'],
        suspendedAt: userMap['suspendedAt'] != null
            ? DateTime.parse(userMap['suspendedAt'])
            : null,
        suspendedBy: userMap['suspendedBy'],
      );
    } catch (e) {
      _logger.e('UserStatusMonitorService: Error parsing user data: $e');
      return null;
    }
  }

  /// Compare current and updated user status and handle changes
  Future<void> _compareAndHandleStatusChanges(User updatedUser) async {
    if (_currentUser == null) return;

    final previousUser = _currentUser!;
    
    // Check for suspension status change
    if (!previousUser.isAccountSuspended && updatedUser.isAccountSuspended) {
      _logger.w('UserStatusMonitorService: User ${updatedUser.userId} has been suspended');
      _currentUser = updatedUser;
      onSuspended?.call(updatedUser);
      return;
    }

    // Check for role restriction (though this is less likely to change)
    if (previousUser.canAccessMobileApp && !updatedUser.canAccessMobileApp) {
      _logger.w('UserStatusMonitorService: User ${updatedUser.userId} role no longer allows mobile access');
      _currentUser = updatedUser;
      onRoleRestricted?.call(updatedUser);
      return;
    }

    // Update current user with latest data
    _currentUser = updatedUser;
    
    _logger.d('UserStatusMonitorService: User status check completed - no issues detected');
  }

  /// Check if currently monitoring
  bool get isMonitoring => _isMonitoring;

  /// Get current monitored user
  User? get currentUser => _currentUser;

  /// Dispose resources
  void dispose() {
    stopMonitoring();
  }
} 