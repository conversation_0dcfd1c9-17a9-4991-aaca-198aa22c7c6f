import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/data/models/employee_model.dart';
import 'package:deyncare_app/data/services/user_management_service.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/injection_container.dart' as di;

/// Edit User Role Screen - Update employee permissions
class EditUserRoleScreen extends StatefulWidget {
  final EmployeeModel employee;

  const EditUserRoleScreen({
    super.key,
    required this.employee,
  });

  @override
  State<EditUserRoleScreen> createState() => _EditUserRoleScreenState();
}

class _EditUserRoleScreenState extends State<EditUserRoleScreen> {
  final _formKey = GlobalKey<FormState>();
  final _userManagementService = di.sl<UserManagementService>();

  // Loading state
  bool _isLoading = false;

  // Permission states - Customer Management
  bool _customerCreate = false;
  bool _customerUpdate = false;
  bool _customerView = false;
  bool _customerDelete = false;

  // Permission states - Debt Management
  bool _debtCreate = false;
  bool _debtUpdate = false;
  bool _debtView = false;
  bool _debtDelete = false;

  // Permission states - Report Management
  bool _reportGenerate = false;
  bool _reportView = false;
  bool _reportDelete = false;

  // Check all state
  bool _checkAll = false;

  @override
  void initState() {
    super.initState();
    _initializePermissions();
  }

  void _initializePermissions() {
    final visibility = widget.employee.visibility;

    // Customer Management
    _customerCreate = visibility?.customerManagement?.create ?? false;
    _customerUpdate = visibility?.customerManagement?.update ?? false;
    _customerView = visibility?.customerManagement?.view ?? false;
    _customerDelete = visibility?.customerManagement?.delete ?? false;

    // Debt Management
    _debtCreate = visibility?.debtManagement?.create ?? false;
    _debtUpdate = visibility?.debtManagement?.update ?? false;
    _debtView = visibility?.debtManagement?.view ?? false;
    _debtDelete = visibility?.debtManagement?.delete ?? false;

    // Report Management
    _reportGenerate = visibility?.reportManagement?.generate ?? false;
    _reportView = visibility?.reportManagement?.view ?? false;
    _reportDelete = visibility?.reportManagement?.delete ?? false;

    _updateCheckAllStatus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Edit ${widget.employee.fullName}'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        scrolledUnderElevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Employee Info Section
            _buildEmployeeInfoSection(),

            const SizedBox(height: 24),

            // Permissions Section
            _buildPermissionsSection(),

            const SizedBox(height: 32),

            // Action Buttons
            _buildActionButtons(),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildEmployeeInfoSection() {
    return CommonCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.person_outline, color: AppThemes.primaryColor),
              const SizedBox(width: 8),
              Text(
                'Employee Information',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppThemes.primaryColor,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Employee details (read-only)
          _buildInfoRow('Full Name', widget.employee.fullName),
          _buildInfoRow('Email', widget.employee.email),
          _buildInfoRow('Phone', widget.employee.phone ?? 'Not provided'),
          _buildInfoRow('Job Title/Position',
              widget.employee.userTitle ?? 'Not specified'),
          _buildInfoRow('Status', widget.employee.status),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
            ),
          ),
          const Text(': '),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionsSection() {
    return CommonCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.security_outlined,
                  color: AppThemes.primaryColor),
              const SizedBox(width: 8),
              Text(
                'Permissions',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppThemes.primaryColor,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Enable All Permissions Toggle
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppThemes.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border:
                  Border.all(color: AppThemes.primaryColor.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Switch(
                  value: _checkAll,
                  onChanged: _setAllPermissions,
                  activeColor: AppThemes.primaryColor,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Enable All Permissions',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                      Text(
                        'Grant all permissions',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const Divider(),

          // Customer Management Section
          _buildPermissionModule(
            'Customer Management',
            Icons.people_outline,
            [
              _buildPermissionSwitch('Create', _customerCreate,
                  (value) => setState(() => _customerCreate = value)),
              _buildPermissionSwitch('Update', _customerUpdate,
                  (value) => setState(() => _customerUpdate = value)),
              _buildPermissionSwitch('View', _customerView,
                  (value) => setState(() => _customerView = value)),
              _buildPermissionSwitch('Delete', _customerDelete,
                  (value) => setState(() => _customerDelete = value)),
            ],
          ),

          const Divider(),

          // Debt Management Section
          _buildPermissionModule(
            'Debt Management',
            Icons.account_balance_wallet_outlined,
            [
              _buildPermissionSwitch('Create', _debtCreate,
                  (value) => setState(() => _debtCreate = value)),
              _buildPermissionSwitch('Update', _debtUpdate,
                  (value) => setState(() => _debtUpdate = value)),
              _buildPermissionSwitch('View', _debtView,
                  (value) => setState(() => _debtView = value)),
              _buildPermissionSwitch('Delete', _debtDelete,
                  (value) => setState(() => _debtDelete = value)),
            ],
          ),

          const Divider(),

          // Report Management Section
          _buildPermissionModule(
            'Report Management',
            Icons.assessment_outlined,
            [
              _buildPermissionSwitch('Generate', _reportGenerate,
                  (value) => setState(() => _reportGenerate = value)),
              _buildPermissionSwitch('View', _reportView,
                  (value) => setState(() => _reportView = value)),
              _buildPermissionSwitch('Delete', _reportDelete,
                  (value) => setState(() => _reportDelete = value)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionModule(
      String title, IconData icon, List<Widget> permissions) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Row(
          children: [
            Icon(icon, size: 20, color: AppThemes.primaryColor),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppThemes.primaryColor,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...permissions,
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildPermissionSwitch(
      String title, bool value, ValueChanged<bool> onChanged) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          const SizedBox(width: 28), // Indent for alignment
          Switch(
            value: value,
            onChanged: (newValue) {
              onChanged(newValue);
              _updateCheckAllStatus();
            },
            activeColor: AppThemes.primaryColor,
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _updateEmployee,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppThemes.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text(
                    'Update Permissions',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          height: 48,
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: AppThemes.primaryColor),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Cancel',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppThemes.primaryColor,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _setAllPermissions(bool value) {
    setState(() {
      _checkAll = value;

      // Customer Management
      _customerCreate = value;
      _customerUpdate = value;
      _customerView = value;
      _customerDelete = value;

      // Debt Management
      _debtCreate = value;
      _debtUpdate = value;
      _debtView = value;
      _debtDelete = value;

      // Report Management
      _reportGenerate = value;
      _reportView = value;
      _reportDelete = value;
    });
  }

  void _updateCheckAllStatus() {
    final allSelected = _customerCreate &&
        _customerUpdate &&
        _customerView &&
        _customerDelete &&
        _debtCreate &&
        _debtUpdate &&
        _debtView &&
        _debtDelete &&
        _reportGenerate &&
        _reportView &&
        _reportDelete;

    setState(() => _checkAll = allSelected);
  }

  bool _hasAtLeastOnePermission() {
    return _customerCreate ||
        _customerUpdate ||
        _customerView ||
        _customerDelete ||
        _debtCreate ||
        _debtUpdate ||
        _debtView ||
        _debtDelete ||
        _reportGenerate ||
        _reportView ||
        _reportDelete;
  }

  void _updateEmployee() async {
    print('🔄 Update Employee button pressed');

    if (!_hasAtLeastOnePermission()) {
      print('❌ No permissions selected');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one permission'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    print('✅ Validation passed, updating employee...');
    setState(() => _isLoading = true);

    try {
      final request = UpdateEmployeeRequestModel(
        visibility: EmployeeVisibilityModel(
          customerManagement: ModulePermissionsModel(
            create: _customerCreate,
            update: _customerUpdate,
            view: _customerView,
            delete: _customerDelete,
          ),
          debtManagement: ModulePermissionsModel(
            create: _debtCreate,
            update: _debtUpdate,
            view: _debtView,
            delete: _debtDelete,
          ),
          reportManagement: ReportPermissionsModel(
            generate: _reportGenerate,
            view: _reportView,
            delete: _reportDelete,
          ),
        ),
      );

      await _userManagementService.updateEmployeePermissions(
        widget.employee.userId,
        request,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '${widget.employee.fullName} permissions updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true); // Return true to indicate success
      }
    } catch (e) {
      print('❌ Error updating employee: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update employee: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
