import 'package:equatable/equatable.dart';
import 'package:deyncare_app/domain/models/debt.dart';
import 'package:deyncare_app/domain/models/payment.dart';

/// Debt Events - aligned with backend use cases
abstract class DebtEvent extends Equatable {
  const DebtEvent();

  @override
  List<Object?> get props => [];
}

/// Load debts list with pagination and filters
class LoadDebts extends DebtEvent {
  final int page;
  final int limit;
  final DebtStatus? status;
  final RiskLevel? riskLevel;
  final String? customerType;
  final String? search;
  final String? sortBy;
  final bool ascending;

  const LoadDebts({
    this.page = 1,
    this.limit = 20,
    this.status,
    this.riskLevel,
    this.customerType,
    this.search,
    this.sortBy,
    this.ascending = false,
  });

  @override
  List<Object?> get props => [
        page,
        limit,
        status,
        riskLevel,
        customerType,
        search,
        sortBy,
        ascending,
      ];
}

/// Create new debt
class CreateDebt extends DebtEvent {
  final String customerId;
  final double debtAmount;
  final DateTime dueDate;
  final String? description;
  final double? paidAmount;
  final DateTime? paidDate;
  final PaymentMethod? paymentMethod;

  const CreateDebt({
    required this.customerId,
    required this.debtAmount,
    required this.dueDate,
    this.description,
    this.paidAmount,
    this.paidDate,
    this.paymentMethod,
  });

  @override
  List<Object?> get props => [
        customerId,
        debtAmount,
        dueDate,
        description,
        paidAmount,
        paidDate,
        paymentMethod,
      ];
}

/// Load debt details by ID
class LoadDebtDetails extends DebtEvent {
  final String debtId;

  const LoadDebtDetails(this.debtId);

  @override
  List<Object> get props => [debtId];
}

/// Update existing debt
class UpdateDebt extends DebtEvent {
  final String debtId;
  final double? amount;
  final DateTime? dueDate;
  final String? description;

  const UpdateDebt({
    required this.debtId,
    this.amount,
    this.dueDate,
    this.description,
  });

  @override
  List<Object?> get props => [debtId, amount, dueDate, description];
}

/// Delete debt
class DeleteDebt extends DebtEvent {
  final String debtId;

  const DeleteDebt(this.debtId);

  @override
  List<Object> get props => [debtId];
}

/// Add payment to debt with dual time tracking support
class AddPaymentToDebt extends DebtEvent {
  final String debtId;
  final double amount;
  final PaymentMethod paymentMethod;
  final String? notes;
  final DateTime? paymentDate;      // Official server time
  final DateTime? realPaymentDate; // Staff-recorded real payment time

  const AddPaymentToDebt({
    required this.debtId,
    required this.amount,
    required this.paymentMethod,
    this.notes,
    this.paymentDate,
    this.realPaymentDate,
  });

  @override
  List<Object?> get props => [
        debtId,
        amount,
        paymentMethod,
        notes,
        paymentDate,
        realPaymentDate,
      ];
}

/// Load debt statistics
class LoadDebtStats extends DebtEvent {
  const LoadDebtStats();
}

/// Refresh debts list
class RefreshDebts extends DebtEvent {
  const RefreshDebts();
}

/// Clear debt details
class ClearDebtDetails extends DebtEvent {
  const ClearDebtDetails();
} 