import 'package:flutter/foundation.dart';

/// Environment types for the application
enum AppEnvironment {
  development,
  staging, 
  production,
}

/// Environment configurations for the application
/// MODIFIED: Always uses production environment regardless of build mode
/// This ensures both local development and production builds use the same production API
class EnvConfig {
  /// COMMENTED OUT: Automatic environment determination based on Flutter build mode
  /// Now always returns production environment
  static AppEnvironment get _currentEnvironment {
    // FORCE PRODUCTION ENVIRONMENT FOR ALL BUILDS
    return AppEnvironment.production;
    
    // COMMENTED OUT: Original automatic switching logic
    // if (kDebugMode) {
    //   // Debug mode = Development environment
    //   return AppEnvironment.development;
    // } else if (kReleaseMode) {
    //   // Release mode = Production environment  
    //   return AppEnvironment.production;
    // } else {
    //   // Profile mode = Staging environment (for testing)
    //   return AppEnvironment.staging;
    // }
  }

  /// Base API URL - MODIFIED: Always uses production URL
  /// Both local development and production builds will use the same production API
  static String get baseApiUrl {
    // FORCE PRODUCTION URL FOR ALL ENVIRONMENTS
    return 'https://deyncare-backend.khanciye.com/api';
    
    // COMMENTED OUT: Original environment-based switching logic
    // switch (_currentEnvironment) {
    //   case AppEnvironment.development:
    //     // Use IP address instead of localhost when testing with physical devices
    //     // ******** is the special IP for Android emulator to reach host machine
    //     // For physical devices connected via USB, use your computer's actual IP address
    //     // Simple direct approach for development testing
    //     if (kIsWeb) {
    //       return 'http://localhost:5000/api';
    //     } else {
    //       // For physical devices connected via USB with ADB port forwarding
    //       // Use 127.0.0.1 which gets forwarded to the computer's localhost
    //       return 'http://127.0.0.1:5000/api';
    //     }
    //   case AppEnvironment.staging:
    //     // Staging environment (for testing releases)
    //     return 'https://staging-api.deyncare.com/api';
    //   case AppEnvironment.production:
    //     // Production API URL - Your VPS server
    //     return 'https://deyncare-backend.khanciye.com/api';
    // }
  }

  /// Web app URL based on current build mode
  static String get webAppUrl {
    switch (_currentEnvironment) {
      case AppEnvironment.development:
        return 'http://localhost:3000';
      case AppEnvironment.staging:
        return 'https://staging.deyncare.com';
      case AppEnvironment.production:
        return 'https://deyncare.com';
    }
  }

  /// Get current environment (for debugging/logging)
  static AppEnvironment get currentEnvironment => _currentEnvironment;

  /// Get current environment name as string
  static String get environmentName => _currentEnvironment.name;

  /// Check if running in development mode
  static bool get isDevelopment => _currentEnvironment == AppEnvironment.development;

  /// Check if running in staging mode  
  static bool get isStaging => _currentEnvironment == AppEnvironment.staging;

  /// Check if running in production mode
  static bool get isProduction => _currentEnvironment == AppEnvironment.production;

  /// Debug information about current configuration
  static Map<String, dynamic> get debugInfo => {
    'environment': environmentName,
    'baseApiUrl': baseApiUrl,
    'webAppUrl': webAppUrl,
    'isDebugMode': kDebugMode,
    'isReleaseMode': kReleaseMode,
    'isProfileMode': kProfileMode,
    'isWeb': kIsWeb,
    'note': 'FORCED PRODUCTION MODE - Always uses production API URL',
  };
}
