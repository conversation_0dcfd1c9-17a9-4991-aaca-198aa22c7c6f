import 'package:deyncare_app/data/repositories/customer_report_repository.dart';
import 'package:deyncare_app/domain/repositories/auth_repository.dart';
import 'package:deyncare_app/core/services/pdf_service.dart';
import 'package:flutter/foundation.dart';

class GenerateCustomerReportUseCase {
  final CustomerReportRepository _reportRepository;
  final AuthRepository _authRepository;

  GenerateCustomerReportUseCase(
    this._reportRepository,
    this._authRepository,
  );

  /// Generate customer report PDF
  Future<String> execute({
    String? month,
    String? year,
    String? startDate,
    String? endDate,
    String dateRange = 'monthly',
  }) async {
    try {
      if (kDebugMode) {
        print('🔍 GenerateCustomerReportUseCase: Starting execution');
        print(
            '🔍 Parameters: month=$month, year=$year, startDate=$startDate, endDate=$endDate, dateRange=$dateRange');
      }

      // Get current user/shop info
      if (kDebugMode) {
        print('🔍 Getting current user info...');
      }
      final user = await _authRepository.getCurrentUser();
      final shopName = user?.shopName ?? 'DeynCare Shop';
      final String? shopLogo =
          null; // Shop logo not available in current user model

      if (kDebugMode) {
        print('🔍 Shop name: $shopName');
        print('🔍 Fetching report data...');
      }

      // Fetch report data
      final reportData = await _reportRepository.getCustomerReportData(
        month: month,
        year: year,
        startDate: startDate,
        endDate: endDate,
        dateRange: dateRange,
      );

      if (kDebugMode) {
        print('🔍 Report data received');
        print('🔍 Report data type: ${reportData.runtimeType}');
        print('🔍 Report data keys: ${reportData.keys.toList()}');
        print('🔍 Customers type: ${reportData['customers'].runtimeType}');
        print(
            '🔍 Number of customers: ${reportData['customers'] is List ? (reportData['customers'] as List).length : 'Not a list'}');
      }

      // Determine report period description
      final reportPeriod = _getReportPeriodDescription(
        month: month,
        year: year,
        startDate: startDate,
        endDate: endDate,
        dateRange: dateRange,
      );

      if (kDebugMode) {
        print('🔍 Report period: $reportPeriod');
        print('🔍 Generating PDF...');
      }

      // Generate PDF
      final pdfPath = await PDFService.generateCustomerReport(
        customers: reportData['customers'],
        summary: reportData['summary'],
        reportPeriod: reportPeriod,
        shopName: shopName,
        shopLogo: shopLogo,
      );

      if (kDebugMode) {
        print('✅ PDF generated successfully: $pdfPath');
      }

      return pdfPath;
    } catch (e) {
      if (kDebugMode) {
        print('❌ GenerateCustomerReportUseCase Error: $e');
        print('❌ Error type: ${e.runtimeType}');
        print('❌ Stack trace: ${StackTrace.current}');
      }
      rethrow;
    }
  }

  /// Get formatted report period description
  String _getReportPeriodDescription({
    String? month,
    String? year,
    String? startDate,
    String? endDate,
    String dateRange = 'monthly',
  }) {
    if (month != null && year != null) {
      final monthNames = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December'
      ];
      return '${monthNames[int.parse(month) - 1]} $year';
    }

    if (startDate != null && endDate != null) {
      return '$startDate to $endDate';
    }

    switch (dateRange) {
      case 'daily':
        return 'Today';
      case 'weekly':
        return 'This Week';
      case 'monthly':
        return 'This Month';
      case 'yearly':
        return 'This Year';
      default:
        return 'All Time';
    }
  }
}
