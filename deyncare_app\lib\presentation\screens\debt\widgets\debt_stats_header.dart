import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/skeleton_loader.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_bloc.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_state.dart';

/// Header widget displaying debt statistics
class DebtStatsHeader extends StatelessWidget {
  const DebtStatsHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DebtBloc, DebtState>(
      buildWhen: (previous, current) =>
          current is DebtStatsLoaded ||
          current is DebtStatsLoading ||
          current is DebtStatsError,
      builder: (context, state) {
        if (state is DebtStatsLoading) {
          return _buildLoadingState();
        }
        
        if (state is DebtStatsError) {
          return _buildErrorState(state);
        }
        
        if (state is DebtStatsLoaded) {
          return _buildStatsState(state);
        }
        
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildLoadingState() {
    return Builder(
      builder: (context) => Container(
        margin: const EdgeInsets.all(16),
        child: Row(
          children: List.generate(
            3,
            (index) => Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                  left: index > 0 ? 4 : 0,
                  right: index < 2 ? 4 : 0,
                ),
                child: SkeletonPatterns.kpiCard(context, enabled: true),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState(DebtStatsError state) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppThemes.errorColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppThemes.errorColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: AppThemes.errorColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Failed to load debt statistics',
              style: TextStyle(
                color: AppThemes.errorColor,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsState(DebtStatsLoaded state) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      child: Row(
        children: [
          Expanded(
            child: StatusCard(
              title: 'Total Debts',
              value: state.totalDebts.toString(),
              icon: Icons.receipt_long_rounded,
              color: AppThemes.primaryColor,
              subtitle: 'All records',
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: StatusCard(
              title: 'Active',
              value: state.activeDebts.toString(),
              icon: Icons.trending_up_rounded,
              color: AppThemes.successColor,
              subtitle: 'In progress',
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: StatusCard(
              title: 'Overdue',
              value: state.overdueDebts.toString(),
              icon: Icons.schedule_rounded,
              color: AppThemes.errorColor,
              subtitle: 'Past due',
            ),
          ),
        ],
      ),
    );
  }
} 