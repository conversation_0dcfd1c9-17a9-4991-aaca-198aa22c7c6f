import 'package:deyncare_app/domain/repositories/auth_repository.dart';

/// Use case for fetching available payment methods.
///
/// This class is responsible for calling the repository to get the list
/// of payment methods enabled in the backend based on a given context.
class GetAvailablePaymentMethodsUseCase {
  final AuthRepository _repository;

  /// Creates a new instance with the required repository.
  GetAvailablePaymentMethodsUseCase(this._repository);

  /// Executes the use case to fetch available payment methods.
  ///
  /// The [context] parameter (e.g., 'subscription', 'pos') determines which
  /// set of payment methods to retrieve.
  /// Returns a Future<List<String>> containing the names of available payment methods.
  Future<List<String>> execute(String context) async {
    return await _repository.getAvailablePaymentMethods(context);
  }
}
