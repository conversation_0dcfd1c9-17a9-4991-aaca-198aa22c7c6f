import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:network_info_plus/network_info_plus.dart';

/// Service to monitor and manage network connectivity
class ConnectivityService {
  // Singleton instance
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() {
    _instance._ensureInitialized();
    return _instance;
  }
  ConnectivityService._internal();

  // Connectivity plugin
  final Connectivity _connectivity = Connectivity();
  
  // Stream controllers
  final _connectivityStreamController = StreamController<ConnectivityStatus>.broadcast();
  
  // Current status
  ConnectivityStatus _currentStatus = ConnectivityStatus.unknown;

  // Public stream
  Stream<ConnectivityStatus> get statusStream => _connectivityStreamController.stream;
  
  // Current status getter
  ConnectivityStatus get currentStatus => _currentStatus;
  
  // Network info plugin
  final NetworkInfo _networkInfo = NetworkInfo();
  
  // Initialize the service
  bool _initialized = false;

  // Ensure the service is initialized
  void _ensureInitialized() {
    if (!_initialized) {
      initialize();
      _initialized = true;
    }
  }

  Future<void> initialize() async {
    try {
      List<ConnectivityResult> results = await _connectivity.checkConnectivity();
      _updateStatus(results.first);
      _connectivity.onConnectivityChanged.listen((List<ConnectivityResult> results) {
        _updateStatus(results.first);
      });
    } catch (e) {
      debugPrint('ConnectivityService initialization error: $e');
      _updateStatus(ConnectivityResult.none);
    }
  }
  
  // Update status based on connectivity result
  void _updateStatus(ConnectivityResult result) {
    ConnectivityStatus status;
    
    switch (result) {
      case ConnectivityResult.mobile:
        status = ConnectivityStatus.mobile;
        break;
      case ConnectivityResult.wifi:
        status = ConnectivityStatus.wifi;
        break;
      case ConnectivityResult.ethernet:
        status = ConnectivityStatus.ethernet;
        break;
      case ConnectivityResult.none:
        status = ConnectivityStatus.disconnected;
        break;
      default:
        status = ConnectivityStatus.unknown;
    }
    
    // Only emit event if status changed
    if (status != _currentStatus) {
      _currentStatus = status;
      _connectivityStreamController.add(status);
      debugPrint('Connectivity status changed: $_currentStatus');
    }
  }
  
  // Check if device is currently connected
  bool isConnected() {
    return _currentStatus == ConnectivityStatus.wifi ||
           _currentStatus == ConnectivityStatus.mobile ||
           _currentStatus == ConnectivityStatus.ethernet;
  }
  
  // Dispose resources
  void dispose() {
    _connectivityStreamController.close();
  }

  /// Get a human-readable description of the current network, with error handling
  Future<String> getNetworkDetails() async {
    try {
      var connectivityResult = await _connectivity.checkConnectivity();
      switch (connectivityResult) {
        case ConnectivityResult.wifi:
          final wifiName = await _networkInfo.getWifiName();
          final wifiIP = await _networkInfo.getWifiIP();
          return 'WiFi: ${wifiName ?? "Unknown"} (${wifiIP ?? "No IP"})';
        case ConnectivityResult.mobile:
          return 'Mobile Data';
        case ConnectivityResult.ethernet:
          return 'Ethernet';
        case ConnectivityResult.none:
          return 'No Connection';
        default:
          return 'Unknown';
      }
    } catch (e) {
      return 'Network info unavailable (permissions?)';
    }
  }
}

/// Enum representing connectivity status, now more granular
enum ConnectivityStatus {
  disconnected,
  unknown,
  wifi,
  mobile,
  ethernet,
}
