import 'package:deyncare_app/domain/models/user.dart';

/// Utility class for checking user permissions and managing UI access control
class PermissionUtils {
  /// Check if user has permission to access customer management features
  static bool canAccessCustomers(User? user) {
    if (user == null) return false;
    if (user.isAdmin) return true; // Admins have all permissions

    // For employees, check visibility permissions
    if (user.visibility == null) return false;
    final customerMgmt =
        user.visibility!['customerManagement'] as Map<String, dynamic>?;
    if (customerMgmt == null) return false;

    // Check if user has any customer management permission
    return customerMgmt['view'] == true ||
        customerMgmt['create'] == true ||
        customerMgmt['update'] == true ||
        customerMgmt['delete'] == true;
  }

  /// Check if user has permission to create customers
  static bool canCreateCustomers(User? user) {
    if (user == null) return false;
    if (user.isAdmin) return true;
    if (user.visibility == null) return false;
    final customerMgmt =
        user.visibility!['customerManagement'] as Map<String, dynamic>?;
    return customerMgmt?['create'] == true;
  }

  /// Check if user has permission to view customers
  static bool canViewCustomers(User? user) {
    if (user == null) return false;
    if (user.isAdmin) return true;
    if (user.visibility == null) return false;
    final customerMgmt =
        user.visibility!['customerManagement'] as Map<String, dynamic>?;
    return customerMgmt?['view'] == true;
  }

  /// Check if user has permission to update customers
  static bool canUpdateCustomers(User? user) {
    if (user == null) return false;
    if (user.isAdmin) return true;
    if (user.visibility == null) return false;
    final customerMgmt =
        user.visibility!['customerManagement'] as Map<String, dynamic>?;
    return customerMgmt?['update'] == true;
  }

  /// Check if user has permission to delete customers
  static bool canDeleteCustomers(User? user) {
    if (user == null) return false;
    if (user.isAdmin) return true;
    if (user.visibility == null) return false;
    final customerMgmt =
        user.visibility!['customerManagement'] as Map<String, dynamic>?;
    return customerMgmt?['delete'] == true;
  }

  /// Check if user has permission to access debt management features
  static bool canAccessDebts(User? user) {
    if (user == null) return false;
    if (user.isAdmin) return true;

    // For employees, check visibility permissions
    if (user.visibility == null) {
      // Fallback: Check legacy permissions array
      return user.hasPermission('debt_view') ||
          user.hasPermission('debt_create') ||
          user.hasPermission('debt_update') ||
          user.hasPermission('debt_delete');
    }

    final debtMgmt =
        user.visibility!['debtManagement'] as Map<String, dynamic>?;
    if (debtMgmt == null) return false;

    // Check if user has any debt management permission
    return debtMgmt['view'] == true ||
        debtMgmt['create'] == true ||
        debtMgmt['update'] == true ||
        debtMgmt['delete'] == true;
  }

  /// Check if user has permission to create debts
  static bool canCreateDebts(User? user) {
    if (user == null) return false;
    if (user.isAdmin) return true;
    if (user.visibility == null) {
      return user.hasPermission('debt_create');
    }
    final debtMgmt =
        user.visibility!['debtManagement'] as Map<String, dynamic>?;
    return debtMgmt?['create'] == true;
  }

  /// Check if user has permission to view debts
  static bool canViewDebts(User? user) {
    if (user == null) return false;
    if (user.isAdmin) return true;
    if (user.visibility == null) {
      return user.hasPermission('debt_view');
    }
    final debtMgmt =
        user.visibility!['debtManagement'] as Map<String, dynamic>?;
    return debtMgmt?['view'] == true;
  }

  /// Check if user has permission to update debts
  static bool canUpdateDebts(User? user) {
    if (user == null) return false;
    if (user.isAdmin) return true;
    if (user.visibility == null) {
      return user.hasPermission('debt_update');
    }
    final debtMgmt =
        user.visibility!['debtManagement'] as Map<String, dynamic>?;
    return debtMgmt?['update'] == true;
  }

  /// Check if user has permission to delete debts
  static bool canDeleteDebts(User? user) {
    if (user == null) return false;
    if (user.isAdmin) return true;
    if (user.visibility == null) {
      return user.hasPermission('debt_delete');
    }
    final debtMgmt =
        user.visibility!['debtManagement'] as Map<String, dynamic>?;
    return debtMgmt?['delete'] == true;
  }

  /// Check if user has permission to access reports
  static bool canAccessReports(User? user) {
    if (user == null) return false;
    if (user.isAdmin) return true;

    // For employees, check visibility permissions
    if (user.visibility == null) {
      // Fallback: Check legacy permissions array
      return user.hasPermission('report_view') ||
          user.hasPermission('report_generate') ||
          user.hasPermission('report_delete');
    }

    final reportMgmt =
        user.visibility!['reportManagement'] as Map<String, dynamic>?;
    if (reportMgmt == null) return false;

    // Check if user has any report management permission
    return reportMgmt['view'] == true ||
        reportMgmt['generate'] == true ||
        reportMgmt['delete'] == true;
  }

  /// Check if user has permission to generate reports
  static bool canGenerateReports(User? user) {
    if (user == null) return false;
    if (user.isAdmin) return true;
    if (user.visibility == null) {
      return user.hasPermission('report_generate');
    }
    final reportMgmt =
        user.visibility!['reportManagement'] as Map<String, dynamic>?;
    return reportMgmt?['generate'] == true;
  }

  /// Check if user has permission to view reports
  static bool canViewReports(User? user) {
    if (user == null) return false;
    if (user.isAdmin) return true;
    if (user.visibility == null) {
      return user.hasPermission('report_view');
    }
    final reportMgmt =
        user.visibility!['reportManagement'] as Map<String, dynamic>?;
    return reportMgmt?['view'] == true;
  }

  /// Check if user has permission to delete reports
  static bool canDeleteReports(User? user) {
    if (user == null) return false;
    if (user.isAdmin) return true;
    if (user.visibility == null) {
      return user.hasPermission('report_delete');
    }
    final reportMgmt =
        user.visibility!['reportManagement'] as Map<String, dynamic>?;
    return reportMgmt?['delete'] == true;
  }

  /// Check if user can access user role management (admin only)
  static bool canAccessUserManagement(User? user) {
    if (user == null) return false;
    return user.isAdmin; // Only admins can manage users
  }

  /// Check if user can access shop settings (admin only)
  static bool canAccessShopSettings(User? user) {
    if (user == null) return false;
    return user.isAdmin; // Only admins can manage shop settings
  }

  /// Check if user can access subscription management (admin only)
  static bool canAccessSubscription(User? user) {
    if (user == null) return false;
    return user.isAdmin; // Only admins can manage subscriptions
  }

  /// Get user-friendly message for restricted access
  static String getRestrictedAccessMessage(String feature) {
    return 'You don\'t have permission to access $feature. Contact your administrator for access.';
  }

  /// Get list of accessible features for user
  static List<String> getAccessibleFeatures(User? user) {
    if (user == null) return [];

    final features = <String>[];

    if (canAccessCustomers(user)) features.add('Customers');
    if (canAccessDebts(user)) features.add('Debts');
    if (canAccessReports(user)) features.add('Reports');
    if (canAccessUserManagement(user)) features.add('User Management');
    if (canAccessShopSettings(user)) features.add('Shop Settings');
    if (canAccessSubscription(user)) features.add('Subscription');

    return features;
  }

  /// Check if user has any permissions at all
  static bool hasAnyPermissions(User? user) {
    if (user == null) return false;
    if (user.isAdmin) return true;

    return canAccessCustomers(user) ||
        canAccessDebts(user) ||
        canAccessReports(user);
  }

  /// Get permission level description for UI
  static String getPermissionLevelDescription(User? user) {
    if (user == null) return 'No Access';
    if (user.isAdmin) return 'Full Access (Administrator)';
    if (user.isEmployee) {
      final accessibleFeatures = getAccessibleFeatures(user);
      if (accessibleFeatures.isEmpty) {
        return 'Limited Access (No Permissions)';
      }
      return 'Limited Access (${accessibleFeatures.length} features)';
    }
    return 'Unknown Access Level';
  }
}
