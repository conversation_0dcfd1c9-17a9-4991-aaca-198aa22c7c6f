import 'package:flutter/material.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'package:deyncare_app/presentation/widgets/skeleton_loader.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_constants.dart';

/// Common loading states for modal forms
class ModalLoadingStates {

  /// Standard form skeleton loader with multiple fields
  static Widget formSkeleton({
    int fieldCount = 5,
    bool includeButton = true,
  }) {
    return Padding(
      padding: ModalConstants.defaultPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Generate field skeletons
          ...List.generate(fieldCount, (index) => Column(
            children: [
              SkeletonLoader.text(width: 120, height: 20),
              ModalConstants.defaultSpacing,
              SkeletonLoader(width: double.infinity, height: 56),
              ModalConstants.defaultSpacing,
            ],
          )),
          
          // Button skeleton if needed
          if (includeButton) ...[
            ModalConstants.largeSpacing,
            SkeletonLoader(width: double.infinity, height: 56),
          ],
        ],
      ),
    );
  }

  /// Detailed view skeleton loader
  static Widget detailsSkeleton() {
    return Padding(
      padding: ModalConstants.defaultPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header card skeleton
          SkeletonLoader(width: double.infinity, height: 120),
          ModalConstants.defaultSpacing,
          
          // Details sections
          ...List.generate(3, (index) => Column(
            children: [
              SkeletonLoader(width: double.infinity, height: 80),
              ModalConstants.defaultSpacing,
            ],
          )),
          
          // Action button
          ModalConstants.largeSpacing,
          Center(
            child: SkeletonLoader(width: 100, height: 48),
          ),
        ],
      ),
    );
  }

  /// Compact form skeleton for smaller forms
  static Widget compactFormSkeleton() {
    return Padding(
      padding: ModalConstants.defaultPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          SkeletonLoader.text(width: 120, height: 20),
          ModalConstants.defaultSpacing,
          SkeletonLoader(width: double.infinity, height: 56),
          ModalConstants.defaultSpacing,
          SkeletonLoader.text(width: 100, height: 20),
          ModalConstants.defaultSpacing,
          SkeletonLoader(width: double.infinity, height: 56),
          ModalConstants.largeSpacing,
          SkeletonLoader(width: double.infinity, height: 56),
        ],
      ),
    );
  }

  /// Delete confirmation skeleton
  static Widget deleteSkeleton() {
    return Padding(
      padding: ModalConstants.defaultPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Warning icon and title
          Center(
            child: Column(
              children: [
                SkeletonLoader.circular(size: 64),
                ModalConstants.defaultSpacing,
                SkeletonLoader.text(width: 120, height: 28),
              ],
            ),
          ),
          ModalConstants.largeSpacing,
          
          // Info card
          SkeletonLoader(width: double.infinity, height: 120),
          ModalConstants.defaultSpacing,
          
          // Warning message
          SkeletonLoader(width: double.infinity, height: 80),
          ModalConstants.largeSpacing,
          
          // Action buttons
          Row(
            children: [
              Expanded(child: SkeletonLoader(width: double.infinity, height: 56)),
              ModalConstants.defaultSpacing,
              Expanded(child: SkeletonLoader(width: double.infinity, height: 56)),
            ],
          ),
        ],
      ),
    );
  }

  /// Loading animation for buttons
  static Widget buttonLoading(BuildContext context, {Color? color}) {
    return SizedBox(
      height: 20,
      width: 20,
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? ThemeUtils.getTextColor(context, type: TextColorType.primary)
        ),
      ),
    );
  }

  /// Loading row for forms
  static Widget loadingRow(BuildContext context, {required String text}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        buttonLoading(context, color: ThemeUtils.getTextColor(context, type: TextColorType.secondary)),
        ModalConstants.defaultSpacing,
        Text(text),
      ],
    );
  }
} 