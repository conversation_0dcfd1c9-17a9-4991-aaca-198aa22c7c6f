/// Model representing an error response from the API
class ErrorModel {
  final String code;
  final String message;
  final dynamic data;
  final String type;

  ErrorModel({
    required this.code,
    required this.message,
    this.data,
    this.type = 'error',
  });

  factory ErrorModel.fromJson(Map<String, dynamic> json) {
    return ErrorModel(
      code: json['code'] ?? 'unknown_error',
      message: json['message'] ?? 'An unknown error occurred',
      data: json['data'],
      type: json['type'] ?? 'error',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data,
      'type': type,
    };
  }
}
