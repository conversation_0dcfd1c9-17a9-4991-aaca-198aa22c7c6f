import 'package:deyncare_app/domain/models/user.dart';

/// Data model for user with JSON serialization/deserialization
class UserModel extends User {
  UserModel({
    required super.userId,
    required super.fullName,
    required super.email,
    super.phone,
    required super.role,
    super.shopId,
    required super.status,
    super.permissions,
    super.visibility,
    required super.registrationStatus,
    required super.isEmailVerified,
    required super.isPaid,
    super.emailVerifiedAt,
    super.paymentCompletedAt,
    super.verificationCode,
    super.verificationCodeExpiresAt,
    super.shopName,
    super.shopStatus,
    required super.isShopActive,
    super.accessToken,
    super.refreshToken,
    super.tokenExpiresAt,
    super.isSuspended = false,
    super.suspensionReason,
    super.suspendedAt,
    super.suspendedBy,
  });

  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      userId: map['userId'] ?? map['id'] ?? '',
      fullName: map['fullName'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'],
      role: map['role'] ?? '',
      shopId: map['shopId'],
      status: map['status'] ?? '',
      permissions: (map['permissions'] as List?)?.cast<String>(),
      visibility: map['visibility'] as Map<String, dynamic>?,
      registrationStatus: map['registrationStatus'] ?? '',
      isEmailVerified: map['isEmailVerified'] ?? false,
      isPaid: map['isPaid'] ?? false,
      emailVerifiedAt: map['emailVerifiedAt'] != null
          ? DateTime.tryParse(map['emailVerifiedAt'])
          : null,
      paymentCompletedAt: map['paymentCompletedAt'] != null
          ? DateTime.tryParse(map['paymentCompletedAt'])
          : null,
      verificationCode: map['verificationCode'],
      verificationCodeExpiresAt: map['verificationCodeExpiresAt'] != null
          ? DateTime.tryParse(map['verificationCodeExpiresAt'])
          : null,
      shopName: map['shopName'],
      shopStatus: map['shopStatus'],
      isShopActive: map['isShopActive'] ?? false,
      accessToken: map['accessToken'],
      refreshToken: map['refreshToken'],
      tokenExpiresAt: map['tokenExpiresAt'] != null
          ? DateTime.tryParse(map['tokenExpiresAt'])
          : null,
      // Suspension fields
      isSuspended: map['isSuspended'] ?? false,
      suspensionReason: map['suspensionReason'],
      suspendedAt: map['suspendedAt'] != null
          ? DateTime.tryParse(map['suspendedAt'])
          : null,
      suspendedBy: map['suspendedBy'],
    );
  }

  /// Create UserModel from JSON map
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      userId: json['userId'] ?? json['_id'] ?? '',
      fullName: json['fullName'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      role: json['role'] ?? 'employee',
      shopId: json['shopId'] ?? '',
      status: json['status'] ?? 'inactive',
      permissions: json['permissions'] != null
          ? List<String>.from(json['permissions'])
          : null,
      visibility: json['visibility'] as Map<String, dynamic>?,
      registrationStatus: json['registrationStatus'] ?? 'pending_registration',
      isEmailVerified: json['isEmailVerified'] ?? false,
      isPaid: json['isPaid'] ?? false,
      emailVerifiedAt: json['emailVerifiedAt'] != null
          ? DateTime.tryParse(json['emailVerifiedAt'])
          : null,
      paymentCompletedAt: json['paymentCompletedAt'] != null
          ? DateTime.tryParse(json['paymentCompletedAt'])
          : null,
      verificationCode: json['verificationCode'] ?? '',
      verificationCodeExpiresAt: json['verificationCodeExpiresAt'] != null
          ? DateTime.tryParse(json['verificationCodeExpiresAt'])
          : null,
      shopName: json['shopName'] ?? '',
      shopStatus: json['shopStatus'] ?? '',
      isShopActive: json['isShopActive'] ?? false,
      accessToken: json['accessToken'] ?? '',
      refreshToken: json['refreshToken'] ?? '',
      tokenExpiresAt:
          json['expiresAt'] != null ? DateTime.parse(json['expiresAt']) : null,
      // Suspension fields
      isSuspended: json['isSuspended'] ?? false,
      suspensionReason: json['suspensionReason'],
      suspendedAt: json['suspendedAt'] != null
          ? DateTime.tryParse(json['suspendedAt'])
          : null,
      suspendedBy: json['suspendedBy'],
    );
  }

  /// Convert UserModel to JSON map
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'fullName': fullName,
      'email': email,
      'phone': phone,
      'role': role,
      'shopId': shopId,
      'status': status,
      'permissions': permissions,
      'registrationStatus': registrationStatus,
      'isEmailVerified': isEmailVerified,
      'isPaid': isPaid,
      'emailVerifiedAt': emailVerifiedAt?.toIso8601String(),
      'paymentCompletedAt': paymentCompletedAt?.toIso8601String(),
      'verificationCode': verificationCode,
      'verificationCodeExpiresAt': verificationCodeExpiresAt?.toIso8601String(),
      'shopName': shopName,
      'shopStatus': shopStatus,
      'isShopActive': isShopActive,
      // Suspension fields
      'isSuspended': isSuspended,
      'suspensionReason': suspensionReason,
      'suspendedAt': suspendedAt?.toIso8601String(),
      'suspendedBy': suspendedBy,
    };
  }

  /// Convert to domain User entity
  User toDomain() {
    return User(
      userId: userId,
      fullName: fullName,
      email: email,
      phone: phone,
      role: role,
      shopId: shopId,
      status: status,
      permissions: permissions,
      visibility: visibility,
      registrationStatus: registrationStatus,
      isEmailVerified: isEmailVerified,
      isPaid: isPaid,
      emailVerifiedAt: emailVerifiedAt,
      paymentCompletedAt: paymentCompletedAt,
      verificationCode: verificationCode,
      verificationCodeExpiresAt: verificationCodeExpiresAt,
      shopName: shopName,
      shopStatus: shopStatus,
      isShopActive: isShopActive,
      accessToken: accessToken,
      refreshToken: refreshToken,
      tokenExpiresAt: tokenExpiresAt,
      // Suspension fields
      isSuspended: isSuspended,
      suspensionReason: suspensionReason,
      suspendedAt: suspendedAt,
      suspendedBy: suspendedBy,
    );
  }

  /// Create a copy of UserModel with some fields replaced
  @override
  UserModel copyWith({
    String? userId,
    String? fullName,
    String? email,
    String? phone,
    String? role,
    String? shopId,
    String? status,
    List<String>? permissions,
    Map<String, dynamic>? visibility,
    String? registrationStatus,
    bool? isEmailVerified,
    bool? isPaid,
    DateTime? emailVerifiedAt,
    DateTime? paymentCompletedAt,
    String? verificationCode,
    DateTime? verificationCodeExpiresAt,
    String? shopName,
    String? shopStatus,
    bool? isShopActive,
    String? accessToken,
    String? refreshToken,
    DateTime? tokenExpiresAt,
    bool? isSuspended,
    String? suspensionReason,
    DateTime? suspendedAt,
    String? suspendedBy,
  }) {
    return UserModel(
      userId: userId ?? this.userId,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      shopId: shopId ?? this.shopId,
      status: status ?? this.status,
      permissions: permissions ?? this.permissions,
      visibility: visibility ?? this.visibility,
      registrationStatus: registrationStatus ?? this.registrationStatus,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPaid: isPaid ?? this.isPaid,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      paymentCompletedAt: paymentCompletedAt ?? this.paymentCompletedAt,
      verificationCode: verificationCode ?? this.verificationCode,
      verificationCodeExpiresAt:
          verificationCodeExpiresAt ?? this.verificationCodeExpiresAt,
      shopName: shopName ?? this.shopName,
      shopStatus: shopStatus ?? this.shopStatus,
      isShopActive: isShopActive ?? this.isShopActive,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      tokenExpiresAt: tokenExpiresAt ?? this.tokenExpiresAt,
      isSuspended: isSuspended ?? this.isSuspended,
      suspensionReason: suspensionReason ?? this.suspensionReason,
      suspendedAt: suspendedAt ?? this.suspendedAt,
      suspendedBy: suspendedBy ?? this.suspendedBy,
    );
  }
}
