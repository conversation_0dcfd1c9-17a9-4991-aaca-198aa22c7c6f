import 'package:flutter/material.dart';
import 'package:flutter_card_swiper/flutter_card_swiper.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:deyncare_app/core/constants/app_strings.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/routes/app_router.dart';
import 'package:deyncare_app/presentation/screens/onboarding/onboarding_content.dart';

/// Screen for first-time user onboarding
class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  // Controller for card swiper
  final CardSwiperController _cardController = CardSwiperController();

  // Track current page index
  int _currentPage = 0;

  // Key for storing onboarding completion status
  static const String _onboardingCompletedKey = 'onboarding_completed';

  @override
  void dispose() {
    _cardController.dispose();
    super.dispose();
  }

  // Mark onboarding as completed in shared preferences
  Future<void> _completeOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_onboardingCompletedKey, true);

    if (mounted) {
      AppRouter.navigateToLogin(context);
    }
  }

  // Skip all onboarding and go directly to login
  void _skipOnboarding() {
    _completeOnboarding();
  }

  // Navigate to next page or complete onboarding if on last page
  void _nextPage() {
    if (_currentPage < onboardingContents.length - 1) {
      _cardController.swipe(CardSwiperDirection.left);
    } else {
      _completeOnboarding();
    }
  }

  // Navigate to previous page
  void _previousPage() {
    if (_currentPage > 0) {
      _cardController.undo();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // Skip button (only show on first three pages)
            Padding(
              padding: const EdgeInsets.only(top: 16, right: 24),
              child: Align(
                alignment: Alignment.topRight,
                child: _currentPage < onboardingContents.length - 1
                    ? TextButton(
                        onPressed: _skipOnboarding,
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          AppStrings.skip,
                          style: TextStyle(
                            color: AppThemes.textSecondaryColor,
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                      )
                    : const SizedBox(height: 48),
              ),
            ),

            // Card Swiper with images
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: CardSwiper(
                  controller: _cardController,
                  cardsCount: onboardingContents.length,
                  onSwipe: (previousIndex, currentIndex, direction) {
                    setState(() {
                      _currentPage = currentIndex ?? 0;
                    });
                    return true;
                  },
                  onEnd: () {
                    // Handle when all cards are swiped
                    _completeOnboarding();
                  },
                  cardBuilder:
                      (context, index, percentThresholdX, percentThresholdY) {
                    return _OnboardingCard(
                      content: onboardingContents[index],
                    );
                  },
                  // Customize swiper settings
                  duration: const Duration(milliseconds: 300),
                  maxAngle: 15,
                  threshold: 50,
                  scale: 0.9,
                  numberOfCardsDisplayed: 2,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
                  allowedSwipeDirection: AllowedSwipeDirection.all(),
                ),
              ),
            ),

            // Page indicator dots
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                onboardingContents.length,
                (index) => AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  margin: const EdgeInsets.symmetric(horizontal: 6),
                  width: _currentPage == index ? 32 : 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: _currentPage == index
                        ? AppThemes.primaryColor
                        : AppThemes.dividerColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 32),

            // Content outside the card (title and description)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Column(
                children: [
                  // Title
                  Text(
                    onboardingContents[_currentPage].title,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppThemes.textPrimaryColor,
                          height: 1.2,
                        ),
                  ),

                  const SizedBox(height: 16),

                  // Description
                  Text(
                    onboardingContents[_currentPage].description,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: AppThemes.textSecondaryColor,
                          height: 1.6,
                          fontSize: 16,
                        ),
                  ),

                  const SizedBox(height: 32),
                ],
              ),
            ),

            // Navigation buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Back button (only show after first page)
                  _currentPage > 0
                      ? Container(
                          decoration: BoxDecoration(
                            color: AppThemes.backgroundColor,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppThemes.dividerColor,
                              width: 1,
                            ),
                          ),
                          child: IconButton(
                            onPressed: _previousPage,
                            icon: const Icon(Icons.arrow_back_ios_new),
                            color: AppThemes.textSecondaryColor,
                            iconSize: 20,
                          ),
                        )
                      : const SizedBox(width: 48),

                  // Next/Get Started button
                  _currentPage < onboardingContents.length - 1
                      ? Container(
                          decoration: BoxDecoration(
                            color: AppThemes.primaryColor,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            onPressed: _nextPage,
                            icon: const Icon(Icons.arrow_forward_ios),
                            color: Colors.white,
                            iconSize: 20,
                          ),
                        )
                      : ElevatedButton(
                          onPressed: _nextPage,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppThemes.primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 32,
                              vertical: 16,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 0,
                          ),
                          child: Text(
                            AppStrings.getStarted,
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                ],
              ),
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}

/// Individual onboarding card with image as focal visual
class _OnboardingCard extends StatelessWidget {
  final OnboardingContent content;

  const _OnboardingCard({
    required this.content,
  });

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final cardHeight = screenHeight * 0.4; // 40% of screen height for the card

    return Container(
      height: cardHeight,
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppThemes.surfaceColor,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: AppThemes.shadowLight,
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: Image.asset(
          content.imagePath,
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
        ),
      ),
    );
  }
}
