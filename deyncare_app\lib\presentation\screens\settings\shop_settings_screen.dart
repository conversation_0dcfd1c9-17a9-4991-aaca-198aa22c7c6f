import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/common_app_bar.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_text_field.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_button.dart';
import 'package:deyncare_app/data/services/shop_service.dart';
import 'package:deyncare_app/domain/models/shop.dart';
import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/data/services/auth_service.dart';

/// Shop settings screen for updating shop information
class ShopSettingsScreen extends StatefulWidget {
  const ShopSettingsScreen({super.key});

  @override
  State<ShopSettingsScreen> createState() => _ShopSettingsScreenState();
}

class _ShopSettingsScreenState extends State<ShopSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _shopNameController = TextEditingController();
  final _shopAddressController = TextEditingController();
  final _businessTypeController = TextEditingController();
  final _contactNumberController = TextEditingController();
  final _emailController = TextEditingController();
  // Removed _websiteController and _descriptionController

  File? _logoImage;
  bool _isLoading = false;
  final ImagePicker _picker = ImagePicker();
  
  // Services
  final ShopService _shopService = ShopService();
  final AuthService _authService = AuthService();
  
  // State
  Shop? _currentShop;
  User? _currentUser;

  @override
  void initState() {
    super.initState();
    _loadShopData();
  }

  @override
  void dispose() {
    _shopNameController.dispose();
    _shopAddressController.dispose();
    _businessTypeController.dispose();
    _contactNumberController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  Future<void> _loadShopData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Get current user to access shopId
      _currentUser = await _authService.getCurrentUser();
      
      if (_currentUser?.shopId != null) {
        // Load shop data from API
        _currentShop = await _shopService.getShopById(_currentUser!.shopId!);
        
        // Populate form fields with real data
        _shopNameController.text = _currentShop?.shopName ?? '';
        _shopAddressController.text = _currentShop?.address ?? '';
        _businessTypeController.text = _currentShop?.businessDetails?.type ?? 'retail';
        _contactNumberController.text = _currentShop?.phone ?? '';
        _emailController.text = _currentShop?.email ?? '';
        // Removed _websiteController.text = _currentShop?.socialMedia?.website ?? '';
        // Removed _descriptionController.text = _currentShop?.businessDetails?.category ?? '';
      }
    } catch (e) {
      // Handle error - show snackbar or dialog
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load shop data: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CommonAppBar(
        title: 'Shop Settings',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Shop Logo Section
              _buildLogoSection(),
              
              const SizedBox(height: 32),
              
              // Basic Information Section
              _buildSectionTitle('Basic Information'),
              const SizedBox(height: 16),
              _buildBasicInfoSection(),
              
              const SizedBox(height: 32),
              
              // Contact Information Section
              _buildSectionTitle('Contact Information'),
              const SizedBox(height: 16),
              _buildContactInfoSection(),
              
              const SizedBox(height: 40),
              
              // Action Buttons
              _buildActionButtons(),
              
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.onSurface,
      ),
    );
  }

  Widget _buildLogoSection() {
    return CommonCard(
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.store_outlined,
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Shop Logo',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Logo Display/Upload Area
          Center(
            child: GestureDetector(
              onTap: _pickLogo,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(60),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                    width: 2,
                    style: BorderStyle.solid,
                  ),
                ),
                child: _logoImage != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(58),
                        child: Image.file(
                          _logoImage!,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.add_a_photo_outlined,
                            size: 32,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Add Logo',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          if (_logoImage != null)
            TextButton.icon(
              onPressed: _removeLogo,
              icon: const Icon(Icons.delete_outline, color: Colors.red),
              label: const Text(
                'Remove Logo',
                style: TextStyle(color: Colors.red),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return CommonCard(
      child: Column(
        children: [
          AuthTextField(
            label: 'Shop Name',
            hintText: 'Enter your shop name',
            controller: _shopNameController,
            prefixIcon: Icon(Icons.store, color: Theme.of(context).colorScheme.primary),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter shop name';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 20),
          
          AuthTextField(
            label: 'Shop Address',
            hintText: 'Enter your shop address',
            controller: _shopAddressController,
            prefixIcon: Icon(Icons.location_on, color: Theme.of(context).colorScheme.primary),
            keyboardType: TextInputType.streetAddress,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter shop address';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 20),
          
          AuthTextField(
            label: 'Business Type',
            hintText: 'e.g., General Store, Electronics, etc.',
            controller: _businessTypeController,
            prefixIcon: Icon(Icons.business, color: Theme.of(context).colorScheme.primary),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter business type';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildContactInfoSection() {
    return CommonCard(
      child: Column(
        children: [
          AuthTextField(
            label: 'Contact Number',
            hintText: '+252 61 000 0000',
            controller: _contactNumberController,
            prefixIcon: Icon(Icons.phone, color: Theme.of(context).colorScheme.primary),
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter contact number';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 20),
          
          AuthTextField(
            label: 'Email Address',
            hintText: '<EMAIL>',
            controller: _emailController,
            prefixIcon: Icon(Icons.email, color: Theme.of(context).colorScheme.primary),
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter email address';
              }
              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                return 'Please enter a valid email';
              }
              return null;
            },
          ),
          // Removed Website field as requested
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        AuthButton(
          label: 'Save Changes',
          isLoading: _isLoading,
          onPressed: _saveShopSettings,
          icon: const Icon(Icons.save, color: Colors.white),
        ),
        
        const SizedBox(height: 12),
        
        AuthButton(
          label: 'Reset to Default',
          isOutlined: true,
          onPressed: _resetToDefault,
          icon: Icon(Icons.refresh, color: Theme.of(context).colorScheme.primary),
        ),
      ],
    );
  }

  void _pickLogo() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );
      
      if (image != null) {
        setState(() {
          _logoImage = File(image.path);
        });
      }
    } catch (e) {
      _showErrorSnackBar('Failed to pick image: $e');
    }
  }

  void _removeLogo() {
    setState(() {
      _logoImage = null;
    });
  }

  void _resetToDefault() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Reset Settings'),
          content: const Text('Are you sure you want to reset all settings to default values?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _loadShopData(); // Reload default data
                setState(() {
                  _logoImage = null;
                });
                _showSuccessSnackBar('Settings reset to default values');
              },
              child: const Text('Reset'),
            ),
          ],
        );
      },
    );
  }

  void _saveShopSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (_currentUser?.shopId == null) {
        throw Exception('Shop ID not found. Please ensure you are logged in.');
      }

      print('🔍 DEYNCARE_DEBUG: Updating shop with ID: ${_currentUser!.shopId}');
      print('🔍 DEYNCARE_DEBUG: Current user: ${_currentUser?.email}');

      // Upload logo if changed
      if (_logoImage != null) {
        print('🔍 DEYNCARE_DEBUG: Uploading logo...');
        await _shopService.uploadShopLogo(_currentUser!.shopId!, _logoImage!);
        print('✅ DEYNCARE_DEBUG: Logo uploaded successfully');
      }

      // Prepare update data
      final updateData = {
        'name': _shopNameController.text.trim(),
        'address': _shopAddressController.text.trim(),
        'phone': _contactNumberController.text.trim(),
        'email': _emailController.text.trim(),
        'businessDetails': {
          'type': _businessTypeController.text.trim(),
          // Removed category field
        },
        // Removed socialMedia section
      };

      print('🔍 DEYNCARE_DEBUG: Update data: $updateData');

      // Update shop via API
      _currentShop = await _shopService.updateShop(_currentUser!.shopId!, updateData);
      
      print('✅ DEYNCARE_DEBUG: Shop updated successfully');
      _showSuccessSnackBar('Shop settings updated successfully!');
      
    } catch (e) {
      _showErrorSnackBar('Failed to update shop settings: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppThemes.successColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppThemes.errorColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
} 