/// Employee domain entity for user role management
class Employee {
  final String userId;
  final String fullName;
  final String email;
  final String? phone;
  final String role;
  final String? shopId;
  final String status;
  final EmployeeVisibility? visibility;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  // Additional fields from backend
  final String? userTitle;
  final bool isSuspended;
  final String? suspensionReason;
  final DateTime? suspendedAt;
  final String? suspendedBy;
  final bool verified;
  final bool emailVerified;
  final bool isPaid;
  final bool isActivated;
  final DateTime? lastLoginAt;
  final String? profilePicture;
  final bool isDeleted;
  final DateTime? deletedAt;

  Employee({
    required this.userId,
    required this.fullName,
    required this.email,
    this.phone,
    required this.role,
    this.shopId,
    required this.status,
    this.visibility,
    this.createdAt,
    this.updatedAt,
    // Additional fields with defaults
    this.userTitle,
    this.isSuspended = false,
    this.suspensionReason,
    this.suspendedAt,
    this.suspendedBy,
    this.verified = false,
    this.emailVerified = false,
    this.isPaid = false,
    this.isActivated = false,
    this.lastLoginAt,
    this.profilePicture,
    this.isDeleted = false,
    this.deletedAt,
  });

  /// Check if user is admin
  bool get isAdmin => role == 'admin';

  /// Check if user is employee
  bool get isEmployee => role == 'employee';

  /// Check if user is active
  bool get isActive => status == 'active';

  /// Create a copy with some fields replaced
  Employee copyWith({
    String? userId,
    String? fullName,
    String? email,
    String? phone,
    String? role,
    String? shopId,
    String? status,
    EmployeeVisibility? visibility,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? userTitle,
    bool? isSuspended,
    String? suspensionReason,
    DateTime? suspendedAt,
    String? suspendedBy,
    bool? verified,
    bool? emailVerified,
    bool? isPaid,
    bool? isActivated,
    DateTime? lastLoginAt,
    String? profilePicture,
    bool? isDeleted,
    DateTime? deletedAt,
  }) {
    return Employee(
      userId: userId ?? this.userId,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      shopId: shopId ?? this.shopId,
      status: status ?? this.status,
      visibility: visibility ?? this.visibility,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      userTitle: userTitle ?? this.userTitle,
      isSuspended: isSuspended ?? this.isSuspended,
      suspensionReason: suspensionReason ?? this.suspensionReason,
      suspendedAt: suspendedAt ?? this.suspendedAt,
      suspendedBy: suspendedBy ?? this.suspendedBy,
      verified: verified ?? this.verified,
      emailVerified: emailVerified ?? this.emailVerified,
      isPaid: isPaid ?? this.isPaid,
      isActivated: isActivated ?? this.isActivated,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      profilePicture: profilePicture ?? this.profilePicture,
      isDeleted: isDeleted ?? this.isDeleted,
      deletedAt: deletedAt ?? this.deletedAt,
    );
  }
}

/// Employee visibility/permissions structure
class EmployeeVisibility {
  final ModulePermissions customerManagement;
  final ModulePermissions debtManagement;
  final ReportPermissions reportManagement;

  EmployeeVisibility({
    required this.customerManagement,
    required this.debtManagement,
    required this.reportManagement,
  });
}

/// Module permissions for customer and debt management
class ModulePermissions {
  final bool create;
  final bool update;
  final bool view;
  final bool delete;

  ModulePermissions({
    required this.create,
    required this.update,
    required this.view,
    required this.delete,
  });

  /// Check if all permissions are granted
  bool get hasFullAccess => create && update && view && delete;

  /// Check if no permissions are granted
  bool get hasNoAccess => !create && !update && !view && !delete;
}

/// Report permissions
class ReportPermissions {
  final bool generate;
  final bool delete;
  final bool view;

  ReportPermissions({
    required this.generate,
    required this.delete,
    required this.view,
  });

  /// Check if all permissions are granted
  bool get hasFullAccess => generate && delete && view;

  /// Check if no permissions are granted
  bool get hasNoAccess => !generate && !delete && !view;
}
