import 'package:deyncare_app/domain/repositories/auth_repository.dart';

/// Use case for checking the current authentication status
///
/// Following the single responsibility principle, this class only
/// handles checking if the user is currently authenticated.
class CheckAuthStatusUseCase {
  final AuthRepository _repository;

  /// Creates a new instance with the required repository
  CheckAuthStatusUseCase(this._repository);

  /// Executes the use case
  ///
  /// Returns a Future<bool> indicating whether the user is authenticated
  Future<bool> execute() async {
    return await _repository.isLoggedIn();
  }
}
