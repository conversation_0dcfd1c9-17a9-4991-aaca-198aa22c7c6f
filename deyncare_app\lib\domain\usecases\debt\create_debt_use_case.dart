import 'package:dartz/dartz.dart';
import 'package:deyncare_app/domain/repositories/debt_repository.dart';
import 'package:deyncare_app/domain/models/debt.dart';
import 'package:deyncare_app/domain/models/payment.dart';
import 'package:deyncare_app/core/errors/failures.dart';

/// Use case for creating new debt - BACKEND ALIGNED
/// Matches POST /api/debts endpoint
class CreateDebtUseCase {
  final DebtRepository _repository;

  CreateDebtUseCase(this._repository);

  /// Execute the use case to create debt
  Future<Either<Failure, Debt>> execute({
    required String customerId,
    required double debtAmount,
    required DateTime dueDate,
    String? description,
    double? paidAmount,
    DateTime? paidDate,
    PaymentMethod? paymentMethod,
  }) async {
    // Validate debt amount
    if (debtAmount <= 0) {
      return Left(ValidationFailure(message: 'Debt amount must be greater than 0'));
    }

    // Validate customer ID exists (basic validation)
    if (customerId.trim().isEmpty) {
      return Left(ValidationFailure(message: 'Customer ID is required'));
    }

    // Validate description length if provided
    if (description != null && description.length > 500) {
      return Left(ValidationFailure(message: 'Description must be less than 500 characters'));
    }

    // Validate paid amount if provided
    if (paidAmount != null && paidAmount < 0) {
      return Left(ValidationFailure(message: 'Paid amount cannot be negative'));
    }

    // Validate paid amount doesn't exceed debt amount
    if (paidAmount != null && paidAmount > debtAmount) {
      return Left(ValidationFailure(message: 'Paid amount cannot exceed debt amount'));
    }

    // If payment method is provided, paid amount and date must also be provided
    if (paymentMethod != null && (paidAmount == null || paidDate == null)) {
      return Left(ValidationFailure(message: 'Payment amount and date are required when payment method is specified'));
    }

    return await _repository.createDebt(
      customerId: customerId.trim(),
      amount: debtAmount,
      dueDate: dueDate,
      description: description?.trim(),
      paidAmount: paidAmount,
      paidDate: paidDate,
      paymentMethod: paymentMethod,
    );
  }
} 