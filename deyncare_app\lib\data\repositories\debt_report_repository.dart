import 'package:deyncare_app/data/network/clients/dio_client.dart';
import 'package:deyncare_app/data/models/debt_report_model.dart';
import 'package:flutter/foundation.dart';

/// Repository for debt report data operations
/// Handles API communication for debt report generation and statistics
class DebtReportRepository {
  final DioClient _dioClient;

  DebtReportRepository(this._dioClient);

  /// Get debt report data from the backend API
  /// Returns formatted debt data suitable for PDF generation
  Future<Map<String, dynamic>> getDebtReportData({
    String? startDate,
    String? endDate,
    String period = 'all',
  }) async {
    try {
      if (kDebugMode) {
        print('📊 [DEBT REPO] Fetching debt report data...');
        print('📊 [DEBT REPO] Period: $period');
        print('📊 [DEBT REPO] Date range: $startDate to $endDate');
      }

      // Build query parameters
      final Map<String, dynamic> queryParams = {
        'period': period,
      };

      if (startDate != null && endDate != null) {
        queryParams['startDate'] = startDate;
        queryParams['endDate'] = endDate;
      }

      if (kDebugMode) {
        print('📊 [DEBT REPO] Query params: $queryParams');
      }

      // Make API call
      final response = await _dioClient.get(
        '/reports/debts/data',
        queryParameters: queryParams,
      );

      if (kDebugMode) {
        print('📊 [DEBT REPO] API response type: ${response.runtimeType}');
      }

      // DioClient returns processed response data directly (not a Dio Response object)
      final responseData = response as Map<String, dynamic>;

      // Validate response structure
      if (!responseData.containsKey('data')) {
        throw Exception('Invalid API response: missing data field');
      }

      final data = responseData['data'] as Map<String, dynamic>;

      // Validate data structure
      if (!data.containsKey('debts')) {
        throw Exception('Invalid API response: missing debts field');
      }

      if (kDebugMode) {
        print('📊 [DEBT REPO] Response data keys: ${data.keys.toList()}');
        print('📊 [DEBT REPO] Debts count: ${(data['debts'] as List).length}');
      }

      // Parse debts using the report-specific model
      final List<DebtReportData> debts = (data['debts'] as List).map((json) {
        final debtJson = json as Map<String, dynamic>;
        if (kDebugMode) {
          print('🔍 Raw debt JSON keys: ${debtJson.keys.toList()}');
          print('🔍 Raw debt JSON: $debtJson');
          print('🔍 debtAmount field: ${debtJson['debtAmount']} (${debtJson['debtAmount'].runtimeType})');
          print('🔍 outstandingDebt field: ${debtJson['outstandingDebt']} (${debtJson['outstandingDebt'].runtimeType})');
          print('🔍 paidAmount field: ${debtJson['paidAmount']} (${debtJson['paidAmount'].runtimeType})');
          print('🔍 customerName: ${debtJson['customerName']}');
          print('🔍 debtId: ${debtJson['debtId']}');
        }
        final debt = DebtReportData.fromJson(debtJson);
        if (kDebugMode) {
          print('🔍 Parsed debt data:');
          print('   - Customer: ${debt.customerName}');
          print('   - Debt Amount: ${debt.debtAmount}');
          print('   - Outstanding Debt: ${debt.outstandingDebt}');
          print('   - Paid Amount: ${debt.paidAmount}');
          print('   - Overdue Debt: ${debt.overdueDebt}');
        }
        return debt;
      }).toList();

      if (kDebugMode) {
        print('✅ Successfully parsed ${debts.length} debts');
        if (debts.isNotEmpty) {
          final firstDebt = debts.first;
          print('🔍 First debt parsed data:');
          print('   - Customer: ${firstDebt.customerName}');
          print('   - Debt Amount: ${firstDebt.debtAmount}');
          print('   - Outstanding Debt: ${firstDebt.outstandingDebt}');
          print('   - Paid Amount: ${firstDebt.paidAmount}');
          print('   - Overdue Debt: ${firstDebt.overdueDebt}');
        }
      }

      // Extract summary data
      final summary = data['summary'] as Map<String, dynamic>? ?? {};

      if (kDebugMode) {
        print('📊 [DEBT REPO] Summary data: $summary');
      }

      return {
        'debts': debts,
        'summary': summary,
        'period': data['period'] ?? period,
        'generatedAt': data['generatedAt'] ?? DateTime.now().toIso8601String(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [DEBT REPO] Error fetching debt report data: $e');
      }
      rethrow;
    }
  }

  /// Get debt report statistics
  /// Returns aggregated statistics for debt reporting
  Future<Map<String, dynamic>> getDebtReportStats({
    String? startDate,
    String? endDate,
    String period = 'all',
  }) async {
    try {
      if (kDebugMode) {
        print('📊 [DEBT REPO] Fetching debt report statistics...');
      }

      // For now, we'll use the same endpoint and extract summary
      final reportData = await getDebtReportData(
        startDate: startDate,
        endDate: endDate,
        period: period,
      );

      final summary = reportData['summary'] as Map<String, dynamic>;
      final debts = reportData['debts'] as List<DebtReportData>;

      // Calculate additional statistics
      final stats = {
        ...summary,
        'averageDebtAmount': debts.isNotEmpty
            ? debts.fold<double>(0.0, (sum, debt) => sum + debt.debtAmount) / debts.length
            : 0.0,
        'averageOutstandingDebt': debts.isNotEmpty
            ? debts.fold<double>(0.0, (sum, debt) => sum + debt.outstandingDebt) / debts.length
            : 0.0,
        'paymentRate': summary['totalDebtAmount'] != null && summary['totalDebtAmount'] > 0
            ? ((summary['totalPaidAmount'] ?? 0) / summary['totalDebtAmount']) * 100
            : 0.0,
        'overdueRate': debts.isNotEmpty
            ? (debts.where((debt) => debt.overdueDebt > 0).length / debts.length) * 100
            : 0.0,
      };

      if (kDebugMode) {
        print('📊 [DEBT REPO] Calculated statistics: $stats');
      }

      return stats;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [DEBT REPO] Error fetching debt report statistics: $e');
      }
      rethrow;
    }
  }
}
