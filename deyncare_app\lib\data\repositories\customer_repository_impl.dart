import 'package:dartz/dartz.dart';
import 'package:deyncare_app/core/errors/failures.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:deyncare_app/core/utils/logger.dart';
import 'package:deyncare_app/data/models/customer_model.dart' as data_models;
import 'package:deyncare_app/data/network/customer/customer_remote_data_source.dart';
import 'package:deyncare_app/domain/repositories/customer_repository.dart';

/// Customer Repository Implementation
/// Matches backend customerRoutes.js and controllers 100%
class CustomerRepositoryImpl implements CustomerRepository {
  final CustomerRemoteDataSource _remoteDataSource;

  CustomerRepositoryImpl({
    required CustomerRemoteDataSource remoteDataSource,
  }) : _remoteDataSource = remoteDataSource;

  /// GET /api/customers - matches getAllCustomers controller
  @override
  Future<Either<Failure, data_models.CustomerListResponse>> getCustomers({
    data_models.CustomerQueryParams? params,
  }) async {
    try {
      logger.d('CustomerRepository: Making API request for customers');
      
      // Build search criteria
      final criteria = data_models.SearchCriteria(
        search: params?.search,
        riskLevel: params?.riskLevel,
        customerType: params?.customerType,
      );

      final response = await _remoteDataSource.getAllCustomers(
        page: params?.page ?? 1,
        limit: params?.limit ?? 20,
        criteria: criteria,
      );

      logger.i('CustomerRepository: Successfully retrieved customers');
      return Right(response);
    } on ApiException catch (e) {
      logger.e('CustomerRepository: API error getting customers: $e');
      
      // Handle specific authentication errors
      if (e.code == 'unauthorized' || e.code == 'token_expired') {
        return Left(AuthFailure(message: 'Your session has expired. Please log in again.', code: e.code));
      }
      
      return Left(ServerFailure(message: 'Failed to fetch customers: ${e.message}', code: e.code));
    } catch (e) {
      logger.e('CustomerRepository: Error getting customers: $e');
      return Left(ServerFailure(message: 'Failed to fetch customers: $e'));
    }
  }

  /// GET /api/customers/stats - matches getCustomerStats controller
  @override
  Future<Either<Failure, data_models.CustomerStatsResponse>> getCustomerStats() async {
    try {
      logger.d('CustomerRepository: Getting customer statistics');
      
      // This would typically be a separate API endpoint
      // For now, we'll calculate from getCustomers
      final result = await getCustomers();
      
      return result.fold(
        (failure) => Left(failure),
        (response) {
          // Access customers from the response data structure
          final customers = response.data?.customers ?? [];
          final statsResponse = data_models.CustomerStatsResponse(
            success: true,
            message: 'Customer stats retrieved successfully',
            data: data_models.CustomerStats(
              totalCustomers: customers.length,
              activeCustomers: customers.length, // Assuming all retrieved customers are active
              newCustomers: customers.where((c) => c.customerType == 'New').length,
              returningCustomers: customers.where((c) => c.customerType == 'Returning').length,
              riskDistribution: {
                'Low Risk': customers.where((c) => c.riskProfile.currentRiskLevel == 'Low Risk').length,
                'Medium Risk': customers.where((c) => c.riskProfile.currentRiskLevel == 'Medium Risk').length,
                'High Risk': customers.where((c) => c.riskProfile.currentRiskLevel == 'High Risk').length,
                'Active Debt': customers.where((c) => c.riskProfile.currentRiskLevel == 'Active Debt').length,
              },
              categoryDistribution: {},
              totalOutstandingBalance: 0.0,
              totalCreditLimit: 0.0,
              avgRiskScore: 0.0,
            ),
          );
          
          logger.i('CustomerRepository: Generated customer stats: ${statsResponse.data}');
          return Right(statsResponse);
        },
      );
    } catch (e, stackTrace) {
      logger.e('CustomerRepository: Error getting customer stats: $e');
      logger.e('CustomerRepository: Stack trace: $stackTrace');
      return Left(ServerFailure(message: 'Failed to get customer stats: $e'));
    }
  }

  /// GET /api/customers/:customerId - matches getCustomerById controller
  @override
  Future<Either<Failure, data_models.CustomerDetailResponse>> getCustomerById(
    String customerId,
  ) async {
    try {
      logger.d('CustomerRepository: Getting customer details for ID: $customerId');
      
      final response = await _remoteDataSource.getCustomerById(customerId);
      
      logger.i('CustomerRepository: Got customer detail response');
      logger.d('CustomerRepository: Response structure: ${response.keys.toList()}');
      
      // Parse the response safely - the backend returns {success, message, data: {customer: {...}}}
      if (response['success'] != true) {
        return Left(ServerFailure(message: response['message'] ?? 'Unknown error'));
      }
      
      final data = response['data'] as Map<String, dynamic>?;
      if (data == null) {
        return Left(ServerFailure(message: 'No data in response'));
      }
      
      final customerData = data['customer'] as Map<String, dynamic>?;
      if (customerData == null) {
        return Left(ServerFailure(message: 'No customer data in response'));
      }
      
      // Build the response with correct structure
      final customerResponse = data_models.CustomerDetailResponse(
        success: true,
        message: response['message'] as String? ?? 'Customer retrieved successfully',
        data: data_models.CustomerDetailData(
          customer: data_models.CustomerBasicInfo(
            customerId: customerData['customerId'] as String?,
            customerName: customerData['customerName'] as String?,
            phone: customerData['phone'] as String?,
            customerType: customerData['customerType'] as String?,
            createdAt: customerData['createdAt'] is String 
              ? DateTime.tryParse(customerData['createdAt']) 
              : null,
            updatedAt: customerData['updatedAt'] is String 
              ? DateTime.tryParse(customerData['updatedAt']) 
              : null,
          ),
          statistics: data['statistics'] != null 
            ? _buildSafeCustomerStatistics(data['statistics'] as Map<String, dynamic>)
            : null,
          riskProfile: data['riskProfile'] != null 
            ? _buildSafeRiskProfile(data['riskProfile'] as Map<String, dynamic>)
            : null,
          debtHistory: data['debtHistory'] != null 
            ? _buildSafeDebtHistory(data['debtHistory'] as Map<String, dynamic>)
            : null,
          paymentHistory: data['paymentHistory'] != null 
            ? _buildSafePaymentHistory(data['paymentHistory'] as Map<String, dynamic>)
            : null,
        ),
      );
      
      return Right(customerResponse);
    } on ApiException catch (e) {
      logger.e('CustomerRepository: API error getting customer $customerId: $e');
      
      // Handle specific authentication errors
      if (e.code == 'unauthorized' || e.code == 'token_expired') {
        return Left(AuthFailure(message: 'Your session has expired. Please log in again.', code: e.code));
      }
      
      if (e.code == 'not_found') {
        return Left(NotFoundFailure(message: 'Customer not found.', code: e.code));
      }
      
      return Left(ServerFailure(message: 'Failed to get customer: ${e.message}', code: e.code));
    } catch (e, stackTrace) {
      logger.e('CustomerRepository: Unexpected error getting customer $customerId: $e');
      logger.e('CustomerRepository: Stack trace: $stackTrace');
      return Left(ServerFailure(message: 'Failed to get customer: $e'));
    }
  }

  /// POST /api/customers - matches createCustomer controller
  @override
  Future<Either<Failure, data_models.CustomerDetailResponse>> createCustomer(
    data_models.CreateCustomerRequest request,
  ) async {
    try {
      logger.d('CustomerRepository: Creating customer with name: ${request.customerName}');
      
      final response = await _remoteDataSource.createCustomer(request);
      
      logger.i('CustomerRepository: Customer created successfully');
      
      if (!response['success']) {
        return Left(ServerFailure(message: response['message'] ?? 'Unknown error'));
      }
      
      // Parse response as CustomerDetailResponse with safe parsing
      final customerResponse = _parseCustomerDetailResponse(response);
      
      return Right(customerResponse);
    } on ApiException catch (e) {
      logger.e('CustomerRepository: API error creating customer: $e');
      
      // Handle specific authentication errors
      if (e.code == 'unauthorized' || e.code == 'token_expired') {
        return Left(AuthFailure(message: 'Your session has expired. Please log in again.', code: e.code));
      }
      
      if (e.code == 'validation_error' || e.code == 'bad_request') {
        return Left(ValidationFailure(message: e.message, code: e.code, data: e.data));
      }
      
      return Left(ServerFailure(message: 'Failed to create customer: ${e.message}', code: e.code));
    } catch (e, stackTrace) {
      logger.e('CustomerRepository: Error creating customer: $e');
      logger.e('CustomerRepository: Stack trace: $stackTrace');
      return Left(ServerFailure(message: 'Failed to create customer: $e'));
    }
  }

  /// PUT /api/customers/:customerId - matches updateCustomer controller
  @override
  Future<Either<Failure, data_models.CustomerDetailResponse>> updateCustomer(
    String customerId,
    data_models.UpdateCustomerRequest request,
  ) async {
    try {
      logger.d('CustomerRepository: Updating customer: $customerId');
      final response = await _remoteDataSource.updateCustomer(customerId, request);

      logger.i('CustomerRepository: Customer updated successfully');

      if (response['success'] != true) {
        return Left(ServerFailure(message: response['message'] ?? 'Unknown error'));
      }
      if (response['data'] == null) {
        logger.e('CustomerRepository: Update response missing data field');
        return Left(ServerFailure(message: 'Update failed: missing data in response'));
      }

      // Parse response as CustomerDetailResponse with safe parsing
      final customerResponse = _parseCustomerDetailResponse(response);
      return Right(customerResponse);
    } on ApiException catch (e) {
      logger.e('CustomerRepository: API error updating customer: $e');
      
      // Handle specific authentication errors
      if (e.code == 'unauthorized' || e.code == 'token_expired') {
        return Left(AuthFailure(message: 'Your session has expired. Please log in again.', code: e.code));
      }
      
      if (e.code == 'not_found') {
        return Left(NotFoundFailure(message: 'Customer not found.', code: e.code));
      }
      
      if (e.code == 'validation_error' || e.code == 'bad_request') {
        return Left(ValidationFailure(message: e.message, code: e.code, data: e.data));
      }
      
      return Left(ServerFailure(message: 'Failed to update customer: ${e.message}', code: e.code));
    } catch (e, stackTrace) {
      logger.e('CustomerRepository: Error updating customer: $e');
      logger.e('CustomerRepository: Stack trace: $stackTrace');
      return Left(ServerFailure(message: 'Failed to update customer: $e'));
    }
  }

  /// DELETE /api/customers/:customerId - matches deleteCustomer controller
  @override
  Future<Either<Failure, bool>> deleteCustomer(
    String customerId, {
    bool force = false,
  }) async {
    try {
      logger.d('CustomerRepository: Deleting customer: $customerId (force: $force)');
      await _remoteDataSource.deleteCustomer(customerId, force: force);

      logger.i('CustomerRepository: Customer deleted successfully');
      return const Right(true);
    } on ApiException catch (e) {
      logger.e('CustomerRepository: API error deleting customer: $e');
      
      // Handle specific authentication errors
      if (e.code == 'unauthorized' || e.code == 'token_expired') {
        return Left(AuthFailure(message: 'Your session has expired. Please log in again.', code: e.code));
      }
      
      if (e.code == 'not_found') {
        return Left(NotFoundFailure(message: 'Customer not found.', code: e.code));
      }
      
      // Handle specific business rules for customer deletion
      if (e.code == 'customer_has_active_debts') {
        return Left(ValidationFailure(
          message: 'Cannot delete customer with active debts. Please resolve all outstanding debts first.',
          code: e.code,
          data: e.data,
        ));
      }
      
      if (e.code == 'recent_activity_exists') {
        return Left(ValidationFailure(
          message: 'Cannot delete customer with recent activity. Customer has transactions within the last 30 days.',
          code: e.code,
          data: e.data,
        ));
      }
      
      if (e.code == 'customer_has_debt_history') {
        return Left(ValidationFailure(
          message: 'Customer has debt history. Add force parameter to proceed with deletion.',
          code: e.code,
          data: e.data,
        ));
      }
      
      if (e.code == 'validation_error' || e.code == 'bad_request') {
        return Left(ValidationFailure(message: e.message, code: e.code, data: e.data));
      }
      
      return Left(ServerFailure(message: 'Failed to delete customer: ${e.message}', code: e.code));
    } catch (e, stackTrace) {
      logger.e('CustomerRepository: Error deleting customer: $e');
      logger.e('CustomerRepository: Stack trace: $stackTrace');
      return Left(ServerFailure(message: 'Failed to delete customer: $e'));
    }
  }

  /// GET /api/customers/:customerId/debts
  @override
  Future<Either<Failure, data_models.CustomerDebtsResponse>> getCustomerDebts(
    String customerId, {
    bool includeCompleted = false,
  }) async {
    try {
      logger.d('CustomerRepository: Getting debts for customer: $customerId');
      
      // For now, return an empty response until debt integration is implemented
      final response = data_models.CustomerDebtsResponse(
        success: true,
        message: 'Customer debts retrieved successfully',
        data: [], // Empty list for now, until debt integration is complete
      );
      
      logger.w('CustomerRepository: Customer debts not fully implemented yet');
      return Right(response);
    } catch (e, stackTrace) {
      logger.e('CustomerRepository: Error getting customer debts: $e');
      logger.e('CustomerRepository: Stack trace: $stackTrace');
      return Left(ServerFailure(message: 'Failed to get customer debts: $e'));
    }
  }

  // ===================================
  // PRIVATE HELPER METHODS
  // ===================================

  /// Safely parse customer detail response with enhanced error handling
  data_models.CustomerDetailResponse _parseCustomerDetailResponse(Map<String, dynamic> response) {
    try {
      // First try the normal parsing
      return data_models.CustomerDetailResponse.fromJson(response);
    } catch (e) {
      logger.w('CustomerRepository: Standard parsing failed, attempting safe parsing: $e');
      
      // If standard parsing fails, try safe parsing with null handling
      return _createSafeCustomerDetailResponse(response);
    }
  }
  
  /// Create a safe customer detail response by handling null values gracefully
  data_models.CustomerDetailResponse _createSafeCustomerDetailResponse(Map<String, dynamic> response) {
    try {
      // Use a more robust approach - manually construct the response with proper error handling
      return _buildSafeCustomerDetailResponse(response);
    } catch (e) {
      logger.e('CustomerRepository: Failed to parse customer response: $e');
      logger.e('CustomerRepository: Response data keys: ${response.keys.toList()}');
      
      // Try to identify the problematic field
      if (response['data'] is Map<String, dynamic>) {
        final data = response['data'] as Map<String, dynamic>;
        logger.e('CustomerRepository: Data keys: ${data.keys.toList()}');
        
        if (data['statistics'] is Map<String, dynamic>) {
          final stats = data['statistics'] as Map<String, dynamic>;
          logger.e('CustomerRepository: Statistics keys: ${stats.keys.toList()}');
          
          // Log specific field types that might be causing issues
          const statisticsFields = ['totalDebts', 'activeDebts', 'paidDebts'];
          for (final field in statisticsFields) {
            if (stats.containsKey(field)) {
              logger.e('CustomerRepository: $field type: ${stats[field].runtimeType}, value: ${stats[field]}');
            }
          }
          
          if (stats['financials'] is Map<String, dynamic>) {
            final financials = stats['financials'] as Map<String, dynamic>;
            logger.e('CustomerRepository: Financials keys: ${financials.keys.toList()}');
            
            const financialFields = ['totalBorrowed', 'totalOutstanding', 'totalPaid', 'averageDebtAmount', 'collectionRate'];
            for (final field in financialFields) {
              if (financials.containsKey(field)) {
                logger.e('CustomerRepository: $field type: ${financials[field].runtimeType}, value: ${financials[field]}');
              }
            }
          }
          
          if (stats['paymentBehavior'] is Map<String, dynamic>) {
            final paymentBehavior = stats['paymentBehavior'] as Map<String, dynamic>;
            logger.e('CustomerRepository: PaymentBehavior keys: ${paymentBehavior.keys.toList()}');
            
            const paymentFields = ['totalPayments', 'averagePaymentAmount', 'onTimePayments', 'latePayments', 'averagePaymentDelay', 'maxPaymentDelay'];
            for (final field in paymentFields) {
              if (paymentBehavior.containsKey(field)) {
                logger.e('CustomerRepository: $field type: ${paymentBehavior[field].runtimeType}, value: ${paymentBehavior[field]}');
              }
            }
          }
          
          if (stats['riskAnalysis'] is Map<String, dynamic>) {
            final riskAnalysis = stats['riskAnalysis'] as Map<String, dynamic>;
            logger.e('CustomerRepository: RiskAnalysis keys: ${riskAnalysis.keys.toList()}');
            
            const riskFields = ['riskScore', 'assessmentHistory'];
            for (final field in riskFields) {
              if (riskAnalysis.containsKey(field)) {
                logger.e('CustomerRepository: $field type: ${riskAnalysis[field].runtimeType}, value: ${riskAnalysis[field]}');
              }
            }
          }
        }
        
        // Check debtHistory section
        if (data['debtHistory'] is Map<String, dynamic>) {
          final debtHistory = data['debtHistory'] as Map<String, dynamic>;
          logger.e('CustomerRepository: DebtHistory keys: ${debtHistory.keys.toList()}');
          
          if (debtHistory['debts'] is List) {
            final debts = debtHistory['debts'] as List;
            logger.e('CustomerRepository: Debts count: ${debts.length}');
            for (int i = 0; i < debts.length && i < 1; i++) { // Only check first debt
              if (debts[i] is Map<String, dynamic>) {
                final debt = debts[i] as Map<String, dynamic>;
                logger.e('CustomerRepository: Debt[$i] keys: ${debt.keys.toList()}');
                const debtFields = ['amount', 'outstandingAmount', 'paidAmount', 'paymentRatio', 'paymentDelay'];
                for (final field in debtFields) {
                  if (debt.containsKey(field)) {
                    logger.e('CustomerRepository: Debt[$i].$field type: ${debt[field].runtimeType}, value: ${debt[field]}');
                  }
                }
              }
            }
          }
        }
      }
      
      rethrow;
    }
  }
  
  /// Build a safe customer detail response by ensuring all fields are properly typed
  data_models.CustomerDetailResponse _buildSafeCustomerDetailResponse(Map<String, dynamic> response) {
    // Extract and validate basic response structure
    final success = response['success'] as bool? ?? false;
    final message = response['message'] as String? ?? '';
    final data = response['data'] as Map<String, dynamic>? ?? {};
    
    // Build customer detail data safely
    final safeData = _buildSafeCustomerDetailData(data);
    
    return data_models.CustomerDetailResponse(
      success: success,
      message: message,
      data: safeData,
    );
  }
  
  /// Build safe customer detail data with proper type handling
  data_models.CustomerDetailData _buildSafeCustomerDetailData(Map<String, dynamic> data) {
    // Extract customer basic info
    final customerData = data['customer'] as Map<String, dynamic>? ?? {};
    final customer = _buildSafeCustomerBasicInfo(customerData);
    
    // Extract optional sections with null safety
    final statisticsData = data['statistics'] as Map<String, dynamic>?;
    final statistics = statisticsData != null ? _buildSafeCustomerStatistics(statisticsData) : null;
    
    final debtHistoryData = data['debtHistory'] as Map<String, dynamic>?;
    final debtHistory = debtHistoryData != null ? _buildSafeDebtHistory(debtHistoryData) : null;
    
    final paymentHistoryData = data['paymentHistory'] as Map<String, dynamic>?;
    final paymentHistory = paymentHistoryData != null ? _buildSafePaymentHistory(paymentHistoryData) : null;
    
    final recentActivityData = data['recentActivity'] as Map<String, dynamic>?;
    final recentActivity = recentActivityData != null ? _buildSafeRecentActivity(recentActivityData) : null;
    
    final riskProfileData = data['riskProfile'] as Map<String, dynamic>?;
    final riskProfile = riskProfileData != null ? _buildSafeRiskProfile(riskProfileData) : null;
    
    return data_models.CustomerDetailData(
      customer: customer,
      statistics: statistics,
      debtHistory: debtHistory,
      paymentHistory: paymentHistory,
      recentActivity: recentActivity,
      riskProfile: riskProfile,
    );
  }
  
  /// Build safe customer basic info
  data_models.CustomerBasicInfo _buildSafeCustomerBasicInfo(Map<String, dynamic> data) {
    return data_models.CustomerBasicInfo(
      customerId: data['customerId'] as String?,
      customerName: data['customerName'] as String?,
      phone: data['phone'] as String?,
      customerType: data['customerType'] as String?,
      createdAt: data['createdAt'] is String ? DateTime.tryParse(data['createdAt']) : null,
      updatedAt: data['updatedAt'] is String ? DateTime.tryParse(data['updatedAt']) : null,
    );
  }
  
  /// Build safe customer statistics
  data_models.CustomerStatistics _buildSafeCustomerStatistics(Map<String, dynamic> data) {
    return data_models.CustomerStatistics(
      totalDebts: _safeToInt(data['totalDebts']) ?? 0,
      activeDebts: _safeToInt(data['activeDebts']) ?? 0,
      paidDebts: _safeToInt(data['paidDebts']) ?? 0,
      financials: data['financials'] is Map<String, dynamic> 
        ? _buildSafeCustomerFinancials(data['financials'] as Map<String, dynamic>)
        : data_models.CustomerFinancials(
            totalBorrowed: 0.0,
            totalOutstanding: 0.0,
            totalPaid: 0.0,
            averageDebtAmount: 0.0,
            collectionRate: 0,
          ),
      paymentBehavior: data['paymentBehavior'] is Map<String, dynamic>
        ? _buildSafePaymentBehavior(data['paymentBehavior'] as Map<String, dynamic>)
        : data_models.PaymentBehavior(
            totalPayments: 0,
            averagePaymentAmount: 0.0,
            onTimePayments: 0,
            latePayments: 0,
            averagePaymentDelay: 0,
            maxPaymentDelay: 0,
          ),
      riskAnalysis: data['riskAnalysis'] is Map<String, dynamic>
        ? _buildSafeRiskAnalysis(data['riskAnalysis'] as Map<String, dynamic>)
        : data_models.RiskAnalysis(
            currentRiskLevel: 'Not Assessed',
            riskScore: 0.0,
            lastAssessment: null,
            assessmentHistory: 0,
            riskFactors: [],
          ),
    );
  }

  /// Build safe risk profile
  data_models.RiskProfile _buildSafeRiskProfile(Map<String, dynamic> data) {
    return data_models.RiskProfile(
      currentRiskLevel: data['currentRiskLevel'] as String? ?? 'Not Assessed',
      riskScore: _safeToDouble(data['riskScore']) ?? 0.0,
      lastAssessment: data['lastAssessment'] is String 
        ? DateTime.tryParse(data['lastAssessment']) 
        : null,
      assessmentCount: _safeToInt(data['assessmentCount']) ?? 0,
    );
  }
  
  /// Build safe customer financials
  data_models.CustomerFinancials _buildSafeCustomerFinancials(Map<String, dynamic> data) {
    return data_models.CustomerFinancials(
      totalBorrowed: _safeToDouble(data['totalBorrowed']),
      totalOutstanding: _safeToDouble(data['totalOutstanding']),
      totalPaid: _safeToDouble(data['totalPaid']),
      averageDebtAmount: _safeToDouble(data['averageDebtAmount']),
      collectionRate: _safeToInt(data['collectionRate']),
    );
  }
  
  /// Build safe payment behavior
  data_models.PaymentBehavior _buildSafePaymentBehavior(Map<String, dynamic> data) {
    return data_models.PaymentBehavior(
      totalPayments: _safeToInt(data['totalPayments']),
      averagePaymentAmount: _safeToDouble(data['averagePaymentAmount']),
      onTimePayments: _safeToInt(data['onTimePayments']),
      latePayments: _safeToInt(data['latePayments']),
      averagePaymentDelay: _safeToInt(data['averagePaymentDelay']),
      maxPaymentDelay: _safeToInt(data['maxPaymentDelay']),
    );
  }
  
  /// Build safe risk analysis
  data_models.RiskAnalysis _buildSafeRiskAnalysis(Map<String, dynamic> data) {
    return data_models.RiskAnalysis(
      currentRiskLevel: data['currentRiskLevel'] as String?,
      riskScore: _safeToDouble(data['riskScore']),
      lastAssessment: data['lastAssessment'] is String ? DateTime.tryParse(data['lastAssessment']) : null,
      assessmentHistory: _safeToInt(data['assessmentHistory']),
      riskFactors: (data['riskFactors'] as List<dynamic>?)?.map((e) => e.toString()).toList(),
    );
  }
  
  /// Build safe debt history
  data_models.DebtHistory _buildSafeDebtHistory(Map<String, dynamic> data) {
    final debtsData = data['debts'] as List<dynamic>?;
    final debts = <data_models.DebtSummary>[];
    
    if (debtsData != null) {
      for (final debtData in debtsData) {
        if (debtData is Map<String, dynamic>) {
          debts.add(_buildSafeDebtSummary(debtData));
        }
      }
    }
    
    return data_models.DebtHistory(
      total: _safeToInt(data['total']),
      debts: debts,
    );
  }
  
  /// Build safe debt summary
  data_models.DebtSummary _buildSafeDebtSummary(Map<String, dynamic> data) {
    return data_models.DebtSummary(
      debtId: data['debtId'] as String?,
      amount: _safeToDouble(data['amount']),
      outstandingAmount: _safeToDouble(data['outstandingAmount']),
      paidAmount: _safeToDouble(data['paidAmount']),
      paymentRatio: _safeToInt(data['paymentRatio']),
      dueDate: data['dueDate'] is String ? DateTime.tryParse(data['dueDate']) : null,
      riskLevel: data['riskLevel'] as String?,
      paymentDelay: _safeToInt(data['paymentDelay']),
      isOnTime: data['isOnTime'] as bool?,
      status: data['status'] as String?,
      createdAt: data['createdAt'] is String ? DateTime.tryParse(data['createdAt']) : null,
    );
  }
  
  /// Build safe payment history
  data_models.PaymentHistory _buildSafePaymentHistory(Map<String, dynamic> data) {
    final recentData = data['recent'] as List<dynamic>?;
    final recent = <data_models.PaymentSummary>[];
    
    if (recentData != null) {
      for (final paymentData in recentData) {
        if (paymentData is Map<String, dynamic>) {
          recent.add(_buildSafePaymentSummary(paymentData));
        }
      }
    }
    
    return data_models.PaymentHistory(
      total: _safeToInt(data['total']),
      recent: recent,
      note: data['note'] as String?,
    );
  }
  
  /// Build safe payment summary
  data_models.PaymentSummary _buildSafePaymentSummary(Map<String, dynamic> data) {
    final paymentId = data['paymentId'] as String? ?? 'unknown';
    final amount = _safeToDouble(data['amount']) ?? 0.0;
    final paymentDate = data['paymentDate'] is String ? DateTime.tryParse(data['paymentDate']) ?? DateTime.now() : DateTime.now();
    final paymentMethod = data['paymentMethod'] as String? ?? 'unknown';
    
    return data_models.PaymentSummary(
      paymentId: paymentId,
      debtId: data['debtId'] as String?,
      amount: amount,
      paymentDate: paymentDate,
      paymentMethod: paymentMethod,
      isOnTime: data['isOnTime'] as bool?,
      paymentDelay: _safeToInt(data['paymentDelay']),
      notes: data['notes'] as String?,
    );
  }
  
  /// Build safe recent activity
  data_models.RecentActivity _buildSafeRecentActivity(Map<String, dynamic> data) {
    return data_models.RecentActivity(
      newDebtsLast30Days: _safeToInt(data['newDebtsLast30Days']),
      paymentsLast30Days: _safeToInt(data['paymentsLast30Days']),
      amountPaidLast30Days: _safeToDouble(data['amountPaidLast30Days']),
      lastPaymentDate: data['lastPaymentDate'] is String ? DateTime.tryParse(data['lastPaymentDate']) : null,
      lastDebtDate: data['lastDebtDate'] is String ? DateTime.tryParse(data['lastDebtDate']) : null,
    );
  }
  
  /// Safely convert a value to int
  int? _safeToInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value);
    return null;
  }
  
  /// Safely convert a value to double
  double? _safeToDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }
} 