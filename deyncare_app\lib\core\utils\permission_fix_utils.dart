import 'package:deyncare_app/domain/models/user.dart';

/// Utility to fix missing permission data for specific users
/// This is a temporary workaround until the backend properly sends visibility data
class PermissionFixUtils {
  /// Fix permissions for known users who should have specific access
  static User fixUserPermissions(User user) {
    // If visibility is already properly set, don't modify
    if (user.visibility != null && _hasValidVisibilityStructure(user.visibility!)) {
      return user;
    }
    
    // Apply fixes based on user email or other identifiers
    if (user.email == '<EMAIL>' || user.fullName.toLowerCase() == 'carab') {
      return _applyCarabPermissions(user);
    }
    
    // For other employees, apply default employee permissions
    if (user.isEmployee) {
      return _applyDefaultEmployeePermissions(user);
    }
    
    return user;
  }
  
  /// Apply <PERSON><PERSON>'s specific permissions (debt + report management)
  static User _applyCarabPermissions(User user) {
    final visibility = {
      'customerManagement': {
        'create': false,
        'update': false,
        'view': false,
        'delete': false,
      },
      'debtManagement': {
        'create': true,
        'update': true,
        'view': true,
        'delete': true,
      },
      'reportManagement': {
        'generate': true,
        'view': true,
        'delete': true,
      },
    };
    
    return user.copyWith(
      visibility: visibility,
      permissions: [
        'debt_create',
        'debt_update',
        'debt_view',
        'debt_delete',
        'report_generate',
        'report_view',
        'report_delete',
      ],
    );
  }
  
  /// Apply default employee permissions (no access)
  static User _applyDefaultEmployeePermissions(User user) {
    final visibility = {
      'customerManagement': {
        'create': false,
        'update': false,
        'view': false,
        'delete': false,
      },
      'debtManagement': {
        'create': false,
        'update': false,
        'view': false,
        'delete': false,
      },
      'reportManagement': {
        'generate': false,
        'view': false,
        'delete': false,
      },
    };
    
    return user.copyWith(
      visibility: visibility,
      permissions: [],
    );
  }
  
  /// Check if visibility structure is valid and complete
  static bool _hasValidVisibilityStructure(Map<String, dynamic> visibility) {
    // Check if all required sections exist
    if (!visibility.containsKey('customerManagement') ||
        !visibility.containsKey('debtManagement') ||
        !visibility.containsKey('reportManagement')) {
      return false;
    }
    
    // Check if each section has the required permissions
    final customerMgmt = visibility['customerManagement'];
    final debtMgmt = visibility['debtManagement'];
    final reportMgmt = visibility['reportManagement'];
    
    if (customerMgmt is! Map<String, dynamic> ||
        debtMgmt is! Map<String, dynamic> ||
        reportMgmt is! Map<String, dynamic>) {
      return false;
    }
    
    // Check customer management permissions
    if (!customerMgmt.containsKey('create') ||
        !customerMgmt.containsKey('update') ||
        !customerMgmt.containsKey('view') ||
        !customerMgmt.containsKey('delete')) {
      return false;
    }
    
    // Check debt management permissions
    if (!debtMgmt.containsKey('create') ||
        !debtMgmt.containsKey('update') ||
        !debtMgmt.containsKey('view') ||
        !debtMgmt.containsKey('delete')) {
      return false;
    }
    
    // Check report management permissions
    if (!reportMgmt.containsKey('generate') ||
        !reportMgmt.containsKey('view') ||
        !reportMgmt.containsKey('delete')) {
      return false;
    }
    
    return true;
  }
  
  /// Get permission summary for debugging
  static String getPermissionSummary(User user) {
    final buffer = StringBuffer();
    buffer.writeln('User: ${user.fullName} (${user.email})');
    buffer.writeln('Role: ${user.role}');
    
    if (user.visibility != null) {
      buffer.writeln('Visibility Structure: Present');
      
      final customerMgmt = user.visibility!['customerManagement'] as Map<String, dynamic>?;
      if (customerMgmt != null) {
        final customerPerms = customerMgmt.entries
            .where((e) => e.value == true)
            .map((e) => e.key)
            .toList();
        buffer.writeln('Customer Permissions: ${customerPerms.isEmpty ? 'None' : customerPerms.join(', ')}');
      }
      
      final debtMgmt = user.visibility!['debtManagement'] as Map<String, dynamic>?;
      if (debtMgmt != null) {
        final debtPerms = debtMgmt.entries
            .where((e) => e.value == true)
            .map((e) => e.key)
            .toList();
        buffer.writeln('Debt Permissions: ${debtPerms.isEmpty ? 'None' : debtPerms.join(', ')}');
      }
      
      final reportMgmt = user.visibility!['reportManagement'] as Map<String, dynamic>?;
      if (reportMgmt != null) {
        final reportPerms = reportMgmt.entries
            .where((e) => e.value == true)
            .map((e) => e.key)
            .toList();
        buffer.writeln('Report Permissions: ${reportPerms.isEmpty ? 'None' : reportPerms.join(', ')}');
      }
    } else {
      buffer.writeln('Visibility Structure: MISSING');
      buffer.writeln('Legacy Permissions: ${user.permissions ?? 'None'}');
    }
    
    return buffer.toString();
  }
}
