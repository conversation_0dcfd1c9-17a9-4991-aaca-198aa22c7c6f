import 'package:deyncare_app/domain/models/auth_token.dart';

/// User entity representing authenticated user data
class User {
  final String userId;
  final String fullName;
  final String email;
  final String? phone;
  final String role;
  final String? shopId;
  final String status;
  final List<String>? permissions;
  final Map<String, dynamic>?
      visibility; // Add visibility field for employee permissions
  final String registrationStatus;
  final bool isEmailVerified;
  final bool isPaid;
  final DateTime? emailVerifiedAt;
  final DateTime? paymentCompletedAt;
  final String? verificationCode;
  final DateTime? verificationCodeExpiresAt;
  final String? shopName;
  final String? shopStatus;
  final bool isShopActive;
  final String? accessToken;
  final String? refreshToken;
  final DateTime? tokenExpiresAt;

  // Suspension-related fields
  final bool isSuspended;
  final String? suspensionReason;
  final DateTime? suspendedAt;
  final String? suspendedBy;

  User({
    required this.userId,
    required this.fullName,
    required this.email,
    this.phone,
    required this.role,
    this.shopId,
    required this.status,
    this.permissions,
    this.visibility,
    required this.registrationStatus,
    required this.isEmailVerified,
    required this.isPaid,
    this.emailVerifiedAt,
    this.paymentCompletedAt,
    this.verificationCode,
    this.verificationCodeExpiresAt,
    this.shopName,
    this.shopStatus,
    required this.isShopActive,
    this.accessToken,
    this.refreshToken,
    this.tokenExpiresAt,
    this.isSuspended = false,
    this.suspensionReason,
    this.suspendedAt,
    this.suspendedBy,
  });

  /// Check if user has given permission
  bool hasPermission(String permission) {
    if (role == 'admin' || role == 'superAdmin') {
      return true; // Admins have all permissions
    }
    return permissions?.contains(permission) ?? false;
  }

  /// Check if user is an admin (either shop admin or super admin)
  bool get isAdmin => role == 'admin' || role == 'superAdmin';

  /// Check if user is a shop admin
  bool get isShopAdmin => role == 'admin';

  /// Check if user is a super admin
  bool get isSuperAdmin => role == 'superAdmin';

  /// Check if user is an employee
  bool get isEmployee => role == 'employee';

  /// Check if user account is active
  bool get isActive => status == 'active';

  /// Check if user is suspended
  bool get isAccountSuspended => isSuspended || status == 'suspended';

  /// Check if user can access mobile app (only admin and employee roles)
  bool get canAccessMobileApp => role == 'admin' || role == 'employee';

  /// Check if user should be blocked from using the app
  bool get shouldBeBlocked => isAccountSuspended || !canAccessMobileApp;

  /// Create a copy of User with some fields replaced
  User copyWith({
    String? userId,
    String? fullName,
    String? email,
    String? phone,
    String? role,
    String? shopId,
    String? status,
    List<String>? permissions,
    Map<String, dynamic>? visibility,
    String? registrationStatus,
    bool? isEmailVerified,
    bool? isPaid,
    DateTime? emailVerifiedAt,
    DateTime? paymentCompletedAt,
    String? verificationCode,
    DateTime? verificationCodeExpiresAt,
    String? shopName,
    String? shopStatus,
    bool? isShopActive,
    String? accessToken,
    String? refreshToken,
    DateTime? tokenExpiresAt,
    bool? isSuspended,
    String? suspensionReason,
    DateTime? suspendedAt,
    String? suspendedBy,
  }) {
    return User(
      userId: userId ?? this.userId,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      shopId: shopId ?? this.shopId,
      status: status ?? this.status,
      permissions: permissions ?? this.permissions,
      visibility: visibility ?? this.visibility,
      registrationStatus: registrationStatus ?? this.registrationStatus,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPaid: isPaid ?? this.isPaid,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      paymentCompletedAt: paymentCompletedAt ?? this.paymentCompletedAt,
      verificationCode: verificationCode ?? this.verificationCode,
      verificationCodeExpiresAt:
          verificationCodeExpiresAt ?? this.verificationCodeExpiresAt,
      shopName: shopName ?? this.shopName,
      shopStatus: shopStatus ?? this.shopStatus,
      isShopActive: isShopActive ?? this.isShopActive,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      tokenExpiresAt: tokenExpiresAt ?? this.tokenExpiresAt,
      isSuspended: isSuspended ?? this.isSuspended,
      suspensionReason: suspensionReason ?? this.suspensionReason,
      suspendedAt: suspendedAt ?? this.suspendedAt,
      suspendedBy: suspendedBy ?? this.suspendedBy,
    );
  }

  /// Converts this User object to an AuthToken object
  AuthToken? toAuthToken() {
    if (accessToken != null && refreshToken != null && tokenExpiresAt != null) {
      return AuthToken(
        accessToken: accessToken!,
        refreshToken: refreshToken!,
        expiresAt: tokenExpiresAt!,
      );
    } else if (accessToken != null && refreshToken != null) {
      // If we have tokens but no expiry, set a default expiry time (1 hour from now)
      return AuthToken(
        accessToken: accessToken!,
        refreshToken: refreshToken!,
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      );
    } else {
      return null; // Cannot create AuthToken if token details are missing
    }
  }
}
