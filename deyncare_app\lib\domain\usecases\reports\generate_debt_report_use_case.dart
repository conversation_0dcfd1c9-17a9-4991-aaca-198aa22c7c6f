import 'package:deyncare_app/data/repositories/debt_report_repository.dart';
import 'package:deyncare_app/core/services/pdf_service.dart';
import 'package:flutter/foundation.dart';

/// Use case for generating debt reports
/// Coordinates between repository and PDF service to generate debt reports
class GenerateDebtReportUseCase {
  final DebtReportRepository _debtReportRepository;

  GenerateDebtReportUseCase(this._debtReportRepository);

  /// Generate a debt report PDF
  /// Returns the file path of the generated PDF
  Future<String> execute({
    String? startDate,
    String? endDate,
    String period = 'all',
    required String shopName,
    String? shopLogo,
  }) async {
    try {
      if (kDebugMode) {
        print('📊 [DEBT USE CASE] Starting debt report generation...');
        print('📊 [DEBT USE CASE] Period: $period');
        print('📊 [DEBT USE CASE] Date range: $startDate to $endDate');
        print('📊 [DEBT USE CASE] Shop: $shopName');
      }

      // Step 1: Fetch debt report data from repository
      if (kDebugMode) {
        print('📊 [DEBT USE CASE] Step 1: Fetching debt data...');
      }

      final reportData = await _debtReportRepository.getDebtReportData(
        startDate: startDate,
        endDate: endDate,
        period: period,
      );

      if (kDebugMode) {
        print('📊 [DEBT USE CASE] Data fetched successfully');
        print('📊 [DEBT USE CASE] Debts count: ${(reportData['debts'] as List).length}');
        print('📊 [DEBT USE CASE] Summary: ${reportData['summary']}');
      }

      // Step 2: Generate PDF using PDF service
      if (kDebugMode) {
        print('📊 [DEBT USE CASE] Step 2: Generating PDF...');
      }

      final pdfPath = await PDFService.generateDebtReport(
        debts: reportData['debts'],
        summary: reportData['summary'],
        reportPeriod: _formatReportPeriod(period, startDate, endDate),
        shopName: shopName,
        shopLogo: shopLogo,
      );

      if (kDebugMode) {
        print('📊 [DEBT USE CASE] PDF generated successfully: $pdfPath');
      }

      return pdfPath;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [DEBT USE CASE] Error generating debt report: $e');
      }
      rethrow;
    }
  }

  /// Get debt report statistics
  /// Returns statistical data for debt reporting
  Future<Map<String, dynamic>> getStatistics({
    String? startDate,
    String? endDate,
    String period = 'all',
  }) async {
    try {
      if (kDebugMode) {
        print('📊 [DEBT USE CASE] Fetching debt statistics...');
      }

      final stats = await _debtReportRepository.getDebtReportStats(
        startDate: startDate,
        endDate: endDate,
        period: period,
      );

      if (kDebugMode) {
        print('📊 [DEBT USE CASE] Statistics fetched: $stats');
      }

      return stats;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [DEBT USE CASE] Error fetching debt statistics: $e');
      }
      rethrow;
    }
  }

  /// Format the report period for display
  String _formatReportPeriod(String period, String? startDate, String? endDate) {
    switch (period.toLowerCase()) {
      case 'daily':
        return 'Daily Report';
      case 'weekly':
        return 'Weekly Report';
      case 'monthly':
        return 'Monthly Report';
      case 'yearly':
        return 'Yearly Report';
      case 'custom':
        if (startDate != null && endDate != null) {
          return 'Custom Period: $startDate to $endDate';
        }
        return 'Custom Period';
      case 'all':
      default:
        return 'All Time Report';
    }
  }

  /// Validate date range
  bool _isValidDateRange(String? startDate, String? endDate) {
    if (startDate == null || endDate == null) return true;
    
    try {
      final start = DateTime.parse(startDate);
      final end = DateTime.parse(endDate);
      return start.isBefore(end) || start.isAtSameMomentAs(end);
    } catch (e) {
      return false;
    }
  }

  /// Get formatted date range string
  String _getDateRangeString(String? startDate, String? endDate) {
    if (startDate == null || endDate == null) {
      return 'All Time';
    }
    
    try {
      final start = DateTime.parse(startDate);
      final end = DateTime.parse(endDate);
      
      final startFormatted = '${start.day}/${start.month}/${start.year}';
      final endFormatted = '${end.day}/${end.month}/${end.year}';
      
      return '$startFormatted - $endFormatted';
    } catch (e) {
      return 'Invalid Date Range';
    }
  }

  /// Calculate period summary
  Map<String, dynamic> _calculatePeriodSummary(
    List<dynamic> debts,
    String period,
    String? startDate,
    String? endDate,
  ) {
    final debtList = debts.cast<Map<String, dynamic>>();
    
    return {
      'period': period,
      'dateRange': _getDateRangeString(startDate, endDate),
      'totalDebts': debtList.length,
      'totalAmount': debtList.fold<double>(
        0.0,
        (sum, debt) => sum + ((debt['debtAmount'] as num?)?.toDouble() ?? 0.0),
      ),
      'totalOutstanding': debtList.fold<double>(
        0.0,
        (sum, debt) => sum + ((debt['outstandingDebt'] as num?)?.toDouble() ?? 0.0),
      ),
      'totalPaid': debtList.fold<double>(
        0.0,
        (sum, debt) => sum + ((debt['paidAmount'] as num?)?.toDouble() ?? 0.0),
      ),
      'overdueCount': debtList.where((debt) => 
        ((debt['overdueDebt'] as num?)?.toDouble() ?? 0.0) > 0
      ).length,
    };
  }
}
