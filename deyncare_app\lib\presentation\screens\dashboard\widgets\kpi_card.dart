import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';

/// A reusable KPI card widget for displaying key performance indicators
/// Follows the AuthButton pattern with consistent styling and animations
class KPICard extends StatefulWidget {
  final String title;
  final String value;
  final String? subtitle;
  final IconData icon;
  final Color? iconColor;
  final Color? backgroundColor;
  final String? trend;
  final double? trendValue;
  final VoidCallback? onTap;
  final bool isLoading;
  final bool showTrend;
  final String? unit;

  const KPICard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    this.subtitle,
    this.iconColor,
    this.backgroundColor,
    this.trend,
    this.trendValue,
    this.onTap,
    this.isLoading = false,
    this.showTrend = true,
    this.unit,
  });

  @override
  State<KPICard> createState() => _KPICardState();
}

class _KPICardState extends State<KPICard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.onTap != null) {
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (widget.onTap != null) {
      _animationController.reverse();
    }
  }

  void _handleTapCancel() {
    if (widget.onTap != null) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Opacity(
              opacity: _fadeAnimation.value,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: widget.backgroundColor ?? ThemeUtils.getBackgroundColor(context, type: BackgroundType.card),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: ThemeUtils.getElevationShadow(context, elevation: 2),
                  border: Border.all(
                    color: ThemeUtils.getBorderColor(context, type: BorderType.divider),
                    width: 1,
                  ),
                ),
                child: widget.isLoading
                    ? _buildLoadingState()
                    : _buildContent(context),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLoadingState() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: ThemeUtils.getShimmerColors(context).baseColor,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            const Spacer(),
            Container(
              width: 50,
              height: 16,
              decoration: BoxDecoration(
                color: ThemeUtils.getShimmerColors(context).baseColor,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          height: 20,
          decoration: BoxDecoration(
            color: ThemeUtils.getShimmerColors(context).baseColor,
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        const SizedBox(height: 6),
        Container(
          width: 80,
          height: 14,
          decoration: BoxDecoration(
            color: ThemeUtils.getShimmerColors(context).baseColor,
            borderRadius: BorderRadius.circular(7),
          ),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildHeader(context),
        const SizedBox(height: 8),
        _buildValue(context),
        if (widget.subtitle != null) ...[
          const SizedBox(height: 2),
          _buildSubtitle(context),
        ],
        if (widget.showTrend && widget.trend != null) ...[
          const SizedBox(height: 6),
          _buildTrend(context),
        ],
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: (widget.iconColor ?? AppThemes.primaryColor).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            widget.icon,
            color: widget.iconColor ?? AppThemes.primaryColor,
            size: 20,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            widget.title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppThemes.textSecondaryColor,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
        if (widget.onTap != null)
          Icon(
            Icons.arrow_forward_ios,
            color: ThemeUtils.getIconColor(context, type: IconColorType.secondary),
            size: 16,
          ),
      ],
    );
  }

  Widget _buildValue(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Expanded(
          child: Text(
            widget.value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: ThemeUtils.getTextColor(context, type: TextColorType.primary),
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (widget.unit != null) ...[
          const SizedBox(width: 4),
          Text(
            widget.unit!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: ThemeUtils.getTextColor(context, type: TextColorType.secondary),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSubtitle(BuildContext context) {
    return Text(
      widget.subtitle!,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        color: ThemeUtils.getTextColor(context, type: TextColorType.secondary),
        fontWeight: FontWeight.w500,
      ),
    );
  }

  Widget _buildTrend(BuildContext context) {
    final isPositive = widget.trend?.toLowerCase() == 'up' || 
                      (widget.trendValue != null && widget.trendValue! > 0);
    final isNegative = widget.trend?.toLowerCase() == 'down' || 
                      (widget.trendValue != null && widget.trendValue! < 0);
    
    final trendColors = ThemeUtils.getTrendColors(context);
    Color trendColor;
    IconData trendIcon;
    
    if (isPositive) {
      trendColor = trendColors.positive;
      trendIcon = Icons.trending_up;
    } else if (isNegative) {
      trendColor = trendColors.negative;
      trendIcon = Icons.trending_down;
    } else {
      trendColor = trendColors.neutral;
      trendIcon = Icons.trending_flat;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: trendColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            trendIcon,
            color: trendColor,
            size: 12,
          ),
          const SizedBox(width: 3),
          Text(
            widget.trendValue != null 
                ? '${widget.trendValue!.abs().toStringAsFixed(1)}%'
                : widget.trend ?? '',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: trendColor,
              fontWeight: FontWeight.w600,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }
} 