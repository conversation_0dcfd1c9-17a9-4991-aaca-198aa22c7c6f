import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/discount_model.dart';
import '../../core/config/env_config.dart';
import '../../core/utils/logger.dart';
import '../../core/exceptions/api_exception.dart';

class DiscountService {
  static String get _baseUrl => EnvConfig.baseApiUrl.replaceAll('/api', '');
  
  /// Validate a discount code without applying it
  /// 
  /// This checks if the discount code is valid for the given amount and context
  /// without incrementing the usage count
  static Future<DiscountValidationResult> validateDiscountCode({
    required String code,
    required double amount,
    required String context, // 'subscription', 'pos', 'debt'
    required String authToken,
  }) async {
    try {
      logger.i('Validating discount code: $code for amount: \$${amount.toStringAsFixed(2)}');
      
      final response = await http.post(
        Uri.parse('$_baseUrl/api/discounts/validate'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
        body: jsonEncode({
          'code': code.trim().toUpperCase(),
          'amount': amount,
          'context': context,
        }),
      );

      logger.i('Discount validation response: ${response.statusCode}');
      
      final responseData = jsonDecode(response.body);
      
      if (response.statusCode == 200) {
        if (responseData['success'] == true) {
          // Valid discount code
          final discountData = responseData['data'];
          logger.i('Discount code validated successfully: ${discountData['code']}');
          
          return DiscountValidationResult(
            isValid: true,
            discountCode: DiscountCode.fromJson(discountData),
            message: responseData['message'] ?? 'Discount code is valid',
          );
        } else {
          // Invalid discount code (but API returned 200)
          logger.w('Discount code validation failed: ${responseData['message']}');
          
          return DiscountValidationResult(
            isValid: false,
            message: responseData['message'] ?? 'Invalid discount code',
            errorCode: responseData['error']?['code'],
          );
        }
      } else {
        // API error
        logger.e('Discount validation API error: ${response.statusCode} - ${responseData['message']}');
        
        throw ApiException(
          message: responseData['message'] ?? 'Failed to validate discount code',
          statusCode: response.statusCode,
          code: responseData['error']?['code'] ?? 'discount_validation_error',
        );
      }
    } catch (error) {
      if (error is ApiException) {
        rethrow;
      }
      
      logger.e('Discount validation error: $error');
      throw ApiException(
        message: 'Network error while validating discount code',
        statusCode: 500,
        code: 'network_error',
      );
    }
  }

  /// Apply a discount code
  /// 
  /// This validates and applies the discount code, incrementing the usage count
  static Future<DiscountApplicationResult> applyDiscountCode({
    required String code,
    required double amount,
    required String context, // 'subscription', 'pos', 'debt'
    required String authToken,
  }) async {
    try {
      logger.i('Applying discount code: $code for amount: \$${amount.toStringAsFixed(2)}');
      
      final response = await http.post(
        Uri.parse('$_baseUrl/api/discounts/apply'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
        body: jsonEncode({
          'code': code.trim().toUpperCase(),
          'amount': amount,
          'context': context,
        }),
      );

      logger.i('Discount application response: ${response.statusCode}');
      
      final responseData = jsonDecode(response.body);
      
      if (response.statusCode == 200) {
        if (responseData['success'] == true) {
          // Successfully applied discount
          final discountData = responseData['data'];
          logger.i('Discount code applied successfully: ${discountData['code']}');
          
          return DiscountApplicationResult(
            isApplied: true,
            discountCode: DiscountCode.fromJson(discountData),
            message: responseData['message'] ?? 'Discount code applied successfully',
          );
        } else {
          // Failed to apply discount (but API returned 200)
          logger.w('Discount code application failed: ${responseData['message']}');
          
          return DiscountApplicationResult(
            isApplied: false,
            message: responseData['message'] ?? 'Failed to apply discount code',
            errorCode: responseData['error']?['code'],
          );
        }
      } else {
        // API error
        logger.e('Discount application API error: ${response.statusCode} - ${responseData['message']}');
        
        throw ApiException(
          message: responseData['message'] ?? 'Failed to apply discount code',
          statusCode: response.statusCode,
          code: responseData['error']?['code'] ?? 'discount_application_error',
        );
      }
    } catch (error) {
      if (error is ApiException) {
        rethrow;
      }
      
      logger.e('Discount application error: $error');
      throw ApiException(
        message: 'Network error while applying discount code',
        statusCode: 500,
        code: 'network_error',
      );
    }
  }

  /// Get available discount codes for admin users
  /// 
  /// This retrieves all discount codes that the admin can view/manage
  static Future<List<DiscountCode>> getDiscountCodes({
    required String authToken,
    String? shopId,
    bool? isActive,
    String? applicableFor,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      logger.i('Fetching discount codes for admin');
      
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };
      
      if (shopId != null) queryParams['shopId'] = shopId;
      if (isActive != null) queryParams['isActive'] = isActive.toString();
      if (applicableFor != null) queryParams['applicableFor'] = applicableFor;
      
      final uri = Uri.parse('$_baseUrl/api/discounts').replace(queryParameters: queryParams);
      
      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
      );

      logger.i('Get discount codes response: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        
        if (responseData['success'] == true) {
          final discountsData = responseData['data']['discounts'] as List;
          final discountCodes = discountsData
              .map((json) => DiscountCode.fromJson(json))
              .toList();
          
          logger.i('Retrieved ${discountCodes.length} discount codes');
          return discountCodes;
        } else {
          throw ApiException(
            message: responseData['message'] ?? 'Failed to retrieve discount codes',
            statusCode: response.statusCode,
            code: 'discount_retrieval_error',
          );
        }
      } else {
        final responseData = jsonDecode(response.body);
        throw ApiException(
          message: responseData['message'] ?? 'Failed to retrieve discount codes',
          statusCode: response.statusCode,
          code: responseData['error']?['code'] ?? 'discount_retrieval_error',
        );
      }
    } catch (error) {
      if (error is ApiException) {
        rethrow;
      }
      
      logger.e('Get discount codes error: $error');
      throw ApiException(
        message: 'Network error while retrieving discount codes',
        statusCode: 500,
        code: 'network_error',
      );
    }
  }
} 