import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/domain/models/customer.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_bloc.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_event.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_state.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_constants.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/form_mixins.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/loading_states.dart';

/// Delete Customer Form Widget
class DeleteCustomerForm extends StatefulWidget {
  final Customer customer;

  const DeleteCustomerForm({super.key, required this.customer});

  @override
  State<DeleteCustomerForm> createState() => _DeleteCustomerFormState();
}

class _DeleteCustomerFormState extends State<DeleteCustomerForm> 
    with ModalFormMixin<DeleteCustomerForm>, BlocListenerMixin<DeleteCustomerForm> {

  @override
  Widget build(BuildContext context) {
    return buildBlocListener<CustomerBloc, CustomerState>(
      listener: (context, state) {
        if (state is CustomerDeleted) {
          handleSuccess('Customer deleted successfully!');
        } else if (state is CustomerError) {
          handleError('Error: ${state.message}');
        }
      },
      child: BlocBuilder<CustomerBloc, CustomerState>(
        builder: (context, state) {
          // Show skeleton loader during deletion
          if (state is CustomerDeleting) {
            return ModalLoadingStates.deleteSkeleton();
          }
          
          return Padding(
            padding: ModalConstants.defaultPadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildWarningHeader(),
                ModalConstants.largeSpacing,
                _buildCustomerInformation(),
                ModalConstants.defaultSpacing,
                _buildWarningMessage(),
                ModalConstants.largeSpacing,
                _buildActionButtons(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildWarningHeader() {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.warning_rounded,
            size: 64,
            color: AppThemes.errorColor,
          ),
          ModalConstants.defaultSpacing,
          Text(
            'Delete Customer',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppThemes.errorColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerInformation() {
    return CommonCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Customer Details',
              style: ModalConstants.sectionTitleStyle(context),
            ),
            const SizedBox(height: 12),
            _buildDetailRow('Customer ID:', widget.customer.customerId),
            _buildDetailRow('Name:', widget.customer.fullName),
            _buildDetailRow('Phone:', widget.customer.phone),
            if (widget.customer.email.isNotEmpty)
              _buildDetailRow('Email:', widget.customer.email),
            if (widget.customer.address?.isNotEmpty == true)
              _buildDetailRow('Address:', widget.customer.address!),
            _buildDetailRow('Status:', widget.customer.status.displayName),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: AppThemes.textSecondaryColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWarningMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppThemes.warningColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppThemes.warningColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '⚠️ WARNING',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              color: AppThemes.warningColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          ModalConstants.defaultSpacing,
          Text(
            'This action cannot be undone. The customer record and all associated data will be permanently deleted from the system.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppThemes.warningColor,
            ),
          ),
          ModalConstants.defaultSpacing,
          Text(
            'Note: If this customer has existing debts or payments, you may need to handle those separately.',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppThemes.warningColor,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ModalConstants.outlinedButtonStyle(),
            child: const Text('Cancel'),
          ),
        ),
        ModalConstants.defaultSpacing,
        Expanded(
          child: ElevatedButton(
            onPressed: !isLoading ? _handleDelete : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppThemes.errorColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(ModalConstants.defaultRadius),
              ),
            ),
            child: isLoading
                ? ModalLoadingStates.buttonLoading(context)
                : const Text(
                    'Delete Customer',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  void _handleDelete() {
    setLoading(true);
    
    context.read<CustomerBloc>().add(DeleteCustomer(widget.customer.customerId));
    
    setLoading(false);
  }
} 