# 🔥 Firebase Push Notifications Setup Guide

## Overview

This guide walks you through setting up Firebase push notifications for the DeynCare Flutter mobile app. The system is integrated with the backend Firebase service and supports real-time notifications for Admin, SuperAdmin, and Employee users.

## ✅ What's Already Implemented

### Flutter Side (Complete)
- ✅ Firebase Service (`lib/data/services/firebase_service.dart`)
- ✅ Notification Service (`lib/data/services/notification_service.dart`)
- ✅ Notification BLoC (`lib/presentation/blocs/notification/notification_bloc.dart`)
- ✅ Test Screen (`lib/presentation/screens/notifications/notification_test_screen.dart`)
- ✅ Dependency injection setup
- ✅ Firebase dependencies in `pubspec.yaml`
- ✅ Android configuration files
- ✅ iOS configuration files (templates)

### Backend Side (Complete)
- ✅ Firebase Admin SDK integration
- ✅ FCM token registration API (`/api/fcm/register`)
- ✅ Test notification API (`/api/fcm/test`)
- ✅ Push notification service
- ✅ Automated debt/payment notifications

## 🚀 Quick Setup

### 1. Install Dependencies

```bash
cd deyncare_app
flutter pub get
```

### 2. Update Firebase Configuration Files

**You need to download the real configuration files from Firebase Console:**

#### Android (`android/app/google-services.json`)
1. Go to [Firebase Console](https://console.firebase.google.com/project/deyncare-47d99)
2. Click on Android app (or add new Android app)
3. Package name: `com.deyncare.app`
4. Download `google-services.json`
5. Replace the template file in `android/app/google-services.json`

#### iOS (`ios/Runner/GoogleService-Info.plist`)
1. Go to Firebase Console
2. Click on iOS app (or add new iOS app)
3. Bundle ID: `com.deyncare.app`
4. Download `GoogleService-Info.plist`
5. Replace the template file in `ios/Runner/GoogleService-Info.plist`

### 3. Run the App

```bash
flutter run
```

### 4. Test Notifications

1. Open the app
2. Navigate to the notification test screen
3. Tap "Initialize Notifications"
4. Tap "Register FCM Token"
5. Tap "Send Test Notification"

## 📱 App Integration

### Navigation to Test Screen

Add this to your navigation:

```dart
// In your app routes
'/notifications/test': (context) => const NotificationTestScreen(),
```

### Initialize Notifications on Login

Add this after successful login:

```dart
// After login success
context.read<NotificationBloc>().add(InitializeNotifications());
```

## 🔔 Notification Types

The app handles these notification types from the backend:

### 1. Debt Created
```json
{
  "type": "debt_created",
  "title": "💰 New Debt Created",
  "body": "Ahmed Hassan - $150.00 due Dec 28, 2024",
  "data": {
    "debtId": "debt_123",
    "customerId": "customer_456"
  }
}
```

### 2. Payment Recorded
```json
{
  "type": "payment_recorded",
  "title": "💳 Payment Recorded", 
  "body": "Ahmed Hassan paid $75.00 - Risk: Low ✅",
  "data": {
    "paymentId": "payment_789",
    "customerId": "customer_456"
  }
}
```

### 3. Debt Reminder
```json
{
  "type": "debt_reminder",
  "title": "⏰ Debt Reminder",
  "body": "3 debts due in 3 days - Total: $450.00",
  "data": {
    "reminderType": "upcoming",
    "debtCount": 3
  }
}
```

### 4. Test Notification
```json
{
  "type": "test",
  "title": "🧪 Test Notification",
  "body": "This is a test notification from DeynCare"
}
```

## 🔧 Backend Integration

### API Endpoints Used

#### Register FCM Token
```http
POST /api/fcm/register
Authorization: Bearer <user_token>
Content-Type: application/json

{
  "token": "fcm_device_token",
  "deviceInfo": {
    "platform": "android",
    "deviceId": "device_unique_id",
    "appVersion": "1.0.0",
    "osVersion": "13.0",
    "deviceModel": "Samsung Galaxy S21"
  }
}
```

#### Send Test Notification
```http
POST /api/fcm/test
Authorization: Bearer <user_token>
```

#### Unregister Token
```http
DELETE /api/fcm/unregister
Authorization: Bearer <user_token>
Content-Type: application/json

{
  "token": "fcm_device_token"
}
```

## 🧪 Testing Guide

### 1. Test Notification Flow

```dart
// 1. Initialize (after app start)
context.read<NotificationBloc>().add(InitializeNotifications());

// 2. Register token (after login)
context.read<NotificationBloc>().add(RegisterFCMToken());

// 3. Send test notification
context.read<NotificationBloc>().add(SendTestNotification());
```

### 2. Handle Notification Responses

```dart
BlocListener<NotificationBloc, NotificationState>(
  listener: (context, state) {
    if (state is NotificationTokenRegistered) {
      // Token registered successfully
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('✅ Notifications enabled')),
      );
    } else if (state is NotificationError) {
      // Handle error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('❌ ${state.message}')),
      );
    }
  },
  child: YourWidget(),
)
```

### 3. Check Notification Status

```dart
// Access notification status anywhere in the app
final notificationBloc = context.read<NotificationBloc>();

bool isFirebaseReady = notificationBloc.isFirebaseInitialized;
bool isTokenRegistered = notificationBloc.isTokenRegistered;
String? fcmToken = notificationBloc.fcmToken;
```

## 🔍 Troubleshooting

### Common Issues

#### 1. "Firebase not initialized"
**Solution:**
- Check if Firebase configuration files are correct
- Verify package name matches Firebase project
- Check Android/iOS bundle ID

#### 2. "FCM token not generated"
**Solution:**
- Check notification permissions are granted
- Verify device has Google Play Services (Android)
- Test on physical device (emulators may have issues)

#### 3. "Token registration failed"
**Solution:**
- Check user is logged in
- Verify API endpoint is correct
- Check network connectivity
- Test with Postman first

#### 4. "Notifications not received"
**Solution:**
- Test with Firebase Console first
- Check device notification settings
- Verify token is registered in backend
- Check app is not in doze mode (Android)

### Debug Commands

```bash
# Flutter logs
flutter logs

# Android logs (when connected)
adb logcat | grep -i firebase
adb logcat | grep -i fcm

# iOS logs (use Xcode console)
```

### Test with Firebase Console

1. Go to Firebase Console → Cloud Messaging
2. Click "Send your first message"
3. Target: Single device
4. FCM registration token: Copy from app
5. Send test message

## 📊 Monitoring

### Check Registration Status

```dart
// Get notification status
final status = context.read<NotificationBloc>();
print('Firebase initialized: ${status.isFirebaseInitialized}');
print('Token registered: ${status.isTokenRegistered}');
print('FCM Token: ${status.fcmToken}');
```

### Device Information

```dart
// Get device info
final deviceInfo = NotificationService.deviceInfo;
print('Platform: ${deviceInfo['platform']}');
print('Device: ${deviceInfo['deviceModel']}');
print('OS: ${deviceInfo['osVersion']}');
```

## 🔒 Security Notes

### User Permissions
- Only Admin, SuperAdmin, and Employee roles can register for notifications
- Customers do NOT use the mobile app
- Backend validates user role before token registration

### Token Management
- FCM tokens are automatically refreshed
- Old tokens are cleaned up from backend
- Invalid tokens are handled gracefully

### Privacy
- Device information is minimal and non-personal
- FCM tokens are securely stored
- All communication is encrypted

## 🚀 Production Deployment

### Android
1. Update `android/app/google-services.json` with production config
2. Build release APK: `flutter build apk --release`
3. Test on multiple devices
4. Monitor Firebase Console for token registrations

### iOS
1. Update `ios/Runner/GoogleService-Info.plist` with production config
2. Enable push notifications in Xcode capabilities
3. Build for App Store: `flutter build ios --release`
4. Test on physical devices only

## 📈 Next Steps

Once notifications are working:

1. **Add Navigation**: Handle notification taps to navigate to specific screens
2. **Custom Sounds**: Add custom notification sounds
3. **Rich Notifications**: Add images and action buttons
4. **Analytics**: Track notification delivery rates
5. **Scheduling**: Add scheduled local notifications

## 🆘 Support

If you encounter issues:

1. Check the notification test screen for detailed status
2. Test API endpoints with Postman
3. Check Firebase Console for token registrations
4. Review backend logs for FCM errors
5. Test on physical devices only

---

**🎉 Your Firebase push notification system is ready to go!**

The system automatically handles:
- ✅ Token registration and refresh
- ✅ Foreground/background notifications
- ✅ Error handling and retry logic
- ✅ Device info tracking
- ✅ Backend integration
- ✅ User role validation

Happy coding! 🚀 