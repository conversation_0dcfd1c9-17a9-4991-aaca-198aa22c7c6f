import 'dart:io';
import 'package:deyncare_app/domain/models/shop.dart';
import 'package:deyncare_app/data/repositories/shop_repository_impl.dart';

/// Shop service for managing shop operations
class ShopService {
  final ShopRepositoryImpl _repository;

  ShopService({ShopRepositoryImpl? repository})
      : _repository = repository ?? ShopRepositoryImpl();

  /// Get shop by ID
  Future<Shop> getShopById(String shopId) async {
    return await _repository.getShopById(shopId);
  }

  /// Update shop information
  Future<Shop> updateShop(String shopId, Map<String, dynamic> updateData) async {
    return await _repository.updateShop(shopId, updateData);
  }

  /// Upload shop logo
  Future<Shop> uploadShopLogo(String shopId, File logoFile) async {
    return await _repository.uploadShopLogo(shopId, logoFile);
  }
} 