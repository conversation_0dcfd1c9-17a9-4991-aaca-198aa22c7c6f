<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Base config for all connections -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <!-- Trust preinstalled system CAs -->
            <certificates src="system" />
            <!-- Uncomment to trust user-added CAs (useful for development with self-signed certificates) -->
            <!-- <certificates src="user" /> -->
        </trust-anchors>
    </base-config>
    
    <!-- Allow cleartext traffic for dev IP -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="false">***********</domain>
        <domain includeSubdomains="false">*************</domain>
    </domain-config>

    <!-- Allow cleartext traffic for emulator -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="false">********</domain>
    </domain-config>

    <!-- Allow cleartext traffic for localhost -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="false">localhost</domain>
    </domain-config>

    <!-- Production: Only HTTPS -->
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">yourdomain.com</domain>
    </domain-config>
    
    <!-- Debug override - uncomment during development if needed -->
    <!-- <debug-overrides>
        <trust-anchors>
            <certificates src="system" />
            <certificates src="user" />
        </trust-anchors>
    </debug-overrides> -->
</network-security-config>
