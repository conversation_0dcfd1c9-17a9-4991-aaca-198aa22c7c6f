import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'package:deyncare_app/core/routes/app_router.dart';
import 'package:deyncare_app/data/models/employee_model.dart';
import 'package:deyncare_app/data/services/user_management_service.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/common_list_item.dart';
import 'package:deyncare_app/injection_container.dart' as di;
import 'package:deyncare_app/presentation/widgets/skeleton_loader.dart';
import 'package:deyncare_app/domain/models/employee.dart';

/// User Role List Screen - Shows all shop users with management options
class UserRoleListScreen extends StatefulWidget {
  const UserRoleListScreen({super.key});

  @override
  State<UserRoleListScreen> createState() => _UserRoleListScreenState();
}

class _UserRoleListScreenState extends State<UserRoleListScreen> {
  final TextEditingController _searchController = TextEditingController();
  final List<EmployeeModel> _users = [];
  List<EmployeeModel> _filteredUsers = [];
  bool _isLoading = false;
  bool _hasError = false;
  late final UserManagementService _userManagementService;

  @override
  void initState() {
    super.initState();
    _userManagementService = di.sl<UserManagementService>();
    _loadUsers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadUsers() async {
    setState(() => _isLoading = true);

    try {
      final users = await _userManagementService.getAllShopUsers();
      setState(() {
        _users.clear();
        _users.addAll(users);
        _filteredUsers = List.from(_users);
        _hasError = false;
      });
    } catch (e) {
      if (mounted) {
        setState(() => _hasError = true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load users: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _filterUsers(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredUsers = List.from(_users);
      } else {
        _filteredUsers = _users
            .where((user) =>
                user.fullName.toLowerCase().contains(query.toLowerCase()) ||
                user.email.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('User Role Management'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        scrolledUnderElevation: 0,
      ),
      body: Column(
        children: [
          // Search and Add Button Section
          _buildSearchAndActionSection(),

          // Users List
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildBody(),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return RefreshIndicator(
      onRefresh: _loadUsers,
      child: _buildUsersList(),
    );
  }

  Widget _buildSearchAndActionSection() {
    return CommonCard(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Search Field
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search users...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _filterUsers('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Theme.of(context).colorScheme.surfaceContainerHighest,
            ),
            onChanged: _filterUsers,
          ),

          const SizedBox(height: 16),

          // Add New User Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _navigateToAddUser(),
              icon: const Icon(Icons.person_add),
              label: const Text('Add New User Role'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppThemes.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsersList() {
    if (_isLoading) {
      return SkeletonListView(
        itemCount: 8,
        padding: const EdgeInsets.all(16),
        itemBuilder: (index) =>
            CustomerSkeletonPatterns.customerListItem(context),
      );
    }

    if (_hasError && _filteredUsers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('Failed to load employees'),
            SizedBox(height: 8),
            ElevatedButton(
              onPressed: _loadUsers,
              child: Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_filteredUsers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No employees found'),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _filteredUsers.length,
      itemBuilder: (context, index) {
        final user = _filteredUsers[index];
        return _buildUserCard(user);
      },
    );
  }

  /// User item skeleton loader
  Widget _buildUserItemSkeleton() {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Avatar skeleton
            SkeletonLoader.circular(size: 48),
            const SizedBox(width: 16),

            // Content skeleton
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SkeletonLoader.text(width: double.infinity, height: 18),
                  const SizedBox(height: 4),
                  SkeletonLoader.text(width: 150, height: 14),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      SkeletonLoader.text(width: 60, height: 20),
                      const SizedBox(width: 8),
                      SkeletonLoader.text(width: 80, height: 16),
                    ],
                  ),
                ],
              ),
            ),

            // Actions skeleton
            Column(
              children: [
                SkeletonLoader.circular(size: 32),
                const SizedBox(height: 8),
                SkeletonLoader.circular(size: 32),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No users found',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add your first employee to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _navigateToAddUser(),
            icon: const Icon(Icons.person_add),
            label: const Text('Add New User Role'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppThemes.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserCard(EmployeeModel user) {
    return CommonCard(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor:
              user.isAdmin ? AppThemes.primaryColor : AppThemes.accentColor,
          child: Text(
            user.fullName.isNotEmpty
                ? user.fullName.substring(0, 1).toUpperCase()
                : 'U',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          user.fullName,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(user.email),
            if (user.userTitle != null && user.userTitle!.isNotEmpty) ...[
              const SizedBox(height: 2),
              Text(
                user.userTitle!,
                style: TextStyle(
                  fontSize: 12,
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
            const SizedBox(height: 4),
            Row(
              children: [
                _buildRoleBadge(user.role),
                const SizedBox(width: 8),
                _buildStatusBadge(user.status),
              ],
            ),
          ],
        ),
        trailing: user.isAdmin
            ? const Icon(Icons.admin_panel_settings,
                color: AppThemes.primaryColor)
            : PopupMenuButton<String>(
                onSelected: (value) => _handleUserAction(value, user),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'view',
                    child: Row(
                      children: [
                        Icon(Icons.visibility, color: Colors.blue),
                        SizedBox(width: 8),
                        Text('View Details'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit),
                        SizedBox(width: 8),
                        Text('Edit Permissions'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Delete User',
                            style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
        onTap: user.isAdmin ? null : () => _navigateToEditUser(user),
      ),
    );
  }

  Widget _buildRoleBadge(String role) {
    final isAdmin = role == 'admin';
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: isAdmin
            ? AppThemes.primaryColor.withOpacity(0.1)
            : AppThemes.accentColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isAdmin ? AppThemes.primaryColor : AppThemes.accentColor,
          width: 1,
        ),
      ),
      child: Text(
        isAdmin ? 'Admin' : 'Employee',
        style: TextStyle(
          color: isAdmin ? AppThemes.primaryColor : AppThemes.accentColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status) {
    final isActive = status == 'active';
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: isActive
            ? Colors.green.withOpacity(0.1)
            : Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isActive ? Colors.green : Colors.orange,
          width: 1,
        ),
      ),
      child: Text(
        isActive ? 'Active' : 'Inactive',
        style: TextStyle(
          color: isActive ? Colors.green : Colors.orange,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  void _handleUserAction(String action, EmployeeModel user) {
    switch (action) {
      case 'view':
        _showUserDetails(user);
        break;
      case 'edit':
        _navigateToEditUser(user);
        break;
      case 'delete':
        _showDeleteConfirmation(user);
        break;
    }
  }

  void _showDeleteConfirmation(EmployeeModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete User'),
        content: Text(
          'Are you sure you want to delete ${user.fullName}? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteUser(user);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteUser(EmployeeModel user) async {
    try {
      // Call the backend to delete the user
      await _userManagementService.deleteEmployee(user.userId);

      setState(() {
        _users.removeWhere((u) => u.userId == user.userId);
        _filteredUsers.removeWhere((u) => u.userId == user.userId);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${user.fullName} deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete user: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _navigateToAddUser() {
    AppRouter.navigateToAddUserRole(context);
  }

  void _navigateToEditUser(EmployeeModel user) async {
    final result = await AppRouter.navigateToEditUserRole(context, user);
    // Refresh the list if the edit was successful
    if (result == true) {
      _loadUsers();
    }
  }

  void _showUserDetails(EmployeeModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.person, color: AppThemes.primaryColor),
            const SizedBox(width: 8),
            Text('${user.fullName} Details'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Full Name', user.fullName),
              _buildDetailRow('Email', user.email),
              _buildDetailRow('Phone', user.phone ?? 'Not provided'),
              _buildDetailRow(
                  'Job Title/Position', user.userTitle ?? 'Not specified'),
              _buildDetailRow('Status', user.status),
              _buildDetailRow('Role', user.role),
              const SizedBox(height: 16),

              // Permissions section
              Text(
                'Permissions',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppThemes.primaryColor,
                    ),
              ),
              const SizedBox(height: 8),

              if (user.visibility != null) ...[
                _buildPermissionSection(
                    'Customer Management', user.visibility!.customerManagement),
                _buildPermissionSection(
                    'Debt Management', user.visibility!.debtManagement),
                _buildPermissionSection(
                    'Report Management', user.visibility!.reportManagement),
              ] else
                const Text('No permissions assigned'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _navigateToEditUser(user);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppThemes.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Edit Permissions'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionSection(String title, dynamic permissions) {
    if (permissions == null) return const SizedBox.shrink();

    final List<String> enabledPermissions = [];

    // Handle ModulePermissions (customer and debt management)
    if (permissions is ModulePermissions) {
      if (permissions.create == true) enabledPermissions.add('Create');
      if (permissions.update == true) enabledPermissions.add('Update');
      if (permissions.view == true) enabledPermissions.add('View');
      if (permissions.delete == true) enabledPermissions.add('Delete');
    }
    // Handle ReportPermissions (report management)
    else if (permissions is ReportPermissions) {
      if (permissions.generate == true) enabledPermissions.add('Generate');
      if (permissions.view == true) enabledPermissions.add('View');
      if (permissions.delete == true) enabledPermissions.add('Delete');
    }
    // Fallback for dynamic access (in case of unexpected types)
    else {
      try {
        if (permissions.create == true) enabledPermissions.add('Create');
      } catch (_) {}
      try {
        if (permissions.update == true) enabledPermissions.add('Update');
      } catch (_) {}
      try {
        if (permissions.view == true) enabledPermissions.add('View');
      } catch (_) {}
      try {
        if (permissions.delete == true) enabledPermissions.add('Delete');
      } catch (_) {}
      try {
        if (permissions.generate == true) enabledPermissions.add('Generate');
      } catch (_) {}
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
          ),
          const SizedBox(height: 4),
          if (enabledPermissions.isNotEmpty)
            Wrap(
              spacing: 4,
              children: enabledPermissions
                  .map((permission) => Chip(
                        label: Text(
                          permission,
                          style: const TextStyle(fontSize: 12),
                        ),
                        backgroundColor:
                            AppThemes.primaryColor.withValues(alpha: 0.1),
                        side: BorderSide(
                            color:
                                AppThemes.primaryColor.withValues(alpha: 0.3)),
                      ))
                  .toList(),
            )
          else
            Text(
              'No permissions',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
            ),
        ],
      ),
    );
  }
}
