import 'package:get_it/get_it.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

// Network layer imports
import 'package:deyncare_app/core/config/env_config.dart';
import 'package:deyncare_app/data/network/clients/dio_client.dart';
import 'package:deyncare_app/data/network/token/token_manager.dart';
import 'package:deyncare_app/data/network/handlers/connectivity_handler.dart';
import 'package:deyncare_app/data/network/handlers/retry_handler.dart';
import 'package:deyncare_app/data/network/interceptors/logging_interceptor.dart';
import 'package:deyncare_app/core/managers/connectivity_manager.dart';

// Auth
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/dashboard/dashboard_bloc.dart';
import 'package:deyncare_app/domain/repositories/auth_repository.dart';
import 'package:deyncare_app/data/services/auth_service.dart';
import 'package:deyncare_app/data/repositories/auth_repository_impl.dart';
import 'package:deyncare_app/data/services/auth/auth_remote_source.dart';
import 'package:deyncare_app/data/services/auth/auth_utils.dart';

// Customer BLoC
import 'package:deyncare_app/presentation/blocs/customer/customer_bloc.dart';

// Debt BLoC
import 'package:deyncare_app/presentation/blocs/debt/debt_bloc.dart';

// Payment BLoC
import 'package:deyncare_app/presentation/blocs/payment/payment_bloc.dart';

// Plan
import 'package:deyncare_app/domain/repositories/plan_repository.dart';
import 'package:deyncare_app/data/services/plan/plan_repository_impl.dart';
import 'package:deyncare_app/data/services/plan/plan_remote_source.dart';

// Subscription
import 'package:deyncare_app/presentation/blocs/subscription/subscription_bloc.dart';
import 'package:deyncare_app/domain/repositories/subscription_repository.dart';
import 'package:deyncare_app/data/repositories/subscription_repository_impl.dart';
import 'package:deyncare_app/data/services/subscription_service.dart';
import 'package:deyncare_app/domain/usecases/subscription/get_current_subscription_use_case.dart';
import 'package:deyncare_app/domain/usecases/subscription/get_available_plans_use_case.dart';
import 'package:deyncare_app/domain/usecases/subscription/upgrade_subscription_use_case.dart';

// Debt
import 'package:deyncare_app/domain/repositories/debt_repository.dart';
import 'package:deyncare_app/data/repositories/debt_repository_impl.dart';
import 'package:deyncare_app/data/network/debt/debt_remote_data_source.dart';
import 'package:deyncare_app/data/network/debt/debt_remote_data_source_impl.dart';

// Customer
import 'package:deyncare_app/domain/repositories/customer_repository.dart';
import 'package:deyncare_app/data/repositories/customer_repository_impl.dart';
import 'package:deyncare_app/data/network/customer/customer_remote_data_source.dart';

// Payment
import 'package:deyncare_app/domain/repositories/payment_repository.dart';
import 'package:deyncare_app/data/repositories/payment_repository_impl.dart';
import 'package:deyncare_app/data/network/payment/payment_remote_data_source.dart';
import 'package:deyncare_app/data/network/payment/payment_remote_data_source_impl.dart';

// Use cases
import 'package:deyncare_app/domain/usecases/auth/check_auth_status_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/login_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/register_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/logout_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/forgot_password_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/verify_email_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/change_password_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/refresh_token_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/reset_password_success_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/process_payment_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/get_available_payment_methods_use_case.dart';
import 'package:deyncare_app/domain/usecases/plan/get_plan_details_use_case.dart';

// Customer Use Cases
import 'package:deyncare_app/domain/usecases/customer/get_customers_use_case.dart';
import 'package:deyncare_app/domain/usecases/customer/create_customer_use_case.dart';
import 'package:deyncare_app/domain/usecases/customer/get_customer_details_use_case.dart';
import 'package:deyncare_app/domain/usecases/customer/update_customer_use_case.dart';
import 'package:deyncare_app/domain/usecases/customer/delete_customer_use_case.dart';
import 'package:deyncare_app/domain/usecases/customer/get_customer_debts_use_case.dart';
import 'package:deyncare_app/domain/usecases/customer/customer_business_logic_use_case.dart';

// Debt Use Cases
import 'package:deyncare_app/domain/usecases/debt/create_debt_use_case.dart';
import 'package:deyncare_app/domain/usecases/debt/get_debts_use_case.dart';
import 'package:deyncare_app/domain/usecases/debt/get_debt_details_use_case.dart';
import 'package:deyncare_app/domain/usecases/debt/update_debt_use_case.dart';
import 'package:deyncare_app/domain/usecases/debt/delete_debt_use_case.dart';
import 'package:deyncare_app/domain/usecases/debt/add_payment_to_debt_use_case.dart';
import 'package:deyncare_app/domain/usecases/debt/get_debt_analytics_use_case.dart';
import 'package:deyncare_app/domain/usecases/debt/validate_payment_use_case.dart';
import 'package:deyncare_app/domain/usecases/debt/debt_business_logic_use_case.dart';

// Payment Use Cases
import 'package:deyncare_app/domain/usecases/payment/get_payment_history_use_case.dart';

// Notification
import 'package:deyncare_app/presentation/blocs/notification/notification_bloc.dart';
import 'package:deyncare_app/data/services/notification_service.dart';
import 'package:deyncare_app/data/services/firebase_service.dart';

// NEW: Report imports
import 'package:deyncare_app/data/repositories/customer_report_repository.dart';
import 'package:deyncare_app/data/repositories/debt_report_repository.dart';
import 'package:deyncare_app/data/repositories/risk_report_repository.dart';
import 'package:deyncare_app/domain/usecases/reports/generate_customer_report_use_case.dart';
import 'package:deyncare_app/domain/usecases/reports/generate_debt_report_use_case.dart';
import 'package:deyncare_app/domain/usecases/reports/generate_risk_report_use_case.dart';
import 'package:deyncare_app/presentation/blocs/reports/customer_report_bloc.dart';
import 'package:deyncare_app/presentation/blocs/reports/debt_report_bloc.dart';
import 'package:deyncare_app/presentation/blocs/reports/risk_report_bloc.dart';

// NEW: User management imports
import 'package:deyncare_app/data/services/user_management_service.dart';

// Service locator instance
final sl = GetIt.instance;

/// Initialize all dependencies for dependency injection
Future<void> init() async {
  //! Features - Auth
  // BLoC
  sl.registerFactory(
    () => AuthBloc(
      checkAuthStatus: sl<CheckAuthStatusUseCase>(),
      login: sl<LoginUseCase>(),
      logout: sl<LogoutUseCase>(),
      register: sl<RegisterUseCase>(),
      forgotPassword: sl<ForgotPasswordUseCase>(),
      verifyEmail: sl<VerifyEmailUseCase>(),
      changePassword: sl<ChangePasswordUseCase>(),
      refreshToken: sl<RefreshTokenUseCase>(),
      resetPasswordSuccess: sl<ResetPasswordSuccessUseCase>(),
      processPayment: sl<ProcessPaymentUseCase>(),
      getAvailablePaymentMethods: sl<GetAvailablePaymentMethodsUseCase>(),
      authRepository: sl<AuthRepository>(),
    ),
  );

  //! Features - Dashboard
  // BLoC
  sl.registerFactory(
    () => DashboardBloc(
      getDebtAnalyticsUseCase: sl<GetDebtAnalyticsUseCase>(),
      getDebtsUseCase: sl<GetDebtsUseCase>(),
      getCustomersUseCase: sl<GetCustomersUseCase>(),
    ),
  );

  //! Features - Customer
  // BLoC
  sl.registerFactory(
    () => CustomerBloc(
      getCustomersUseCase: sl<GetCustomersUseCase>(),
      createCustomerUseCase: sl<CreateCustomerUseCase>(),
      getCustomerDetailsUseCase: sl<GetCustomerDetailsUseCase>(),
      updateCustomerUseCase: sl<UpdateCustomerUseCase>(),
      deleteCustomerUseCase: sl<DeleteCustomerUseCase>(),
      getCustomerDebtsUseCase: sl<GetCustomerDebtsUseCase>(),
    ),
  );

  //! Features - Debt
  // BLoC
  sl.registerLazySingleton(
    () => DebtBloc(
      createDebtUseCase: sl<CreateDebtUseCase>(),
      getDebtsUseCase: sl<GetDebtsUseCase>(),
      getDebtDetailsUseCase: sl<GetDebtDetailsUseCase>(),
      updateDebtUseCase: sl<UpdateDebtUseCase>(),
      deleteDebtUseCase: sl<DeleteDebtUseCase>(),
      addPaymentToDebtUseCase: sl<AddPaymentToDebtUseCase>(),
      getDebtAnalyticsUseCase: sl<GetDebtAnalyticsUseCase>(),
    ),
  );

  //! Features - Payment
  // BLoC
  sl.registerFactory(
    () => PaymentBloc(
      getPaymentHistoryUseCase: sl<GetPaymentHistoryUseCase>(),
    ),
  );

  //! Features - Notifications
  // BLoC
  sl.registerLazySingleton(
    () => NotificationBloc(
      notificationService: sl<NotificationService>(),
    ),
  );

  //! NEW: Features - Reports
  // BLoC
  sl.registerFactory(
    () => CustomerReportBloc(
      generateCustomerReportUseCase: sl<GenerateCustomerReportUseCase>(),
    ),
  );

  sl.registerFactory(
    () => DebtReportBloc(
      generateDebtReportUseCase: sl<GenerateDebtReportUseCase>(),
    ),
  );

  sl.registerFactory(
    () => RiskReportBloc(
      generateRiskReportUseCase: sl<GenerateRiskReportUseCase>(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => CheckAuthStatusUseCase(sl<AuthRepository>()));
  sl.registerLazySingleton(() => LoginUseCase(sl<AuthRepository>()));
  sl.registerLazySingleton(() => RegisterUseCase(sl<AuthRepository>()));
  sl.registerLazySingleton(() => LogoutUseCase(sl<AuthRepository>()));
  sl.registerLazySingleton(() => ForgotPasswordUseCase(sl<AuthRepository>()));
  sl.registerLazySingleton(() => VerifyEmailUseCase(sl<AuthRepository>()));
  sl.registerLazySingleton(() => ChangePasswordUseCase(sl<AuthRepository>()));
  sl.registerLazySingleton(() => RefreshTokenUseCase(sl<AuthRepository>()));
  sl.registerLazySingleton(
      () => ResetPasswordSuccessUseCase(sl<AuthRepository>()));
  sl.registerLazySingleton(() => ProcessPaymentUseCase(sl<AuthRepository>()));
  sl.registerLazySingleton(
      () => GetAvailablePaymentMethodsUseCase(sl<AuthRepository>()));

  //! Features - Customer
  // Use cases
  sl.registerLazySingleton(() => GetCustomersUseCase(sl<CustomerRepository>()));
  sl.registerLazySingleton(
      () => CreateCustomerUseCase(sl<CustomerRepository>()));
  sl.registerLazySingleton(
      () => GetCustomerDetailsUseCase(sl<CustomerRepository>()));
  sl.registerLazySingleton(
      () => UpdateCustomerUseCase(sl<CustomerRepository>()));
  sl.registerLazySingleton(
      () => DeleteCustomerUseCase(sl<CustomerRepository>()));
  sl.registerLazySingleton(
      () => GetCustomerDebtsUseCase(sl<CustomerRepository>()));
  sl.registerLazySingleton(
      () => CustomerBusinessLogicUseCase(sl<CustomerRepository>()));

  //! NEW: Features - Reports
  // Use cases - Reports
  sl.registerLazySingleton(() => GenerateCustomerReportUseCase(
        sl<CustomerReportRepository>(),
        sl<AuthRepository>(),
      ));
  sl.registerLazySingleton(() => GenerateDebtReportUseCase(
        sl<DebtReportRepository>(),
      ));
  sl.registerLazySingleton(() => GenerateRiskReportUseCase(
        sl<RiskReportRepository>(),
      ));

  // Repository
  sl.registerLazySingleton<AuthRepository>(
    () => sl<AuthRepositoryImpl>(),
  );

  sl.registerLazySingleton<AuthRepositoryImpl>(
    () => AuthRepositoryImpl(
      remoteSource: sl<AuthRemoteSource>(),
      tokenManager: sl<TokenManager>(),
      authUtils: sl<AuthUtils>(),
      dioClient: sl<DioClient>(),
    ),
  );

  // Auth Services
  sl.registerLazySingleton<AuthService>(() => AuthService(
        dioClient: sl<DioClient>(),
        tokenManager: sl<TokenManager>(),
        secureStorage: sl<FlutterSecureStorage>(),
        repository: sl<AuthRepositoryImpl>(),
      ));

  sl.registerLazySingleton<AuthRemoteSource>(() => AuthRemoteSource(
        dioClient: sl<DioClient>(),
      ));

  sl.registerLazySingleton<AuthUtils>(() => AuthUtils(
        secureStorage: sl<FlutterSecureStorage>(),
      ));

  //! Features - Plan
  // Use cases
  sl.registerLazySingleton(() => GetPlanDetailsUseCase(sl<PlanRepository>()));

  // Repository
  sl.registerLazySingleton<PlanRepository>(
    () => sl<PlanRepositoryImpl>(),
  );

  sl.registerLazySingleton<PlanRepositoryImpl>(
    () => PlanRepositoryImpl(
      remoteSource: sl<PlanRemoteSource>(),
    ),
  );

  // Data Sources
  sl.registerLazySingleton<PlanRemoteSource>(() => PlanRemoteSource(
        dioClient: sl<DioClient>(),
      ));

  //! Features - Customer
  // Repository
  sl.registerLazySingleton<CustomerRepository>(
    () => sl<CustomerRepositoryImpl>(),
  );

  sl.registerLazySingleton<CustomerRepositoryImpl>(
    () => CustomerRepositoryImpl(
      remoteDataSource: sl<CustomerRemoteDataSource>(),
    ),
  );

  // Data Sources
  sl.registerLazySingleton<CustomerRemoteDataSource>(
      () => CustomerRemoteDataSource(
            dioClient: sl<DioClient>(),
          ));

  //! Features - Debt
  // Use cases
  sl.registerLazySingleton(() => CreateDebtUseCase(sl<DebtRepository>()));
  sl.registerLazySingleton(() => GetDebtsUseCase(sl<DebtRepository>()));
  sl.registerLazySingleton(() => GetDebtDetailsUseCase(sl<DebtRepository>()));
  sl.registerLazySingleton(() => UpdateDebtUseCase(sl<DebtRepository>()));
  sl.registerLazySingleton(() => DeleteDebtUseCase(sl<DebtRepository>()));
  sl.registerLazySingleton(() => AddPaymentToDebtUseCase(sl<DebtRepository>()));
  sl.registerLazySingleton(() => GetDebtAnalyticsUseCase(sl<DebtRepository>()));
  sl.registerLazySingleton(() => ValidatePaymentUseCase(sl<DebtRepository>()));
  sl.registerLazySingleton(() =>
      DebtBusinessLogicUseCase(sl<DebtRepository>(), sl<CustomerRepository>()));

  // Repository
  sl.registerLazySingleton<DebtRepository>(
    () => sl<DebtRepositoryImpl>(),
  );

  sl.registerLazySingleton<DebtRepositoryImpl>(
    () => DebtRepositoryImpl(
      sl<DebtRemoteDataSource>(),
    ),
  );

  // Data Sources
  sl.registerLazySingleton<DebtRemoteDataSource>(
    () => sl<DebtRemoteDataSourceImpl>(),
  );

  sl.registerLazySingleton<DebtRemoteDataSourceImpl>(
    () => DebtRemoteDataSourceImpl(sl<DioClient>()),
  );

  //! Features - Payment
  // Use cases
  sl.registerLazySingleton(
      () => GetPaymentHistoryUseCase(sl<PaymentRepository>()));

  // Repository
  sl.registerLazySingleton<PaymentRepository>(
    () => sl<PaymentRepositoryImpl>(),
  );

  sl.registerLazySingleton<PaymentRepositoryImpl>(
    () => PaymentRepositoryImpl(
      sl<PaymentRemoteDataSource>(),
    ),
  );

  // Data Sources
  sl.registerLazySingleton<PaymentRemoteDataSource>(
    () => sl<PaymentRemoteDataSourceImpl>(),
  );

  sl.registerLazySingleton<PaymentRemoteDataSourceImpl>(
    () => PaymentRemoteDataSourceImpl(sl<Dio>()),
  );

  //! Features - Subscription
  // BLoC
  sl.registerFactory(() => SubscriptionBloc(
        getCurrentSubscriptionUseCase: sl<GetCurrentSubscriptionUseCase>(),
        getAvailablePlansUseCase: sl<GetAvailablePlansUseCase>(),
        requestUpgradeUseCase: sl<RequestUpgradeUseCase>(),
        subscriptionRepository: sl<SubscriptionRepository>(),
      ));

  // Use cases
  sl.registerLazySingleton(
      () => GetCurrentSubscriptionUseCase(sl<SubscriptionRepository>()));
  sl.registerLazySingleton(
      () => GetAvailablePlansUseCase(sl<SubscriptionRepository>()));
  sl.registerLazySingleton(
      () => RequestUpgradeUseCase(sl<SubscriptionRepository>()));

  // Repository
  sl.registerLazySingleton<SubscriptionRepository>(
    () => sl<SubscriptionRepositoryImpl>(),
  );

  sl.registerLazySingleton<SubscriptionRepositoryImpl>(
    () => SubscriptionRepositoryImpl(
      sl<SubscriptionService>(),
    ),
  );

  // Services
  sl.registerLazySingleton<SubscriptionService>(() => SubscriptionService(
        sl<Dio>(),
      ));

  //! Features - Notification Services
  sl.registerLazySingleton<NotificationService>(
    () => NotificationService(sl<Dio>()),
  );

  //! NEW: Features - User Management
  // Services
  sl.registerLazySingleton<UserManagementService>(
    () => UserManagementService(sl<DioClient>()),
  );

  //! NEW: Features - Reports
  // Repository - Reports
  sl.registerLazySingleton<CustomerReportRepository>(
    () => CustomerReportRepository(
      dioClient: sl<DioClient>(),
    ),
  );
  sl.registerLazySingleton<DebtReportRepository>(
    () => DebtReportRepository(sl<DioClient>()),
  );
  sl.registerLazySingleton<RiskReportRepository>(
    () => RiskReportRepository(sl<DioClient>()),
  );

  //! External
  // Secure storage for tokens
  sl.registerLazySingleton<FlutterSecureStorage>(
      () => const FlutterSecureStorage());

  // Connectivity checker
  sl.registerLazySingleton<Connectivity>(() => Connectivity());

  // Token Manager
  sl.registerLazySingleton<TokenManager>(() => TokenManager(
        secureStorage: sl<FlutterSecureStorage>(),
      ));

  // Connectivity Handler
  sl.registerLazySingleton<ConnectivityHandler>(() => ConnectivityHandler());

  // Connectivity Manager
  sl.registerLazySingleton<ConnectivityManager>(() => ConnectivityManager());

  // Retry Handler
  sl.registerLazySingleton<RetryHandler>(() => const RetryHandler());

  // DioClient
  sl.registerLazySingleton<DioClient>(() => DioClient(
        dio: sl<Dio>(),
        tokenManager: sl<TokenManager>(),
        connectivityHandler: sl<ConnectivityHandler>(),
        retryHandler: sl<RetryHandler>(),
      ));

  // Note: ApiClient has been fully refactored into modular components
  // If you need HTTP functionality, inject DioClient instead

  // Environment configuration (automatically detects debug/release mode)
  sl.registerLazySingleton(() {
    // Environment is now automatically detected based on build mode
    // No manual initialization needed - EnvConfig.currentEnvironment is automatic
    return EnvConfig;
  });

  // Dio for network requests
  sl.registerLazySingleton<Dio>(() {
    final dio = Dio();
    dio.options.baseUrl = EnvConfig.baseApiUrl; // Using dynamic environment URL
    dio.options.connectTimeout = const Duration(seconds: 15);
    dio.options.receiveTimeout = const Duration(seconds: 15);
    dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Add our custom logging interceptor for better debugging
    if (kDebugMode) {
      dio.interceptors.add(LoggingInterceptor());
    }

    return dio;
  });

  // CRITICAL: Initialize DioClient after all dependencies are registered
  // This ensures tokens are loaded and interceptors are added
  try {
    final dioClient = sl<DioClient>();
    await dioClient.init();

    if (kDebugMode) {
      print(
          '🔑 DioClient initialized successfully with token loading and interceptors');
    }
  } catch (e) {
    if (kDebugMode) {
      print('⚠️ Error during DioClient initialization: $e');
      print('📋 This may cause authentication issues with API calls');
    }
  }
}
