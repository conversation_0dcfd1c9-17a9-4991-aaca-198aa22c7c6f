import 'package:deyncare_app/domain/repositories/auth_repository.dart';
import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/domain/models/registration_progress.dart';
import 'package:logger/logger.dart';

/// Use case for processing user payment
///
/// This class orchestrates the payment process after email verification,
/// interacting with the authentication repository to call the payment API.
class ProcessPaymentUseCase {
  final AuthRepository _repository;
  final Logger _logger = Logger();

  /// Creates a new instance with the required repository
  ProcessPaymentUseCase(this._repository);

  /// Executes the payment process
  ///
  /// Returns a Future<(User, RegistrationProgress)> that completes when payment is successful
  /// Throws exceptions if payment fails
  Future<(User, RegistrationProgress)> execute({
    required String userId,
    required String shopId,
    required String planId,
    required String paymentMethod,
    required String phoneNumber,
    double? amount,
    String? discountCode,
  }) async {
    _logger.d('ProcessPaymentUseCase: Executing with userId=$userId, shopId=$shopId, planId=$planId, paymentMethod=$paymentMethod');
    
    try {
      final result = await _repository.processPayment(
        userId: userId,
        shopId: shopId,
        planId: planId,
        paymentMethod: paymentMethod,
        phoneNumber: phoneNumber,
        amount: amount,
        discountCode: discountCode,
      );
      
      _logger.d('ProcessPaymentUseCase: Payment processed successfully');
      _logger.d('ProcessPaymentUseCase: User status=${result.$1.status}, Shop ID=${result.$1.shopId}');
      _logger.d('ProcessPaymentUseCase: Next step=${result.$2.nextStep}, Progress=${result.$2.progress}%');
      
      return result;
    } catch (e) {
      _logger.e('ProcessPaymentUseCase: Error processing payment: $e');
      rethrow;
    }
  }
} 