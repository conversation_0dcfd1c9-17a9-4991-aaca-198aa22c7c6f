import 'package:deyncare_app/domain/repositories/auth_repository.dart';

/// Use case for initiating password reset (forgot password)
///
/// This class handles initiating the password reset process
/// using the hybrid approach where the user receives an email
/// with a link to reset their password in a web browser.
class ForgotPasswordUseCase {
  final AuthRepository _repository;

  /// Creates a new instance with the required repository
  ForgotPasswordUseCase(this._repository);

  /// Executes the forgot password use case with the user's email
  ///
  /// Returns a Future<void> that completes when the reset email is sent
  /// Throws exceptions if the request fails
  Future<void> execute(String email) async {
    await _repository.forgotPassword(email);
  }
}
