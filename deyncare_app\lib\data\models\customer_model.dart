import 'package:json_annotation/json_annotation.dart';

part 'customer_model.g.dart';

/// Customer Model - matches backend customer.model.js exactly
@JsonSerializable()
class CustomerModel {
  final String customerId;
  final String shopId;
  @Json<PERSON>ey(name: 'CustomerName')
  final String customerName;
  @JsonKey(name: 'CustomerType')
  final String customerType; // 'New' or 'Returning'
  final String phone;
  final String? email;
  final String? address;
  final double outstandingBalance;
  final double creditLimit;
  final double totalPurchaseAmount;
  final DateTime? lastPurchaseDate;
  final String category; // 'regular', 'vip', 'corporate', 'other'
  final String? notes;
  final RiskProfile riskProfile;
  final bool isDeleted;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CustomerModel({
    required this.customerId,
    required this.shopId,
    required this.customerName,
    required this.customerType,
    required this.phone,
    this.email,
    this.address,
    required this.outstandingBalance,
    required this.creditLimit,
    required this.totalPurchaseAmount,
    this.lastPurchaseDate,
    required this.category,
    this.notes,
    required this.riskProfile,
    required this.isDeleted,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CustomerModel.fromJson(Map<String, dynamic> json) =>
      _$CustomerModelFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerModelToJson(this);

  CustomerModel copyWith({
    String? customerId,
    String? shopId,
    String? customerName,
    String? customerType,
    String? phone,
    String? email,
    String? address,
    double? outstandingBalance,
    double? creditLimit,
    double? totalPurchaseAmount,
    DateTime? lastPurchaseDate,
    String? category,
    String? notes,
    RiskProfile? riskProfile,
    bool? isDeleted,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CustomerModel(
      customerId: customerId ?? this.customerId,
      shopId: shopId ?? this.shopId,
      customerName: customerName ?? this.customerName,
      customerType: customerType ?? this.customerType,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      outstandingBalance: outstandingBalance ?? this.outstandingBalance,
      creditLimit: creditLimit ?? this.creditLimit,
      totalPurchaseAmount: totalPurchaseAmount ?? this.totalPurchaseAmount,
      lastPurchaseDate: lastPurchaseDate ?? this.lastPurchaseDate,
      category: category ?? this.category,
      notes: notes ?? this.notes,
      riskProfile: riskProfile ?? this.riskProfile,
      isDeleted: isDeleted ?? this.isDeleted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Check if customer has exceeded credit limit (from backend method)
  bool get hasExceededCreditLimit => 
      creditLimit > 0 && outstandingBalance >= creditLimit;

  /// Get risk color based on risk level
  String get getRiskColor {
    switch (riskProfile.currentRiskLevel) {
      case 'Low Risk':
        return '#4CAF50'; // Green
      case 'Medium Risk':
        return '#FF9800'; // Orange
      case 'High Risk':
        return '#F44336'; // Red
      case 'Critical Risk':
        return '#D32F2F'; // Dark Red
      default:
        return '#9E9E9E'; // Grey for 'Not Assessed'
    }
  }
}

/// Risk Profile - matches backend riskProfile schema exactly
@JsonSerializable()
class RiskProfile {
  final String currentRiskLevel; // 'Low Risk', 'Medium Risk', 'High Risk', 'Critical Risk'
  final double riskScore; // 0-100
  final DateTime? lastAssessment;
  final int assessmentCount;
  final String mlSource; // 'manual', 'ml_api', 'system'

  const RiskProfile({
    required this.currentRiskLevel,
    required this.riskScore,
    this.lastAssessment,
    required this.assessmentCount,
    this.mlSource = 'system', // Default value for backward compatibility
  });

  factory RiskProfile.fromJson(Map<String, dynamic> json) =>
      _$RiskProfileFromJson(json);

  Map<String, dynamic> toJson() => _$RiskProfileToJson(this);

  RiskProfile copyWith({
    String? currentRiskLevel,
    double? riskScore,
    DateTime? lastAssessment,
    int? assessmentCount,
    String? mlSource,
  }) {
    return RiskProfile(
      currentRiskLevel: currentRiskLevel ?? this.currentRiskLevel,
      riskScore: riskScore ?? this.riskScore,
      lastAssessment: lastAssessment ?? this.lastAssessment,
      assessmentCount: assessmentCount ?? this.assessmentCount,
      mlSource: mlSource ?? this.mlSource,
    );
  }
}

/// Create Customer Request - matches backend validation schema
@JsonSerializable()
class CreateCustomerRequest {
  @JsonKey(name: 'CustomerName')
  final String customerName;
  @JsonKey(name: 'CustomerType')
  final String customerType; // 'new' or 'returning' (lowercase as per validation)
  final String phone;
  @JsonKey(includeIfNull: false)
  final String? email;
  @JsonKey(includeIfNull: false)
  final String? address;
  @JsonKey(includeIfNull: false)
  final double? creditLimit;
  @JsonKey(includeIfNull: false)
  final String? category; // 'regular', 'vip', 'premium'
  @JsonKey(includeIfNull: false)
  final String? notes;

  const CreateCustomerRequest({
    required this.customerName,
    required this.customerType,
    required this.phone,
    this.email,
    this.address,
    this.creditLimit = 0,
    this.category = 'regular',
    this.notes,
  });

  factory CreateCustomerRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateCustomerRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CreateCustomerRequestToJson(this);
}

/// Update Customer Request - matches backend validation schema
@JsonSerializable()
class UpdateCustomerRequest {
  @JsonKey(name: 'CustomerName')
  final String? customerName;
  @JsonKey(includeIfNull: false)
  final String? phone;
  @JsonKey(includeIfNull: false)
  final String? email;
  @JsonKey(includeIfNull: false)
  final String? address;
  @JsonKey(includeIfNull: false)
  final double? creditLimit;
  @JsonKey(includeIfNull: false)
  final String? category;
  @JsonKey(includeIfNull: false)
  final String? notes;

  const UpdateCustomerRequest({
    this.customerName,
    this.phone,
    this.email,
    this.address,
    this.creditLimit,
    this.category,
    this.notes,
  });

  factory UpdateCustomerRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateCustomerRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateCustomerRequestToJson(this);
}

/// Customer List Response - matches backend getAllCustomers response exactly
@JsonSerializable()
class CustomerListResponse {
  final bool success;
  final String message;
  final CustomerListData data;

  const CustomerListResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory CustomerListResponse.fromJson(Map<String, dynamic> json) =>
      _$CustomerListResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerListResponseToJson(this);
}

/// Customer List Data - matches backend response structure
@JsonSerializable()
class CustomerListData {
  final List<CustomerSummary> customers;
  final PaginationInfo pagination;
  final CustomerSummaryStats summary;

  const CustomerListData({
    required this.customers,
    required this.pagination,
    required this.summary,
  });

  factory CustomerListData.fromJson(Map<String, dynamic> json) =>
      _$CustomerListDataFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerListDataToJson(this);
}

/// Customer Summary - matches backend aggregation response
@JsonSerializable()
class CustomerSummary {
  final String customerId;
  final String customerName;
  final String phone;
  final String customerType;
  final RiskProfile riskProfile;
  final FinancialSummary financialSummary;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CustomerSummary({
    required this.customerId,
    required this.customerName,
    required this.phone,
    required this.customerType,
    required this.riskProfile,
    required this.financialSummary,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CustomerSummary.fromJson(Map<String, dynamic> json) =>
      _$CustomerSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerSummaryToJson(this);
}

/// Financial Summary - matches backend aggregation fields
@JsonSerializable()
class FinancialSummary {
  final int totalDebts;
  final int activeDebts;
  final double totalDebtAmount;
  final double totalOutstanding;
  final double totalPaid;
  final int paymentRatio; // percentage (0-100)
  final bool hasOverdueDebts;

  const FinancialSummary({
    required this.totalDebts,
    required this.activeDebts,
    required this.totalDebtAmount,
    required this.totalOutstanding,
    required this.totalPaid,
    required this.paymentRatio,
    required this.hasOverdueDebts,
  });

  factory FinancialSummary.fromJson(Map<String, dynamic> json) =>
      _$FinancialSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$FinancialSummaryToJson(this);
}

/// Pagination Info - matches backend pagination helper
@JsonSerializable()
class PaginationInfo {
  final int currentPage;
  final int totalPages;
  final int totalCustomers;
  final bool hasNextPage;
  final bool hasPrevPage;

  const PaginationInfo({
    required this.currentPage,
    required this.totalPages,
    required this.totalCustomers,
    required this.hasNextPage,
    required this.hasPrevPage,
  });

  factory PaginationInfo.fromJson(Map<String, dynamic> json) =>
      _$PaginationInfoFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationInfoToJson(this);
}

/// Customer Summary Stats - matches backend response
@JsonSerializable()
class CustomerSummaryStats {
  final int totalCustomers;
  final Map<String, int> riskDistribution;
  final SearchCriteria? searchCriteria;

  const CustomerSummaryStats({
    required this.totalCustomers,
    required this.riskDistribution,
    this.searchCriteria,
  });

  factory CustomerSummaryStats.fromJson(Map<String, dynamic> json) =>
      _$CustomerSummaryStatsFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerSummaryStatsToJson(this);
}

/// Search Criteria - matches backend filter structure
@JsonSerializable()
class SearchCriteria {
  final String? search;
  final String? riskLevel;
  final String? customerType;

  const SearchCriteria({
    this.search,
    this.riskLevel,
    this.customerType,
  });

  factory SearchCriteria.fromJson(Map<String, dynamic> json) =>
      _$SearchCriteriaFromJson(json);

  Map<String, dynamic> toJson() => _$SearchCriteriaToJson(this);
}

/// Customer Detail Response - for single customer API
@JsonSerializable()
class CustomerDetailResponse {
  final bool success;
  final String message;
  final CustomerDetailData? data;

  const CustomerDetailResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory CustomerDetailResponse.fromJson(Map<String, dynamic> json) =>
      CustomerDetailResponse(
        success: json['success'] as bool,
        message: json['message'] as String,
        data: json['data'] == null
            ? null
            : CustomerDetailData.fromJson(json['data'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => _$CustomerDetailResponseToJson(this);
}

/// Customer Detail Data - matches backend getCustomerById response structure
@JsonSerializable()
class CustomerDetailData {
  final CustomerBasicInfo customer;
  final CustomerStatistics? statistics;
  final DebtHistory? debtHistory;
  final PaymentHistory? paymentHistory;
  final RecentActivity? recentActivity;
  final RiskProfile? riskProfile;

  const CustomerDetailData({
    required this.customer,
    this.statistics,
    this.debtHistory,
    this.paymentHistory,
    this.recentActivity,
    this.riskProfile,
  });

  factory CustomerDetailData.fromJson(Map<String, dynamic> json) =>
      _$CustomerDetailDataFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerDetailDataToJson(this);
}

/// Customer Basic Info - core customer data
@JsonSerializable()
class CustomerBasicInfo {
  final String? customerId;
  final String? customerName;
  final String? phone;
  final String? customerType;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const CustomerBasicInfo({
    this.customerId,
    this.customerName,
    this.phone,
    this.customerType,
    this.createdAt,
    this.updatedAt,
  });

  factory CustomerBasicInfo.fromJson(Map<String, dynamic> json) =>
      _$CustomerBasicInfoFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerBasicInfoToJson(this);
}

/// Customer Statistics
@JsonSerializable()
class CustomerStatistics {
  final int? totalDebts;
  final int? activeDebts;
  final int? paidDebts;
  final CustomerFinancials? financials;
  final PaymentBehavior? paymentBehavior;
  final RiskAnalysis? riskAnalysis;

  const CustomerStatistics({
    this.totalDebts,
    this.activeDebts,
    this.paidDebts,
    this.financials,
    this.paymentBehavior,
    this.riskAnalysis,
  });

  factory CustomerStatistics.fromJson(Map<String, dynamic> json) =>
      _$CustomerStatisticsFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerStatisticsToJson(this);
}

/// Customer Financials
@JsonSerializable()
class CustomerFinancials {
  final double? totalBorrowed;
  final double? totalOutstanding;
  final double? totalPaid;
  final double? averageDebtAmount;
  final int? collectionRate;

  const CustomerFinancials({
    this.totalBorrowed,
    this.totalOutstanding,
    this.totalPaid,
    this.averageDebtAmount,
    this.collectionRate,
  });

  factory CustomerFinancials.fromJson(Map<String, dynamic> json) =>
      _$CustomerFinancialsFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerFinancialsToJson(this);
}

/// Payment Behavior
@JsonSerializable()
class PaymentBehavior {
  final int? totalPayments;
  final double? averagePaymentAmount;
  final int? onTimePayments;
  final int? latePayments;
  final int? averagePaymentDelay;
  final int? maxPaymentDelay;

  const PaymentBehavior({
    this.totalPayments,
    this.averagePaymentAmount,
    this.onTimePayments,
    this.latePayments,
    this.averagePaymentDelay,
    this.maxPaymentDelay,
  });

  factory PaymentBehavior.fromJson(Map<String, dynamic> json) =>
      _$PaymentBehaviorFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentBehaviorToJson(this);
}

/// Risk Analysis
@JsonSerializable()
class RiskAnalysis {
  final String? currentRiskLevel;
  final double? riskScore;
  final DateTime? lastAssessment;
  final int? assessmentHistory;
  final List<String>? riskFactors;

  const RiskAnalysis({
    this.currentRiskLevel,
    this.riskScore,
    this.lastAssessment,
    this.assessmentHistory,
    this.riskFactors,
  });

  factory RiskAnalysis.fromJson(Map<String, dynamic> json) =>
      _$RiskAnalysisFromJson(json);

  Map<String, dynamic> toJson() => _$RiskAnalysisToJson(this);
}

/// Debt History
@JsonSerializable()
class DebtHistory {
  final int? total;
  final List<DebtSummary>? debts;

  const DebtHistory({
    this.total,
    this.debts,
  });

  factory DebtHistory.fromJson(Map<String, dynamic> json) =>
      _$DebtHistoryFromJson(json);

  Map<String, dynamic> toJson() => _$DebtHistoryToJson(this);
}

/// Debt Summary
@JsonSerializable()
class DebtSummary {
  final String? debtId;
  final double? amount;
  final double? outstandingAmount;
  final double? paidAmount;
  final int? paymentRatio;
  final DateTime? dueDate;
  final String? riskLevel;
  final int? paymentDelay;
  final bool? isOnTime;
  final String? status;
  final DateTime? createdAt;

  const DebtSummary({
    this.debtId,
    this.amount,
    this.outstandingAmount,
    this.paidAmount,
    this.paymentRatio,
    this.dueDate,
    this.riskLevel,
    this.paymentDelay,
    this.isOnTime,
    this.status,
    this.createdAt,
  });

  factory DebtSummary.fromJson(Map<String, dynamic> json) =>
      _$DebtSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$DebtSummaryToJson(this);
}

/// Payment History
@JsonSerializable()
class PaymentHistory {
  final int? total;
  final List<PaymentSummary>? recent;
  final String? note;

  const PaymentHistory({
    this.total,
    this.recent,
    this.note,
  });

  factory PaymentHistory.fromJson(Map<String, dynamic> json) =>
      _$PaymentHistoryFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentHistoryToJson(this);
}

/// Payment Summary
@JsonSerializable()
class PaymentSummary {
  final String paymentId;
  final String? debtId;
  final double amount;
  final DateTime paymentDate;
  final String paymentMethod;
  final bool? isOnTime;
  final int? paymentDelay;
  final String? notes;

  const PaymentSummary({
    required this.paymentId,
    this.debtId,
    required this.amount,
    required this.paymentDate,
    required this.paymentMethod,
    this.isOnTime,
    this.paymentDelay,
    this.notes,
  });

  factory PaymentSummary.fromJson(Map<String, dynamic> json) =>
      _$PaymentSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentSummaryToJson(this);
}

/// Recent Activity
@JsonSerializable()
class RecentActivity {
  final int? newDebtsLast30Days;
  final int? paymentsLast30Days;
  final double? amountPaidLast30Days;
  final DateTime? lastPaymentDate;
  final DateTime? lastDebtDate;

  const RecentActivity({
    this.newDebtsLast30Days,
    this.paymentsLast30Days,
    this.amountPaidLast30Days,
    this.lastPaymentDate,
    this.lastDebtDate,
  });

  factory RecentActivity.fromJson(Map<String, dynamic> json) =>
      _$RecentActivityFromJson(json);

  Map<String, dynamic> toJson() => _$RecentActivityToJson(this);
}

/// Customer Stats Response - for customer statistics API
@JsonSerializable()
class CustomerStatsResponse {
  final bool success;
  final String message;
  final CustomerStats data;

  const CustomerStatsResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory CustomerStatsResponse.fromJson(Map<String, dynamic> json) =>
      _$CustomerStatsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerStatsResponseToJson(this);
}

/// Customer Stats Data - matches backend customer stats structure
@JsonSerializable()
class CustomerStats {
  final int totalCustomers;
  final int activeCustomers;
  final int newCustomers;
  final int returningCustomers;
  final Map<String, int> riskDistribution;
  final Map<String, int> categoryDistribution;
  final double totalOutstandingBalance;
  final double totalCreditLimit;
  final double avgRiskScore;

  const CustomerStats({
    required this.totalCustomers,
    required this.activeCustomers,
    required this.newCustomers,
    required this.returningCustomers,
    required this.riskDistribution,
    required this.categoryDistribution,
    required this.totalOutstandingBalance,
    required this.totalCreditLimit,
    required this.avgRiskScore,
  });

  factory CustomerStats.fromJson(Map<String, dynamic> json) =>
      _$CustomerStatsFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerStatsToJson(this);
}

/// Customer Debts Response - for customer debts API  
@JsonSerializable()
class CustomerDebtsResponse {
  final bool success;
  final String message;
  final List<dynamic> data; // This should be properly typed based on debt model

  const CustomerDebtsResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory CustomerDebtsResponse.fromJson(Map<String, dynamic> json) =>
      _$CustomerDebtsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerDebtsResponseToJson(this);
}

/// Customer Query Parameters - for API requests
class CustomerQueryParams {
  final int page;
  final int limit;
  final String? search;
  final String? riskLevel;
  final String? customerType;
  final String? category;
  final String sortBy;
  final String sortOrder;
  final bool? hasDebt;
  final bool? hasOutstandingBalance;

  const CustomerQueryParams({
    this.page = 1,
    this.limit = 20,
    this.search,
    this.riskLevel,
    this.customerType,
    this.category,
    this.sortBy = 'createdAt',
    this.sortOrder = 'desc',
    this.hasDebt,
    this.hasOutstandingBalance,
  });

  Map<String, dynamic> toQueryMap() {
    final Map<String, dynamic> params = {
      'page': page,
      'limit': limit,
      'sortBy': sortBy,
      'sortOrder': sortOrder,
    };

    if (search != null && search!.isNotEmpty) params['search'] = search;
    if (riskLevel != null) params['riskLevel'] = riskLevel;
    if (customerType != null) params['customerType'] = customerType;
    if (category != null) params['category'] = category;
    if (hasDebt != null) params['hasDebt'] = hasDebt;
    if (hasOutstandingBalance != null) params['hasOutstandingBalance'] = hasOutstandingBalance;

    return params;
  }
}

/// Customer Constants - matching backend enums
class CustomerConstants {
  static const List<String> customerTypes = ['new', 'returning'];
  static const List<String> customerTypesDisplay = ['New', 'Returning'];
  static const List<String> categories = ['regular', 'vip', 'premium'];
  static const List<String> riskLevels = ['Low Risk', 'Medium Risk', 'High Risk', 'Critical Risk'];
  static const List<String> sortByOptions = ['CustomerName', 'createdAt', 'riskScore', 'totalPurchaseAmount', 'outstandingBalance'];
  static const List<String> sortOrderOptions = ['asc', 'desc'];
} 