import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_constants.dart';

/// Common form behavior mixin for modal forms
mixin ModalFormMixin<T extends StatefulWidget> on State<T> {
  
  /// Form key for validation
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  
  /// Loading state
  bool isLoading = false;

  /// Update loading state
  void setLoading(bool loading) {
    if (mounted) {
      setState(() => isLoading = loading);
    }
  }

  /// Validate form
  bool validateForm() {
    return formKey.currentState?.validate() ?? false;
  }

  /// Build standard action buttons
  Widget buildActionButtons({
    required String primaryButtonText,
    required VoidCallback onPrimaryPressed,
    String cancelButtonText = 'Cancel',
    VoidCallback? onCancelPressed,
    bool showCancel = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Primary button
        ElevatedButton(
          onPressed: isLoading ? null : onPrimaryPressed,
          style: ModalConstants.primaryButtonStyle(),
          child: isLoading
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(primaryButtonText),
        ),
        
        // Cancel button
        if (showCancel) ...[
          ModalConstants.defaultSpacing,
          OutlinedButton(
            onPressed: isLoading ? null : (onCancelPressed ?? () => Navigator.of(context).pop()),
            style: ModalConstants.outlinedButtonStyle(),
            child: Text(cancelButtonText),
          ),
        ],
      ],
    );
  }

  /// Build form field with standard decoration
  Widget buildFormField({
    required TextEditingController controller,
    required String labelText,
    String? hintText,
    Widget? prefixIcon,
    String? helperText,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    int? maxLines,
    bool enabled = true,
    TextCapitalization textCapitalization = TextCapitalization.none,
  }) {
    return TextFormField(
      controller: controller,
      decoration: ModalConstants.inputDecoration(
        hintText: hintText ?? '',
      ),
      validator: validator,
      keyboardType: keyboardType,
      maxLines: maxLines ?? 1,
      enabled: enabled,
      textCapitalization: textCapitalization,
    );
  }

  /// Build dropdown field
  Widget buildDropdownField<K>({
    required K? value,
    required List<DropdownMenuItem<K>> items,
    required String labelText,
    String? hintText,
    Widget? prefixIcon,
    required void Function(K?) onChanged,
    String? Function(K?)? validator,
  }) {
    return DropdownButtonFormField<K>(
      value: value,
      decoration: ModalConstants.inputDecoration(
        hintText: hintText ?? labelText,
        prefixIcon: prefixIcon,
      ),
      items: items,
      onChanged: onChanged,
      validator: validator,
    );
  }

  /// Build date picker field
  Widget buildDatePickerField({
    required DateTime? selectedDate,
    required String labelText,
    required void Function(DateTime) onDateSelected,
    DateTime? firstDate,
    DateTime? lastDate,
    String? Function(DateTime?)? validator,
  }) {
    return InkWell(
      onTap: () => _selectDate(
        selectedDate: selectedDate,
        onDateSelected: onDateSelected,
        firstDate: firstDate,
        lastDate: lastDate,
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: ThemeUtils.getBorderColor(context, type: BorderType.border)),
          borderRadius: BorderRadius.circular(ModalConstants.defaultRadius),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              selectedDate != null
                  ? '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}'
                  : 'Select $labelText',
            ),
            const Icon(Icons.calendar_today),
          ],
        ),
      ),
    );
  }

  /// Date picker helper
  Future<void> _selectDate({
    required DateTime? selectedDate,
    required void Function(DateTime) onDateSelected,
    DateTime? firstDate,
    DateTime? lastDate,
  }) async {
    final date = await showDatePicker(
      context: context,
      initialDate: selectedDate ?? DateTime.now(),
      firstDate: firstDate ?? DateTime.now().subtract(const Duration(days: 365)),
      lastDate: lastDate ?? DateTime.now().add(const Duration(days: 365)),
    );
    if (date != null) {
      onDateSelected(date);
    }
  }
}

/// Bloc listener mixin for common bloc operations
mixin BlocListenerMixin<T extends StatefulWidget> on State<T> {
  
  /// Show success message and close modal
  void handleSuccess(String message) {
    Navigator.of(context).pop();
    ModalConstants.showSuccessSnackBar(context, message);
  }

  /// Show error message
  void handleError(String message) {
    ModalConstants.showErrorSnackBar(context, message);
  }

  /// Common bloc listener for CRUD operations
  Widget buildBlocListener<B extends StateStreamable<S>, S>({
    required Widget child,
    required void Function(BuildContext, S) listener,
  }) {
    return BlocListener<B, S>(
      listener: listener,
      child: child,
    );
  }
} 