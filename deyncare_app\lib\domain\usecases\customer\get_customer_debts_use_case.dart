import 'package:dartz/dartz.dart';
import 'package:deyncare_app/domain/repositories/customer_repository.dart';
import 'package:deyncare_app/data/models/customer_model.dart';
import 'package:deyncare_app/core/errors/failures.dart';

/// Use case for retrieving customer debts - BACKEND ALIGNED
class GetCustomerDebtsUseCase {
  final CustomerRepository _repository;

  GetCustomerDebtsUseCase(this._repository);

  /// Execute the use case to get customer debts
  Future<Either<Failure, CustomerDebtsResponse>> execute({
    required String customerId,
    bool includeCompleted = false,
  }) async {
    if (customerId.trim().isEmpty) {
      return Left(ValidationFailure(message: 'Customer ID is required'));
    }

    // Validate customer ID format (matches backend validation)
    if (!RegExp(r'^CUST\d{3}$').hasMatch(customerId.trim())) {
      return Left(ValidationFailure(message: 'Invalid customer ID format. Expected: CUST001, CUST002, etc.'));
    }

    return await _repository.getCustomerDebts(
      customerId.trim(),
      includeCompleted: includeCompleted,
    );
  }
} 