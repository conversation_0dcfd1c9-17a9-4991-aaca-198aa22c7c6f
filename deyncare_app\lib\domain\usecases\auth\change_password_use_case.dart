import 'package:deyncare_app/domain/repositories/auth_repository.dart';

/// Use case for changing user password
///
/// This class handles the process of changing a user's password
/// when they are already authenticated.
class ChangePasswordUseCase {
  final AuthRepository _repository;

  /// Creates a new instance with the required repository
  ChangePasswordUseCase(this._repository);

  /// Executes the password change with current and new passwords
  ///
  /// Returns a Future<void> that completes when password change is successful
  /// Throws exceptions if the operation fails (e.g., incorrect current password)
  Future<void> execute({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    // Validate that new password and confirm password match
    if (newPassword != confirmPassword) {
      throw Exception('New password and confirmation do not match');
    }
    
    await _repository.changePassword(
      currentPassword: currentPassword,
      newPassword: newPassword,
      confirmPassword: confirmPassword,
    );
  }
}
