import 'package:deyncare_app/core/utils/logger.dart';
import 'package:deyncare_app/data/network/clients/dio_client.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';

/// Remote data source for plan-related operations
///
/// Handles all API interactions with the backend plan endpoints
class PlanRemoteSource {
  final DioClient _dioClient;

  /// Creates a new instance with the required client
  PlanRemoteSource({
    DioClient? dioClient,
  }) : _dioClient = dioClient ?? DioClient();

  /// Fetches all available subscription plans
  /// GET /public/plans
  Future<List<dynamic>> getPlans() async {
    logger.i('Fetching subscription plans...');
    try {
      final response = await _dioClient.get(
        '/public/plans',
        showSuccessToast: false,
      );

      if (response == null || response is! Map<String, dynamic>) {
        logger.e('Invalid response format from server for plans list');
        throw ApiException(
          message: 'Invalid response format from server for plans list',
          code: 'invalid_plans_response_format',
        );
      }

      if (!response.containsKey('data') || response['data'] is! List) {
        logger.e('Plans data missing or invalid in response: $response');
        throw ApiException(
          message: 'Plans data missing or invalid in response',
          code: 'missing_plans_data',
        );
      }

      logger.d('Successfully fetched plans data: ${response['data']}');
      return response['data'];
    } catch (e, stackTrace) {
      logger.e('Failed to fetch plans', error: e, stackTrace: stackTrace);
      rethrow; // Re-throw the exception to be handled by the repository/bloc
    }
  }

  /// Fetches a single plan by its ID
  /// GET /public/plans (filters for specific plan)
  Future<Map<String, dynamic>> getPlanById(String planId) async {
    logger.i('Fetching plan details for ID: $planId');
    
    try {
      // Always use the public endpoint to avoid authentication issues during registration
      final response = await _dioClient.get(
        '/public/plans',
        showSuccessToast: false,
      );

      if (response == null || response is! Map<String, dynamic>) {
        logger.e('Invalid response format from server for plans list');
        throw ApiException(
          message: 'Invalid response format from server for plans list',
          code: 'invalid_plans_response_format',
        );
      }

      if (!response.containsKey('data') || response['data'] is! List) {
        logger.e('Plans data missing or invalid in response: $response');
        throw ApiException(
          message: 'Plans data missing or invalid in response',
          code: 'missing_plans_data',
        );
      }

      // Find the specific plan in the list
      final plans = response['data'] as List;
      
      // Try to find plan by planId first
      dynamic plan = plans.cast<Map<String, dynamic>>().firstWhere(
        (plan) => plan['planId'] == planId,
        orElse: () => <String, dynamic>{},
      );
      
      // If not found by planId, try to find by type (trial, monthly, yearly)
      if (plan.isEmpty) {
        plan = plans.cast<Map<String, dynamic>>().firstWhere(
          (plan) => plan['type'] == planId,
          orElse: () => <String, dynamic>{},
        );
      }
      
      // If still not found, try to find by name
      if (plan.isEmpty) {
        plan = plans.cast<Map<String, dynamic>>().firstWhere(
          (plan) => plan['name']?.toString().toLowerCase() == planId.toLowerCase(),
          orElse: () => <String, dynamic>{},
        );
      }

      if (plan.isEmpty) {
        logger.e('Plan not found with ID: $planId in plans list: ${plans.map((p) => p['planId']).toList()}');
        throw ApiException(
          message: 'Plan not found with ID: $planId',
          code: 'plan_not_found',
        );
      }

      logger.d('Successfully found plan details in public plans list');
      return plan as Map<String, dynamic>;
    } catch (e) {
      logger.e('Error fetching plan from public endpoint: $e');
      rethrow; // Don't try authenticated endpoint during registration flow
    }
  }
} 