# Firebase Setup Guide for DeynCare Push Notifications

## Overview
This guide provides step-by-step instructions to set up Firebase Cloud Messaging (FCM) for the DeynCare notification system, including all required environment variables and configuration.

## Prerequisites
- Google/Gmail account
- Access to Firebase Console
- Admin access to your project

## Step 1: Create Firebase Project

### 1.1 Go to Firebase Console
1. Visit [Firebase Console](https://console.firebase.google.com/)
2. Sign in with your Google account
3. Click **"Add project"** or **"Create a project"**

### 1.2 Project Setup
1. **Project Name**: Enter `DeynCare-Notifications` (or your preferred name)
2. **Project ID**: Firebase will auto-generate (e.g., `deyncare-notifications-abc123`)
3. **Analytics**: You can disable Google Analytics for this project
4. Click **"Create project"**
5. Wait for project creation to complete

## Step 2: Enable Cloud Messaging

### 2.1 Navigate to Cloud Messaging
1. In your Firebase project dashboard
2. Go to **Build** → **Cloud Messaging** from the left sidebar
3. Cloud Messaging should be automatically enabled

### 2.2 Configure Cloud Messaging
1. You'll see the Cloud Messaging dashboard
2. Note down your **Server Key** (we'll get this in Step 4)

## Step 3: Generate Service Account Key

### 3.1 Go to Project Settings
1. Click the **gear icon** ⚙️ next to "Project Overview"
2. Select **"Project settings"**
3. Go to the **"Service accounts"** tab

### 3.2 Generate Private Key
1. In the **"Service accounts"** tab
2. Click **"Generate new private key"**
3. A dialog will appear warning about security
4. Click **"Generate key"**
5. A JSON file will be downloaded (e.g., `deyncare-notifications-firebase-adminsdk-xyz123.json`)

### 3.3 Secure the Service Account File
```bash
# Create a secure directory for credentials
mkdir -p /secure/credentials/

# Move the downloaded file to secure location
mv ~/Downloads/deyncare-notifications-firebase-adminsdk-*.json /secure/credentials/firebase-service-account.json

# Set proper permissions (Linux/macOS)
chmod 600 /secure/credentials/firebase-service-account.json
```

## Step 4: Get Required Configuration Values

### 4.1 Project Information
From your Firebase project settings, collect:

1. **Project ID**: Found in "General" tab
2. **Web API Key**: Found in "General" tab under "Web API Key"
3. **Project Number**: Found in "General" tab (also called Sender ID)

### 4.2 Service Account Information
Open the downloaded JSON file and extract:
- `project_id`
- `private_key_id`
- `private_key`
- `client_email`
- `client_id`
- `auth_uri`
- `token_uri`

## Step 5: Environment Variables Setup

### 5.1 Required Environment Variables

Create or update your `.env` file with these variables:

```bash
# =================================
# FIREBASE CONFIGURATION
# =================================

# Firebase Project Configuration
FIREBASE_PROJECT_ID=your-project-id-here
FIREBASE_WEB_API_KEY=your-web-api-key-here
FIREBASE_PROJECT_NUMBER=your-project-number-here

# Firebase Admin SDK Service Account (Method 1: File Path)
GOOGLE_APPLICATION_CREDENTIALS=/secure/credentials/firebase-service-account.json

# Firebase Admin SDK Service Account (Method 2: JSON Content - Alternative)
# Use this if you can't use file path (e.g., cloud deployment)
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"...","private_key_id":"..."}

# Firebase Cloud Messaging Server Key (Legacy - Optional)
FCM_SERVER_KEY=your-fcm-server-key-here

# =================================
# NOTIFICATION SYSTEM CONFIGURATION
# =================================

# Enable/Disable Push Notifications
PUSH_NOTIFICATIONS_ENABLED=true

# Enable/Disable Debt Reminders Cron Job
DEBT_REMINDERS_ENABLED=true

# Notification Settings
NOTIFICATION_BATCH_SIZE=500
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_TIMEOUT_SECONDS=35

# =================================
# DEVELOPMENT/TESTING
# =================================

# Firebase Emulator (Development Only)
FIREBASE_EMULATOR_ENABLED=false
FIREBASE_EMULATOR_HOST=localhost
FIREBASE_EMULATOR_PORT=9099

# Debug Tokens (Development Only)
FIREBASE_APPCHECK_DEBUG_TOKEN=your-debug-token-here
```

### 5.2 Production Environment Variables

For production deployment, use these additional variables:

```bash
# =================================
# PRODUCTION FIREBASE CONFIGURATION
# =================================

# Environment
NODE_ENV=production

# Firebase Configuration (Production)
FIREBASE_PROJECT_ID=deyncare-prod-abc123
FIREBASE_WEB_API_KEY=AIzaSyABC123...
FIREBASE_PROJECT_NUMBER=************

# Service Account (Inline JSON for cloud deployment)
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"deyncare-prod","private_key_id":"abc123","private_key":"-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----\n","client_email":"*******","client_id":"************345678901","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-abc123%40deyncare-prod.iam.gserviceaccount.com"}

# Security
SECURE_COOKIES=true
FIREBASE_SECURITY_ENABLED=true

# Monitoring
FIREBASE_MONITORING_ENABLED=true
NOTIFICATION_ANALYTICS_ENABLED=true
```

## Step 6: Update Firebase Service Configuration

### 6.1 Update FirebaseService.js Settings Helper

Add this method to your settings helper:

```javascript
// In src/utils/settingsHelper.js or create new file

class SettingsHelper {
  /**
   * Get Firebase credentials from environment or settings
   * @returns {Object} Firebase configuration
   */
  static async getFirebaseCredentials() {
    try {
      // Method 1: From environment variable (JSON string)
      if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        return {
          serviceAccountKey: process.env.FIREBASE_SERVICE_ACCOUNT_KEY,
          projectId: process.env.FIREBASE_PROJECT_ID
        };
      }
      
      // Method 2: From file path
      if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
        const fs = require('fs');
        const serviceAccountKey = fs.readFileSync(process.env.GOOGLE_APPLICATION_CREDENTIALS, 'utf8');
        return {
          serviceAccountKey: serviceAccountKey,
          projectId: process.env.FIREBASE_PROJECT_ID
        };
      }
      
      // Method 3: From database settings (if stored there)
      // const settings = await this.getSystemSettings();
      // return settings.firebaseCredentials;
      
      throw new Error('No Firebase credentials found in environment');
    } catch (error) {
      console.error('Failed to get Firebase credentials:', error);
      return null;
    }
  }

  /**
   * Get Firebase configuration for client-side
   * @returns {Object} Client Firebase config
   */
  static getFirebaseClientConfig() {
    return {
      apiKey: process.env.FIREBASE_WEB_API_KEY,
      authDomain: `${process.env.FIREBASE_PROJECT_ID}.firebaseapp.com`,
      projectId: process.env.FIREBASE_PROJECT_ID,
      storageBucket: `${process.env.FIREBASE_PROJECT_ID}.appspot.com`,
      messagingSenderId: process.env.FIREBASE_PROJECT_NUMBER,
      appId: process.env.FIREBASE_APP_ID // Optional
    };
  }

  /**
   * Validate Firebase configuration
   * @returns {boolean} True if valid
   */
  static validateFirebaseConfig() {
    const required = [
      'FIREBASE_PROJECT_ID',
      'FIREBASE_WEB_API_KEY',
      'FIREBASE_PROJECT_NUMBER'
    ];

    const missing = required.filter(key => !process.env[key]);
    
    if (missing.length > 0) {
      console.error('Missing required Firebase environment variables:', missing);
      return false;
    }

    // Check if we have service account credentials
    const hasCredentials = process.env.FIREBASE_SERVICE_ACCOUNT_KEY || 
                          process.env.GOOGLE_APPLICATION_CREDENTIALS;
    
    if (!hasCredentials) {
      console.error('Missing Firebase service account credentials');
      return false;
    }

    return true;
  }
}

module.exports = { SettingsHelper };
```

## Step 7: Mobile App Configuration

### 7.1 Android Configuration

1. In Firebase Console, click **"Add app"** → **Android**
2. **Package Name**: Enter your Android package name (e.g., `com.deyncare.admin`)
3. **App Nickname**: `DeynCare Admin Android`
4. Download `google-services.json`
5. Place it in `android/app/` directory

### 7.2 iOS Configuration

1. In Firebase Console, click **"Add app"** → **iOS**
2. **Bundle ID**: Enter your iOS bundle ID (e.g., `com.deyncare.admin`)
3. **App Nickname**: `DeynCare Admin iOS`
4. Download `GoogleService-Info.plist`
5. Add it to your iOS project in Xcode

### 7.3 Flutter Configuration

Add to `pubspec.yaml`:
```yaml
dependencies:
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10
```

## Step 8: Testing Firebase Setup

### 8.1 Test Environment Variables

Create a test script:

```javascript
// test-firebase-config.js
require('dotenv').config();
const { SettingsHelper } = require('./src/utils/settingsHelper');

async function testFirebaseConfig() {
  console.log('🔥 Testing Firebase Configuration...\n');
  
  // Test environment variables
  console.log('📋 Environment Variables:');
  console.log(`FIREBASE_PROJECT_ID: ${process.env.FIREBASE_PROJECT_ID || '❌ Missing'}`);
  console.log(`FIREBASE_WEB_API_KEY: ${process.env.FIREBASE_WEB_API_KEY ? '✅ Set' : '❌ Missing'}`);
  console.log(`FIREBASE_PROJECT_NUMBER: ${process.env.FIREBASE_PROJECT_NUMBER || '❌ Missing'}`);
  console.log(`GOOGLE_APPLICATION_CREDENTIALS: ${process.env.GOOGLE_APPLICATION_CREDENTIALS || '❌ Missing'}`);
  console.log(`FIREBASE_SERVICE_ACCOUNT_KEY: ${process.env.FIREBASE_SERVICE_ACCOUNT_KEY ? '✅ Set' : '❌ Missing'}\n`);
  
  // Test configuration validation
  const isValid = SettingsHelper.validateFirebaseConfig();
  console.log(`🔍 Configuration Valid: ${isValid ? '✅ Yes' : '❌ No'}\n`);
  
  // Test credentials loading
  try {
    const credentials = await SettingsHelper.getFirebaseCredentials();
    console.log(`🔑 Credentials Loaded: ${credentials ? '✅ Success' : '❌ Failed'}\n`);
  } catch (error) {
    console.log(`🔑 Credentials Error: ❌ ${error.message}\n`);
  }
  
  // Test Firebase service initialization
  try {
    const FirebaseService = require('./src/services/firebaseService');
    const testResult = await FirebaseService.testConnection();
    console.log(`🚀 Firebase Connection: ${testResult.success ? '✅ Success' : '❌ Failed'}`);
    console.log(`   Message: ${testResult.message}\n`);
  } catch (error) {
    console.log(`🚀 Firebase Connection: ❌ Error - ${error.message}\n`);
  }
}

testFirebaseConfig();
```

Run the test:
```bash
node test-firebase-config.js
```

### 8.2 Test API Endpoints

```bash
# Test Firebase connection
curl -X GET http://localhost:3000/api/admin/notifications/push/test \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Test notification targets
curl -X GET http://localhost:3000/api/admin/notifications/push/targets \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Step 9: Security Best Practices

### 9.1 Service Account Security

```bash
# Set proper file permissions
chmod 600 /secure/credentials/firebase-service-account.json

# Ensure secure directory
chmod 700 /secure/credentials/

# Add to .gitignore
echo "/secure/" >> .gitignore
echo "firebase-service-account.json" >> .gitignore
echo ".env" >> .gitignore
```

### 9.2 Environment Variables Security

1. **Never commit** `.env` files to version control
2. **Use encrypted storage** for production secrets
3. **Rotate service account keys** regularly
4. **Monitor Firebase usage** for unusual activity
5. **Use least privilege principle** for service accounts

## Step 10: Deployment Considerations

### 10.1 Cloud Platform Setup

**For Google Cloud Platform:**
```bash
# Set environment variables
gcloud config set project YOUR_PROJECT_ID
gcloud secrets create firebase-service-account --data-file=firebase-service-account.json
```

**For AWS/Other Platforms:**
```bash
# Use inline JSON in environment variable
export FIREBASE_SERVICE_ACCOUNT_KEY='{"type":"service_account",...}'
```

### 10.2 Environment-Specific Configuration

Create separate `.env` files:
- `.env.development`
- `.env.staging`
- `.env.production`

## Step 11: Monitoring and Troubleshooting

### 11.1 Common Issues

1. **"Firebase not initialized"**
   - Check `GOOGLE_APPLICATION_CREDENTIALS` path
   - Verify service account JSON format

2. **"Invalid credentials"**
   - Verify service account has FCM permissions
   - Check project ID matches

3. **"Token registration failed"**
   - Verify mobile app configuration
   - Check FCM server key

### 11.2 Monitoring Setup

Add monitoring to your Firebase service:

```javascript
// Add to firebaseService.js
const monitoring = {
  totalSent: 0,
  totalFailed: 0,
  lastError: null,
  
  logSuccess(count) {
    this.totalSent += count;
    console.log(`📊 Firebase Stats: ${this.totalSent} sent, ${this.totalFailed} failed`);
  },
  
  logError(error) {
    this.totalFailed++;
    this.lastError = error;
    console.error(`📊 Firebase Error: ${error.message}`);
  }
};
```

## Environment Variables Summary

Here's the complete list of required environment variables:

```bash
# Essential Firebase Variables
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_WEB_API_KEY=your-api-key
FIREBASE_PROJECT_NUMBER=your-project-number
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json
# OR
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account",...}

# Optional Configuration
PUSH_NOTIFICATIONS_ENABLED=true
DEBT_REMINDERS_ENABLED=true
NOTIFICATION_BATCH_SIZE=500
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_TIMEOUT_SECONDS=35

# Development Only
FIREBASE_EMULATOR_ENABLED=false
FIREBASE_APPCHECK_DEBUG_TOKEN=debug-token
```

## Next Steps

1. ✅ **Complete Firebase project setup**
2. ✅ **Set all environment variables**
3. ✅ **Test Firebase connection**
4. ✅ **Configure mobile apps**
5. ✅ **Test notification sending**
6. ✅ **Deploy to production**
7. ✅ **Monitor and maintain**

Your Firebase setup is now complete! The notification system should be fully functional with proper push notification capabilities. 