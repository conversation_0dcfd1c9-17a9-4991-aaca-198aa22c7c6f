import 'package:dartz/dartz.dart';
import 'package:deyncare_app/core/errors/failures.dart';
import 'package:deyncare_app/data/models/customer_model.dart';

/// Customer Repository Interface - matches backend customerRoutes.js exactly
abstract class CustomerRepository {
  /// GET /api/customers
  Future<Either<Failure, CustomerListResponse>> getCustomers({
    CustomerQueryParams? params,
  });

  /// GET /api/customers/stats
  Future<Either<Failure, CustomerStatsResponse>> getCustomerStats();

  /// GET /api/customers/:customerId
  Future<Either<Failure, CustomerDetailResponse>> getCustomerById(String customerId);

  /// POST /api/customers
  Future<Either<Failure, CustomerDetailResponse>> createCustomer(CreateCustomerRequest request);

  /// PUT /api/customers/:customerId
  Future<Either<Failure, CustomerDetailResponse>> updateCustomer(String customerId, UpdateCustomerRequest request);

  /// DELETE /api/customers/:customerId
  /// Supports force parameter for customers with debt history
  Future<Either<Failure, bool>> deleteCustomer(String customerId, {bool force = false});

  /// GET /api/customers/:customerId/debts
  Future<Either<Failure, CustomerDebtsResponse>> getCustomerDebts(String customerId, {bool includeCompleted});
} 
