import 'dart:async';
import 'package:flutter/material.dart';
import 'package:app_links/app_links.dart';
import 'package:deyncare_app/core/routes/app_router.dart';

/// Service to handle deep links for the DeynCare app
class DeepLinkService {
  static StreamSubscription? _deepLinkSubscription;
  static final AppLinks _appLinks = AppLinks();
  
  /// Initialize deep link handling
  static Future<void> initDeepLinks(BuildContext context) async {
    // Handle initial link (app opened from link)
    try {
      final initialLink = await _appLinks.getInitialAppLink();
      if (initialLink != null) {
        _handleDeepLink(context, initialLink.toString());
      }
    
      // Handle links when app is running
      _deepLinkSubscription = _appLinks.uriLinkStream.listen((Uri? link) {
        if (link != null) {
          _handleDeepLink(context, link.toString());
        }
      }, onError: (error) {
        debugPrint('Deep link error: $error');
      });
    } catch (e) {
      debugPrint('Failed to handle deep links: $e');
    }
  }
  
  /// Handle deep link based on URI
  static void _handleDeepLink(BuildContext context, String link) {
    debugPrint('🔗 Received deep link: $link');
    
    try {
      final uri = Uri.parse(link);
      
      // Handle deyncare:// scheme deep links
      if (uri.scheme == 'deyncare') {
        
        // Handle authentication deep links
        if (uri.host == 'auth') {
          if (uri.path == '/reset-success' || uri.path == '/reset-password/success') {
            AppRouter.navigateToResetSuccess(context);
          }
        }
        
        // 📱 Handle dashboard deep links (from push notifications)
        else if (uri.host == 'dashboard') {
          
          // Handle debt-related deep links → Navigate to DebtListScreen (actual screen)
          if (uri.path.startsWith('/debts/')) {
            final debtId = uri.path.split('/').last;
            debugPrint('📄 Navigating to debt list (debt details coming soon): $debtId');
            AppRouter.navigateToDebtList(context);
          }
          
          // Handle filtered debt list → Navigate to DebtListScreen
          else if (uri.path == '/debts' && uri.queryParameters.containsKey('filter')) {
            final filter = uri.queryParameters['filter'];
            debugPrint('📋 Navigating to debt list with filter: $filter');
            AppRouter.navigateToDebtList(context);
          }
          
          // Handle customer details → Navigate to CustomerListScreen (actual screen)
          else if (uri.path.startsWith('/customers/')) {
            final customerId = uri.path.split('/').last;
            debugPrint('👤 Navigating to customer list (customer details coming soon): $customerId');
            AppRouter.navigateToCustomerList(context);
          }
          
          // Default dashboard navigation
          else {
            debugPrint('🏠 Navigating to dashboard home');
            AppRouter.navigateToDashboard(context);
          }
        }
        
        // Handle any other deyncare:// links
        else {
          debugPrint('🏠 Fallback to dashboard for unknown link: $link');
          AppRouter.navigateToDashboard(context);
        }
      }
      
    } catch (e) {
      debugPrint('❌ Failed to parse deep link: $e');
      // Fallback to dashboard on error
      AppRouter.navigateToDashboard(context);
    }
  }
  
  /// Dispose the deep link subscription
  static void dispose() {
    _deepLinkSubscription?.cancel();
    _deepLinkSubscription = null;
  }
}
