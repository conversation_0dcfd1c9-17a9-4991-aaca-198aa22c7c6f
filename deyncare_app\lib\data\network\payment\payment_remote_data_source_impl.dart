import 'package:dio/dio.dart';
import 'package:deyncare_app/data/network/payment/payment_remote_data_source.dart';
import 'package:deyncare_app/data/models/payment_model.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';

/// Implementation of payment remote data source
class PaymentRemoteDataSourceImpl implements PaymentRemoteDataSource {
  final Dio _dio;

  PaymentRemoteDataSourceImpl(this._dio);

  @override
  Future<PaymentModel> createPayment(Map<String, dynamic> paymentData) async {
    try {
      final response = await _dio.post('/payments', data: paymentData);
      
      if (response.statusCode == 201) {
        return PaymentModel.fromJson(response.data['payment']);
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'Failed to create payment',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw ApiException(
          message: e.response?.data['message'] ?? 'Failed to create payment',
          statusCode: e.response?.statusCode,
        );
      } else {
        throw ApiException(
          message: 'Network error: ${e.message}',
          statusCode: null,
        );
      }
    }
  }

  @override
  Future<Map<String, dynamic>> getPayments(Map<String, dynamic> queryParams) async {
    try {
      final response = await _dio.get('/payments', queryParameters: queryParams);
      
      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'Failed to fetch payments',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw ApiException(
          message: e.response?.data['message'] ?? 'Failed to fetch payments',
          statusCode: e.response?.statusCode,
        );
      } else {
        throw ApiException(
          message: 'Network error: ${e.message}',
          statusCode: null,
        );
      }
    }
  }

  @override
  Future<PaymentModel> getPaymentById(String paymentId) async {
    try {
      final response = await _dio.get('/payments/$paymentId');
      
      if (response.statusCode == 200) {
        return PaymentModel.fromJson(response.data['payment']);
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'Failed to fetch payment',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw ApiException(
          message: e.response?.data['message'] ?? 'Failed to fetch payment',
          statusCode: e.response?.statusCode,
        );
      } else {
        throw ApiException(
          message: 'Network error: ${e.message}',
          statusCode: null,
        );
      }
    }
  }

  @override
  Future<PaymentModel> confirmPayment(String paymentId, Map<String, dynamic> confirmationData) async {
    try {
      final response = await _dio.post('/payments/$paymentId/confirm', data: confirmationData);
      
      if (response.statusCode == 200) {
        return PaymentModel.fromJson(response.data['payment']);
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'Failed to confirm payment',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw ApiException(
          message: e.response?.data['message'] ?? 'Failed to confirm payment',
          statusCode: e.response?.statusCode,
        );
      } else {
        throw ApiException(
          message: 'Network error: ${e.message}',
          statusCode: null,
        );
      }
    }
  }

  @override
  Future<Map<String, dynamic>> processEvcPayment(Map<String, dynamic> evcData) async {
    try {
      final response = await _dio.post('/payments/evc', data: evcData);
      
      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'Failed to process EVC payment',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw ApiException(
          message: e.response?.data['message'] ?? 'Failed to process EVC payment',
          statusCode: e.response?.statusCode,
        );
      } else {
        throw ApiException(
          message: 'Network error: ${e.message}',
          statusCode: null,
        );
      }
    }
  }

  @override
  Future<PaymentModel> verifyPayment(String paymentId) async {
    try {
      final response = await _dio.post('/payments/$paymentId/verify');
      
      if (response.statusCode == 200) {
        return PaymentModel.fromJson(response.data['payment']);
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'Failed to verify payment',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw ApiException(
          message: e.response?.data['message'] ?? 'Failed to verify payment',
          statusCode: e.response?.statusCode,
        );
      } else {
        throw ApiException(
          message: 'Network error: ${e.message}',
          statusCode: null,
        );
      }
    }
  }

  @override
  Future<PaymentModel> refundPayment(String paymentId, Map<String, dynamic> refundData) async {
    try {
      final response = await _dio.post('/payments/$paymentId/refund', data: refundData);
      
      if (response.statusCode == 200) {
        return PaymentModel.fromJson(response.data['payment']);
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'Failed to refund payment',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw ApiException(
          message: e.response?.data['message'] ?? 'Failed to refund payment',
          statusCode: e.response?.statusCode,
        );
      } else {
        throw ApiException(
          message: 'Network error: ${e.message}',
          statusCode: null,
        );
      }
    }
  }

  @override
  Future<Map<String, dynamic>> getPaymentAnalytics(Map<String, dynamic> queryParams) async {
    try {
      final response = await _dio.get('/payments/stats', queryParameters: queryParams);
      
      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'Failed to fetch payment analytics',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw ApiException(
          message: e.response?.data['message'] ?? 'Failed to fetch payment analytics',
          statusCode: e.response?.statusCode,
        );
      } else {
        throw ApiException(
          message: 'Network error: ${e.message}',
          statusCode: null,
        );
      }
    }
  }

  @override
  Future<List<PaymentModel>> getPaymentHistory(String referenceId, String context) async {
    try {
      final response = await _dio.get(
        '/payments/history/$referenceId',
        queryParameters: {'context': context},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> paymentsJson = response.data['payments'];
        return paymentsJson.map((json) => PaymentModel.fromJson(json)).toList();
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'Failed to fetch payment history',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw ApiException(
          message: e.response?.data['message'] ?? 'Failed to fetch payment history',
          statusCode: e.response?.statusCode,
        );
      } else {
        throw ApiException(
          message: 'Network error: ${e.message}',
          statusCode: null,
        );
      }
    }
  }
} 