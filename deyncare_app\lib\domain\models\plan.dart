import 'package:equatable/equatable.dart';

/// Domain model for subscription plan
class Plan extends Equatable {
  final String id;
  final String name;
  final String type; // 'trial', 'monthly', 'yearly'
  final String description;
  final double price;
  final String currency;
  final String billingCycle;
  final int trialDays;
  final Map<String, dynamic> features;
  final Map<String, dynamic> limits;
  final bool isActive;
  final bool isPopular;
  final int displayOrder;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Plan({
    required this.id,
    required this.name,
    required this.type,
    required this.description,
    required this.price,
    required this.currency,
    required this.billingCycle,
    required this.trialDays,
    required this.features,
    required this.limits,
    required this.isActive,
    required this.isPopular,
    required this.displayOrder,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        type,
        description,
        price,
        currency,
        billingCycle,
        trialDays,
        features,
        limits,
        isActive,
        isPopular,
        displayOrder,
        createdAt,
        updatedAt,
      ];

  /// Get feature value as bool
  bool getFeature(String key) {
    return features[key] as bool? ?? false;
  }

  /// Get limit value as int
  int getLimit(String key) {
    return limits[key] as int? ?? 0;
  }

  /// Get formatted price
  String get formattedPrice {
    if (price == 0) {
      return 'Free';
    }
    return '\$${price.toStringAsFixed(2)}';
  }

  /// Get billing display text
  String get billingText {
    switch (billingCycle) {
      case 'monthly':
        return '/month';
      case 'yearly':
        return '/year';
      default:
        return '';
    }
  }

  /// Get full price display
  String get priceDisplay => '$formattedPrice$billingText';

  /// Check if plan has specific feature
  bool get hasRiskAnalysis => getFeature('riskAnalysis');
  bool get hasNotifications => getFeature('notifications');
  bool get hasAnalytics => getFeature('analytics');
  bool get hasApiAccess => getFeature('apiAccess');
  bool get hasPrioritySupport => getFeature('prioritySupport');
  bool get hasCustomBranding => getFeature('customBranding');
  bool get hasBulkOperations => getFeature('bulkOperations');
  bool get hasDataExport => getFeature('dataExport');

  /// Get limits
  int get maxCustomers => getLimit('maxCustomers');
  int get maxDebts => getLimit('maxDebts');
  int get maxUsers => getLimit('maxUsers');

  /// Get features as a list of strings for display
  List<String> get featureList {
    List<String> features = [];
    
    // Add limits
    if (maxCustomers == -1) {
      features.add('Unlimited Customers');
    } else {
      features.add('Up to $maxCustomers Customers');
    }
    
    if (maxDebts == -1) {
      features.add('Unlimited Debts');
    } else {
      features.add('Up to $maxDebts Debts');
    }
    
    features.add('$maxUsers User${maxUsers > 1 ? 's' : ''}');
    
    // Add features
    if (hasRiskAnalysis) features.add('AI Risk Analysis');
    if (hasNotifications) features.add('SMS & Email Notifications');
    if (hasAnalytics) features.add('Advanced Analytics');
    if (hasApiAccess) features.add('API Access');
    if (hasPrioritySupport) features.add('Priority Support');
    if (hasCustomBranding) features.add('Custom Branding');
    if (hasBulkOperations) features.add('Bulk Operations');
    if (hasDataExport) features.add('Data Export');
    
    return features;
  }

  /// Get plan color for UI
  String get planColor {
    switch (type) {
      case 'trial':
        return '#6C757D'; // Gray
      case 'monthly':
        return '#2E5BFF'; // Blue
      case 'yearly':
        return '#28A745'; // Green
      default:
        return '#2E5BFF';
    }
  }

  /// Check if this is a free plan
  bool get isFree => price == 0 || type == 'trial';

  /// Check if this is a paid plan
  bool get isPaid => !isFree;

  /// Get savings compared to monthly (for yearly plans)
  double getSavingsComparedTo(Plan monthlyPlan) {
    if (type != 'yearly' || monthlyPlan.type != 'monthly') {
      return 0;
    }
    final yearlyAsMonthly = price / 12;
    final monthlySavings = monthlyPlan.price - yearlyAsMonthly;
    return monthlySavings * 12;
  }

  /// Get savings percentage compared to monthly
  double getSavingsPercentage(Plan monthlyPlan) {
    if (type != 'yearly' || monthlyPlan.type != 'monthly') {
      return 0;
    }
    final yearlyAsMonthly = price / 12;
    final savings = monthlyPlan.price - yearlyAsMonthly;
    return (savings / monthlyPlan.price) * 100;
  }
} 