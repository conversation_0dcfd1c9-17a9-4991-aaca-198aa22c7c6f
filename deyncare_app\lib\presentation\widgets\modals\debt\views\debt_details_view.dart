import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/domain/models/debt.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_constants.dart';

/// Debt Details View Widget - Updated for new debt architecture
class DebtDetailsView extends StatelessWidget {
  final Debt debt;

  const DebtDetailsView({super.key, required this.debt});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: ModalConstants.defaultPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildDebtHeader(context),
          ModalConstants.defaultSpacing,
          _buildAmountInformation(context),
          ModalConstants.defaultSpacing,
          _buildCustomerInformation(context),
          ModalConstants.defaultSpacing,
          _buildDatesAndStatus(context),
          if (debt.description?.isNotEmpty == true) ...[
            ModalConstants.defaultSpacing,
            _buildDescriptionCard(context),
          ],
          ModalConstants.largeSpacing,
        ],
      ),
    );
  }

  Widget _buildDebtHeader(BuildContext context) {
    return CommonCard(
      child: Column(
        children: [
          Icon(
            _getDebtIcon(debt.status),
            size: 48,
            color: _getStatusColor(debt.status),
          ),
          ModalConstants.defaultSpacing,
          Text(
            'Debt #${debt.debtId}',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          ModalConstants.defaultSpacing,
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getStatusColor(debt.status).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: _getStatusColor(debt.status).withValues(alpha: 0.3)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _getStatusIcon(debt.status),
                  size: 16,
                  color: _getStatusColor(debt.status),
                ),
                const SizedBox(width: 8),
                Text(
                  debt.status.displayName,
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                color: _getStatusColor(debt.status),
                fontWeight: FontWeight.bold,
              ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountInformation(BuildContext context) {
    return CommonCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.account_balance_wallet_rounded, 
                color: Theme.of(context).colorScheme.primary, size: 20),
              const SizedBox(width: 8),
          Text(
            'Financial Details',
            style: ModalConstants.sectionTitleStyle(context),
              ),
            ],
          ),
          ModalConstants.defaultSpacing,
          _buildAmountRow(context, 'Total Amount', debt.amount, isPrimary: true),
          const SizedBox(height: 12),
          _buildAmountRow(context, 'Paid Amount', debt.totalPaid, 
                          color: AppThemes.successColor),
          const SizedBox(height: 12),
          _buildAmountRow(context, 'Remaining', debt.remainingAmount, 
                          color: debt.remainingAmount > 0 ? AppThemes.warningColor : AppThemes.successColor),
          if (debt.remainingAmount > 0) ...[
            const SizedBox(height: 16),
            _buildProgressBar(context),
          ],
        ],
      ),
    );
  }

  Widget _buildAmountRow(BuildContext context, String label, double amount, 
                        {bool isPrimary = false, Color? color}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            '$label:',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: AppThemes.textSecondaryColor,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            '\$${amount.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isPrimary ? FontWeight.bold : FontWeight.w600,
              fontSize: isPrimary ? 16 : 14,
              color: color,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProgressBar(BuildContext context) {
    final progress = debt.amount > 0 ? debt.totalPaid / debt.amount : 0.0;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Payment Progress',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
                color: AppThemes.textSecondaryColor,
              ),
            ),
            Text(
              '${(progress * 100).toStringAsFixed(0)}%',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppThemes.primaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: AppThemes.backgroundColor,
          valueColor: AlwaysStoppedAnimation<Color>(AppThemes.successColor),
          minHeight: 6,
        ),
      ],
    );
  }

  Widget _buildCustomerInformation(BuildContext context) {
    return CommonCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.person_outline, 
                   color: AppThemes.primaryColor, size: 20),
              const SizedBox(width: 8),
          Text(
            'Customer Information',
            style: ModalConstants.sectionTitleStyle(context),
              ),
            ],
          ),
          ModalConstants.defaultSpacing,
          _buildDetailRow(context, 'Customer Name', debt.customerName),
          if (debt.customerId.isNotEmpty)
            _buildDetailRow(context, 'Customer ID', debt.customerId),
        ],
      ),
    );
  }

  Widget _buildDatesAndStatus(BuildContext context) {
    return CommonCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.schedule_outlined, 
                   color: AppThemes.primaryColor, size: 20),
              const SizedBox(width: 8),
              Text(
                'Timeline Information',
                style: ModalConstants.sectionTitleStyle(context),
              ),
            ],
          ),
          ModalConstants.defaultSpacing,
          _buildDetailRow(context, 'Due Date', _formatDate(debt.dueDate)),
          if (debt.isOverdue) ...[
            _buildDetailRow(context, 'Days Overdue', '${debt.daysPastDue} days', 
                           isWarning: true),
          ],
          _buildDetailRow(context, 'Created', _formatDate(debt.createdAt)),
          _buildDetailRow(context, 'Last Updated', _formatDate(debt.updatedAt)),
        ],
      ),
    );
  }

  Widget _buildDescriptionCard(BuildContext context) {
    return CommonCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.description_outlined, 
                   color: AppThemes.primaryColor, size: 20),
              const SizedBox(width: 8),
          Text(
            'Description',
            style: ModalConstants.sectionTitleStyle(context),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppThemes.backgroundColor.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppThemes.textSecondaryColor.withValues(alpha: 0.3)),
            ),
            child: Text(
            debt.description!,
            style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(BuildContext context, String label, String value, 
                        {bool isWarning = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
          SizedBox(
            width: 100,
            child: Text(
          '$label:',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
                color: AppThemes.textSecondaryColor,
              ),
            ),
          ),
          const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: isWarning ? AppThemes.errorColor : null,
              fontWeight: isWarning ? FontWeight.bold : null,
            ),
          ),
        ),
      ],
      ),
    );
  }

  // Helper methods for status display
  Color _getStatusColor(DebtStatus status) {
    switch (status) {
      case DebtStatus.active:
        return AppThemes.primaryColor;
      case DebtStatus.completed:
        return AppThemes.successColor;
      case DebtStatus.overdue:
        return AppThemes.warningColor;
      case DebtStatus.defaulted:
        return AppThemes.errorColor;
      case DebtStatus.cancelled:
        return AppThemes.textSecondaryColor;
    }
  }

  IconData _getDebtIcon(DebtStatus status) {
    switch (status) {
      case DebtStatus.active:
        return Icons.receipt_long_rounded;
      case DebtStatus.completed:
        return Icons.check_circle_outline;
      case DebtStatus.overdue:
        return Icons.warning_amber_rounded;
      case DebtStatus.defaulted:
        return Icons.error_outline;
      case DebtStatus.cancelled:
        return Icons.cancel_outlined;
    }
  }

  IconData _getStatusIcon(DebtStatus status) {
    switch (status) {
      case DebtStatus.active:
        return Icons.schedule;
      case DebtStatus.completed:
        return Icons.check_circle;
      case DebtStatus.overdue:
        return Icons.warning;
      case DebtStatus.defaulted:
        return Icons.error;
      case DebtStatus.cancelled:
        return Icons.cancel;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
} 