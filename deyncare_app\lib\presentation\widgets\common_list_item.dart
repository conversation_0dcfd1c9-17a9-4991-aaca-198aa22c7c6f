import 'package:flutter/material.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'common_card.dart';

/// Common list item widget for displaying data in lists
class CommonListItem extends StatelessWidget {
  final String title;
  final String? subtitle;
  final String? trailing;
  final Widget? leadingIcon;
  final Widget? trailingWidget;
  final VoidCallback? onTap;
  final List<Widget>? actions;
  final Color? statusColor;
  final String? status;

  const CommonListItem({
    super.key,
    required this.title,
    this.subtitle,
    this.trailing,
    this.leadingIcon,
    this.trailingWidget,
    this.onTap,
    this.actions,
    this.statusColor,
    this.status,
  });

  @override
  Widget build(BuildContext context) {
    return CommonCard(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      onTap: onTap,
      isInteractive: onTap != null,
      child: Column(
        children: [
          Row(
            children: [
              // Leading icon
              if (leadingIcon != null) ...[
                leadingIcon!,
                const SizedBox(width: 12),
              ],
              
              // Main content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            title,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                        ),
                        if (status != null) ...[
                          const SizedBox(width: 8),
                          _buildStatusChip(context),
                        ],
                      ],
                    ),
                    if (subtitle != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        subtitle!,
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              // Trailing content
              if (trailing != null || trailingWidget != null) ...[
                const SizedBox(width: 12),
                trailingWidget ??
                    Text(
                      trailing!,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
              ],
              
              // Arrow for navigation
              if (onTap != null) ...[
                const SizedBox(width: 8),
                Icon(
                  Icons.chevron_right,
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                  size: 20,
                ),
              ],
            ],
          ),
          
          // Actions row
          if (actions != null && actions!.isNotEmpty) ...[
            const SizedBox(height: 12),
            const Divider(height: 1),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: actions!,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: (statusColor ?? Theme.of(context).colorScheme.primary).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: (statusColor ?? Theme.of(context).colorScheme.primary).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Text(
        status!,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: statusColor ?? Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }
}

/// Specialized list item for customers
class CustomerListItem extends StatelessWidget {
  final String customerId;
  final String customerName;
  final String customerType;
  final String phone;
  final double? totalDebt;
  final VoidCallback? onTap;
  final List<Widget>? actions;

  const CustomerListItem({
    super.key,
    required this.customerId,
    required this.customerName,
    required this.customerType,
    required this.phone,
    this.totalDebt,
    this.onTap,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return CommonListItem(
      title: customerName,
      subtitle: '$customerId • $phone',
      trailing: totalDebt != null ? '\$${totalDebt!.toStringAsFixed(2)}' : null,
      status: customerType,
      statusColor: _getCustomerTypeColor(context, customerType),
      leadingIcon: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primary.withOpacity(0.1),
              Theme.of(context).colorScheme.primary.withOpacity(0.2),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          Icons.account_circle_rounded,
          color: Theme.of(context).colorScheme.primary,
          size: 24,
        ),
      ),
      onTap: onTap,
      actions: actions,
    );
  }

  Color _getCustomerTypeColor(BuildContext context, String type) {
    switch (type.toLowerCase()) {
      case 'individual':
        return Theme.of(context).colorScheme.primary;
      case 'business':
        return AppThemes.successColor;
      default:
        return Theme.of(context).colorScheme.onSurface.withOpacity(0.7);
    }
  }
}

/// Specialized list item for debts
class DebtListItem extends StatelessWidget {
  final String debtId;
  final String customerName;
  final double amount;
  final double remainingAmount;
  final DateTime dueDate;
  final String status;
  final VoidCallback? onTap;
  final List<Widget>? actions;

  const DebtListItem({
    super.key,
    required this.debtId,
    required this.customerName,
    required this.amount,
    required this.remainingAmount,
    required this.dueDate,
    required this.status,
    this.onTap,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    final isOverdue = DateTime.now().isAfter(dueDate) && remainingAmount > 0;
    final iconColor = _getDebtStatusColor(context, status);
    
    return CommonListItem(
      title: customerName,
      subtitle: '$debtId • Due: ${_formatDate(dueDate)}',
      trailing: '\$${remainingAmount.toStringAsFixed(2)}',
      status: status,
      statusColor: iconColor,
      leadingIcon: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              iconColor.withOpacity(0.1),
              iconColor.withOpacity(0.2),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: iconColor.withOpacity(0.15),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          _getDebtIcon(status, isOverdue),
          color: iconColor,
          size: 24,
        ),
      ),
      onTap: onTap,
      actions: actions,
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Color _getDebtStatusColor(BuildContext context, String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'pending':
        return Theme.of(context).colorScheme.primary;
      case 'paid':
      case 'completed':
        return AppThemes.successColor;
      case 'overdue':
      case 'late':
        return AppThemes.errorColor;
      default:
        return Theme.of(context).colorScheme.onSurface.withOpacity(0.7);
    }
  }

  IconData _getDebtIcon(String status, bool isOverdue) {
    if (isOverdue) return Icons.warning_rounded;
    
    switch (status.toLowerCase()) {
      case 'active':
        return Icons.receipt_long_rounded;
      case 'completed':
        return Icons.check_circle_rounded;
      case 'overdue':
        return Icons.access_time_filled_rounded;
      default:
        return Icons.description_rounded;
    }
  }
} 