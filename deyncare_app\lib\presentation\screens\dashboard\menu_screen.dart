import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/providers/theme_provider.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'package:deyncare_app/core/utils/permission_utils.dart';
import 'package:deyncare_app/core/routes/app_router.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_event.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/common_list_item.dart';

/// Main menu screen with user profile and app options
class MenuScreen extends StatefulWidget {
  final dynamic user;

  const MenuScreen({
    super.key,
    required this.user,
  });

  @override
  State<MenuScreen> createState() => _MenuScreenState();
}

class _MenuScreenState extends State<MenuScreen>
    with AutomaticKeepAliveClientMixin {
  ThemeProvider? _themeProvider;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Safely get theme provider after dependencies are established
    _themeProvider ??= Provider.of<ThemeProvider>(context, listen: false);
  }

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const SizedBox(height: 20), // Reduced from 40

        // User Profile Header
        _buildUserProfileHeader(context),

        const SizedBox(height: 24), // Reduced from 32

        // My Account Section - Removed title
        _buildMyAccountOptions(context),

        const SizedBox(height: 20), // Reduced from 32

        // Shop Management Section - Removed title
        _buildShopManagementOptions(context),

        const SizedBox(height: 20), // Reduced from 32

        // App Settings Section - Removed title
        _buildAppSettingsOptions(context),

        const SizedBox(height: 20), // Reduced from 32

        // Help & Support Section - Removed title
        _buildHelpSupportOptions(context),

        const SizedBox(height: 20), // Reduced from 32

        // Logout Option
        _buildLogoutOption(context),

        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildUserProfileHeader(BuildContext context) {
    return CommonCard(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          // Profile Avatar
          Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [
                  AppThemes.primaryColor,
                  AppThemes.primaryLight,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Center(
              child: Text(
                widget.user?.fullName?.isNotEmpty == true
                    ? widget.user!.fullName!.substring(0, 1).toUpperCase()
                    : 'U',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          const SizedBox(width: 16),

          // User Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.user?.fullName ?? 'User Name',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: ThemeUtils.getTextColor(context,
                            type: TextColorType.primary),
                      ),
                ),
                const SizedBox(height: 4),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppThemes.primaryColor.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    widget.user?.role ?? 'Manager',
                    style: TextStyle(
                      color: AppThemes.primaryColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.user?.email ?? '<EMAIL>',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeUtils.getTextColor(context,
                            type: TextColorType.secondary),
                      ),
                ),
              ],
            ),
          ),

          // Edit profile button
          IconButton(
            onPressed: () => _navigateToEditProfile(context),
            icon: Icon(
              Icons.edit_outlined,
              color:
                  ThemeUtils.getIconColor(context, type: IconColorType.accent),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color:
                  ThemeUtils.getTextColor(context, type: TextColorType.primary),
            ),
      ),
    );
  }

  Widget _buildMyAccountOptions(BuildContext context) {
    final statusColors = ThemeUtils.getStatusColors(context);

    return Column(
      children: [
        CommonListItem(
          title: 'My Profile',
          leadingIcon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: statusColors.infoSurface,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.person_outline,
              color: statusColors.info,
              size: 20,
            ),
          ),
          onTap: () => _navigateToEditProfile(context),
        ),
        const SizedBox(height: 4),
        // Only show subscription for admins
        if (PermissionUtils.canAccessSubscription(widget.user))
          CommonListItem(
            title: 'My Subscription',
            leadingIcon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppThemes.accentColor.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.card_membership_outlined,
                color: AppThemes.accentColor,
                size: 20,
              ),
            ),
            onTap: () => _navigateToMySubscription(context),
          ),
        if (PermissionUtils.canAccessSubscription(widget.user))
          const SizedBox(height: 4),
        // Removed 'Change Password' and 'Account Settings' as requested
        // Password change is available in profile screen
      ],
    );
  }

  Widget _buildShopManagementOptions(BuildContext context) {
    return Column(
      children: [
        // Only show shop settings for admins
        if (PermissionUtils.canAccessShopSettings(widget.user))
          CommonListItem(
            title: 'Shop Settings',
            leadingIcon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppThemes.secondaryColor.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.store_outlined,
                color: AppThemes.secondaryColor,
                size: 20,
              ),
            ),
            onTap: () => _navigateToShopSettings(context),
          ),
        if (PermissionUtils.canAccessShopSettings(widget.user))
          const SizedBox(height: 4),

        // Only show user role management for admins
        if (PermissionUtils.canAccessUserManagement(widget.user))
          CommonListItem(
            title: 'User Role',
            leadingIcon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppThemes.accentColor.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.people_outline,
                color: AppThemes.accentColor,
                size: 20,
              ),
            ),
            onTap: () => _navigateToUserRole(context),
          ),
        if (PermissionUtils.canAccessUserManagement(widget.user))
          const SizedBox(height: 4),

        // Only show reports if user has access
        if (PermissionUtils.canAccessReports(widget.user)) ...[
          CommonListItem(
            title: 'Reports',
            leadingIcon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppThemes.infoColor.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.analytics_outlined,
                color: AppThemes.infoColor,
                size: 20,
              ),
            ),
            onTap: () => _navigateToReports(context),
          ),
        ],
      ],
    );
  }

  Widget _buildAppSettingsOptions(BuildContext context) {
    final statusColors = ThemeUtils.getStatusColors(context);

    return Column(
      children: [
        _buildThemeToggleOption(context),
        const SizedBox(height: 4), // Reduced from 8
        CommonListItem(
          title: 'Notifications',
          leadingIcon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: statusColors.infoSurface,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.notifications_outlined,
              color: statusColors.info,
              size: 20,
            ),
          ),
          onTap: () => _navigateToNotificationSettings(context),
        ),
        const SizedBox(height: 4), // Reduced from 8
        CommonListItem(
          title: 'Language',
          leadingIcon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: statusColors.successSurface,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.language_outlined,
              color: statusColors.success,
              size: 20,
            ),
          ),
          onTap: () => _navigateToLanguageSettings(context),
        ),
      ],
    );
  }

  Widget _buildThemeToggleOption(BuildContext context) {
    return CommonCard(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppThemes.primaryColor.withValues(alpha: 0.15),
            borderRadius: BorderRadius.circular(8),
          ),
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: Icon(
              _themeProvider?.isDarkMode == true
                  ? Icons.light_mode_outlined
                  : Icons.dark_mode_outlined,
              key: ValueKey(_themeProvider?.isDarkMode),
              color: AppThemes.primaryColor,
              size: 20,
            ),
          ),
        ),
        title: Text(
          'Dark Mode',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        subtitle: Text(
          _themeProvider?.isDarkMode == true
              ? 'Switch to light mode'
              : 'Switch to dark mode',
          style: TextStyle(
            color:
                Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        trailing: AnimatedSwitcher(
          duration: const Duration(milliseconds: 200),
          child: Switch(
            key: ValueKey(_themeProvider?.isDarkMode),
            value: _themeProvider?.isDarkMode ?? false,
            onChanged: (bool value) async {
              // Provide immediate visual feedback
              HapticFeedback.lightImpact();

              // Store context before async operation
              final currentContext = context;

              // Update theme without causing navigation reset
              await _themeProvider?.setDarkMode(value);

              // Update local state to reflect change immediately
              if (mounted) {
                setState(() {});

                // Show optimized snackbar only if still mounted
                _showOptimizedThemeChangeSnackBar(currentContext, value);
              }
            },
            activeColor: AppThemes.primaryColor,
            activeTrackColor: AppThemes.primaryColor.withValues(alpha: 0.3),
            inactiveThumbColor: Colors.grey[400],
            inactiveTrackColor: Colors.grey[300],
          ),
        ),
      ),
    );
  }

  Widget _buildHelpSupportOptions(BuildContext context) {
    final statusColors = ThemeUtils.getStatusColors(context);

    return Column(
      children: [
        CommonListItem(
          title: 'Help Center',
          leadingIcon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: statusColors.infoSurface,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.help_outline,
              color: statusColors.info,
              size: 20,
            ),
          ),
          onTap: () => _navigateToHelp(context),
        ),
        const SizedBox(height: 4), // Reduced from 8
        CommonListItem(
          title: 'Contact Support',
          leadingIcon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: statusColors.successSurface,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.contact_support_outlined,
              color: statusColors.success,
              size: 20,
            ),
          ),
          onTap: () => _navigateToContactSupport(context),
        ),
        const SizedBox(height: 4), // Reduced from 8
        CommonListItem(
          title: 'About DeynCare',
          leadingIcon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppThemes.primaryColor.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.info_outline,
              color: AppThemes.primaryColor,
              size: 20,
            ),
          ),
          onTap: () => _showAboutDialog(context),
        ),
      ],
    );
  }

  Widget _buildLogoutOption(BuildContext context) {
    final statusColors = ThemeUtils.getStatusColors(context);

    return CommonListItem(
      title: 'Logout',
      leadingIcon: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: statusColors.errorSurface,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.logout_outlined,
          color: statusColors.error,
          size: 20,
        ),
      ),
      onTap: () => _showLogoutDialog(context),
    );
  }

  // Navigation methods
  void _navigateToEditProfile(BuildContext context) {
    AppRouter.navigateToProfileSettings(context);
  }

  void _navigateToShopSettings(BuildContext context) {
    AppRouter.navigateToShopSettings(context);
  }

  void _navigateToNotificationSettings(BuildContext context) {
    AppRouter.navigateToNotificationSettings(context);
  }

  void _navigateToLanguageSettings(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Language Settings - Coming Soon'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _navigateToHelp(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Help Center - Coming Soon'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _navigateToContactSupport(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Contact Support - Coming Soon'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _navigateToMySubscription(BuildContext context) {
    AppRouter.navigateToSubscription(context);
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('About DeynCare'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('DeynCare - Debt Management System'),
              SizedBox(height: 8),
              Text('Version: 1.0.0'),
              SizedBox(height: 8),
              Text('© 2024 DeynCare. All rights reserved.'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Logout'),
          content: const Text('Are you sure you want to logout?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                context.read<AuthBloc>().add(LoggedOut());
                Navigator.pop(context);
              },
              child: const Text('Logout'),
            ),
          ],
        );
      },
    );
  }

  void _showOptimizedThemeChangeSnackBar(BuildContext context, bool isDark) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: Icon(
                isDark ? Icons.dark_mode : Icons.light_mode,
                key: ValueKey(isDark),
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              isDark ? 'Dark mode enabled' : 'Light mode enabled',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        backgroundColor: AppThemes.primaryColor,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _navigateToReports(BuildContext context) {
    AppRouter.navigateToReports(context);
  }

  void _navigateToUserRole(BuildContext context) {
    Navigator.pushNamed(context, '/user-role-list');
  }

  @override
  bool get wantKeepAlive => true;
}
