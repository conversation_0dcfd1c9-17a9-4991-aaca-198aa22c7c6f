import 'package:deyncare_app/domain/models/payment.dart';

/// Repository interface for debt payment management
/// Focused on manual payment recording for debt context only
abstract class PaymentRepository {
  /// Create a new debt payment record (manual recording)
  /// POST /api/payments
  Future<Payment> createPayment({
    required double amount,
    required PaymentMethod paymentMethod,
    required String debtId, // Only debt context
    String? description,
  });

  /// Get payments for a specific debt
  /// GET /api/payments with debt context filter
  Future<List<Payment>> getDebtPayments(String debtId);

  /// Get payment by ID
  /// GET /api/payments/:paymentId
  Future<Payment> getPaymentById(String paymentId);

  /// Get payment history for specific debt
  /// GET /api/payments/history/:debtId
  Future<List<Payment>> getPaymentHistory(String debtId);
} 