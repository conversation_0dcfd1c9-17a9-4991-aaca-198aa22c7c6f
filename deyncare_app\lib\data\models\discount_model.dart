class DiscountCode {
  final String discountId;
  final String code;
  final String description;
  final String type; // 'fixed' or 'percentage'
  final double value;
  final double minimumPurchase;
  final double? maxDiscountAmount;
  final DateTime startDate;
  final DateTime expiryDate;
  final int? usageLimit;
  final int usageCount;
  final int perUserLimit;
  final List<String> applicableFor;
  final String scope; // 'global' or 'shop'
  final String? shopId;
  final bool isActive;
  final bool isDeleted;
  final String createdBy;
  final String? updatedBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Calculated fields for UI
  final double discountAmount;
  final double finalAmount;

  const DiscountCode({
    required this.discountId,
    required this.code,
    required this.description,
    required this.type,
    required this.value,
    required this.minimumPurchase,
    this.maxDiscountAmount,
    required this.startDate,
    required this.expiryDate,
    this.usageLimit,
    required this.usageCount,
    required this.perUserLimit,
    required this.applicableFor,
    required this.scope,
    this.shopId,
    required this.isActive,
    required this.isDeleted,
    required this.createdBy,
    this.updatedBy,
    required this.createdAt,
    required this.updatedAt,
    this.discountAmount = 0.0,
    this.finalAmount = 0.0,
  });

  factory DiscountCode.fromJson(Map<String, dynamic> json) {
    return DiscountCode(
      discountId: json['discountId'] ?? '',
      code: json['code'] ?? '',
      description: json['description'] ?? '',
      type: json['type'] ?? 'percentage',
      value: (json['value'] ?? 0).toDouble(),
      minimumPurchase: (json['minimumPurchase'] ?? 0).toDouble(),
      maxDiscountAmount: json['maxDiscountAmount']?.toDouble(),
      startDate: DateTime.parse(json['startDate'] ?? DateTime.now().toIso8601String()),
      expiryDate: DateTime.parse(json['expiryDate'] ?? DateTime.now().toIso8601String()),
      usageLimit: json['usageLimit'],
      usageCount: json['usageCount'] ?? 0,
      perUserLimit: json['perUserLimit'] ?? 1,
      applicableFor: List<String>.from(json['applicableFor'] ?? ['subscription']),
      scope: json['scope'] ?? 'shop',
      shopId: json['shopId'],
      isActive: json['isActive'] ?? true,
      isDeleted: json['isDeleted'] ?? false,
      createdBy: json['createdBy'] ?? '',
      updatedBy: json['updatedBy'],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      discountAmount: (json['discountAmount'] ?? 0).toDouble(),
      finalAmount: (json['finalAmount'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'discountId': discountId,
      'code': code,
      'description': description,
      'type': type,
      'value': value,
      'minimumPurchase': minimumPurchase,
      'maxDiscountAmount': maxDiscountAmount,
      'startDate': startDate.toIso8601String(),
      'expiryDate': expiryDate.toIso8601String(),
      'usageLimit': usageLimit,
      'usageCount': usageCount,
      'perUserLimit': perUserLimit,
      'applicableFor': applicableFor,
      'scope': scope,
      'shopId': shopId,
      'isActive': isActive,
      'isDeleted': isDeleted,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'discountAmount': discountAmount,
      'finalAmount': finalAmount,
    };
  }

  /// Calculate discount amount for a given purchase amount
  double calculateDiscount(double purchaseAmount) {
    // If purchase amount is less than minimum, no discount
    if (purchaseAmount < minimumPurchase) {
      return 0.0;
    }
    
    double discountAmount = 0.0;
    
    if (type == 'fixed') {
      // Fixed amount discount
      discountAmount = value;
    } else if (type == 'percentage') {
      // Percentage discount
      discountAmount = (purchaseAmount * value) / 100;
      
      // Apply max discount if specified
      if (maxDiscountAmount != null && discountAmount > maxDiscountAmount!) {
        discountAmount = maxDiscountAmount!;
      }
    }
    
    // Ensure discount doesn't exceed purchase amount
    return discountAmount > purchaseAmount ? purchaseAmount : discountAmount;
  }

  /// Check if the discount code is currently valid
  bool get isValid {
    final now = DateTime.now();
    
    // Check if code is active and not deleted
    if (!isActive || isDeleted) {
      return false;
    }
    
    // Check if code has expired
    if (now.isAfter(expiryDate)) {
      return false;
    }
    
    // Check if code is not yet active
    if (now.isBefore(startDate)) {
      return false;
    }
    
    // Check if usage limit has been reached
    if (usageLimit != null && usageCount >= usageLimit!) {
      return false;
    }
    
    return true;
  }

  /// Get formatted discount value for display
  String get formattedValue {
    if (type == 'percentage') {
      return '${value.toStringAsFixed(0)}%';
    } else {
      return '\$${value.toStringAsFixed(2)}';
    }
  }

  /// Get usage status text
  String get usageStatus {
    if (usageLimit == null) {
      return '$usageCount uses';
    }
    return '$usageCount / $usageLimit uses';
  }

  /// Check if discount is applicable for given context
  bool isApplicableFor(String context) {
    return applicableFor.contains('all') || applicableFor.contains(context);
  }

  DiscountCode copyWith({
    String? discountId,
    String? code,
    String? description,
    String? type,
    double? value,
    double? minimumPurchase,
    double? maxDiscountAmount,
    DateTime? startDate,
    DateTime? expiryDate,
    int? usageLimit,
    int? usageCount,
    int? perUserLimit,
    List<String>? applicableFor,
    String? scope,
    String? shopId,
    bool? isActive,
    bool? isDeleted,
    String? createdBy,
    String? updatedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? discountAmount,
    double? finalAmount,
  }) {
    return DiscountCode(
      discountId: discountId ?? this.discountId,
      code: code ?? this.code,
      description: description ?? this.description,
      type: type ?? this.type,
      value: value ?? this.value,
      minimumPurchase: minimumPurchase ?? this.minimumPurchase,
      maxDiscountAmount: maxDiscountAmount ?? this.maxDiscountAmount,
      startDate: startDate ?? this.startDate,
      expiryDate: expiryDate ?? this.expiryDate,
      usageLimit: usageLimit ?? this.usageLimit,
      usageCount: usageCount ?? this.usageCount,
      perUserLimit: perUserLimit ?? this.perUserLimit,
      applicableFor: applicableFor ?? this.applicableFor,
      scope: scope ?? this.scope,
      shopId: shopId ?? this.shopId,
      isActive: isActive ?? this.isActive,
      isDeleted: isDeleted ?? this.isDeleted,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      discountAmount: discountAmount ?? this.discountAmount,
      finalAmount: finalAmount ?? this.finalAmount,
    );
  }
}

/// Result of discount code validation
class DiscountValidationResult {
  final bool isValid;
  final DiscountCode? discountCode;
  final String message;
  final String? errorCode;

  const DiscountValidationResult({
    required this.isValid,
    this.discountCode,
    required this.message,
    this.errorCode,
  });

  factory DiscountValidationResult.fromJson(Map<String, dynamic> json) {
    return DiscountValidationResult(
      isValid: json['valid'] ?? false,
      discountCode: json['discountCode'] != null 
          ? DiscountCode.fromJson(json['discountCode'])
          : null,
      message: json['message'] ?? '',
      errorCode: json['errorCode'],
    );
  }
}

/// Result of discount code application
class DiscountApplicationResult {
  final bool isApplied;
  final DiscountCode? discountCode;
  final String message;
  final String? errorCode;

  const DiscountApplicationResult({
    required this.isApplied,
    this.discountCode,
    required this.message,
    this.errorCode,
  });

  factory DiscountApplicationResult.fromJson(Map<String, dynamic> json) {
    return DiscountApplicationResult(
      isApplied: json['applied'] ?? false,
      discountCode: json['discountCode'] != null 
          ? DiscountCode.fromJson(json['discountCode'])
          : null,
      message: json['message'] ?? '',
      errorCode: json['errorCode'],
    );
  }
} 