import 'package:equatable/equatable.dart';

/// Customer Events - aligned with backend use cases
abstract class CustomerEvent extends Equatable {
  const CustomerEvent();

  @override
  List<Object?> get props => [];
}

/// Load customers list with pagination and filters
class LoadCustomers extends CustomerEvent {
  final int page;
  final int limit;
  final String? search;
  final String? customerType;
  final String? riskLevel;
  final String? category;
  final String? sortBy;
  final bool ascending;

  const LoadCustomers({
    this.page = 1,
    this.limit = 20,
    this.search,
    this.customerType,
    this.riskLevel,
    this.category,
    this.sortBy,
    this.ascending = true,
  });

  @override
  List<Object?> get props => [
        page,
        limit,
        search,
        customerType,
        riskLevel,
        category,
        sortBy,
        ascending,
      ];
}

/// Create new customer
class CreateCustomer extends CustomerEvent {
  final String customerName;
  final String customerType;
  final String phone;
  final String? email;
  final String? address;
  final double? creditLimit;
  final String? category;
  final String? notes;

  const CreateCustomer({
    required this.customerName,
    required this.customerType,
    required this.phone,
    this.email,
    this.address,
    this.creditLimit,
    this.category,
    this.notes,
  });

  @override
  List<Object?> get props => [
        customerName,
        customerType,
        phone,
        email,
        address,
        creditLimit,
        category,
        notes,
      ];
}

/// Load customer details by ID
class LoadCustomerDetails extends CustomerEvent {
  final String customerId;

  const LoadCustomerDetails(this.customerId);

  @override
  List<Object> get props => [customerId];
}

/// Update existing customer
class UpdateCustomer extends CustomerEvent {
  final String customerId;
  final String? customerName;
  final String? email;
  final String? phone;
  final String? address;
  final double? creditLimit;
  final String? category;
  final String? notes;

  const UpdateCustomer({
    required this.customerId,
    this.customerName,
    this.email,
    this.phone,
    this.address,
    this.creditLimit,
    this.category,
    this.notes,
  });

  @override
  List<Object?> get props => [
        customerId,
        customerName,
        email,
        phone,
        address,
        creditLimit,
        category,
        notes,
      ];
}

/// Delete customer event - supports force parameter for customers with debt history
class DeleteCustomer extends CustomerEvent {
  final String customerId;
  final bool force;

  const DeleteCustomer(this.customerId, {this.force = false});

  @override
  List<Object?> get props => [customerId, force];
}

/// Load customer debts
class LoadCustomerDebts extends CustomerEvent {
  final String customerId;
  final bool includeCompleted;

  const LoadCustomerDebts({
    required this.customerId,
    this.includeCompleted = false,
  });

  @override
  List<Object> get props => [customerId, includeCompleted];
}

/// Refresh customers list
class RefreshCustomers extends CustomerEvent {
  const RefreshCustomers();
}

/// Clear customer details
class ClearCustomerDetails extends CustomerEvent {
  const ClearCustomerDetails();
} 