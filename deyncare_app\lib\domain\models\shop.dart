/// Shop domain model representing shop entity data
class Shop {
  final String shopId;
  final String shopName;
  final String ownerName;
  final String email;
  final String phone;
  final String address;
  final ShopLocation? location;
  final BusinessDetails? businessDetails;
  final String logoUrl;
  final String bannerUrl;
  final SocialMedia? socialMedia;
  final String status;
  final ShopAccess access;
  final bool verified;
  final VerificationDetails? verificationDetails;
  final String registeredBy;
  final List<PaymentHistory> paymentHistory;
  final String? currentSubscriptionId;
  final ShopStatistics statistics;
  final ContactPerson? contactPerson;
  final bool isDeleted;
  final DateTime? deletedAt;
  final NotificationPreferences notifications;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Shop({
    required this.shopId,
    required this.shopName,
    required this.ownerName,
    required this.email,
    required this.phone,
    required this.address,
    this.location,
    this.businessDetails,
    required this.logoUrl,
    required this.bannerUrl,
    this.socialMedia,
    required this.status,
    required this.access,
    required this.verified,
    this.verificationDetails,
    required this.registeredBy,
    required this.paymentHistory,
    this.currentSubscriptionId,
    required this.statistics,
    this.contactPerson,
    required this.isDeleted,
    this.deletedAt,
    required this.notifications,
    required this.createdAt,
    required this.updatedAt,
  });

  Shop copyWith({
    String? shopId,
    String? shopName,
    String? ownerName,
    String? email,
    String? phone,
    String? address,
    ShopLocation? location,
    BusinessDetails? businessDetails,
    String? logoUrl,
    String? bannerUrl,
    SocialMedia? socialMedia,
    String? status,
    ShopAccess? access,
    bool? verified,
    VerificationDetails? verificationDetails,
    String? registeredBy,
    List<PaymentHistory>? paymentHistory,
    String? currentSubscriptionId,
    ShopStatistics? statistics,
    ContactPerson? contactPerson,
    bool? isDeleted,
    DateTime? deletedAt,
    NotificationPreferences? notifications,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Shop(
      shopId: shopId ?? this.shopId,
      shopName: shopName ?? this.shopName,
      ownerName: ownerName ?? this.ownerName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      location: location ?? this.location,
      businessDetails: businessDetails ?? this.businessDetails,
      logoUrl: logoUrl ?? this.logoUrl,
      bannerUrl: bannerUrl ?? this.bannerUrl,
      socialMedia: socialMedia ?? this.socialMedia,
      status: status ?? this.status,
      access: access ?? this.access,
      verified: verified ?? this.verified,
      verificationDetails: verificationDetails ?? this.verificationDetails,
      registeredBy: registeredBy ?? this.registeredBy,
      paymentHistory: paymentHistory ?? this.paymentHistory,
      currentSubscriptionId: currentSubscriptionId ?? this.currentSubscriptionId,
      statistics: statistics ?? this.statistics,
      contactPerson: contactPerson ?? this.contactPerson,
      isDeleted: isDeleted ?? this.isDeleted,
      deletedAt: deletedAt ?? this.deletedAt,
      notifications: notifications ?? this.notifications,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// Shop location details
class ShopLocation {
  final String? street;
  final String? city;
  final String? district;
  final String? state;
  final String? postalCode;
  final String? country;
  final Coordinates? coordinates;
  final String? placeId;
  final String? formattedAddress;

  const ShopLocation({
    this.street,
    this.city,
    this.district,
    this.state,
    this.postalCode,
    this.country,
    this.coordinates,
    this.placeId,
    this.formattedAddress,
  });
}

/// Geographic coordinates
class Coordinates {
  final double? latitude;
  final double? longitude;

  const Coordinates({
    this.latitude,
    this.longitude,
  });
}

/// Business details and information
class BusinessDetails {
  final String type;
  final String? category;
  final DateTime? foundedDate;
  final String? registrationNumber;
  final String? taxId;
  final int employeeCount;
  final OperatingHours? operatingHours;

  const BusinessDetails({
    required this.type,
    this.category,
    this.foundedDate,
    this.registrationNumber,
    this.taxId,
    required this.employeeCount,
    this.operatingHours,
  });
}

/// Operating hours for each day
class OperatingHours {
  final DayHours? monday;
  final DayHours? tuesday;
  final DayHours? wednesday;
  final DayHours? thursday;
  final DayHours? friday;
  final DayHours? saturday;
  final DayHours? sunday;

  const OperatingHours({
    this.monday,
    this.tuesday,
    this.wednesday,
    this.thursday,
    this.friday,
    this.saturday,
    this.sunday,
  });
}

/// Hours for a specific day
class DayHours {
  final String open;
  final String close;

  const DayHours({
    required this.open,
    required this.close,
  });
}

/// Social media profiles
class SocialMedia {
  final String? facebook;
  final String? instagram;
  final String? twitter;
  final String? website;

  const SocialMedia({
    this.facebook,
    this.instagram,
    this.twitter,
    this.website,
  });
}

/// Shop access and activation status
class ShopAccess {
  final bool isPaid;
  final bool isActivated;

  const ShopAccess({
    required this.isPaid,
    required this.isActivated,
  });
}

/// Verification details
class VerificationDetails {
  final DateTime? verifiedAt;
  final String? verifiedBy;
  final List<VerificationDocument> documents;

  const VerificationDetails({
    this.verifiedAt,
    this.verifiedBy,
    required this.documents,
  });
}

/// Verification document
class VerificationDocument {
  final String type;
  final String? fileId;
  final bool verified;
  final String? notes;

  const VerificationDocument({
    required this.type,
    this.fileId,
    required this.verified,
    this.notes,
  });
}

/// Payment history entry
class PaymentHistory {
  final String status;
  final String? reason;
  final DateTime date;
  final double? amount;
  final String? currency;
  final String? transactionId;

  const PaymentHistory({
    required this.status,
    this.reason,
    required this.date,
    this.amount,
    this.currency,
    this.transactionId,
  });
}

/// Shop statistics
class ShopStatistics {
  final int totalCustomers;
  final double totalRevenue;
  final int totalDebts;
  final double totalDebtAmount;
  final DateTime lastUpdated;

  const ShopStatistics({
    required this.totalCustomers,
    required this.totalRevenue,
    required this.totalDebts,
    required this.totalDebtAmount,
    required this.lastUpdated,
  });
}

/// Contact person information
class ContactPerson {
  final String? name;
  final String? phone;
  final String? email;
  final String? position;

  const ContactPerson({
    this.name,
    this.phone,
    this.email,
    this.position,
  });
}

/// Notification preferences
class NotificationPreferences {
  final bool smsEnabled;
  final bool emailEnabled;
  final bool dailySummary;
  final bool dueReminders;
  final bool highRiskAlerts;
  final bool newCustomerNotifications;
  final bool paymentConfirmations;

  const NotificationPreferences({
    required this.smsEnabled,
    required this.emailEnabled,
    required this.dailySummary,
    required this.dueReminders,
    required this.highRiskAlerts,
    required this.newCustomerNotifications,
    required this.paymentConfirmations,
  });
} 