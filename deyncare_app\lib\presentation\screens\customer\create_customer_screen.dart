import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/widgets/common_app_bar.dart';
import 'package:deyncare_app/presentation/widgets/common_button.dart';
import 'package:deyncare_app/presentation/screens/customer/widgets/customer_form.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_bloc.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_state.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_event.dart';
import 'package:deyncare_app/domain/models/customer_models.dart';
import 'package:deyncare_app/injection_container.dart' as di;

/// Screen for creating a new customer
class CreateCustomerScreen extends StatelessWidget {
  const CreateCustomerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<CustomerBloc>(),
      child: const CreateCustomerView(),
    );
  }
}

class CreateCustomerView extends StatefulWidget {
  const CreateCustomerView({super.key});

  @override
  State<CreateCustomerView> createState() => _CreateCustomerViewState();
}

class _CreateCustomerViewState extends State<CreateCustomerView> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  
  CustomerType _selectedCustomerType = CustomerType.new_;
  bool _isSubmitting = false;

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CommonAppBar(
        title: 'Add Customer',
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => _handleBackPressed(context),
        ),
      ),
      body: BlocConsumer<CustomerBloc, CustomerState>(
        listener: _handleStateChanges,
        builder: (context, state) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Header
                  _buildHeader(context),
                  
                  const SizedBox(height: 24),
                  
                  // Form
                  CustomerForm(
                    nameController: _nameController,
                    phoneController: _phoneController,
                    emailController: _emailController,
                    addressController: _addressController,
                    selectedCustomerType: _selectedCustomerType,
                    onCustomerTypeChanged: (type) {
                      setState(() {
                        _selectedCustomerType = type;
                      });
                    },
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Submit button
                  CommonButton(
                    label: 'Create Customer',
                    onPressed: _isSubmitting ? null : _submitForm,
                    isLoading: _isSubmitting,
                    loadingText: 'Creating...',
                    icon: const Icon(Icons.person_add),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Cancel button
                  CommonButton(
                    label: 'Cancel',
                    type: ButtonType.outlined,
                    onPressed: _isSubmitting ? null : () => _handleBackPressed(context),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Customer Information',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Fill in the details to create a new customer account',
          style: TextStyle(
            fontSize: 16,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  void _handleStateChanges(BuildContext context, CustomerState state) {
    if (state is CustomerCreating) {
      setState(() {
        _isSubmitting = true;
      });
    } else {
      setState(() {
        _isSubmitting = false;
      });
    }

    if (state is CustomerCreated) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Customer ${state.customer.customerName} created successfully!'),
          backgroundColor: AppThemes.successColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
      Navigator.pop(context, true); // Return true to indicate success
    } else if (state is CustomerError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: AppThemes.errorColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _submitForm() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    context.read<CustomerBloc>().add(CreateCustomer(
      customerName: _nameController.text.trim(),
      customerType: _selectedCustomerType.value, // Convert enum to string
      phone: _phoneController.text.trim(),
      email: _emailController.text.trim().isNotEmpty 
          ? _emailController.text.trim() 
          : null,
      address: _addressController.text.trim().isNotEmpty 
          ? _addressController.text.trim() 
          : null,
    ));
  }

  void _handleBackPressed(BuildContext context) {
    if (_hasUnsavedChanges()) {
      _showDiscardDialog(context);
    } else {
      Navigator.pop(context);
    }
  }

  bool _hasUnsavedChanges() {
    return _nameController.text.isNotEmpty ||
           _phoneController.text.isNotEmpty ||
           _emailController.text.isNotEmpty ||
           _addressController.text.isNotEmpty;
  }

  void _showDiscardDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).dialogBackgroundColor,
        title: Text(
          'Discard Changes?',
          style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
        ),
        content: Text(
          'You have unsaved changes. Are you sure you want to discard them?',
          style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(color: Theme.of(context).colorScheme.primary),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close screen
            },
            child: Text(
              'Discard',
              style: TextStyle(color: AppThemes.errorColor),
            ),
          ),
        ],
      ),
    );
  }
} 