import 'package:equatable/equatable.dart';

/// Payment Events - focused on payment history functionality
abstract class PaymentEvent extends Equatable {
  const PaymentEvent();

  @override
  List<Object?> get props => [];
}

/// Load payment history for a specific debt
class LoadPaymentHistory extends PaymentEvent {
  final String debtId;

  const LoadPaymentHistory(this.debtId);

  @override
  List<Object> get props => [debtId];
}

/// Refresh payment history
class RefreshPaymentHistory extends PaymentEvent {
  final String debtId;

  const RefreshPaymentHistory(this.debtId);

  @override
  List<Object> get props => [debtId];
}

/// Clear payment history
class ClearPaymentHistory extends PaymentEvent {
  const ClearPaymentHistory();
} 