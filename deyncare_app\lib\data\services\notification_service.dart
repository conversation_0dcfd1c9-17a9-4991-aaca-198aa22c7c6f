import 'package:dio/dio.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';
import 'firebase_service.dart';

/// Notification Service for DeynCare Admin App
/// Handles FCM token registration and notification management
/// Communicates with backend Firebase notification system
/// Only for Admin, , and Employee roles
class NotificationService {
  final Dio _dio;
  static Map<String, String> _deviceInfo = {};
  static bool _tokenRegistered = false;

  NotificationService(this._dio);

  /// Initialize notification service and register FCM token
  Future<void> initialize() async {
    try {
      await _getDeviceInfo();
      await registerFCMToken();
    } catch (e) {
      debugPrint('❌ Failed to initialize notification service: $e');
    }
  }

  /// Get device information for registration
  static Future<void> _getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      final packageInfo = await PackageInfo.fromPlatform();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        _deviceInfo = {
          'platform': 'android', // Backend expects: ['android', 'ios'] only
          'deviceId': androidInfo.id,
          'osVersion': androidInfo.version.release,
          'appVersion': packageInfo.version,
          'deviceModel': '${androidInfo.manufacturer} ${androidInfo.model}',
          'deviceName': androidInfo.device,
        };
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        _deviceInfo = {
          'platform': 'ios', // Backend expects: ['android', 'ios'] only
          'deviceId': iosInfo.identifierForVendor ?? 'unknown',
          'osVersion': iosInfo.systemVersion,
          'appVersion': packageInfo.version,
          'deviceModel': iosInfo.model,
          'deviceName': iosInfo.name,
        };
      }
      
      debugPrint('📱 Device info collected: ${_deviceInfo['platform']} ${_deviceInfo['deviceModel']}');
    } catch (e) {
      debugPrint('❌ Failed to get device info: $e');
    }
  }

  /// Register FCM token with backend
  /// Endpoint: POST /api/fcm/register ✅ EXISTS
  Future<Map<String, dynamic>> registerFCMToken() async {
    try {
      final fcmToken = FirebaseService.fcmToken;
      
      if (fcmToken == null) {
        throw Exception('FCM token not available');
      }

      final response = await _dio.post('/fcm/register', data: {
        'token': fcmToken,
        'deviceInfo': _deviceInfo,
      });

      if (response.data['success'] == true) {
        _tokenRegistered = true;
        debugPrint('✅ FCM token registered successfully');
        return response.data;
      } else {
        throw Exception(response.data['message'] ?? 'Token registration failed');
      }
    } on DioException catch (e) {
      debugPrint('❌ Failed to register FCM token: ${e.response?.data ?? e.message}');
      rethrow;
    } catch (e) {
      debugPrint('❌ FCM token registration error: $e');
      rethrow;
    }
  }

  /// Send test notification
  /// Endpoint: POST /api/fcm/test ✅ EXISTS
  Future<Map<String, dynamic>> sendTestNotification() async {
    try {
      final response = await _dio.post('/fcm/test');
      
      if (response.data['success'] == true) {
        debugPrint('✅ Test notification sent successfully');
        return response.data;
      } else {
        throw Exception(response.data['message'] ?? 'Test notification failed');
      }
    } on DioException catch (e) {
      debugPrint('❌ Failed to send test notification: ${e.response?.data ?? e.message}');
      rethrow;
    } catch (e) {
      debugPrint('❌ Test notification error: $e');
      rethrow;
    }
  }

  /// Handle FCM token refresh
  Future<void> onTokenRefresh(String newToken) async {
    try {
      debugPrint('🔄 Handling FCM token refresh');
      await registerFCMToken();
    } catch (e) {
      debugPrint('❌ Failed to handle token refresh: $e');
    }
  }

  /// Check if token is registered
  static bool get isTokenRegistered => _tokenRegistered;

  /// Get device info
  static Map<String, String> get deviceInfo => _deviceInfo;

  /// Handle notification types for backend communication
  static Map<String, dynamic> createNotificationData({
    required String type,
    required String title,
    required String body,
    Map<String, dynamic>? additionalData,
  }) {
    return {
      'type': type,
      'title': title,
      'body': body,
      'data': {
        'timestamp': DateTime.now().toIso8601String(),
        ...?additionalData,
      },
    };
  }

  /// Notification type constants (matching backend exactly)
  static const String debtCreatedType = 'debt_created';
  static const String paymentRecordedType = 'payment_recorded';
  static const String debtReminderType = 'debt_reminder';
  static const String testType = 'test';
  static const String customType = 'custom';

  /// Debt reminder types (matching backend validation)
  static const List<String> supportedDebtReminderTypes = [
    '7_days',    // 7 days before due date
    '3_days',    // 3 days before due date  
    'overdue'    // Overdue debts
  ];

  /// Supported device platforms (matching backend validation)
  static const List<String> supportedPlatforms = ['android', 'ios'];
} 