import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/widgets/common_button.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_bloc.dart';
import 'package:deyncare_app/presentation/blocs/debt/debt_event.dart';
import 'package:deyncare_app/domain/models/debt.dart';

/// Filter bottom sheet for debt list
class DebtFilterSheet extends StatefulWidget {
  const DebtFilterSheet({super.key});

  @override
  State<DebtFilterSheet> createState() => _DebtFilterSheetState();
}

class _DebtFilterSheetState extends State<DebtFilterSheet> {
  String? selectedStatus;
  String? selectedAmountRange;
  String? selectedDueDateRange;
  String? selectedSortBy;
  bool ascending = true;

  final List<String> statusOptions = ['Active', 'Completed', 'Overdue'];
  final List<String> amountRanges = [
    'Under \$100',
    '\$100 - \$500',
    '\$500 - \$1,000',
    'Over \$1,000'
  ];
  final List<String> dueDateRanges = [
    'Due Today',
    'Due This Week',
    'Due This Month',
    'Overdue'
  ];
  final List<String> sortOptions = [
    'Due Date',
    'Amount',
    'Customer Name',
    'Created Date'
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Text(
                  'Filter Debts',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _clearFilters,
                  child: Text(
                    'Clear All',
                    style: TextStyle(
                      color: AppThemes.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          Divider(color: Theme.of(context).colorScheme.outline),
          
          // Filter options
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status Filter
                  _buildFilterSection(
                    context: context,
                    title: 'Status',
                    options: statusOptions,
                    selectedValue: selectedStatus,
                    onChanged: (value) {
                      setState(() {
                        selectedStatus = value;
                      });
                    },
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Amount Range Filter
                  _buildFilterSection(
                    context: context,
                    title: 'Amount Range',
                    options: amountRanges,
                    selectedValue: selectedAmountRange,
                    onChanged: (value) {
                      setState(() {
                        selectedAmountRange = value;
                      });
                    },
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Due Date Range Filter
                  _buildFilterSection(
                    context: context,
                    title: 'Due Date',
                    options: dueDateRanges,
                    selectedValue: selectedDueDateRange,
                    onChanged: (value) {
                      setState(() {
                        selectedDueDateRange = value;
                      });
                    },
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Sort Options
                  _buildSortSection(context),
                ],
              ),
            ),
          ),
          
          // Action buttons
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).colorScheme.outline,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: CommonButton(
                    label: 'Reset',
                    type: ButtonType.outlined,
                    onPressed: _resetFilters,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CommonButton(
                    label: 'Apply Filters',
                    onPressed: _applyFilters,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection({
    required BuildContext context,
    required String title,
    required List<String> options,
    required String? selectedValue,
    required ValueChanged<String?> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: options.map((option) {
            final isSelected = selectedValue == option;
            return GestureDetector(
              onTap: () {
                onChanged(isSelected ? null : option);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AppThemes.primaryColor.withOpacity(0.1)
                      : Theme.of(context).colorScheme.surface,
                  border: Border.all(
                    color: isSelected 
                        ? AppThemes.primaryColor 
                        : Theme.of(context).colorScheme.outline,
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  option,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    color: isSelected 
                        ? AppThemes.primaryColor 
                        : Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSortSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sort By',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 12),
        
        // Sort options
        ...sortOptions.map((option) {
          final isSelected = selectedSortBy == option;
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: InkWell(
              onTap: () {
                setState(() {
                  selectedSortBy = isSelected ? null : option;
                });
              },
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AppThemes.primaryColor.withOpacity(0.1)
                      : Theme.of(context).colorScheme.surface,
                  border: Border.all(
                    color: isSelected 
                        ? AppThemes.primaryColor 
                        : Theme.of(context).colorScheme.outline,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                      color: isSelected 
                          ? AppThemes.primaryColor 
                          : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        option,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                          color: isSelected 
                              ? AppThemes.primaryColor 
                              : Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
        
        // Sort direction
        if (selectedSortBy != null) ...[
          const SizedBox(height: 16),
          Row(
            children: [
              Text(
                'Sort Direction:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            ascending = true;
                          });
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          decoration: BoxDecoration(
                            color: ascending 
                                ? AppThemes.primaryColor.withOpacity(0.1)
                                : Theme.of(context).colorScheme.surface,
                            border: Border.all(
                              color: ascending 
                                  ? AppThemes.primaryColor 
                                  : Theme.of(context).colorScheme.outline,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.arrow_upward,
                                size: 16,
                                color: ascending 
                                    ? AppThemes.primaryColor 
                                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Ascending',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: ascending 
                                      ? AppThemes.primaryColor 
                                      : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            ascending = false;
                          });
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          decoration: BoxDecoration(
                            color: !ascending 
                                ? AppThemes.primaryColor.withOpacity(0.1)
                                : Theme.of(context).colorScheme.surface,
                            border: Border.all(
                              color: !ascending 
                                  ? AppThemes.primaryColor 
                                  : Theme.of(context).colorScheme.outline,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.arrow_downward,
                                size: 16,
                                color: !ascending 
                                    ? AppThemes.primaryColor 
                                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Descending',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: !ascending 
                                      ? AppThemes.primaryColor 
                                      : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  void _clearFilters() {
    setState(() {
      selectedStatus = null;
      selectedAmountRange = null;
      selectedDueDateRange = null;
      selectedSortBy = null;
      ascending = true;
    });
  }

  void _resetFilters() {
    _clearFilters();
    context.read<DebtBloc>().add(const LoadDebts());
    Navigator.pop(context);
  }

  void _applyFilters() {
    // Convert UI filter values to API parameters
    DebtStatus? statusFilter;
    if (selectedStatus != null) {
      switch (selectedStatus!) {
        case 'Active':
          statusFilter = DebtStatus.active;
          break;
        case 'Completed':
          statusFilter = DebtStatus.completed;
          break;
        case 'Overdue':
          statusFilter = DebtStatus.overdue;
          break;
        case 'Defaulted':
          statusFilter = DebtStatus.defaulted;
          break;
        case 'Cancelled':
          statusFilter = DebtStatus.cancelled;
          break;
      }
    }

    String? sortField;
    if (selectedSortBy != null) {
      switch (selectedSortBy!) {
        case 'Due Date':
          sortField = 'dueDate';
          break;
        case 'Amount':
          sortField = 'amount';
          break;
        case 'Customer Name':
          sortField = 'customerName';
          break;
        case 'Created Date':
          sortField = 'createdAt';
          break;
      }
    }

    // Only use supported parameters from LoadDebts event
    context.read<DebtBloc>().add(
          LoadDebts(
            status: statusFilter,
            sortBy: sortField,
            ascending: ascending,
          ),
        );
    
    Navigator.pop(context);
  }
} 
