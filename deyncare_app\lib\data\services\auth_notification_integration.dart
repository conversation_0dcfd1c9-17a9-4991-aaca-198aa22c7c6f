import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import 'firebase_service.dart';
import 'notification_service.dart';

/// Authentication & Notification Integration Service
/// Handles the complete flow of login + FCM token registration
/// Only for Admin, SuperAdmin, and Employee roles
class AuthNotificationIntegration {
  final Dio _dio;
  late final NotificationService _notificationService;

  AuthNotificationIntegration(this._dio) {
    _notificationService = NotificationService(_dio);
  }

  /// Complete login flow with notification registration
  /// This should be called after successful authentication
  Future<AuthNotificationResult> loginWithNotifications({
    required String userToken,
    required String userRole,
    required String userId,
  }) async {
    try {
      debugPrint('🔐 Starting login with notifications for user: $userId');
      
      // 1. Validate user role (Backend requirement)
      if (!_isValidRole(userRole)) {
        return AuthNotificationResult.roleNotAllowed(
          'Role $userRole is not allowed to receive notifications. Only Ad<PERSON>, SuperAdmin, and Employee users can use notifications.',
        );
      }

      // 2. Set authentication token for API calls
      _setAuthHeader(userToken);

      // 3. Initialize Firebase if not already done
      if (!FirebaseService.isInitialized) {
        await FirebaseService.initialize();
        debugPrint('🔥 Firebase initialized for user: $userId');
      }

      // 4. Register FCM token with backend
      final fcmToken = FirebaseService.fcmToken;
      if (fcmToken == null) {
        return AuthNotificationResult.fcmTokenError(
          'FCM token not available. Check Firebase configuration.',
        );
      }

      debugPrint('🔑 Registering FCM token for user: $userId');
      final registrationResult = await _notificationService.registerFCMToken();
      
      debugPrint('✅ Login with notifications completed for user: $userId');
      
      return AuthNotificationResult.success(
        message: 'Login successful and notifications enabled',
        fcmToken: fcmToken,
        userRole: userRole,
        registrationData: registrationResult,
      );

    } catch (e) {
      debugPrint('❌ Login with notifications failed: $e');
      return AuthNotificationResult.error(
        'Failed to enable notifications: ${e.toString()}',
      );
    }
  }

  /// Test notification after login (optional)
  Future<TestNotificationResult> sendWelcomeNotification() async {
    try {
      debugPrint('🧪 Sending welcome test notification');
      final result = await _notificationService.sendTestNotification();
      
      return TestNotificationResult.success(
        'Welcome notification sent successfully',
        result,
      );
    } catch (e) {
      debugPrint('❌ Welcome notification failed: $e');
      return TestNotificationResult.error(
        'Failed to send welcome notification: ${e.toString()}',
      );
    }
  }

  /// Handle logout (Firebase cleanup)
  Future<void> logout() async {
    try {
      debugPrint('👋 Handling notification cleanup on logout');
      
      // Note: We don't call unregister API because it doesn't exist
      // The backend will handle token cleanup automatically
      
      // Clear local state
      _clearAuthHeader();
      
      debugPrint('✅ Notification cleanup completed');
    } catch (e) {
      debugPrint('❌ Logout cleanup error: $e');
    }
  }

  /// Check if user role is allowed to receive notifications
  bool _isValidRole(String userRole) {
    const allowedRoles = ['admin', 'superAdmin', 'employee'];
    return allowedRoles.contains(userRole.toLowerCase());
  }

  /// Set authentication header for API calls
  void _setAuthHeader(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  /// Clear authentication header
  void _clearAuthHeader() {
    _dio.options.headers.remove('Authorization');
  }

  /// Get notification service for advanced usage
  NotificationService get notificationService => _notificationService;
}

/// Result classes for different operations
class AuthNotificationResult {
  final bool success;
  final String message;
  final String? fcmToken;
  final String? userRole;
  final Map<String, dynamic>? registrationData;
  final String? error;

  AuthNotificationResult._({
    required this.success,
    required this.message,
    this.fcmToken,
    this.userRole,
    this.registrationData,
    this.error,
  });

  factory AuthNotificationResult.success({
    required String message,
    required String fcmToken,
    required String userRole,
    required Map<String, dynamic> registrationData,
  }) {
    return AuthNotificationResult._(
      success: true,
      message: message,
      fcmToken: fcmToken,
      userRole: userRole,
      registrationData: registrationData,
    );
  }

  factory AuthNotificationResult.roleNotAllowed(String message) {
    return AuthNotificationResult._(
      success: false,
      message: message,
      error: 'ROLE_NOT_ALLOWED',
    );
  }

  factory AuthNotificationResult.fcmTokenError(String message) {
    return AuthNotificationResult._(
      success: false,
      message: message,
      error: 'FCM_TOKEN_ERROR',
    );
  }

  factory AuthNotificationResult.error(String message) {
    return AuthNotificationResult._(
      success: false,
      message: message,
      error: 'GENERAL_ERROR',
    );
  }
}

class TestNotificationResult {
  final bool success;
  final String message;
  final Map<String, dynamic>? data;
  final String? error;

  TestNotificationResult._({
    required this.success,
    required this.message,
    this.data,
    this.error,
  });

  factory TestNotificationResult.success(
    String message,
    Map<String, dynamic> data,
  ) {
    return TestNotificationResult._(
      success: true,
      message: message,
      data: data,
    );
  }

  factory TestNotificationResult.error(String message) {
    return TestNotificationResult._(
      success: false,
      message: message,
      error: 'TEST_NOTIFICATION_ERROR',
    );
  }
}

/// Usage Example:
/// ```dart
/// // After successful login
/// final authNotification = AuthNotificationIntegration(dio);
/// final result = await authNotification.loginWithNotifications(
///   userToken: loginResponse.token,
///   userRole: loginResponse.user.role,
///   userId: loginResponse.user.id,
/// );
/// 
/// if (result.success) {
///   print('✅ Notifications enabled: ${result.message}');
///   
///   // Optional: Send welcome notification
///   await authNotification.sendWelcomeNotification();
/// } else {
///   print('❌ Notification setup failed: ${result.message}');
/// }
/// ``` 