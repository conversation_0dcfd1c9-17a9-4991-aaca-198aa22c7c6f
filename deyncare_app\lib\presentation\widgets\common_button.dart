import 'package:flutter/material.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';

/// Common button widget for consistent styling across the app
class CommonButton extends StatelessWidget {
  final String label;
  final VoidCallback? onPressed;
  final bool isLoading;
  final ButtonType type;
  final ButtonSize size;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final Widget? icon;
  final String? loadingText;

  const CommonButton({
    super.key,
    required this.label,
    this.onPressed,
    this.isLoading = false,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.icon,
    this.loadingText,
  });

  @override
  Widget build(BuildContext context) {
    final buttonConfig = _getButtonConfig();
    
    return SizedBox(
      width: width ?? (size == ButtonSize.small ? null : double.infinity),
      height: buttonConfig.height,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: _getBackgroundColor(buttonConfig, context),
          foregroundColor: _getTextColor(buttonConfig, context),
          elevation: type == ButtonType.outlined ? 0 : 2,
          disabledBackgroundColor: _getDisabledBackgroundColor(buttonConfig, context),
          disabledForegroundColor: _getDisabledTextColor(buttonConfig, context),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonConfig.borderRadius),
            side: type == ButtonType.outlined
                ? BorderSide(
                    color: backgroundColor ?? AppThemes.primaryColor,
                    width: 1.5,
                  )
                : BorderSide.none,
          ),
          padding: EdgeInsets.symmetric(
            vertical: buttonConfig.verticalPadding,
            horizontal: buttonConfig.horizontalPadding,
          ),
        ),
        child: isLoading ? _buildLoadingContent(context) : _buildButtonContent(),
      ),
    );
  }

  ButtonConfig _getButtonConfig() {
    switch (size) {
      case ButtonSize.small:
        return const ButtonConfig(
          height: 36,
          fontSize: 14,
          borderRadius: 6,
          verticalPadding: 8,
          horizontalPadding: 12,
        );
      case ButtonSize.medium:
        return const ButtonConfig(
          height: 44,
          fontSize: 16,
          borderRadius: 8,
          verticalPadding: 12,
          horizontalPadding: 16,
        );
      case ButtonSize.large:
        return const ButtonConfig(
          height: 52,
          fontSize: 18,
          borderRadius: 10,
          verticalPadding: 16,
          horizontalPadding: 20,
        );
    }
  }

  Color _getBackgroundColor(ButtonConfig config, BuildContext context) {
    if (type == ButtonType.outlined) return Colors.transparent;
    
    switch (type) {
      case ButtonType.primary:
        return backgroundColor ?? Theme.of(context).colorScheme.primary;
      case ButtonType.secondary:
        return backgroundColor ?? Theme.of(context).colorScheme.secondary;
      case ButtonType.success:
        return backgroundColor ?? AppThemes.successColor;
      case ButtonType.danger:
        return backgroundColor ?? Theme.of(context).colorScheme.error;
      case ButtonType.outlined:
        return Colors.transparent;
    }
  }

  Color _getTextColor(ButtonConfig config, BuildContext context) {
    if (type == ButtonType.outlined) {
      return textColor ?? backgroundColor ?? Theme.of(context).colorScheme.primary;
    }
    return textColor ?? Theme.of(context).colorScheme.onPrimary;
  }

  Color _getDisabledBackgroundColor(ButtonConfig config, BuildContext context) {
    if (type == ButtonType.outlined) return Colors.transparent;
    return _getBackgroundColor(config, context).withValues(alpha: 0.6);
  }

  Color _getDisabledTextColor(ButtonConfig config, BuildContext context) {
    return _getTextColor(config, context).withValues(alpha: 0.6);
  }

  Widget _buildLoadingContent(BuildContext context) {
    final config = _getButtonConfig();
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          height: config.fontSize,
          width: config.fontSize,
          child: CircularProgressIndicator(
            strokeWidth: 2.0,
            valueColor: AlwaysStoppedAnimation<Color>(_getTextColor(config, context)),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          loadingText ?? 'Loading...',
          style: TextStyle(
            fontSize: config.fontSize,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildButtonContent() {
    final config = _getButtonConfig();
    
    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon!,
          const SizedBox(width: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: config.fontSize,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      );
    }
    
    return Text(
      label,
      style: TextStyle(
        fontSize: config.fontSize,
        fontWeight: FontWeight.w600,
      ),
    );
  }
}

enum ButtonType { primary, secondary, danger, success, outlined }
enum ButtonSize { small, medium, large }

class ButtonConfig {
  final double height;
  final double fontSize;
  final double borderRadius;
  final double verticalPadding;
  final double horizontalPadding;

  const ButtonConfig({
    required this.height,
    required this.fontSize,
    required this.borderRadius,
    required this.verticalPadding,
    required this.horizontalPadding,
  });
}

/// Common Floating Action Button with consistent styling and proper positioning
class CommonFAB extends StatelessWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String? tooltip;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final bool mini;
  final bool extended;
  final String? label;
  final bool isLoading;

  const CommonFAB({
    super.key,
    required this.onPressed,
    required this.icon,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
    this.mini = false,
    this.extended = false,
    this.label,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    if (extended && label != null) {
      return FloatingActionButton.extended(
        onPressed: isLoading ? null : onPressed,
        icon: isLoading
            ? SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    foregroundColor ?? Colors.white,
                  ),
                ),
              )
            : Icon(icon, size: 20),
        label: Text(
          isLoading ? 'Loading...' : label!,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
        backgroundColor: backgroundColor ?? AppThemes.primaryColor,
        foregroundColor: foregroundColor ?? Colors.white,
        tooltip: tooltip,
        elevation: 3,
        focusElevation: 5,
        hoverElevation: 4,
        highlightElevation: 6,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      );
    }

    return FloatingActionButton(
      onPressed: isLoading ? null : onPressed,
      mini: mini,
      backgroundColor: backgroundColor ?? AppThemes.primaryColor,
      foregroundColor: foregroundColor ?? Colors.white,
      tooltip: tooltip,
      elevation: 3,
      focusElevation: 5,
      hoverElevation: 4,
      highlightElevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(mini ? 14 : 16),
      ),
      child: isLoading
          ? SizedBox(
              width: mini ? 16 : 20,
              height: mini ? 16 : 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  foregroundColor ?? Colors.white,
                ),
              ),
            )
          : Icon(
              icon,
              size: mini ? 20 : 24,
            ),
    );
  }
} 