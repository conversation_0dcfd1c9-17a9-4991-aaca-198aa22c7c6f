import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:io';
import 'dart:convert';

// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  debugPrint('📨 Background message: ${message.notification?.title}');
}

/// Firebase Service for DeynCare Admin Push Notifications
/// Handles FCM token management and push notification processing
/// Only for Admin, SuperAdmin, and Employee roles
class FirebaseService {
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications = 
      FlutterLocalNotificationsPlugin();
  
  static String? _fcmToken;
  static bool _initialized = false;

  /// Initialize Firebase and FCM
  static Future<void> initialize() async {
    if (_initialized) return;
    
    try {
      // Initialize Firebase
      await Firebase.initializeApp();
      
      // Set background message handler
      FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
      
      // Request permissions
      await _requestPermissions();
      
      // Initialize local notifications
      await _initializeLocalNotifications();
      
      // Get FCM token
      await _getFCMToken();
      
      // Setup message handlers
      _setupMessageHandlers();
      
      _initialized = true;
      debugPrint('🔥 Firebase Service initialized successfully');
    } catch (e) {
      debugPrint('❌ Firebase initialization failed: $e');
      rethrow;
    }
  }

  /// Request notification permissions
  static Future<void> _requestPermissions() async {
    NotificationSettings settings = await _messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    debugPrint('📱 Notification permission: ${settings.authorizationStatus}');
    
    if (settings.authorizationStatus == AuthorizationStatus.denied) {
      throw Exception('Notification permissions denied');
    }
  }

  /// Initialize local notifications for foreground handling
  static Future<void> _initializeLocalNotifications() async {
    const AndroidInitializationSettings androidSettings = 
        AndroidInitializationSettings('@mipmap/ic_launcher');
    
    const DarwinInitializationSettings iosSettings = 
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings settings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      settings,
      onDidReceiveNotificationResponse: _onLocalNotificationTap,
    );

    // Create notification channel for Android
    if (Platform.isAndroid) {
      const AndroidNotificationChannel channel = AndroidNotificationChannel(
        'deyncare_admin',
        'DeynCare Admin Notifications',
        description: 'Important notifications for DeynCare admins',
        importance: Importance.high,
      );

      await _localNotifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);
    }
  }

  /// Get FCM token
  static Future<String?> _getFCMToken() async {
    try {
      _fcmToken = await _messaging.getToken();
      debugPrint('🔑 FCM Token: $_fcmToken');
      return _fcmToken;
    } catch (e) {
      debugPrint('❌ Failed to get FCM token: $e');
      return null;
    }
  }

  /// Setup message handlers
  static void _setupMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
    
    // Handle notification tap when app is terminated
    _handleInitialMessage();
    
    // Handle token refresh
    _messaging.onTokenRefresh.listen(_onTokenRefresh);
  }

  /// Handle foreground messages
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    debugPrint('📨 Foreground message: ${message.notification?.title}');
    debugPrint('📨 Foreground message body: ${message.notification?.body}');
    debugPrint('📨 Foreground message data: ${message.data}');
    
    // Enhanced notification processing for payment notifications
    if (message.data['type'] == 'payment_recorded') {
      await _showEnhancedPaymentNotification(message);
    } else {
      // Show regular local notification for other types
      await _showLocalNotification(message);
    }
    
    // Process notification data
    _processNotificationData(message.data);
  }

  /// Handle notification tap
  static void _handleNotificationTap(RemoteMessage message) {
    debugPrint('👆 Notification tapped: ${message.data}');
    _navigateToScreen(message.data);
  }

  /// Handle initial message when app is opened from terminated state
  static Future<void> _handleInitialMessage() async {
    RemoteMessage? initialMessage = await _messaging.getInitialMessage();
    if (initialMessage != null) {
      debugPrint('🚀 App opened from notification: ${initialMessage.data}');
      _navigateToScreen(initialMessage.data);
    }
  }

  /// Handle local notification tap
  static void _onLocalNotificationTap(NotificationResponse response) {
    debugPrint('👆 Local notification tapped: ${response.payload}');
    
    if (response.payload != null) {
      try {
        // Parse the payload and navigate
        final data = Map<String, dynamic>.from(
          response.payload!.split(',').map((e) => e.split(':')).fold<Map<String, String>>(
            {}, (map, pair) => map..[pair[0]] = pair[1]
          )
        );
        _navigateToScreen(data);
      } catch (e) {
        debugPrint('❌ Failed to parse notification payload: $e');
      }
    }
  }

  /// Handle token refresh
  static void _onTokenRefresh(String newToken) {
    debugPrint('🔄 FCM Token refreshed: $newToken');
    _fcmToken = newToken;
    
    // The notification service will handle backend registration
  }

  /// Show local notification for foreground messages
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'deyncare_admin',
      'DeynCare Admin Notifications',
      channelDescription: 'Important notifications for DeynCare admins',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      icon: '@mipmap/ic_launcher',
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    // Create payload for local notification
    final payload = message.data.entries.map((e) => '${e.key}:${e.value}').join(',');

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'DeynCare',
      message.notification?.body ?? 'New notification',
      details,
      payload: payload,
    );
  }

  /// Show enhanced payment notification with customer and amount details
  static Future<void> _showEnhancedPaymentNotification(RemoteMessage message) async {
    if (_localNotifications == null) {
      debugPrint('⚠️ Local notifications not initialized');
      return;
    }

    // Extract payment details from message data
    final customerName = message.data['customerName'] ?? 'Unknown Customer';
    final amount = message.data['amount'] ?? '0.00';
    final riskStatus = message.data['riskStatus'] ?? 'unknown';
    final paymentMethod = message.data['paymentMethod'] ?? 'cash';
    
    // Create enhanced notification with customer details and ML risk level
    final riskEmoji = _getRiskEmoji(riskStatus);
    final riskLevel = _getRiskLevelText(riskStatus);
    
    String enhancedTitle = '💰 Payment from $customerName';
    String enhancedBody = 'Amount: \$${amount} • Method: ${paymentMethod.toUpperCase()} • Risk: $riskLevel $riskEmoji';
    
    debugPrint('📨 Using customer details from message data:');
    debugPrint('📨 Customer: $customerName');
    debugPrint('📨 Amount: \$${amount}');
    debugPrint('📨 Risk Status: $riskStatus');
    debugPrint('📨 Payment Method: $paymentMethod');

    debugPrint('📨 Enhanced notification: $enhancedTitle');
    debugPrint('📨 Enhanced body: $enhancedBody');

    const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'payment_notifications',
      'Payment Notifications',
      channelDescription: 'Notifications for payment transactions',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      icon: '@mipmap/ic_launcher',
      color: Color(0xFF2E5BFF),
      styleInformation: BigTextStyleInformation(''),
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      categoryIdentifier: 'payment_category',
    );

    const NotificationDetails details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    final payload = jsonEncode({
      'type': 'payment_recorded',
      'data': message.data,
      'timestamp': DateTime.now().toIso8601String(),
    });

    await _localNotifications!.show(
      message.hashCode,
      enhancedTitle,
      enhancedBody,
      details,
      payload: payload,
    );
  }

  /// Get risk emoji based on risk status
  static String _getRiskEmoji(String riskStatus) {
    switch (riskStatus.toLowerCase()) {
      case 'low':
        return '✅';
      case 'medium':
        return '⚠️';
      case 'high':
        return '🚨';
      default:
        return '📊';
    }
  }

  /// Get risk level text based on ML risk status
  static String _getRiskLevelText(String riskStatus) {
    switch (riskStatus.toLowerCase()) {
      case 'low':
        return 'Low Risk';
      case 'medium':
        return 'Medium Risk';
      case 'high':
        return 'High Risk';
      default:
        return 'Unknown Risk';
    }
  }

  /// Process notification data based on type
  static void _processNotificationData(Map<String, dynamic> data) {
    final type = data['type'];
    
    switch (type) {
      case 'debt_created':
        debugPrint('💰 Processing debt creation notification');
        break;
      case 'payment_recorded':
        debugPrint('💳 Processing payment notification');
        break;
      case 'debt_reminder':
        debugPrint('⏰ Processing debt reminder');
        break;
      case 'test':
        debugPrint('🧪 Processing test notification');
        break;
      default:
        debugPrint('📢 Processing general notification');
    }
  }

  /// Navigate to screen based on notification data
  static void _navigateToScreen(Map<String, dynamic> data) {
    final type = data['type'];
    
    // Navigation will be handled by the app's navigation service
    switch (type) {
      case 'debt_created':
        final debtId = data['debtId'];
        debugPrint('🔄 Navigate to debt details: $debtId');
        break;
      case 'payment_recorded':
        final paymentId = data['paymentId'];
        debugPrint('🔄 Navigate to payment details: $paymentId');
        break;
      case 'debt_reminder':
        debugPrint('🔄 Navigate to debt list');
        break;
      case 'test':
        debugPrint('🔄 Navigate to notification test screen');
        break;
      default:
        debugPrint('🔄 Navigate to dashboard');
    }
  }

  /// Get current FCM token
  static String? get fcmToken => _fcmToken;

  /// Check if Firebase is initialized
  static bool get isInitialized => _initialized;

  /// Refresh FCM token manually
  static Future<String?> refreshToken() async {
    try {
      await _messaging.deleteToken();
      return await _getFCMToken();
    } catch (e) {
      debugPrint('❌ Failed to refresh FCM token: $e');
      return null;
    }
  }
} 