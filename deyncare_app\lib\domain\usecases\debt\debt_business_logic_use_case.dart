import 'package:dartz/dartz.dart';
import 'package:deyncare_app/core/errors/failures.dart';
import 'package:deyncare_app/core/utils/business_validation.dart';
import 'package:deyncare_app/domain/repositories/debt_repository.dart';
import 'package:deyncare_app/domain/repositories/customer_repository.dart';
import 'package:deyncare_app/domain/models/debt.dart';
import 'package:deyncare_app/domain/models/payment.dart';

/// Use case for debt business logic validation
/// Implements business rules that match backend exactly from debt.model.js and payment.model.js
class DebtBusinessLogicUseCase {
  final DebtRepository _debtRepository;
  final CustomerRepository _customerRepository;

  DebtBusinessLogicUseCase(this._debtRepository, this._customerRepository);

  /// Validates debt data before create operations
  /// Matches backend validation from debtSchemas.js
  Future<Either<Failure, DebtValidationResult>> validateDebtData({
    required String customerId,
    required double debtAmount,
    required DateTime dueDate,
    String? description,
    double? paidAmount,
    DateTime? paidDate,
    PaymentMethod? paymentMethod,
  }) async {
    try {
      // Validate customer ID format (business validation)
      final customerIdValidation = BusinessValidation.validateCustomerId(customerId);
      if (!customerIdValidation.isValid) {
        return Left(ValidationFailure(message: customerIdValidation.errorMessage!));
      }

      // Validate debt amount (business validation)
      final debtAmountValidation = BusinessValidation.validateDebtAmount(debtAmount);
      if (!debtAmountValidation.isValid) {
        return Left(ValidationFailure(message: debtAmountValidation.errorMessage!));
      }

      // Validate due date must be in future
      if (dueDate.isBefore(DateTime.now())) {
        return Left(ValidationFailure(message: 'Due date must be in the future'));
      }

      // Validate paid amount doesn't exceed debt amount
      if (paidAmount != null && paidAmount > debtAmount) {
        return Left(ValidationFailure(message: 'Paid amount cannot exceed debt amount'));
      }

      // Validate description length
      if (description != null && description.length > 1000) {
        return Left(ValidationFailure(message: 'Description cannot exceed 1000 characters'));
      }

      // Validate paid date if paid amount provided
      if (paidAmount != null && paidAmount > 0 && paidDate == null) {
        return Left(ValidationFailure(message: 'Payment date is required when paid amount is provided'));
      }

      // Validate payment method if paid amount provided
      if (paidAmount != null && paidAmount > 0 && paymentMethod == null) {
        return Left(ValidationFailure(message: 'Payment method is required when paid amount is provided'));
      }

      return Right(DebtValidationResult(
        isValid: true,
        validationMessage: 'All debt data is valid',
      ));

    } catch (e) {
      return Left(ServerFailure(message: 'Failed to validate debt data: ${e.toString()}'));
    }
  }

  /// Validates payment data before add payment operations
  /// Matches backend validation from debtSchemas.js addPayment schema
  Future<Either<Failure, PaymentValidationResult>> validatePaymentData({
    required String debtId,
    required double amount,
    DateTime? paymentDate,
    PaymentMethod? paymentMethod,
    String? notes,
  }) async {
    try {
      // Validate debt ID format
      final debtIdValidation = BusinessValidation.validateDebtId(debtId);
      if (!debtIdValidation.isValid) {
        return Left(ValidationFailure(message: debtIdValidation.errorMessage!));
      }

      // Validate payment amount is positive
      if (amount <= 0) {
        return Left(ValidationFailure(message: 'Payment amount must be positive'));
      }

      // Validate payment date is not in future
      if (paymentDate != null && paymentDate.isAfter(DateTime.now())) {
        return Left(ValidationFailure(message: 'Payment date cannot be in the future'));
      }

      // Validate notes length
      if (notes != null && notes.length > 500) {
        return Left(ValidationFailure(message: 'Notes cannot exceed 500 characters'));
      }

      // Check if debt exists and can accept payment
      final debtResult = await _debtRepository.getDebtById(debtId);
      return debtResult.fold(
        (failure) => Left(failure),
        (debt) {
          // Check if debt is already fully paid
          if (debt.status == DebtStatus.completed) {
            return Left(ValidationFailure(message: 'Cannot add payment to fully paid debt'));
          }

          // Check if payment amount exceeds outstanding amount
          if (amount > debt.remainingAmount) {
            return Left(ValidationFailure(
              message: 'Payment amount (${amount.toStringAsFixed(2)}) cannot exceed outstanding amount (${debt.remainingAmount.toStringAsFixed(2)})'
            ));
          }

          return Right(PaymentValidationResult(
            isValid: true,
            validationMessage: 'Payment data is valid',
            debt: debt,
          ));
        },
      );

    } catch (e) {
      return Left(ServerFailure(message: 'Failed to validate payment data: ${e.toString()}'));
    }
  }

  /// Calculates payment timing and updates payment flags
  /// Matches backend payment.model.js calculatePaymentTiming method
  Future<Either<Failure, PaymentTimingResult>> calculatePaymentTiming({
    required DateTime paymentDate,
    required DateTime dueDate,
  }) async {
    try {
      // Calculate payment delay in days
      final paymentDelay = paymentDate.difference(dueDate).inDays;
      final isOnTime = paymentDelay <= 0;

      String timingStatus;
      if (isOnTime) {
        if (paymentDelay == 0) {
          timingStatus = 'On Time ✅';
        } else {
          timingStatus = '${paymentDelay.abs()} days early ✅';
        }
      } else {
        timingStatus = '$paymentDelay days late ⚠️';
      }

      return Right(PaymentTimingResult(
        paymentDelay: paymentDelay,
        isOnTime: isOnTime,
        timingStatus: timingStatus,
        dueDate: dueDate,
        paymentDate: paymentDate,
      ));

    } catch (e) {
      return Left(ServerFailure(message: 'Failed to calculate payment timing: ${e.toString()}'));
    }
  }

  /// Checks customer's debt eligibility based on existing outstanding debts
  /// Matches backend customer business logic patterns
  Future<Either<Failure, CustomerDebtEligibilityResult>> checkCustomerDebtEligibility({
    required String customerId,
    required double newDebtAmount,
  }) async {
    try {
      // Validate customer ID format
      final customerIdValidation = BusinessValidation.validateCustomerId(customerId);
      if (!customerIdValidation.isValid) {
        return Left(ValidationFailure(message: customerIdValidation.errorMessage!));
      }

      // Validate debt amount
      final debtAmountValidation = BusinessValidation.validateDebtAmount(newDebtAmount);
      if (!debtAmountValidation.isValid) {
        return Left(ValidationFailure(message: debtAmountValidation.errorMessage!));
      }

      // Get customer details
      final customerResult = await _customerRepository.getCustomerById(customerId);
      return customerResult.fold(
        (failure) => Left(failure),
        (customerResponse) {
          final customerData = customerResponse.data;
          if (customerData == null) {
            return Left(ServerFailure(message: 'No customer data found'));
          }

          final customer = customerData.customer;
          final financials = customerData.statistics?.financials;
          final riskProfile = customerData.riskProfile;

          // Get financial data with fallbacks
          final currentOutstanding = financials?.totalOutstanding ?? 0.0;
          final riskLevel = riskProfile?.currentRiskLevel ?? 'Not Assessed';
          
          // Business rule: Check if customer has high risk profile
          if (riskLevel == 'High Risk' || riskLevel == 'Critical Risk') {
            return Right(CustomerDebtEligibilityResult(
              canTakeDebt: false,
              customerId: customerId,
              customerName: customer.customerName ?? 'Unknown Customer',
              currentOutstanding: currentOutstanding,
              requestedDebtAmount: newDebtAmount,
              projectedTotalOutstanding: currentOutstanding + newDebtAmount,
              businessRuleMessage: 'Customer has high risk profile and cannot take new debt',
              riskLevel: riskLevel,
              reason: DebtEligibilityReason.highRisk,
            ));
          }

          // Business rule: Check if customer has too many active debts
          // This would require additional repository method to count active debts
          // For now, we'll use a simple outstanding amount check

          // Calculate new total outstanding after this debt
          final newTotalOutstanding = currentOutstanding + newDebtAmount;

          // Business rule: Maximum outstanding debt threshold (configurable)
          const maxOutstandingThreshold = 10000.0; // This should come from settings

          if (newTotalOutstanding > maxOutstandingThreshold) {
            return Right(CustomerDebtEligibilityResult(
              canTakeDebt: false,
              customerId: customerId,
              customerName: customer.customerName ?? 'Unknown Customer',
              currentOutstanding: currentOutstanding,
              requestedDebtAmount: newDebtAmount,
              projectedTotalOutstanding: newTotalOutstanding,
              businessRuleMessage: 'Total outstanding debt would exceed maximum allowed limit',
              riskLevel: riskLevel,
              reason: DebtEligibilityReason.exceedsLimit,
            ));
          }

          // Customer is eligible for new debt
          return Right(CustomerDebtEligibilityResult(
            canTakeDebt: true,
            customerId: customerId,
            customerName: customer.customerName ?? 'Unknown Customer',
            currentOutstanding: currentOutstanding,
            requestedDebtAmount: newDebtAmount,
            projectedTotalOutstanding: newTotalOutstanding,
            businessRuleMessage: 'Customer is eligible for new debt',
            riskLevel: riskLevel,
            reason: DebtEligibilityReason.eligible,
          ));
        },
      );

    } catch (e) {
      return Left(ServerFailure(message: 'Failed to check debt eligibility: ${e.toString()}'));
    }
  }

  /// Validates debt update data
  /// Matches backend validation patterns
  Future<Either<Failure, DebtUpdateValidationResult>> validateDebtUpdateData({
    required String debtId,
    double? amount,
    DateTime? dueDate,
    String? description,
    DebtStatus? status,
  }) async {
    try {
      // Validate debt ID format
      final debtIdValidation = BusinessValidation.validateDebtId(debtId);
      if (!debtIdValidation.isValid) {
        return Left(ValidationFailure(message: debtIdValidation.errorMessage!));
      }

      // Validate amount if provided
      if (amount != null) {
        final amountValidation = BusinessValidation.validateDebtAmount(amount);
        if (!amountValidation.isValid) {
          return Left(ValidationFailure(message: amountValidation.errorMessage!));
        }
      }

      // Validate due date if provided
      if (dueDate != null && dueDate.isBefore(DateTime.now())) {
        return Left(ValidationFailure(message: 'Due date must be in the future'));
      }

      // Validate description length if provided
      if (description != null && description.length > 1000) {
        return Left(ValidationFailure(message: 'Description cannot exceed 1000 characters'));
      }

      // Get current debt to validate business rules
      final debtResult = await _debtRepository.getDebtById(debtId);
      return debtResult.fold(
        (failure) => Left(failure),
        (debt) {
          // Validate amount change doesn't affect paid amount incorrectly
          if (amount != null && amount < debt.totalPaid) {
            return Left(ValidationFailure(
              message: 'New debt amount (${amount.toStringAsFixed(2)}) cannot be less than already paid amount (${debt.totalPaid.toStringAsFixed(2)})'
            ));
          }

          // Validate status change business rules
          if (status != null) {
            // Cannot change status to completed if there's outstanding amount
            if (status == DebtStatus.completed && debt.remainingAmount > 0) {
              return Left(ValidationFailure(
                message: 'Cannot mark debt as completed while outstanding amount remains'
              ));
            }

            // Cannot change status from completed to active if debt is fully paid
            if (debt.status == DebtStatus.completed && status == DebtStatus.active) {
              return Left(ValidationFailure(
                message: 'Cannot reactivate a fully completed debt'
              ));
            }
          }

          return Right(DebtUpdateValidationResult(
            isValid: true,
            validationMessage: 'Debt update data is valid',
            currentDebt: debt,
          ));
        },
      );

    } catch (e) {
      return Left(ServerFailure(message: 'Failed to validate debt update data: ${e.toString()}'));
    }
  }
}

/// Result of debt validation
class DebtValidationResult {
  final bool isValid;
  final String validationMessage;

  const DebtValidationResult({
    required this.isValid,
    required this.validationMessage,
  });
}

/// Result of payment validation
class PaymentValidationResult {
  final bool isValid;
  final String validationMessage;
  final Debt debt;

  const PaymentValidationResult({
    required this.isValid,
    required this.validationMessage,
    required this.debt,
  });
}

/// Result of customer debt eligibility check
class CustomerDebtEligibilityResult {
  final bool canTakeDebt;
  final String customerId;
  final String customerName;
  final double currentOutstanding;
  final double requestedDebtAmount;
  final double projectedTotalOutstanding;
  final String businessRuleMessage;
  final String riskLevel;
  final DebtEligibilityReason reason;

  const CustomerDebtEligibilityResult({
    required this.canTakeDebt,
    required this.customerId,
    required this.customerName,
    required this.currentOutstanding,
    required this.requestedDebtAmount,
    required this.projectedTotalOutstanding,
    required this.businessRuleMessage,
    required this.riskLevel,
    required this.reason,
  });
}

/// Reasons for debt eligibility decision
enum DebtEligibilityReason {
  eligible,
  highRisk,
  exceedsLimit,
  tooManyActiveDebts,
  customerNotFound,
}

/// Result of payment timing calculation
class PaymentTimingResult {
  final int paymentDelay;
  final bool isOnTime;
  final String timingStatus;
  final DateTime dueDate;
  final DateTime paymentDate;

  const PaymentTimingResult({
    required this.paymentDelay,
    required this.isOnTime,
    required this.timingStatus,
    required this.dueDate,
    required this.paymentDate,
  });
}

/// Result of debt update validation
class DebtUpdateValidationResult {
  final bool isValid;
  final String validationMessage;
  final Debt currentDebt;

  const DebtUpdateValidationResult({
    required this.isValid,
    required this.validationMessage,
    required this.currentDebt,
  });
} 