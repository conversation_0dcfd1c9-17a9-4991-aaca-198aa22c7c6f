import 'dart:async';
import 'package:flutter/material.dart';
import 'package:app_links/app_links.dart';
import 'package:deyncare_app/core/routes/app_router.dart';
import 'package:deyncare_app/data/services/auth_service.dart';

/// Handles authentication-related deep links
class AuthDeepLinkHandler {
  final AppLinks _appLinks = AppLinks();
  final AuthService _authService;
  StreamSubscription? _deepLinkSubscription;

  AuthDeepLinkHandler({required AuthService authService})
      : _authService = authService;
  
  /// Initialize deep link handling for authentication flows
  Future<void> initialize(BuildContext context) async {
    try {
      // Handle initial link (app opened from link)
      final initialLink = await _appLinks.getInitialAppLink();
      if (initialLink != null) {
        _handleDeepLink(context, initialLink.toString());
      }
    
      // Handle links when app is running
      _deepLinkSubscription = _appLinks.uriLinkStream.listen((Uri? link) {
        if (link != null) {
          _handleDeepLink(context, link.toString());
        }
      }, onError: (error) {
        debugPrint('Auth deep link error: $error');
      });
    } catch (e) {
      debugPrint('Failed to handle auth deep links: $e');
    }
  }
  
  /// Handle auth-specific deep links
  Future<void> _handleDeepLink(BuildContext context, String link) async {
    debugPrint('Received auth deep link: $link');
    
    try {
      final uri = Uri.parse(link);
      
      // Process links with deyncare:// scheme
      if (uri.scheme == 'deyncare') {
        // Auth-specific links
        if (uri.host == 'auth') {
          // Handle password reset flows
          if (uri.path.contains('/reset-password')) {
            // Extract token if present
            final token = uri.queryParameters['token'];
            if (token != null) {
              // Navigate to reset password screen
              AppRouter.navigateToResetPassword(context, token: token);
            } else {
              AppRouter.navigateToResetSuccess(context);
            }
          }
          
          // Handle email verification flows
          else if (uri.path.contains('/verify-email')) {
            final email = uri.queryParameters['email'];
            final code = uri.queryParameters['code'];
            
            if (email != null && code != null) {
              // Navigate to verification with the code
              // The current AppRouter doesn't support initialCode parameter
              final user = await _authService.getCurrentUser();
              if (user != null) {
                // For deep links, we don't have selectedPlanId, but that's ok
                // The verification screen will handle this case
                AppRouter.navigateToVerification(context, user: user);
              } else {
                debugPrint('No user found for verification navigation');
                // Optionally: Show an error or fallback UI here
              }
              // TODO: Add support for verification code in verification screen
              debugPrint('Verification code from deep link: $code');
            }
          }
        }
      }
      
      // Process links with https:// scheme (web fallback)
      else if (uri.scheme == 'https' || uri.scheme == 'http') {
        if (uri.path.contains('/reset-password')) {
          final token = uri.queryParameters['token'];
          if (token != null) {
            // Navigate to reset password screen
            // Navigate to reset password screen
            AppRouter.navigateToResetPassword(context, token: token);
          }
        }
      }
    } catch (e) {
      debugPrint('Failed to parse auth deep link: $e');
    }
  }
  
  /// Dispose resources
  void dispose() {
    _deepLinkSubscription?.cancel();
    _deepLinkSubscription = null;
  }
}
