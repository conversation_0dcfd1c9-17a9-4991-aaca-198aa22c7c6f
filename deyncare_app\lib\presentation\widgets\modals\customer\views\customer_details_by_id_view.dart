import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/data/models/customer_model.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_bloc.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_event.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_state.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/modal_constants.dart';
import 'package:deyncare_app/presentation/widgets/modals/shared/form_mixins.dart';

/// Customer Details By ID View Widget
class CustomerDetailsByIdView extends StatefulWidget {
  final String customerId;

  const CustomerDetailsByIdView({super.key, required this.customerId});

  @override
  State<CustomerDetailsByIdView> createState() => _CustomerDetailsByIdViewState();
}

class _CustomerDetailsByIdViewState extends State<CustomerDetailsByIdView> 
    with BlocListenerMixin<CustomerDetailsByIdView> {
  
  bool _isLoading = true;
  CustomerBasicInfo? _customer;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    context.read<CustomerBloc>().add(LoadCustomerDetails(widget.customerId));
  }

  @override
  Widget build(BuildContext context) {
    return buildBlocListener<CustomerBloc, CustomerState>(
      listener: (context, state) {
        if (state is CustomerDetailsLoaded) {
          setState(() {
            _customer = state.response.data?.customer;
            _isLoading = false;
            _errorMessage = null;
          });
        } else if (state is CustomerError) {
          setState(() {
            _isLoading = false;
            _errorMessage = state.message;
          });
        }
      },
      child: Padding(
        padding: ModalConstants.defaultPadding,
        child: _buildContent(context),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState(context);
    }

    if (_customer == null) {
      return _buildNotFoundState(context);
    }

    return _buildCustomerDetails(context, _customer!);
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Theme.of(context).colorScheme.primary),
          ModalConstants.defaultSpacing,
          Text(
            'Loading customer details...',
            style: TextStyle(color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7)),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          ModalConstants.defaultSpacing,
          Text(
            'Error Loading Customer',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.error,
            ),
          ),
          ModalConstants.defaultSpacing,
          Text(
            _errorMessage!,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          ModalConstants.largeSpacing,
          ElevatedButton(
            onPressed: () {
              setState(() {
                _isLoading = true;
                _errorMessage = null;
              });
              context.read<CustomerBloc>().add(LoadCustomerDetails(widget.customerId));
            },
            style: ModalConstants.primaryButtonStyle(),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildNotFoundState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_off,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          ModalConstants.defaultSpacing,
          Text(
            'Customer Not Found',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          ModalConstants.defaultSpacing,
          Text(
            'The customer with ID "${widget.customerId}" could not be found.',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerDetails(BuildContext context, CustomerBasicInfo customer) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildCustomerHeader(context, customer),
        ModalConstants.defaultSpacing,
        _buildBasicInformation(context, customer),
        ModalConstants.defaultSpacing,
        _buildContactInformation(context, customer),
        ModalConstants.defaultSpacing,
        _buildRiskProfile(context),
        ModalConstants.largeSpacing,
      ],
    );
  }

  Widget _buildCustomerHeader(BuildContext context, CustomerBasicInfo customer) {
    return CommonCard(
      child: Column(
        children: [
          Icon(
            Icons.person_rounded,
            size: 48,
            color: Theme.of(context).colorScheme.primary,
          ),
          ModalConstants.defaultSpacing,
          Text(
            customer.customerName ?? 'Unknown Customer',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          ModalConstants.defaultSpacing,
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              customer.customerType ?? 'Unknown',
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInformation(BuildContext context, CustomerBasicInfo customer) {
    return CommonCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Basic Information',
            style: ModalConstants.sectionTitleStyle(context),
          ),
          ModalConstants.defaultSpacing,
          _buildDetailRow(context, 'Customer ID', customer.customerId ?? 'N/A'),
          const SizedBox(height: 12),
          _buildDetailRow(context, 'Name', customer.customerName ?? 'N/A'),
          const SizedBox(height: 12),
          _buildDetailRow(context, 'Customer Type', customer.customerType ?? 'N/A'),
          const SizedBox(height: 12),
          _buildDetailRow(context, 'Created At', _formatDate(customer.createdAt) ?? 'N/A'),
          const SizedBox(height: 12),
          _buildDetailRow(context, 'Updated At', _formatDate(customer.updatedAt) ?? 'N/A'),
        ],
      ),
    );
  }

  Widget _buildContactInformation(BuildContext context, CustomerBasicInfo customer) {
    return CommonCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Contact Information',
            style: ModalConstants.sectionTitleStyle(context),
          ),
          ModalConstants.defaultSpacing,
          _buildDetailRow(context, 'Phone', customer.phone ?? 'N/A'),
        ],
      ),
    );
  }

  Widget _buildRiskProfile(BuildContext context) {
    // Access data from the loaded state
    final state = context.read<CustomerBloc>().state;
    if (state is! CustomerDetailsLoaded) {
      return const SizedBox.shrink();
    }
    
    final data = state.response.data;
    final statistics = data?.statistics;
    final riskProfile = data?.riskProfile;
    final financials = statistics?.financials;
    final riskAnalysis = statistics?.riskAnalysis;
    
    return CommonCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.security_rounded,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Risk Profile & Debt Status',
                style: ModalConstants.sectionTitleStyle(context),
              ),
            ],
          ),
          ModalConstants.defaultSpacing,
          
          // Risk Level Display
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Risk Level:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              ModalConstants.defaultSpacing,
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getRiskLevelColor(riskProfile?.currentRiskLevel ?? riskAnalysis?.currentRiskLevel).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: _getRiskLevelColor(riskProfile?.currentRiskLevel ?? riskAnalysis?.currentRiskLevel).withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getRiskLevelIcon(riskProfile?.currentRiskLevel ?? riskAnalysis?.currentRiskLevel),
                        size: 16,
                        color: _getRiskLevelColor(riskProfile?.currentRiskLevel ?? riskAnalysis?.currentRiskLevel),
                      ),
                      const SizedBox(width: 6),
                      Text(
                        riskProfile?.currentRiskLevel ?? riskAnalysis?.currentRiskLevel ?? 'Unknown',
                        style: Theme.of(context).textTheme.labelMedium?.copyWith(
                          color: _getRiskLevelColor(riskProfile?.currentRiskLevel ?? riskAnalysis?.currentRiskLevel),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Debt Status Summary
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline,
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Debt Summary',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 12),
                
                // Total Outstanding
                _buildRiskDetailRow(
                  context,
                  'Total Outstanding',
                  financials?.totalOutstanding != null 
                    ? '\$${financials!.totalOutstanding!.toStringAsFixed(2)}'
                    : 'N/A',
                  Icons.account_balance_wallet_outlined,
                                      AppThemes.warningColor,
                ),
                
                const SizedBox(height: 8),
                
                // Active Debts Count
                _buildRiskDetailRow(
                  context,
                  'Active Debts',
                  statistics?.activeDebts?.toString() ?? '0',
                  Icons.receipt_long_outlined,
                  Theme.of(context).colorScheme.primary,
                ),
                
                const SizedBox(height: 8),
                
                // Total Debts
                _buildRiskDetailRow(
                  context,
                  'Total Debts',
                  statistics?.totalDebts?.toString() ?? '0',
                  Icons.receipt_outlined,
                  Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                
                const SizedBox(height: 8),
                
                // Collection Rate
                _buildRiskDetailRow(
                  context,
                  'Collection Rate',
                  financials?.collectionRate != null 
                    ? '${financials!.collectionRate}%'
                    : 'N/A',
                  Icons.trending_up,
                                      AppThemes.successColor,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Risk Assessment Details
          if (riskProfile?.riskScore != null || riskAnalysis?.riskScore != null || riskAnalysis?.riskFactors != null) ...[
            Text(
              'Risk Assessment Details',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            
            if (riskProfile?.riskScore != null || riskAnalysis?.riskScore != null)
              _buildDetailRow(context, 'Risk Score', '${(riskProfile?.riskScore ?? riskAnalysis?.riskScore ?? 0).toStringAsFixed(1)}/100'),
            
            if (riskProfile?.lastAssessment != null || riskAnalysis?.lastAssessment != null) ...[
              const SizedBox(height: 8),
              _buildDetailRow(context, 'Last Assessment', _formatDate(riskProfile?.lastAssessment ?? riskAnalysis?.lastAssessment) ?? 'N/A'),
            ],
            
            if (riskAnalysis?.riskFactors != null && riskAnalysis!.riskFactors!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Risk Factors:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              ...riskAnalysis.riskFactors!.map((factor) => Padding(
                padding: const EdgeInsets.only(top: 2),
                child: Text(
                  '• $factor',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              )),
            ],
          ],
        ],
      ),
    );
  }

  Widget _buildRiskDetailRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color iconColor,
  ) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: iconColor,
        ),
        const SizedBox(width: 8),
        Text(
          '$label:',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const Spacer(),
        Text(
          value,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Color _getRiskLevelColor(String? riskLevel) {
    switch (riskLevel?.toLowerCase()) {
      case 'low':
      case 'low risk':
        return AppThemes.successColor; // Use AppThemes for success
      case 'medium':
      case 'medium risk':
        return AppThemes.warningColor; // Use AppThemes for warning
      case 'high':
      case 'high risk':
        return Theme.of(context).colorScheme.error;
      case 'active':
      case 'active debt':
        return Theme.of(context).colorScheme.primary;
      default:
        return Theme.of(context).colorScheme.onSurfaceVariant;
    }
  }

  IconData _getRiskLevelIcon(String? riskLevel) {
    switch (riskLevel?.toLowerCase()) {
      case 'low':
      case 'low risk':
        return Icons.check_circle_outline;
      case 'medium':
      case 'medium risk':
        return Icons.warning_outlined;
      case 'high':
      case 'high risk':
        return Icons.error_outline;
      case 'active':
      case 'active debt':
        return Icons.account_balance_wallet_outlined;
      default:
        return Icons.help_outline;
    }
  }

  Widget _buildDetailRow(BuildContext context, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label:',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        ModalConstants.defaultSpacing,
        Expanded(
          child: Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }

  String? _formatDate(DateTime? date) {
    if (date == null) return null;
    return '${date.day}/${date.month}/${date.year}';
  }
} 