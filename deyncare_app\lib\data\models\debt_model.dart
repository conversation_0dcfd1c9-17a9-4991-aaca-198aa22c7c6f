import 'package:json_annotation/json_annotation.dart';
import '../../domain/models/debt.dart';

part 'debt_model.g.dart';

@JsonSerializable()
class DebtModel {
  final String debtId;
  final String customerId;
  final String shopId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'CustomerName')
  final String customerName;
  @<PERSON>son<PERSON><PERSON>(name: 'CustomerType')
  final String customerType;
  @<PERSON>son<PERSON>ey(name: 'DebtAmount')
  final double debtAmount;
  @<PERSON>son<PERSON>ey(name: 'OutstandingDebt')
  final double outstandingDebt;
  @<PERSON>son<PERSON>ey(name: 'DebtCreationDate')
  final DateTime debtCreationDate;
  @<PERSON>son<PERSON><PERSON>(name: 'DueDate')
  final DateTime dueDate;
  @Json<PERSON>ey(name: 'RepaymentTime')
  final int? repaymentTime;
  @JsonKey(name: 'DebtPaidRatio')
  final double debtPaidRatio;
  @<PERSON>son<PERSON>ey(name: 'PaymentDelay')
  final int paymentDelay;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'IsOnTime')
  final bool isOnTime;
  @<PERSON><PERSON><PERSON>ey(name: 'PaidAmount')
  final double paidAmount;
  @<PERSON>son<PERSON>ey(name: 'PaidDate')
  final DateTime? paidDate;
  @<PERSON>son<PERSON><PERSON>(name: 'RiskLevel')
  final String riskLevel;
  final String status;
  final String? description;
  final bool isDeleted;
  final DateTime createdAt;
  final DateTime updatedAt;

  const DebtModel({
    required this.debtId,
    required this.customerId,
    required this.shopId,
    required this.customerName,
    required this.customerType,
    required this.debtAmount,
    required this.outstandingDebt,
    required this.debtCreationDate,
    required this.dueDate,
    this.repaymentTime,
    required this.debtPaidRatio,
    required this.paymentDelay,
    required this.isOnTime,
    required this.paidAmount,
    this.paidDate,
    required this.riskLevel,
    required this.status,
    this.description,
    required this.isDeleted,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DebtModel.fromJson(Map<String, dynamic> json) =>
      _$DebtModelFromJson(json);

  Map<String, dynamic> toJson() => _$DebtModelToJson(this);

  /// Convert to domain model
  Debt toDomain() => Debt(
        debtId: debtId,
        customerId: customerId,
        customerName: customerName,
        shopId: shopId,
        amount: debtAmount,
        interestRate: null, // Not provided by backend
        dueDate: dueDate,
        createdAt: createdAt,
        updatedAt: updatedAt,
        status: _parseDebtStatus(status),
        description: description,
        payments: [], // Payments loaded separately
        totalPaid: paidAmount,
        remainingAmount: outstandingDebt,
        isOverdue: !isOnTime,
        daysPastDue: paymentDelay,
        riskAssessment: RiskAssessment(
          riskScore: debtPaidRatio * 100, // Convert ratio to score
          riskLevel: _parseRiskLevel(riskLevel),
          source: 'ML',
          assessmentDate: updatedAt,
        ),
      );

  /// Parse debt status string to enum
  DebtStatus _parseDebtStatus(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return DebtStatus.active;
      case 'completed':
      case 'paid':
        return DebtStatus.completed;
      case 'overdue':
        return DebtStatus.overdue;
      case 'defaulted':
        return DebtStatus.defaulted;
      case 'cancelled':
        return DebtStatus.cancelled;
      default:
        return DebtStatus.active;
    }
  }

  /// Parse risk level string to enum
  RiskLevel _parseRiskLevel(String riskLevel) {
    switch (riskLevel.toLowerCase()) {
      case 'low':
      case 'low risk':
        return RiskLevel.low;
      case 'medium':
      case 'medium risk':
        return RiskLevel.medium;
      case 'high':
      case 'high risk':
        return RiskLevel.high;
      case 'critical':
      case 'critical risk':
        return RiskLevel.critical;
      default:
        return RiskLevel.low;
    }
  }

  DebtModel copyWith({
    String? debtId,
    String? customerId,
    String? shopId,
    String? customerName,
    String? customerType,
    double? debtAmount,
    double? outstandingDebt,
    DateTime? debtCreationDate,
    DateTime? dueDate,
    int? repaymentTime,
    double? debtPaidRatio,
    int? paymentDelay,
    bool? isOnTime,
    double? paidAmount,
    DateTime? paidDate,
    String? riskLevel,
    String? status,
    String? description,
    bool? isDeleted,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DebtModel(
      debtId: debtId ?? this.debtId,
      customerId: customerId ?? this.customerId,
      shopId: shopId ?? this.shopId,
      customerName: customerName ?? this.customerName,
      customerType: customerType ?? this.customerType,
      debtAmount: debtAmount ?? this.debtAmount,
      outstandingDebt: outstandingDebt ?? this.outstandingDebt,
      debtCreationDate: debtCreationDate ?? this.debtCreationDate,
      dueDate: dueDate ?? this.dueDate,
      repaymentTime: repaymentTime ?? this.repaymentTime,
      debtPaidRatio: debtPaidRatio ?? this.debtPaidRatio,
      paymentDelay: paymentDelay ?? this.paymentDelay,
      isOnTime: isOnTime ?? this.isOnTime,
      paidAmount: paidAmount ?? this.paidAmount,
      paidDate: paidDate ?? this.paidDate,
      riskLevel: riskLevel ?? this.riskLevel,
      status: status ?? this.status,
      description: description ?? this.description,
      isDeleted: isDeleted ?? this.isDeleted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class CreateDebtRequest {
  final String customerId;
  final double debtAmount;
  final DateTime dueDate;
  final String? description;
  final double? paidAmount;
  final DateTime? paidDate;
  final String? paymentMethod;

  const CreateDebtRequest({
    required this.customerId,
    required this.debtAmount,
    required this.dueDate,
    this.description,
    this.paidAmount,
    this.paidDate,
    this.paymentMethod,
  });

  factory CreateDebtRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateDebtRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CreateDebtRequestToJson(this);
}

@JsonSerializable()
class UpdateDebtRequest {
  final double? debtAmount;
  final DateTime? dueDate;
  final String? description;
  final String? status;

  const UpdateDebtRequest({
    this.debtAmount,
    this.dueDate,
    this.description,
    this.status,
  });

  factory UpdateDebtRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateDebtRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateDebtRequestToJson(this);
}

@JsonSerializable()
class AddPaymentRequest {
  final double amount;
  final String paymentMethod;
  final String? notes;
  final DateTime? paymentDate;

  const AddPaymentRequest({
    required this.amount,
    required this.paymentMethod,
    this.notes,
    this.paymentDate,
  });

  factory AddPaymentRequest.fromJson(Map<String, dynamic> json) =>
      _$AddPaymentRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AddPaymentRequestToJson(this);
} 