import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/routes/app_router.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_header.dart';
import 'package:deyncare_app/presentation/screens/auth/login/widgets/login_form.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_state.dart';
import 'package:deyncare_app/presentation/widgets/deyncare_logo.dart';

/// Login screen that allows existing users to authenticate
class LoginScreen extends StatelessWidget {
  /// Optional email to pre-fill in the login form
  final String? prefilledEmail;

  const LoginScreen({
    super.key,
    this.prefilledEmail,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;

    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        // When user is authenticated, navigate to dashboard
        if (state is AuthAuthenticated) {
          // Show success toast before navigation
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Login successful!'),
              backgroundColor: ThemeUtils.getStatusColors(context).success,
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
          // Navigate to dashboard with replacement (can't go back)
          AppRouter.navigateToDashboard(context);
        }
      },
      child: Scaffold(
        backgroundColor: ThemeUtils.getBackgroundColor(context,
            type: BackgroundType.primary),
        body: SafeArea(
          child: Column(
            children: [
              // Compact header section
              Padding(
                padding: EdgeInsets.fromLTRB(
                  isTablet ? 48.0 : 24.0,
                  isTablet ? 24 : 16,
                  isTablet ? 48.0 : 24.0,
                  0,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Compact header with SVG logo
                    const AuthHeader(
                      title: 'Welcome Back',
                      subtitle: 'Sign in to continue to DeynCare',
                      showLogo: true,
                      logoVariant: DeynCareLogoVariant.svg,
                    ),
                  ],
                ),
              ),

              // Expanded form section to prevent scrolling
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: isTablet ? 48.0 : 24.0,
                  ),
                  child: Column(
                    children: [
                      SizedBox(height: isTablet ? 32 : 24),

                      // Login form in a clean card - takes available space
                      Expanded(
                        child: Card(
                          elevation: isTablet ? 8 : 4,
                          shadowColor: ThemeUtils.getShadowColor(context,
                              type: ShadowType.light),
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(isTablet ? 24 : 16),
                          ),
                          child: Padding(
                            padding: EdgeInsets.all(isTablet ? 32.0 : 24.0),
                            child: LoginForm(prefilledEmail: prefilledEmail),
                          ),
                        ),
                      ),

                      SizedBox(height: isTablet ? 24 : 16),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
