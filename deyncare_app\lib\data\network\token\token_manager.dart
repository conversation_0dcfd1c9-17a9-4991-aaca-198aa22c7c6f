import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logger/logger.dart';

/// Manages authentication tokens securely with JWT parsing and expiry checking
class TokenManager {
  final FlutterSecureStorage _secureStorage;
  final Logger _logger = Logger();
  
  // Token storage keys
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _tokenExpiryKey = 'token_expiry';
  static const String _tokenIssuedAtKey = 'token_issued_at';
  
  TokenManager({FlutterSecureStorage? secureStorage}) 
    : _secureStorage = secureStorage ?? const FlutterSecureStorage();
  
  /// Get the current access token from secure storage
  Future<String?> getAccessToken() async {
    try {
      return await _secureStorage.read(key: _accessTokenKey);
    } catch (e) {
      _logger.e('TokenManager: Error reading access token: $e');
      return null;
    }
  }
  
  /// Get the current refresh token from secure storage
  Future<String?> getRefreshToken() async {
    try {
      return await _secureStorage.read(key: _refreshTokenKey);
    } catch (e) {
      _logger.e('TokenManager: Error reading refresh token: $e');
      return null;
    }
  }
  
  /// Save tokens to secure storage with expiry tracking
  Future<void> saveTokens({
    required String accessToken, 
    required String refreshToken
  }) async {
    try {
      await _secureStorage.write(key: _accessTokenKey, value: accessToken);
      await _secureStorage.write(key: _refreshTokenKey, value: refreshToken);
      
      // Parse and save token expiry and issued time
      final expiry = _parseTokenExpiry(accessToken);
      final issuedAt = _parseTokenIssuedAt(accessToken);
      
      if (expiry != null) {
        await _secureStorage.write(
          key: _tokenExpiryKey, 
          value: expiry.millisecondsSinceEpoch.toString()
        );
      }
      
      if (issuedAt != null) {
        await _secureStorage.write(
          key: _tokenIssuedAtKey,
          value: issuedAt.millisecondsSinceEpoch.toString()
        );
      }
      
      _logger.d('TokenManager: Tokens saved successfully');
    } catch (e) {
      _logger.e('TokenManager: Error saving tokens: $e');
      rethrow;
    }
  }
  
  /// Clear all tokens from secure storage
  Future<void> deleteTokens() async {
    try {
      await _secureStorage.delete(key: _accessTokenKey);
      await _secureStorage.delete(key: _refreshTokenKey);
      await _secureStorage.delete(key: _tokenExpiryKey);
      await _secureStorage.delete(key: _tokenIssuedAtKey);
      _logger.d('TokenManager: All tokens deleted successfully');
    } catch (e) {
      _logger.e('TokenManager: Error deleting tokens: $e');
    }
  }
  
  /// Alias for deleteTokens for backward compatibility
  Future<void> clearTokens() async {
    return deleteTokens();
  }
  
  /// Check if access token is expired or will expire soon
  /// Reduced buffer from 5min to 2min to avoid premature expiry with 2h tokens
  Future<bool> isTokenExpired({Duration buffer = const Duration(minutes: 2)}) async {
    try {
      final expiryString = await _secureStorage.read(key: _tokenExpiryKey);
      if (expiryString == null) {
        _logger.d('TokenManager: No expiry data found, considering token expired');
        return true;
      }
      
      final expiry = DateTime.fromMillisecondsSinceEpoch(int.parse(expiryString));
      final now = DateTime.now();
      
      // Consider token expired if it expires within the buffer time
      final isExpired = now.isAfter(expiry.subtract(buffer));
      
      if (isExpired) {
        _logger.d('TokenManager: Token expired or expiring soon. Expiry: $expiry, Now: $now');
      }
      
      return isExpired;
    } catch (e) {
      _logger.e('TokenManager: Error checking token expiry: $e');
      return true; // Assume expired on error
    }
  }
  
  /// Check if token should be refreshed (expires within a larger buffer)
  Future<bool> shouldRefreshToken({Duration refreshBuffer = const Duration(minutes: 5)}) async {
    try {
      final expiryString = await _secureStorage.read(key: _tokenExpiryKey);
      if (expiryString == null) return true;
      
      final expiry = DateTime.fromMillisecondsSinceEpoch(int.parse(expiryString));
      final now = DateTime.now();
      
      // Should refresh if expires within the refresh buffer
      return now.isAfter(expiry.subtract(refreshBuffer));
    } catch (e) {
      _logger.e('TokenManager: Error checking if token should refresh: $e');
      return true; // Assume should refresh on error
    }
  }
  
  /// Check if refresh token exists and is valid
  Future<bool> hasValidRefreshToken() async {
    try {
      final refreshToken = await getRefreshToken();
      if (refreshToken == null || refreshToken.isEmpty) return false;
      
      // Parse refresh token to check if it's expired
      final expiry = _parseTokenExpiry(refreshToken);
      if (expiry == null) return true; // Assume valid if can't parse
      
      final now = DateTime.now();
      return now.isBefore(expiry.subtract(const Duration(minutes: 1))); // 1 minute buffer
    } catch (e) {
      _logger.e('TokenManager: Error checking refresh token validity: $e');
      return false;
    }
  }
  
  /// Validate token integrity by checking structure and claims
  Future<bool> validateTokenIntegrity(String? token) async {
    if (token == null || token.isEmpty) return false;
    
    try {
      final parts = token.split('.');
      if (parts.length != 3) return false;
      
      // Try to decode payload to validate structure
      final payload = _decodeTokenPayload(token);
      if (payload == null) return false;
      
      // Check for required claims
      if (!payload.containsKey('exp') || !payload.containsKey('iat')) {
        return false;
      }
      
      // Check if token is not expired
      final exp = payload['exp'] as int;
      final expiry = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      
      return DateTime.now().isBefore(expiry);
    } catch (e) {
      _logger.e('TokenManager: Error validating token integrity: $e');
      return false;
    }
  }
  
  /// Get time until token expires
  Future<Duration?> getTimeUntilExpiry() async {
    try {
      final expiryString = await _secureStorage.read(key: _tokenExpiryKey);
      if (expiryString == null) return null;
      
      final expiry = DateTime.fromMillisecondsSinceEpoch(int.parse(expiryString));
      final now = DateTime.now();
      
      if (now.isAfter(expiry)) return Duration.zero;
      
      return expiry.difference(now);
    } catch (e) {
      _logger.e('TokenManager: Error calculating time until expiry: $e');
      return null;
    }
  }
  
  /// Parse JWT token expiry from payload
  DateTime? _parseTokenExpiry(String token) {
    try {
      final payload = _decodeTokenPayload(token);
      if (payload?.containsKey('exp') == true) {
        final exp = payload!['exp'] as int;
        return DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      }
    } catch (e) {
      _logger.e('TokenManager: Error parsing token expiry: $e');
    }
    return null;
  }
  
  /// Parse JWT token issued at time from payload
  DateTime? _parseTokenIssuedAt(String token) {
    try {
      final payload = _decodeTokenPayload(token);
      if (payload?.containsKey('iat') == true) {
        final iat = payload!['iat'] as int;
        return DateTime.fromMillisecondsSinceEpoch(iat * 1000);
      }
    } catch (e) {
      _logger.e('TokenManager: Error parsing token issued at: $e');
    }
    return null;
  }
  
  /// Decode JWT token payload
  Map<String, dynamic>? _decodeTokenPayload(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;
      
      // Decode JWT payload (Base64URL)
      String payload = parts[1];
      
      // Add padding if needed for Base64 decoding
      switch (payload.length % 4) {
        case 2:
          payload += '==';
          break;
        case 3:
          payload += '=';
          break;
      }
      
      final bytes = base64Url.decode(payload);
      final json = utf8.decode(bytes);
      return jsonDecode(json) as Map<String, dynamic>;
    } catch (e) {
      _logger.e('TokenManager: Error decoding token payload: $e');
      return null;
    }
  }
  
  /// Get token expiry information for debugging
  Future<Map<String, dynamic>> getTokenInfo() async {
    final accessToken = await getAccessToken();
    final refreshToken = await getRefreshToken();
    final isExpired = await isTokenExpired();
    final shouldRefresh = await shouldRefreshToken();
    final timeUntilExpiry = await getTimeUntilExpiry();
    
    return {
      'hasAccessToken': accessToken != null,
      'hasRefreshToken': refreshToken != null,
      'isExpired': isExpired,
      'shouldRefresh': shouldRefresh,
      'timeUntilExpiry': timeUntilExpiry?.inMinutes,
      'tokenExpiry': accessToken != null ? _parseTokenExpiry(accessToken)?.toIso8601String() : null,
      'refreshTokenValid': await hasValidRefreshToken(),
      'accessTokenValid': await validateTokenIntegrity(accessToken),
    };
  }
}
