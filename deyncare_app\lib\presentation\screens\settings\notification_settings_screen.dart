import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/common_app_bar.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_button.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'package:deyncare_app/data/services/auth_service.dart';
import 'package:deyncare_app/core/utils/api_toast_handler.dart';

/// Notification settings screen for managing notification preferences
/// Connected to backend ShopSetting model for persistent storage
class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  // Backend-connected notification settings
  bool _paymentRemindersEnabled = true;
  String _reminderFrequency = 'weekly';
  bool _businessHoursOnly = true;
  int _smsReminderDaysBefore = 7;
  
  // Loading state
  bool _isLoading = false;
  bool _isSaving = false;
  
  // Services
  final AuthService _authService = AuthService();

  @override
  void initState() {
    super.initState();
    _loadNotificationSettings();
  }

  Future<void> _loadNotificationSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load notification settings - using default values for now
      // TODO: Implement proper shop settings API integration
      setState(() {
        _paymentRemindersEnabled = true;
        _reminderFrequency = 'weekly';
        _businessHoursOnly = true;
        _smsReminderDaysBefore = 7;
      });
    } catch (e) {
      ApiToastHandler.handleError(e, fallbackMessage: 'Failed to load notification settings');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CommonAppBar(
        title: 'Notification Settings',
      ),
      body: _isLoading 
        ? const Center(child: CircularProgressIndicator())
        : SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                
                // Payment Reminders Section
                _buildSectionTitle('Payment Reminders'),
                const SizedBox(height: 16),
                _buildPaymentReminderSettings(),
                
                const SizedBox(height: 32),
                
                // Advanced Settings Section
                _buildSectionTitle('Advanced Settings'),
                const SizedBox(height: 16),
                _buildAdvancedSettings(),
                
                const SizedBox(height: 32),
                
                // Save Button
                _buildSaveButton(),
                
                const SizedBox(height: 20),
              ],
            ),
          ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
        fontWeight: FontWeight.bold,
        color: ThemeUtils.getTextColor(context, type: TextColorType.primary),
      ),
    );
  }

  Widget _buildPaymentReminderSettings() {
    final statusColors = ThemeUtils.getStatusColors(context);
    
    return Column(
      children: [
        CommonCard(
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: statusColors.infoSurface,
                borderRadius: BorderRadius.circular(8),
              ),
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: Icon(
                  _paymentRemindersEnabled ? Icons.notifications_active : Icons.notifications_off,
                  key: ValueKey(_paymentRemindersEnabled),
                  color: statusColors.info,
                  size: 20,
                ),
              ),
            ),
            title: Text(
              'Payment Reminders',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            subtitle: Text(
              _paymentRemindersEnabled 
                  ? 'Receive reminders for upcoming payments' 
                  : 'Payment reminders disabled',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            trailing: AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: Switch(
                key: ValueKey(_paymentRemindersEnabled),
                value: _paymentRemindersEnabled,
                onChanged: (bool value) {
                  HapticFeedback.lightImpact();
                  setState(() {
                    _paymentRemindersEnabled = value;
                  });
                  _showNotificationStatusSnackBar(value, 'Payment Reminders');
                },
                activeColor: AppThemes.primaryColor,
                activeTrackColor: AppThemes.primaryColor.withValues(alpha: 0.3),
                inactiveThumbColor: Colors.grey[400],
                inactiveTrackColor: Colors.grey[300],
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        CommonCard(
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: statusColors.warningSurface,
                borderRadius: BorderRadius.circular(8),
              ),
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: Icon(
                  _businessHoursOnly ? Icons.business_center : Icons.business_center_outlined,
                  key: ValueKey(_businessHoursOnly),
                  color: statusColors.warning,
                  size: 20,
                ),
              ),
            ),
            title: Text(
              'Business Hours Only',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            subtitle: Text(
              _businessHoursOnly 
                  ? 'Only send reminders during business hours' 
                  : 'Send reminders outside business hours',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            trailing: AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: Switch(
                key: ValueKey(_businessHoursOnly),
                value: _businessHoursOnly,
                onChanged: (bool value) {
                  HapticFeedback.lightImpact();
                  setState(() {
                    _businessHoursOnly = value;
                  });
                  _showNotificationStatusSnackBar(value, 'Business Hours Only');
                },
                activeColor: AppThemes.primaryColor,
                activeTrackColor: AppThemes.primaryColor.withValues(alpha: 0.3),
                inactiveThumbColor: Colors.grey[400],
                inactiveTrackColor: Colors.grey[300],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAdvancedSettings() {
    final statusColors = ThemeUtils.getStatusColors(context);
    
    return Column(
      children: [
        CommonCard(
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: statusColors.infoSurface,
                borderRadius: BorderRadius.circular(8),
              ),
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: Icon(
                  _reminderFrequency == 'daily' ? Icons.access_time : Icons.access_time_rounded,
                  key: ValueKey(_reminderFrequency),
                  color: statusColors.info,
                  size: 20,
                ),
              ),
            ),
            title: Text(
              'Reminder Frequency',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            subtitle: Text(
              _reminderFrequency == 'daily' ? 'Daily reminders' : 'Weekly reminders',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            trailing: AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: DropdownButton<String>(
                key: ValueKey(_reminderFrequency),
                value: _reminderFrequency,
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    HapticFeedback.lightImpact();
                    setState(() {
                      _reminderFrequency = newValue;
                    });
                    _showNotificationStatusSnackBar(true, 'Reminder Frequency');
                  }
                },
                items: const [
                  DropdownMenuItem(value: 'daily', child: Text('Daily')),
                  DropdownMenuItem(value: 'weekly', child: Text('Weekly')),
                ],
                icon: const Icon(Icons.arrow_drop_down, color: Colors.grey),
                underline: Container(),
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        CommonCard(
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: statusColors.warningSurface,
                borderRadius: BorderRadius.circular(8),
              ),
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: Icon(
                  _smsReminderDaysBefore == 1 ? Icons.notifications_active : Icons.notifications_off,
                  key: ValueKey(_smsReminderDaysBefore),
                  color: statusColors.warning,
                  size: 20,
                ),
              ),
            ),
            title: Text(
              'SMS Reminder Days Before Due',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            subtitle: Text(
              'Send SMS reminders ${(_smsReminderDaysBefore == 1) ? '1 day' : '$_smsReminderDaysBefore days'} before due date.',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            trailing: AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: DropdownButton<int>(
                key: ValueKey(_smsReminderDaysBefore),
                value: _smsReminderDaysBefore,
                onChanged: (int? newValue) {
                  if (newValue != null) {
                    HapticFeedback.lightImpact();
                    setState(() {
                      _smsReminderDaysBefore = newValue;
                    });
                    _showNotificationStatusSnackBar(true, 'SMS Reminder Days Before Due');
                  }
                },
                items: const [
                  DropdownMenuItem(value: 1, child: Text('1 day')),
                  DropdownMenuItem(value: 3, child: Text('3 days')),
                  DropdownMenuItem(value: 7, child: Text('7 days')),
                ],
                icon: const Icon(Icons.arrow_drop_down, color: Colors.grey),
                underline: Container(),
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSaveButton() {
    return AuthButton(
      label: 'Save Settings',
      onPressed: _isSaving ? null : _saveNotificationSettings,
      isLoading: _isSaving,
    );
  }

  Future<void> _saveNotificationSettings() async {
    setState(() {
      _isSaving = true;
    });

    try {
      // TODO: Implement proper shop settings update API
      // Simulating save for now
      await Future.delayed(const Duration(seconds: 1));
      
      ApiToastHandler.handleSuccess(message: 'Notification settings saved successfully!');
    } catch (e) {
      ApiToastHandler.handleError(e, fallbackMessage: 'Failed to save notification settings');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  Widget _buildInfoSection() {
    return CommonCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppThemes.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Information',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: ThemeUtils.getTextColor(context, type: TextColorType.primary),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          _buildInfoItem(
            'Notifications',
            'Control whether you receive app notifications for updates, messages, and important events.',
          ),
          
          const SizedBox(height: 16),
          
          _buildInfoItem(
            'Debt Reminders',
            'Enable to receive automatic reminders for upcoming debt payments and due dates.',
          ),
          
          const SizedBox(height: 16),
          
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppThemes.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppThemes.primaryColor.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.construction,
                  color: AppThemes.primaryColor,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Note: These are UI/UX toggles only. Backend integration coming soon.',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppThemes.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String title, String description) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: ThemeUtils.getTextColor(context, type: TextColorType.primary),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          description,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: ThemeUtils.getTextColor(context, type: TextColorType.secondary),
            height: 1.4,
          ),
        ),
      ],
    );
  }

  void _showNotificationStatusSnackBar(bool isEnabled, String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: Icon(
                isEnabled ? Icons.check_circle : Icons.cancel,
                key: ValueKey(isEnabled),
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              '$feature ${isEnabled ? 'enabled' : 'disabled'}',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        backgroundColor: isEnabled ? AppThemes.successColor : AppThemes.warningColor,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }
} 