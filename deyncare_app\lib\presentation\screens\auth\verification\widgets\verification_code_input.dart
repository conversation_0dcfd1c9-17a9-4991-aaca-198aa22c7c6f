import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';

/// A custom verification code input field with separate boxes for each digit
class VerificationCodeInput extends StatefulWidget {
  final int length;
  final Function(String) onCompleted;
  final Function(String) onChanged;
  final TextEditingController? controller;
  
  const VerificationCodeInput({
    super.key,
    this.length = 6,
    required this.onCompleted,
    required this.onChanged,
    this.controller,
  });

  @override
  State<VerificationCodeInput> createState() => _VerificationCodeInputState();
}

class _VerificationCodeInputState extends State<VerificationCodeInput> {
  late List<FocusNode> _focusNodes;
  late List<TextEditingController> _controllers;
  
  @override
  void initState() {
    super.initState();
    
    // Initialize focus nodes and controllers for each digit
    _focusNodes = List.generate(widget.length, (index) => FocusNode());
    _controllers = List.generate(widget.length, (index) => TextEditingController());
    
    // If a main controller is provided, listen for changes
    if (widget.controller != null) {
      widget.controller!.addListener(() {
        final text = widget.controller!.text;
        
        // Fill in the individual boxes
        for (int i = 0; i < widget.length; i++) {
          if (i < text.length) {
            _controllers[i].text = text[i];
          } else {
            _controllers[i].clear();
          }
        }
      });
    }
  }
  
  @override
  void dispose() {
    // Clean up resources
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var node in _focusNodes) {
      node.dispose();
    }
    super.dispose();
  }
  
  // Get the combined value from all digit boxes
  String _getVerificationCode() {
    return _controllers.map((controller) => controller.text).join();
  }
  
  // Handle input in a specific box
  void _handleDigitInput(int index, String value) {
    // If a digit was entered
    if (value.isNotEmpty) {
      // If not the last digit box, move focus to next
      if (index < widget.length - 1) {
        FocusScope.of(context).requestFocus(_focusNodes[index + 1]);
      } else {
        // This is the last box, hide the keyboard
        FocusScope.of(context).unfocus();
        
        // Call onCompleted callback
        widget.onCompleted(_getVerificationCode());
      }
    }
    
    // Call onChange with current combined value
    widget.onChanged(_getVerificationCode());
    
    // Update the main controller if provided
    if (widget.controller != null) {
      widget.controller!.text = _getVerificationCode();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(widget.length, (index) {
        return SizedBox(
          width: 50,
          height: 56,
          child: TextFormField(
            controller: _controllers[index],
            focusNode: _focusNodes[index],
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            decoration: InputDecoration(
              contentPadding: EdgeInsets.zero,
              filled: true,
              fillColor: AppThemes.textFieldBackgroundColor,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: AppThemes.dividerColor,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: AppThemes.primaryColor,
                  width: 2,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: AppThemes.dividerColor,
                  width: 1,
                ),
              ),
            ),
            inputFormatters: [
              LengthLimitingTextInputFormatter(1),
              FilteringTextInputFormatter.digitsOnly,
            ],
            onChanged: (value) {
              // Handle backspace
              if (value.isEmpty && index > 0) {
                FocusScope.of(context).requestFocus(_focusNodes[index - 1]);
              }
              _handleDigitInput(index, value);
            },
            onTap: () {
              // Select all text when tapped
              _controllers[index].selection = TextSelection(
                baseOffset: 0,
                extentOffset: _controllers[index].text.length,
              );
            },
          ),
        );
      }),
    );
  }
}
