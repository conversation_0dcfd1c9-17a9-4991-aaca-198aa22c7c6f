import 'package:flutter/material.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';

/// A custom Speed Dial Floating Action Button with smooth animations
class SpeedDialFAB extends StatefulWidget {
  final AnimatedIconData animatedIcon;
  final Color backgroundColor;
  final Color foregroundColor;
  final Curve curve;
  final List<SpeedDialChild> children;

  const SpeedDialFAB({
    super.key,
    required this.animatedIcon,
    required this.backgroundColor,
    required this.foregroundColor,
    required this.curve,
    required this.children,
  });

  @override
  State<SpeedDialFAB> createState() => _SpeedDialFABState();
}

class _SpeedDialFABState extends State<SpeedDialFAB>
    with TickerProviderStateMixin {
  bool _isOpen = false;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: widget.curve,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggle() {
    setState(() {
      _isOpen = !_isOpen;
    });
    
    if (_isOpen) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        ..._buildSpeedDialChildren(),
        const SizedBox(height: 16),
        FloatingActionButton(
          onPressed: _toggle,
          backgroundColor: widget.backgroundColor,
          foregroundColor: widget.foregroundColor,
          elevation: 8,
          heroTag: "speed_dial_main",
          child: AnimatedRotation(
            turns: _isOpen ? 0.125 : 0.0, // 45 degree rotation when open
            duration: const Duration(milliseconds: 300),
            child: Icon(
              _isOpen ? Icons.close : Icons.add,
              size: 28,
            ),
          ),
        ),
      ],
    );
  }

  List<Widget> _buildSpeedDialChildren() {
    return List.generate(widget.children.length, (index) {
      final child = widget.children[index];
      return AnimatedBuilder(
        animation: _animation,
        builder: (context, widget) {
          return Transform.scale(
            scale: _animation.value,
            child: Opacity(
              opacity: _animation.value,
              child: Container(
                margin: const EdgeInsets.only(bottom: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // Label
                    if (child.label != null)
                      Container(
                        margin: const EdgeInsets.only(right: 8),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).cardColor,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: ThemeUtils.getShadowColor(context, type: ShadowType.light),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Text(
                          child.label!,
                                                      style: child.labelStyle ??
                                TextStyle(
                                 color: ThemeUtils.getTextColor(context, type: TextColorType.primary),
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                        ),
                      ),
                    const SizedBox(width: 16),
                    // FAB
                    FloatingActionButton(
                      onPressed: () {
                        _toggle();
                        child.onTap?.call();
                      },
                      backgroundColor: child.backgroundColor,
                      foregroundColor: child.foregroundColor ?? Colors.white,
                      mini: true,
                      elevation: 4,
                      heroTag: "speed_dial_$index",
                      child: child.child,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );
    }).reversed.toList();
  }
}

/// Individual child item for the Speed Dial
class SpeedDialChild {
  final Widget child;
  final Color backgroundColor;
  final Color? foregroundColor;
  final String? label;
  final TextStyle? labelStyle;
  final VoidCallback? onTap;

  const SpeedDialChild({
    required this.child,
    required this.backgroundColor,
    this.foregroundColor,
    this.label,
    this.labelStyle,
    this.onTap,
  });
} 