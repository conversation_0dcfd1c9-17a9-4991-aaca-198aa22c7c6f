import 'package:deyncare_app/data/models/plan_model.dart';
import 'package:deyncare_app/domain/repositories/plan_repository.dart';

/// Use case for fetching details of a specific subscription plan
///
/// This class handles the business logic for retrieving a plan's data
/// by its ID, interacting with the plan repository.
class GetPlanDetailsUseCase {
  final PlanRepository _repository;

  /// Creates a new instance with the required repository
  GetPlanDetailsUseCase(this._repository);

  /// Executes the use case to fetch plan details by ID.
  ///
  /// Returns a Future<PlanModel> upon successful retrieval.
  /// Throws exceptions if the plan is not found or retrieval fails.
  Future<PlanModel> execute(String planId) async {
    return await _repository.getPlanById(planId);
  }
} 