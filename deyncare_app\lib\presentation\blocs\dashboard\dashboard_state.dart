import 'package:equatable/equatable.dart';

/// KPI Data model for dashboard metrics
class KPIData {
  final double todayPayments;
  final double outstandingDebts;
  final int newCustomers;
  final String riskLevel;
  final double paymentsGrowth;
  final int overdueDebts;
  final String riskTrend;

  const KPIData({
    this.todayPayments = 0.0,
    this.outstandingDebts = 0.0,
    this.newCustomers = 0,
    this.riskLevel = 'Low',
    this.paymentsGrowth = 0.0,
    this.overdueDebts = 0,
    this.riskTrend = 'Stable',
  });

  @override
  List<Object> get props => [
    todayPayments,
    outstandingDebts,
    newCustomers,
    riskLevel,
    paymentsGrowth,
    overdueDebts,
    riskTrend,
  ];

  KPIData copyWith({
    double? todayPayments,
    double? outstandingDebts,
    int? newCustomers,
    String? riskLevel,
    double? paymentsGrowth,
    int? overdueDebts,
    String? riskTrend,
  }) {
    return KPIData(
      todayPayments: todayPayments ?? this.todayPayments,
      outstandingDebts: outstandingDebts ?? this.outstandingDebts,
      newCustomers: newCustomers ?? this.newCustomers,
      riskLevel: riskLevel ?? this.riskLevel,
      paymentsGrowth: paymentsGrowth ?? this.paymentsGrowth,
      overdueDebts: overdueDebts ?? this.overdueDebts,
      riskTrend: riskTrend ?? this.riskTrend,
    );
  }
}

class ActivityItem extends Equatable {
  final String id;
  final String type; // 'sale', 'payment', 'customer', 'debt'
  final String title;
  final String subtitle;
  final String amount;
  final DateTime timestamp;
  final String icon;
  final String status;

  const ActivityItem({
    required this.id,
    required this.type,
    required this.title,
    required this.subtitle,
    required this.amount,
    required this.timestamp,
    required this.icon,
    this.status = 'completed',
  });

  @override
  List<Object?> get props => [
        id,
        type,
        title,
        subtitle,
        amount,
        timestamp,
        icon,
        status,
      ];
}

class ChartData extends Equatable {
  final String type;
  final List<Map<String, dynamic>> data;
  final String period;
  final DateTime lastUpdated;

  const ChartData({
    required this.type,
    required this.data,
    required this.period,
    required this.lastUpdated,
  });

  @override
  List<Object?> get props => [type, data, period, lastUpdated];
}

class NotificationItem extends Equatable {
  final String id;
  final String title;
  final String message;
  final String type; // 'info', 'warning', 'error', 'success'
  final DateTime timestamp;
  final bool isRead;
  final Map<String, dynamic>? actionData;

  const NotificationItem({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    this.isRead = false,
    this.actionData,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        message,
        type,
        timestamp,
        isRead,
        actionData,
      ];
}

/// Base class for all dashboard states
abstract class DashboardState extends Equatable {
  const DashboardState();

  @override
  List<Object?> get props => [];
}

/// Initial state when dashboard is first created
class DashboardInitial extends DashboardState {
  const DashboardInitial();
}

/// State when dashboard is loading
class DashboardLoading extends DashboardState {
  final String? loadingMessage;

  const DashboardLoading({this.loadingMessage});

  @override
  List<Object?> get props => [loadingMessage];
}

/// State when dashboard data is successfully loaded
class DashboardLoaded extends DashboardState {
  final KPIData kpiData;
  final List<ActivityItem> recentActivity;
  final List<ChartData> chartData;
  final List<NotificationItem> notifications;
  final int currentTabIndex;
  final bool isRefreshing;
  final bool isConnected;
  final DateTime lastUpdated;
  final Map<String, dynamic>? appliedFilters;

  const DashboardLoaded({
    required this.kpiData,
    required this.recentActivity,
    required this.chartData,
    required this.notifications,
    this.currentTabIndex = 0,
    this.isRefreshing = false,
    this.isConnected = true,
    required this.lastUpdated,
    this.appliedFilters,
  });

  @override
  List<Object?> get props => [
        kpiData,
        recentActivity,
        chartData,
        notifications,
        currentTabIndex,
        isRefreshing,
        isConnected,
        lastUpdated,
        appliedFilters,
      ];

  DashboardLoaded copyWith({
    KPIData? kpiData,
    List<ActivityItem>? recentActivity,
    List<ChartData>? chartData,
    List<NotificationItem>? notifications,
    int? currentTabIndex,
    bool? isRefreshing,
    bool? isConnected,
    DateTime? lastUpdated,
    Map<String, dynamic>? appliedFilters,
  }) {
    return DashboardLoaded(
      kpiData: kpiData ?? this.kpiData,
      recentActivity: recentActivity ?? this.recentActivity,
      chartData: chartData ?? this.chartData,
      notifications: notifications ?? this.notifications,
      currentTabIndex: currentTabIndex ?? this.currentTabIndex,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      isConnected: isConnected ?? this.isConnected,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      appliedFilters: appliedFilters ?? this.appliedFilters,
    );
  }
}

/// State when dashboard encounters an error
class DashboardError extends DashboardState {
  final String message;
  final String? errorCode;
  final bool canRetry;

  const DashboardError({
    required this.message,
    this.errorCode,
    this.canRetry = true,
  });

  @override
  List<Object?> get props => [message, errorCode, canRetry];
}

/// State when dashboard is refreshing data
class DashboardRefreshing extends DashboardState {
  final DashboardLoaded previousState;

  const DashboardRefreshing({required this.previousState});

  @override
  List<Object?> get props => [previousState];
}

/// State when KPI data is being loaded
class DashboardKPILoading extends DashboardState {
  final DashboardLoaded currentState;

  const DashboardKPILoading({required this.currentState});

  @override
  List<Object?> get props => [currentState];
}

/// State when chart data is being loaded
class DashboardChartLoading extends DashboardState {
  final DashboardLoaded currentState;
  final String chartType;

  const DashboardChartLoading({
    required this.currentState,
    required this.chartType,
  });

  @override
  List<Object?> get props => [currentState, chartType];
}

/// State when activity data is being loaded
class DashboardActivityLoading extends DashboardState {
  final DashboardLoaded currentState;

  const DashboardActivityLoading({required this.currentState});

  @override
  List<Object?> get props => [currentState];
}

/// State when notifications are being loaded
class DashboardNotificationsLoading extends DashboardState {
  final DashboardLoaded currentState;

  const DashboardNotificationsLoading({required this.currentState});

  @override
  List<Object?> get props => [currentState];
}

/// State when user is offline
class DashboardOffline extends DashboardState {
  final DashboardLoaded? cachedState;

  const DashboardOffline({this.cachedState});

  @override
  List<Object?> get props => [cachedState];
}

/// State when search results are available
class DashboardSearchResults extends DashboardState {
  final String query;
  final String searchType;
  final List<Map<String, dynamic>> results;
  final DashboardLoaded baseState;

  const DashboardSearchResults({
    required this.query,
    required this.searchType,
    required this.results,
    required this.baseState,
  });

  @override
  List<Object?> get props => [query, searchType, results, baseState];
}

/// State when navigation action is triggered
class DashboardNavigationTriggered extends DashboardState {
  final String destination;
  final Map<String, dynamic>? parameters;

  const DashboardNavigationTriggered({
    required this.destination,
    this.parameters,
  });

  @override
  List<Object?> get props => [destination, parameters];
} 